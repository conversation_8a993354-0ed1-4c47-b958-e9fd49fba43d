import { IConfig, IPlugin } from 'umi-types';
import defaultSettings from './defaultSettings'; // https://umijs.org/config/
const { REACT_APP_ENV } = process.env;
import slash from 'slash2';
import webpackPlugin from './plugin.config';
import darkTheme from './darkConfig';
import proxy from './proxy';

const { pwa, primaryColor } = defaultSettings; // do not use in your production ;
const plugins: IPlugin[] = [
  ['umi-plugin-antd-icon-config', {}],
  [
    'umi-plugin-react',
    {
      antd: true,
      dva: {
        hmr: true,
      },
      locale: {
        // default false
        enable: true,
        // default zh-CN
        default: 'zh-CN',
        // default true, when it is true, will use `navigator.language` overwrite default
        baseNavigator: true,
      },
      dynamicImport: {
        loadingComponent: './components/PageLoading/index',
        webpackChunkName: true,
        level: 3,
      },
      pwa: pwa
        ? {
          workboxPluginMode: 'InjectManifest',
          workboxOptions: {
            importWorkboxFrom: 'local',
          },
        }
        : false,
      // dll features https://webpack.js.org/plugins/dll-plugin/
      // dll: {
      //   include: ['dva', 'dva/router', 'dva/saga', 'dva/fetch'],
      //   exclude: ['@babel/runtime', 'netlify-lambda'],
      // },
    },
  ],
];

const getFundAnalyzeRoutes = parentPath => {
  return [
    {
      path: `${parentPath}/:id/invest_performance`,
      component: './persona/InvestPerformance',
    },
    {
      path: `${parentPath}/:id/asset_structure`,
      component: './persona/AssetStructure',
    },
    {
      path: `${parentPath}/:id/stock_analyze`,
      component: './persona/StockPositionAnalyze',
    },
    {
      path: `${parentPath}/:id/bond_analyze`,
      component: './persona/BondPositionAnalyze',
    },
    {
      path: `${parentPath}/:id/mom_analyze`,
      component: './persona/MomAnalyze',
    },
    {
      path: `${parentPath}/:id/attribution_analyze`,
      component: './analysis/attribution',
    },
    {
      path: `${parentPath}/:id/absolute_attribution`,
      component: './analysis/attribution',
    },
    {
      path: `${parentPath}/:id/bond_attribution`,
      component: './analysis/attribution',
    },
    {
      path: `${parentPath}/:id/stock_attribution`,
      component: './analysis/attribution/TopDown',
    },
    {
      path: `${parentPath}/:id/convtbond_attribution`,
      component: './analysis/attribution/TopDown',
    },
    {
      path: `${parentPath}/:id/multiasset_attribution`,
      component: './analysis/attribution/TopDown',
    },
    {
      path: `${parentPath}/:id/style_attribution`,
      component: './analysis/attribution/BarraStyle',
    },
    {
      path: `${parentPath}/:id/position_series_query`,
      component: './persona/FundPositionSeriesQuery',
    },
    {
      path: `${parentPath}/:id/factor_evaluation`,
      component: './persona/FactorEvaluation',
    },
    {
      path: `${parentPath}/:id/manager_list`,
      component: './persona/ManagerList',
    },
    {
      path: `${parentPath}/:id/company`,
      component: './companypersona',
    },
    {
      path: `${parentPath}/:id/persona`,
      component: './managerpersona',
    },
    {
      path: `${parentPath}/:id/detection_performance`,
      component: './persona/detection/RevenuePerfWrapper',
    },
    {
      path: `${parentPath}/:id/detection_asset_allocation`,
      component: './persona/detection/AssetAllocationWrapper',
    },
    {
      path: `${parentPath}/:id/detection_ind_allocation`,
      component: './persona/detection/IndustryAllocationWrapper',
    },
    {
      path: `${parentPath}/:id/detection_ind_prefer`,
      component: './persona/detection/IndustryPreferWrapper',
    },
    {
      path: `${parentPath}/:id/detection_position_detail`,
      component: './persona/FundPositionSeriesQuery',
    },
    {
      path: `${parentPath}/:id/detection_stock_attr`,
      component: './analysis/attribution/TopDown',
    },
    {
      path: `${parentPath}/:id/ref_due_doc`,
      component: './duedocs/RefDueDoc',
    },
    {
      path: `${parentPath}/:id/fundtags`,
      component: './persona/FundTags',
    },
  ]
}

const routes = [
  {
    path: '/',
    name: '首页',
    icon: 'dashboard',
    component: './dashboard',
  },
  {
    name: '深度研究',
    icon: 'FundProjectionScreenOutlined',
    path: '/fund',
    routes: [
      {
        name: '基金产品',
        path: '/fund',
        component: './fund/MutualFund',
      },
      {
        path: '/fund/:id',
        component: './fund/Persona',
        routes: getFundAnalyzeRoutes('/fund'),
      },
      {
        name: '基金经理',
        path: '/manager/persona',
        component: './manager/list',
      },
      {
        name: '基金行业',
        path: '/fundindustry',
      },
      {
        name: '因子方案',
        path: '/factorschema',
        component: './factorschema/List',
      },
      {
        path: '/manager/persona/:id',
        component: './manager/persona/Wrapper',
        routes: [
          {
            path: '/manager/persona/:id/manager_property',
            component: './persona/ManagerProperty',
          },
          {
            path: '/manager/persona/:id/team_stability',
            component: './persona/TeamStability',
          },
          ...getFundAnalyzeRoutes('/manager/persona')
        ],
      },
    ],
  },
  {
    name: '资产配置',
    icon: 'HourglassOutlined',
    path: '/assetallocation',
    routes: [{
      name: '经典大类资产配置方案',
      path: '/assetallocation/saa',
      routes: [
        {
          path: '/assetallocation/saa',
          component: './portfolio/allocation/saa/List',
        },
        {
          path: '/assetallocation/saa/schema/:id',
          component: './portfolio/allocation/schema',
        },
        {
          path: '/assetallocation/saa/:tab',
          component: './portfolio/allocation/saa/List',
        },
      ],
    }, {
      name: '配置方案池',
      path: '/assetallocation/saaschema',
      component: './portfolio/saaschema',
    }],
  },
  {
    name: '组合构建',
    icon: 'appstore',
    path: '/fof',
    routes: [
      {
        name: '基于因子的组合构建',
        path: '/fof/factorbased',
        component: './portfolio/factorbased',
      },
      {
        name: '基金组合池',
        path: '/fof/fundpool',
        component: './portfolio/fundpool',
      },
      {
        name: '自上而下组合构建',
        path: '/fof/topdown',
        component: './portfolio/topdown',
      },
      {
        path: '/fof/topdown/:id',
        component: './portfolio/topdown/New',
      },
      {
        path: '/fof/factorbased/:id',
        component: './portfolio/factorbased/Show',
      },
      {
        path: '/fof/portfolios/simulate/:id',
        component: './simulateportfolio',
      },
      {
        path: '/fof/portfolios/:id',
        component: './fund/Persona',
        routes: [
          {
            path: '/fof/portfolios/:id/overview',
            component: './portfolio/overview',
          },
          ...getFundAnalyzeRoutes('/fof/portfolios'),
        ],
      },
    ],
  },
  {
    name: '主动管理',
    icon: 'build',
    path: '/activefund',
    component: './fund/ActiveFund',
  },
  {
    path: '/activefund/:id',
    component: './fund/Persona',
    routes: getFundAnalyzeRoutes('/activefund'),
  },
  {
    name: '风险管理',
    path: '/riskmonitoring',
    icon: 'block',
    routes: [{
      name: '费率监控',
      path: '/riskmonitoring/mgtfee',
      component: './riskmonitoring/MonitoringMgtFee',
    }, {
      name: '指标监控',
      path: '/riskmonitoring/quotas',
      component: './riskmonitoring/index',
    }, {
      name: '监控方案',
      path: '/riskmonitoring/monitoringrules',
      component: './riskmonitoring/MonitoringRuleList',
    }, {
      name: 'MOM默认监控方案',
      path: '/riskmonitoring/mommonitoringrules',
      component: './riskmonitoring/MonitoringRuleList',
    }, {
      path: '/riskmonitoring/monitoringresults',
      component: './riskmonitoring/MonitoringResultList',
    }],
  },
  {
    path: '/fund',
    icon: 'fund',
    component: './fund/MutualFund',
  },
  {
    path: '/fund/:id',
    component: './fund/Persona',
    routes: getFundAnalyzeRoutes('/fund'),
  },
  {
    path: '/factorschema',
    component: './factorschema/List',
  },
  {
    path: '/factorschema/:id',
    component: './factorschema/Show',
  },
  {
    path: '/factorschema/:id/readonly',
    component: './factorschema/ReadOnly',
  },
  {
    path: '/fundindustry1',
    component: './fundindustry',
  },
  {
    path: '/fundindustry',
    component: './fundindustry/Wrapper',
    routes: [{
      path: '/fundindustry',
      redirect: '/fundindustry/productrelease',
    }, {
      path: '/fundindustry/productrelease',
      component: './fundindustry/ProductRelease',
    }, {
      path: '/fundindustry/performance',
      component: './fundindustry/Performance',
    }, {
      path: '/fundindustry/position',
      component: './fundindustry/Position',
    }, {
      path: '/fundindustry/company',
      component: './fundindustry/Company',
    }],
  },
  {
    path: '/industryprosperity',
    component: './industryprosperity/Wrapper',
    routes: [{
      path: '/industryprosperity',
      redirect: '/industryprosperity/overview',
    }, {
      path: '/industryprosperity/overview',
      component: './industryprosperity/Overview',
    }, {
      path: '/industryprosperity/detail',
      component: './industryprosperity/Detail',
    }, {
      path: '/industryprosperity/cycle',
      component: './industryprosperity/Cycle',
    }],
  },
  {
    path: '/ashareretest',
    component: './ashareretest/Wrapper',
    routes: [{
      path: '/ashareretest',
      redirect: '/ashareretest/yoyprofit',
    }, {
      path: '/ashareretest/yoyprofit',
      component: './ashareretest/YoYProfit',
    }, {
      path: '/ashareretest/yoype',
      component: './ashareretest/YoYPE',
    }, {
      path: '/ashareretest/yoyret',
      component: './ashareretest/YoYRet',
    }],
  },
  {
    path: '/manager/persona',
    component: './manager/list',
  },
  {
    path: '/manager/persona/:id',
    component: './manager/persona/Wrapper',
    routes: [
      {
        path: '/manager/persona/:id/manager_property',
        component: './persona/ManagerProperty',
      },
      {
        path: '/manager/persona/:id/team_stability',
        component: './persona/TeamStability',
      },
      {
        path: '/manager/persona/:id/development',
        component: './persona/Development',
      },
      ...getFundAnalyzeRoutes('/manager/persona'),
    ],
  },
  {
    path: '/research',
    routes: [
      {
        name: '宏观框架',
        path: '/research/macrostrategy/framework',
        component: './macro/frameworks/List',
      },
      {
        path: '/research/macrostrategy/framework/:id',
        component: './macro/frameworks/Show',
      },
      {
        path: '/research/macrostrategy/framework/:id/edit',
        component: './macro/frameworks/Edit',
      },
      {
        name: '风格分析',
        path: '/research/macrostrategy/style',
        component: './macro/style',
        routes: [{
          path: '/research/macrostrategy/style',
          redirect: '/research/macrostrategy/style/market',
        }, {
          path: '/research/macrostrategy/style/market',
          component: './macro/style/MarketStyle',
        }, {
          path: '/research/macrostrategy/style/barra',
          component: './macro/style/BarraStyle',
        }],
      },
      {
        name: '情景/事件',
        path: '/research/macrostrategy/scenario',
        component: './macro/scenarios/List',
      },
      {
        path: '/research/macrostrategy/toptaaview',
        component: './toptaaview',
      },
    ],
  },
  {
    name: '工作报表',
    path: '/reportbuilder',
    icon: 'ProfileOutlined',
    routes: [{
      name: '报表模板',
      path: '/reportbuilder/reporttemplate',
      component: './reportbuilder/Templates',
    }, {
      name: '跟踪名单',
      path: '/reportbuilder/trackinglist',
      component: './reportbuilder/TrackingList',
    }, {
      name: '跟踪指标',
      path: '/reportbuilder/quotatemplate',
      component: './reportbuilder/Templates',
    }, {
      name: '编辑器',
      path: '/reportbuilder/editor',
      component: './reportbuilder',
    }, {
      name: '定制报表',
      path: '/reportbuilder/customreport',
      routes: [{
        name: '委外专户业绩追踪表',
        path: '/reportbuilder/customreport/momperf',
        component: './customreport',
      }, {
        name: '委外专户VaR追踪表',
        path: '/reportbuilder/customreport/fundvar',
        component: './customreport/FundVar',
      }, {
        name: 'MOM研究池',
        path: '/reportbuilder/customreport/momrespool',
        component: './momrespool',
      }],
    }]
  },
  {
    path: '/pm',
    routes: [
      {
        name: '基金产品',
        path: '/pm/tracking/fund',
        routes: [
          {
            name: '静态条件筛选',
            path: '/pm/tracking/fund/static',
            component: './investpool/list',
          },
          {
            name: '动态条件筛选',
            path: '/pm/tracking/fund/dynamic',
            component: './investpool/list',
          },
          {
            path: '/pm/tracking/fund/investable',
          },
          {
            path: '/pm/tracking/fund/static/:id',
            component: './investpool/detail/Static',
          },
          {
            path: '/pm/tracking/fund/dynamic/:id',
            component: './investpool/detail/Dynamic',
          },
        ],
      },
      {
        name: '基金经理',
        path: '/pm/tracking/manager',
        // icon: 'compass',
        routes: [
          {
            name: '静态条件筛选',
            path: '/pm/tracking/manager/static',
            component: './investpool/list',
          },
          {
            name: '动态条件筛选',
            path: '/pm/tracking/manager/dynamic',
            component: './investpool/list',
          },
          {
            path: '/pm/tracking/manager/investable',
          },
          {
            path: '/pm/tracking/manager/static/:id',
            component: './investpool/detail/Static',
          },
          {
            path: '/pm/tracking/manager/dynamic/:id',
            component: './investpool/detail/Dynamic',
          },
        ],
      },
    ],
  },

  {
    path: '/report/:id',
    component: './report/Show',
  },
  {
    name: '市场跟踪',
    path: '/market',
    icon: 'RiseOutlined',
    routes: [{
      name: '可转债指数',
      path: '/market/convbond',
      component: './market/ConvBondIndex',
    }, {
      name: '风格指数',
      path: '/market/style',
      component: './market/StyleIndex',
    }]
  },
  {
    name: '智能尽调',
    icon: 'audit',
    path: '/duediligence',
    routes: [
      {
        name: '尽调文档',
        path: '/duediligence/docs',
        component: './duedocs',
        authority: ['Internal_User'],
      },
      {
        path: '/duediligence/sellsidereport',
        component: './sellsidereport',
      },
      {
        name: '问卷管理',
        path: '/duediligence/history',
        component: './surveylist',
      },
      {
        name: '我的问卷',
        path: '/duediligence/survey',
        component: './userSurvey',
      },
      {
        path: '/duediligence/survey/edit',
        component: './editSurvey',
      },
      {
        path: '/duediligence/surveyDetail/:id',
        component: './surveydetail',
      },
      {
        path: '/duediligence/survey/score',
        component: './surveyScore',
      },
      {
        path: '/duediligence/survey/:id',
        component: './survey',
      },
      {
        path: '/duediligence/preview/:id',
        component: './survey',
      },
      {
        path: '/duediligence/stat/cross',
        component: './crossStat',
      },
      {
        path: '/duediligence/stat/crossUser',
        component: './userCrossStat',
      },
      {
        path: '/duediligence/stat/:id',
        component: './stat',
      },
      {
        path: '/duediligence/answers/:id',
        component: './answer',
      },

    ],
  },
  {
    path: '/admin/valuationconfigs',
    component: './admin/valuationconfig',
  },
  {
    name: '用户管理',
    path: '/admin',
    icon: 'UserAddOutlined',
    routes: [{
      name: '内部用户管理',
      path: '/admin/users',
      component: './admin/user',
    }, {
      name: '外部用户管理',
      path: '/admin/exusers',
      component: './admin/user',
    }, {
      name: '在线用户',
      path: '/admin/onlineusers',
      component: './admin/onlineuser',
    }]
  },
  {
    name: '操作日志',
    path: '/actionlogs',
    icon: 'AuditOutlined',
    component: './admin/actionlog',
  },
  {
    name: '系统监控',
    path: '/system/monitor',
    icon: 'MonitorOutlined',
    routes: [{
      path: '/system/monitor',
      redirect: '/system/monitor/position',
    }, {
      path: '/system/monitor/position',
      component: './admin/systemmonitor/Position',
    }, {
      path: '/system/monitor/cronjob',
      component: './admin/systemmonitor/Cronjob',
    }],
  },
  {
    name: '角色管理',
    path: '/rolematrix',
    icon: 'AuditOutlined',
    component: './admin/rolematrix',
  },
  {
    name: '权限矩阵',
    path: '/rolematrix/overview',
    icon: 'AuditOutlined',
    component: './admin/rolematrix/OverView',
  },
  {
    name: '用户组管理',
    path: '/usergroups',
    icon: 'AuditOutlined',
    component: './admin/usergroup',
  },
  {
    path: '/account',
    component: './account/Wrapper',
    routes: [
      {
        path: '/account/password',
        component: './account/ChangePassword',
      },
    ],
  },
  {
    path: '/iframe',
    component: './Iframe',
  },
  {
    component: './404',
  },
];


export default {
  plugins,
  block: {},
  hash: true,
  targets: {
    ie: 11,
  },
  devtool: false,
  // umi routes: https://umijs.org/zh/guide/router.html
  routes: [
    {
      path: '/user',
      component: '../layouts/UserLayout',
      routes: [
        {
          name: 'login',
          path: '/user/login',
          component: './user/login',
        },
      ],
    },
    {
      path: '/transit/etbank',
      component: './etbank',
    },
    // {
    //   name: 'Landing page',
    //   path: '/page',
    //   routes: pageRoutes,
    // },
    {
      path: '/',
      component: '../layouts/SecurityLayout',
      routes: [
        {
          path: '/',
          component: '../layouts/BasicLayout',
          routes: routes,
        },
        {
          component: './404',
        },
      ],
    },
    {
      component: './404',
    },
  ],
  theme: {
    'primary-color': primaryColor,
    'primary-color-hover': primaryColor,
    ...darkTheme,
  },
  ignoreMomentLocale: true,
  lessLoaderOptions: {
    javascriptEnabled: true,
  },
  disableRedirectHoist: true,
  cssLoaderOptions: {
    modules: true,
    getLocalIdent: (
      context: {
        resourcePath: string;
      },
      _: string,
      localName: string,
    ) => {
      if (
        context.resourcePath.includes('node_modules') ||
        context.resourcePath.includes('global.less')
      ) {
        return localName;
      }

      const match = context.resourcePath.match(/src(.*)/);

      if (match && match[1]) {
        const antdProPath = match[1].replace('.less', '');
        const arr = slash(antdProPath)
          .split('/')
          .map((a: string) => a.replace(/([A-Z])/g, '-$1'))
          .map((a: string) => a.toLowerCase());
        return `antd-pro${arr.join('-')}-${localName}`.replace(/--/g, '-');
      }

      return localName;
    },
  },
  manifest: {
    basePath: '/',
  },
  chainWebpack: webpackPlugin,
  proxy: proxy[REACT_APP_ENV || 'dev'],
  externals: {
    jquery: 'jQuery',
  },
} as IConfig;
