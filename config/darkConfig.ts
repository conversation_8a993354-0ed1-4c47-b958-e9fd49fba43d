import { blue, red, gold } from '@ant-design/colors';

// All antd theme variables: https://github.com/ant-design/ant-design/blob/master/components/style/themes/default.less
export default {
  '@font-family':
    'Helvetica Neue,Arial,PingFang SC,Hiragino Sans GB,Microsoft YaHei,WenQuanYi Micro Hei,sans-serif',
  '@font-size-base': '13px',
  '@font-size-lg': '@font-size-base + 2px',
  '@font-size-sm': '12px',
  '@heading-1-size': 'ceil(@font-size-base * 2.71)',
  '@heading-2-size': 'ceil(@font-size-base * 2.14)',
  '@heading-3-size': 'ceil(@font-size-base * 1.71)',
  '@heading-4-size': 'ceil(@font-size-base * 1.42)',
  '@border-radius-base': '2px',
  '@body-background': '#0D131B',
  '@component-background': '#181F29',
  '@pro-header-background': '#0D131B',
  '@layout-sider-background': '#181F29',
  '@text-color': '#D5DFEB',
  '@text-color-secondary': '#D5DFEB',
  '@table-header-color': '#949FB6',
  '@heading-color': '#BCC8D7',
  '@layout-body-background': '#0D131B',
  '@pro-header-box-shadow': '0 1px 4px 0 rgba(0,21,41,0.12)',
  '@btn-default-bg': '#181F29',
  '@btn-primary-bg': '#ff9800',
  '@border-color-split': '#3A404C',
  '@input-bg': '#181F29',
  '@border-color-base': '#3A404C',
  '@btn-default-border': '#3A404C',
  '@btn-default-ghost-color': '#3A404C',
  '@btn-default-ghost-border': '#3A404C',
  '@btn-height-base': '30px',

  '@table-header-bg': '#252B35',
  '@table-row-hover-bg': '#252B35',
  '@table-selected-row-bg': '#252B35',

  '@tag-default-bg': '#181F29',
  '@alert-info-bg-color': '#102134',
  '@highlight-color': red[7],
  '@warning-color': gold[9],
  '@card-actions-background': '#181F29',
  // '@primary-color': '#0EBF9C',
  '@primary-color': '#ff9800',
  // '@item-hover-bg': `fade(#252B35, 40%)`,
  // '@item-active-bg': `fade(#252B35, 40%)`,
  '@item-hover-bg': '#252B35',
  '@item-active-bg': '#252B35',
  '@checkbox-check-color': '#181F29',
  '@disabled-color': '#404C56',
  '@input-disabled-bg': '#404C56',
  '@input-placeholder-color': '#474F5F',
  '@input-height-base': '30px',
  '@background-color-light': '#181F29',

  '@popover-bg': '#181F29',
  '@select-dropdown-bg': '#303540',
  '@slider-rail-background-color': '#D8D8D8',
  '@btn-disable-bg': '#2F353E',
  '@btn-disable-border': '#2F353E',

  // card padding
  '@card-head-padding': '8px',
  '@card-inner-head-padding': '6px',
  '@card-padding-base': '15px',

  '@collapse-header-bg': '@component-background',

  '@tabs-card-head-background': '@body-background',

  '@menu-item-active-bg': '@item-active-bg',

  '@select-item-selected-bg': '@item-active-bg',
  '@select-selection-item-bg': '@item-active-bg',

  '@picker-basic-cell-hover-color': '@item-hover-bg',
  '@picker-basic-cell-active-with-range-color': '@item-active-bg',
  '@picker-basic-cell-hover-with-range-color': 'lighten(@primary-color, 12%)',
  // @picker-border-color: @border-color-split;
  // @picker-date-hover-range-border-color: lighten(@primary-color, 20%);
  // @picker-date-hover-range-color: @picker-basic-cell-hover-with-range-color;

  // '@pagination-item-bg-active': '#949FB6',
};
