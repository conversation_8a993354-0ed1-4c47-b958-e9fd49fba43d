@import '~antd/es/style/themes/default.less';

html,
body,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.colorUp {
  color: #e85655;
}

.colorDown {
  color: #0ebf9c;
}

.colorUpWrap .ant-statistic-content {
  color: #e85655;
}

.colorDownWrap .ant-statistic-content {
  color: #0ebf9c;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.js-fullscreen {
  overflow-y: scroll;
}

ul,
ol {
  list-style: none;
}

.ant-pro-sider-menu-logo h1,
.ant-pro-top-nav-header h1 {
  color: #bcc8d7 !important;
}

.ant-pro-sider-menu-logo img {
  height: 38px !important;
  margin-left: 4px;
}

.ant-pro-sider-menu-sider.light .ant-pro-sider-menu-logo,
.ant-pro-sider-menu-sider.light {
  background-color: #181f29 !important;
}

.ant-modal-close-x:hover {
  color: @primary-color !important;
}

@media only screen and (max-width: 768px) {
  .antd-pro-components-global-header-index-right {
    background: #181f29 !important;
  }
}

// .ant-menu-dark, .ant-menu-dark .ant-menu-sub {
//   background: #181F29 !important;
// }

.ant-table-thead > tr > th.ant-table-column-sort {
  background: none !important;
}

.ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0 !important;
}

.ant-table-small .ant-table-filter-column-title {
  padding: 8px;
}

.ant-table-thead {
  background-color: #252b35;
}

td.ant-table-column-sort {
  background: none !important;
}

.thin-table .ant-table.ant-table-small tbody > tr > td {
  padding: 5px;
}

// .ant-table-pagination.ant-pagination {
//   float: none !important;
// }

.stripe-table {
  .ant-table {
    border: none !important;
  }
  .ant-table-tbody > tr > td {
    border-bottom: none;
  }
  .ant-table-tbody > tr:nth-of-type(odd) {
    background-color: #252b35;
  }
}

.ant-table-row.date-error > td {
  background-color: #ff980033;
}

.ant-card-grid {
  box-shadow: none !important;
}

.ant-card:not(.bordered) {
  border: none !important;
}

.ant-card:not(.bordered) .ant-card-head {
  border-bottom: none !important;
  min-height: 30px !important;
  font-size: 14px !important;
}

.ant-picker-cell-disabled .ant-picker-cell-inner {
  background: @component-background !important;
  text-decoration: line-through !important;
  color: #999 !important;
}

.ant-picker-cell-disabled::before {
  background: @component-background !important;
}

.ant-picker-cell-disabled:hover .ant-picker-cell-inner:hover {
  // background: none !important;
}

.highcharts-tooltip {
  z-index: 9998;
}

.ant-input[disabled] {
  color: #fff !important;
}

.ant-radio-button-wrapper-checked {
  color: #fff !important;
  background-color: @primary-color !important;
  border: none !important;
}

.ant-pagination-item a,
.ant-pagination.mini .ant-pagination-prev .ant-pagination-item-link,
.ant-pagination.mini .ant-pagination-next .ant-pagination-item-link {
  margin: 0 5px;
  background-color: #2f353e !important;
  min-width: 20px;
  border-radius: 2px;
  color: #bcc8d7 !important;
}
.ant-pagination-item-active {
  border: none !important;
}
.ant-pagination-item-active a {
  background-color: #8094bd !important;
  color: #fff !important;
}
.ant-calendar {
  border-color: #3a404c !important;
}
.ant-layout-sider-zero-width-trigger {
  background: #3a404c !important;
}
.ant-dropdown .ant-dropdown-menu {
  background-color: #303540 !important;
}
.ant-empty-description {
  color: #8d96a2 !important;
}
.ant-empty-image {
  height: 70px !important;
}

.ant-collapse-item-active > .ant-collapse-header {
  color: @primary-color !important;
}

.breadcrumb {
  font-size: 16px !important;
  color: #d5dfeb !important;
  font-weight: bold !important;
  margin-bottom: 15px !important;
}

.ant-btn-background-ghost {
  color: @text-color;
  border-color: @btn-default-ghost-color;
}

.ant-menu-item.ant-menu-item-only-child .anticon,
.ant-menu-submenu-title.ant-menu-item-only-child .anticon {
  margin-right: 10px !important;
}

.ant-legacy-form-item .ant-col {
  width: 100%;
}

.ant-legacy-form-item .ant-col label{
  color: @text-color !important;
}

.has-error .ant-input, .has-error .ant-input:hover {
  background-color: transparent !important;
}

.ant-pro-basicLayout-content {
  margin: 16px !important;
}

.ant-cascader-menu-item-active {
  background-color: transparent !important;
}

.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: @table-header-bg !important;
}

.ant-layout-header, .ant-layout-header .ant-pro-global-header {
  padding: 0px !important;
  height: 48px !important;
  line-height: 48px !important;
}

.ant-pro-global-header-trigger {
  height: 48px !important;
  padding-left: 16px !important;
}

.antd-pro-components-global-header-index-right .antd-pro-components-global-header-index-account .antd-pro-components-global-header-index-avatar {
  margin: calc((48px - 24px) / 2) 0 !important;
  margin-right: 8px !important;
}

.ant-pro-sider-menu-sider {
  flex: 0 0 200px !important;
  max-width: 200px !important;
  min-width: 200px !important;
  width: 200px !important;
}

.ant-pro-sider-menu-sider.ant-layout-sider-collapsed {
  flex: 0 0 60px !important;
  max-width: 60px !important;
  min-width: 60px !important;
  width: 60px !important;
}

.ant-menu-inline-collapsed > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item, .ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-submenu > .ant-menu-submenu-title, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
  padding: 0 19.5px !important;
}

.ant-pro-sider-menu-logo {
  padding: 0 5px !important;
}

.ant-pro-sider-menu-logo > a {
  height: 54px !important;
}

.ant-pro-sider-menu {
  padding: 0 !important;
}

h4 {
  font-size: 14px !important;
}

.nav-tab-wrapper .ant-card-body,
.nav-tab-wrapper .ant-tabs-nav {
  padding-bottom: 0;
  padding-top: 0;
  margin-bottom: 0!important;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

.zero-padding-card .ant-card-head,
.zero-padding-card .ant-card-body {
  padding: 0;
}

.zero-padding-modal .ant-modal-body {
  padding: 0;
}

.empty-card .ant-card-body {
  padding: 0;
}

.site-tree-search-value {
  color: #f50;
}

.ant-divider-horizontal.ant-divider-with-text::before, .ant-divider-horizontal.ant-divider-with-text::after {
  border-top-color: @border-color-base !important;
}

@media (min-width: @screen-md) {
  .ant-pro-top-nav-header-menu {
    width: 80%;
  }
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #181f29;
}
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #252b35;
}
::-webkit-scrollbar-thumb:hover {
  background-color: #3a4353;
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 0;
  background-color: #181f29;
}
::-webkit-scrollbar-corner {
  background-color: #181f29;
}
