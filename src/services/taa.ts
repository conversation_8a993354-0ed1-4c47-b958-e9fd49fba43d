import request from '@/utils/request'

export async function queryTaaScore(params: any): Promise<any> {
  return request(`/api/kym/taa/score`, {
    method: 'GET',
    params,
  })
}

export async function queryTaaScoreByDate(params: any): Promise<any> {
  return request(`/api/kym/taa/scorebydate`, {
    method: 'GET',
    params,
  })
}

export async function queryFundList(params: any): Promise<any> {
  return request(`/api/kym/taa/funds`, {
    method: 'GET',
    params,
  })
}
