import request from '@/utils/request'

export async function query(): Promise<any> {
  return request('/api/users')
}

export async function queryCurrent(): Promise<any> {
  return request('/api/users/profile')
}

export async function queryNotices(): Promise<any> {
  return request('/api/notices')
}

export async function updatePassword(data: any): Promise<any> {
  return request('/api/users/password', {
    method: 'PUT',
    data,
  })
}

export async function querySearchOptions() {
  return request('/api/kym/searchoptions')
}

export async function createActionLog(data: any) {
  return request('/api/users/actionlog', {
    method: 'POST',
    data,
  })
}

export async function querySystemInfo(data: any) {
  return request('/api/sessions/systeminfo')
}