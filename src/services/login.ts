import request from '@/utils/request'
import aes256 from '@/utils/aes256'

export interface LoginParamsType {
  email: string;
  password: string;
  mobile: string;
  captcha: string;
}

export async function fakeAccountLogin(params: LoginParamsType) {
  const password = aes256.encrypt(params.password)
  return request('/api/sessions', {
    method: 'POST',
    data: {
      ...params,
      password,
    },
  })
}

export async function getFakeCaptcha(mobile: string) {
  return request(`/api/login/captcha?mobile=${mobile}`)
}

export async function logout() {
  return request(`/api/sessions/logout`, {
    method: 'POST',
  })
}