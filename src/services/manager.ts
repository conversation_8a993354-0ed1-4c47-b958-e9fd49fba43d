import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryManagers(params: TableListParams) {
  return request('/api/managers', {
    params,
  })
}

export async function queryFactorScoreData(params: TableListParams) {
  return request('/api/managers/list/factorscores', {
    params,
  })
}

export async function queryManager(id: string): Promise<any> {
  return request(`/api/managers/${id}`, {
    method: 'GET',
  })
}

export async function queryManagerNav(id: string): Promise<any> {
  return request(`/api/managers/${id}/nav`, {
    method: 'GET',
  })
}

export async function queryBenchmarks(type: string): Promise<any> {
  return request(`/api/managers/benchmarks`, {
    method: 'GET',
    params: { type },
  })
}
