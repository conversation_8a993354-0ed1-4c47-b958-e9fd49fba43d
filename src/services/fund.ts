import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryFund(id: string): Promise<any> {
  return request(`/api/funds/${id}`, {
    method: 'GET',
  })
}

export async function queryBenchmark(id: string, params: any): Promise<any> {
  return request(`/api/funds/benchmarks/${id}`, {
    method: 'GET',
    params,
  })
}

export async function queryFunds(params: TableListParams) {
  return request('/api/funds', {
    params,
  })
}

export async function queryFactorScoreData(params: TableListParams) {
  return request('/api/funds/list/factorscores', {
    params,
  })
}

export async function queryFundQuotas(params: TableListParams) {
  return request('/api/funds/listfundquota', {
    params,
  })
}

export async function queryChildFunds(id: string) {
  return request(`/api/products/${id}/funds`, {
    method: 'GET',
  })
}

export async function queryChildFundsPosition(id: string) {
  return request(`/api/products/${id}/funds/positions`, {
    method: 'GET',
  })
}

export async function queryChildFundsIndustry(id: string, params: any) {
  return request(`/api/products/${id}/funds/industry`, {
    method: 'GET',
    params,
  })
}

export async function queryChildFundsStockData(id: string, params: any) {
  return request(`/api/products/${id}/funds/stockdata`, {
    method: 'GET',
    params,
  })
}

export async function queryChildFactorSchemas(id: string, params?: any) {
  return request(`/api/products/${id}/funds/factorschemas`, {
    method: 'GET',
    params,
  })
}

export async function queryChildFactorData(id: string, params?: any) {
  return request(`/api/products/${id}/funds/factorscore`, {
    method: 'GET',
    params,
  })
}

export async function createNavPortfolio(data: any, params: any): Promise<any> {
  return request('/api/portfolios/net', {
    method: 'POST',
    data,
    params,
  })
}

export async function updateNavPortfolio(id: string, data: any): Promise<any> {
  return request(`/api/portfolios/net/${id}`, {
    method: 'PUT',
    data,
  })
}

export async function getNavPortfolio(id: string): Promise<any> {
  return request(`/api/portfolios/net/${id}`)
}

export async function deleteFund(id: string): Promise<any> {
  return request(`/api/funds/${id}`, {
    method: 'delete',
  })
}

export async function getFundListByMutualCodes(params: any): Promise<any> {
  return request('/api/funds/listbycodes', {
    params,
  })
}

export async function getWaitFundsMutualCodes(isFake: boolean): Promise<any> {
  const url = isFake ? '/api/funds/waitfunds' : '/ocgaas/service/kymconfiguration/list/kk'
  return request(url)
}

export async function queryNavList(params: any) {
  return request('/api/funds/listNav', { params })
}

export function getCssPortfolioWeightPenetrate(id: string) {
  return request(`/api/portfolios/net/${id}/weightpenetrate`)
}

export function getPositionData(id: string, params?: any) {
  return request(`/api/products/${id}/positiondata`, {
    params,
  })
}

export function getAssetData(id: string, params?: any) {
  return request(`/api/products/${id}/assetdata`, {
    params,
  })
}

export function getDailyStockData(id: string, params?: any) {
  return request(`/api/products/${id}/dailystockdata`, {
    params,
  })
}

export function getDailyPositionDetail(id: string, params?: any) {
  return request(`/api/products/${id}/positiondetail`, {
    params,
  })
}

export function getPositionSeries(id: string, params?: any) {
  return request(`/api/products/${id}/positionseries`, {
    params,
  })
}

export function getPositionSeriesWithBmkComponent(id: string, params?: any) {
  return request(`/api/products/${id}/positionseries/benchmarkcomponent`, {
    params,
  })
}

export function getTurnoverData(id: string, params?: any) {
  return request(`/api/products/${id}/turnoverdata`, {
    params,
  })
}

export function getConvtBondWeight(id: string, params?: any) {
  return request(`/api/products/${id}/convtbondweight`, {
    params,
  })
}

export function getLatestBarraStyle(id: string, params?: any) {
  return request(`/api/products/${id}/barrastylelatest`, {
    params,
  })
}

export function getLatestBarraStyleSeries(id: string, params?: any) {
  return request(`/api/products/${id}/barrastyleseries`, {
    params,
  })
}

export function getAIndexPrices(params?: any) {
  return request(`/api/products/aindex/prices`, {
    params,
  })
}

export function getSwIndustryPrices(params?: any) {
  return request(`/api/products/swindustry/prices`, {
    params,
  })
}

export function getSwIndustryExReturns(params?: any) {
  return request(`/api/products/swindustry/exreturn`, {
    params,
  })
}

export function getDailyStockIndicator(id: string, params?: any) {
  return request(`/api/products/${id}/dailystockindicator`, {
    params,
  })
}

export function getDailyConvtBondIndicator(id: string, params?: any) {
  return request(`/api/products/${id}/dailyconvtbondindicator`, {
    params,
  })
}

export function getDailyConvtBondStockIndicator(id: string, params?: any) {
  return request(`/api/products/${id}/dailyconvtbondstockindicator`, {
    params,
  })
}

export function getConvtBondIndustry(id: string, params?: any) {
  return request(`/api/products/${id}/convtbondindustry`, {
    params,
  })
}

export function getStockSizeSeries(id: string, params?: any) {
  return request(`/api/products/${id}/stocksizeseries`, {
    params,
  })
}

export function getConvtBondSizeSeries(id: string, params?: any) {
  return request(`/api/products/${id}/convtbondsizeseries`, {
    params,
  })
}

export function getConvtBondDistirubtion(id: string, params?: any) {
  return request(`/api/products/${id}/convtbonddistribution`, {
    params,
  })
}

export function getBondDistributionData(id: string, params?: any) {
  return request(`/api/products/${id}/bonddistributiondata`, {
    params,
  })
}

export function getStockFactorSeries(id: string, params?: any) {
  return request(`/api/products/${id}/stockfactorseries`, {
    params,
  })
}

export function getSizeStyleHistory(id: string, params?: any) {
  return request(`/api/products/${id}/fundsizestylehistory`, {
    params,
  })
}

export function getDailyIndustryPrefer(id: string, params?: any) {
  return request(`/api/products/${id}/dailyindustryprefer`, {
    params,
  })
}

export function getPositionPriceSeries(params?: any) {
  return request(`/api/products/position/prices`, {
    params,
  })
}

export async function queryKymQueryOptions(): Promise<any> {
  return request(`/api/funds/kym/queryOptions`, {
    method: 'GET',
  })
}

export function getDashboardData() {
  return request(`/api/kym/dashboarddata`, {
    method: 'GET',
  })
}

export function computeCorrelationData(params: any) {
  return request(`/api/funds/compute/correlation`, {
    method: 'GET',
    params,
  })
}

export function getScenarios() {
  return request(`/api/scenarios`, {
    method: 'GET',
    params: {
      page: 1,
      per_page: 100,
    },
  })
}

export function getActiveFunds() {
  return request(`/api/funds/activefunds`, {
    method: 'GET',
  })
}

export function getDetectPositionDetail(id: string, params?: any) {
  return request(`/api/products/${id}/detection/positiondetail`, {
    params,
  })
}

export function getDetectAssetData(id: string, params?: any) {
  return request(`/api/products/${id}/detection/assetdata`, {
    params,
  })
}

export function getDetectIndAllocateData(id: string, params?: any) {
  return request(`/api/products/${id}/detection/indallocate`, {
    params,
  })
}

export function getDetectIndPreferData(id: string, params?: any) {
  return request(`/api/products/${id}/detection/indprefer`, {
    params,
  })
}

export function getDetectNav(id: string, params?: any) {
  return request(`/api/products/${id}/detection/nav`, {
    params,
  })
}

export function getStockIndAllocData(id: string, params?: any) {
  return request(`/api/products/${id}/stock_ind_alloc`, {
    params,
  })
}

export function getFundPositionDatesRp(id: string) {
  return request(`/api/products/${id}/rp/dates`)
}

export function getFundBarraStylePrefer(id: string, params) {
  return request(`/api/products/${id}/barrastyleprefer`, {
    params,
  })
}

export function getDailyReviewDates() {
  return request(`/api/products/dailyreviewdates`)
}

export async function getGroupList() {
  return request(`/api/duedocs/usergroup/list`)
}

export async function authorizeUserGroups(id: string, data: any) {
  return request(`/api/portfolios/${id}/usergroups`, {
    method: 'post',
    data,
  })
}