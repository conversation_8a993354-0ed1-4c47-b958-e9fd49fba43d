import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryInvestPools(params: TableListParams) {
  return request('/api/investpools', {
    params,
  })
}

export async function queryInvestPool(id: string): Promise<any> {
  return request(`/api/investpools/${id}`, {
    method: 'GET',
  })
}

export async function createInvestPool(data: any): Promise<any> {
  return request(`/api/investpools`, {
    method: 'POST',
    data,
  })
}

export async function updateInvestPool(id: string, data: any): Promise<any> {
  return request(`/api/investpools/${id}`, {
    method: 'PUT',
    data,
  })
}

export async function deleteInvestPool(id: string): Promise<any> {
  return request(`/api/investpools/${id}`, {
    method: 'DELETE',
  })
}

export async function addFundsToInvestPool(id: string, data: any): Promise<any> {
  return request(`/api/investpools/${id}/funds`, {
    method: 'POST',
    data,
  })
}

export async function deleteFundsFromInvestsPool(id: string, data: any, params: any): Promise<any> {
  return request(`/api/investpools/${id}/funds`, {
    method: 'DELETE',
    data,
    params,
  })
}

export async function queryFunds(id: string, params: TableListParams) {
  return request(`/api/investpools/${id}/funds`, {
    method: 'GET',
    params,
  })
}

export async function queryAllOrigFunds(id: string, params: TableListParams) {
  return request(`/api/investpools/${id}/allorigfunds`, {
    method: 'GET',
    params,
  })
}

export async function queryGroupList() {
  return request(`/api/duedocs/usergroup/list`)
}