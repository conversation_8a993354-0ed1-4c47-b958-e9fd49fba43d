import request from '@/utils/request';

export async function query(params: any) {
  return request('/api/sys/roles', {
    method: 'GET',
    params
  });
}

export async function create(body: any) {
  return request('/api/sys/roles', {
    method: 'POST',
    data: body,
  });
}

export async function getUserInfo(roleId: any) {
  return request(`/api/sys/roles/${roleId}`, {
    method: 'GET'
  });
}

export async function edit(id: any, data: any) {
  return request(`/api/sys/roles/${id}`, {
    method: 'PUT',
    data
  });
}

export async function del(id: string, data?: any) {
  return request(`/api/sys/roles/${id}`, {
    method: 'DELETE',
    data
  });
}


