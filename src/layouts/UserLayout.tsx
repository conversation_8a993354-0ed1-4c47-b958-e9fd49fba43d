import { MenuDataItem, getMenuData, getPageTitle } from '@ant-design/pro-layout'
import DocumentTitle from 'react-document-title'
import Link from 'umi/link'
import React, { useEffect } from 'react'
import { connect } from 'dva'
import { formatMessage } from 'umi-plugin-react/locale'

import SelectLang from '@/components/SelectLang'
import GlobalFooter from '@/components/GlobalFooter'
import { ConnectProps, ConnectState } from '@/models/connect'
import logo from '../assets/logo.png'
import logoPlus from '../assets/logo-plus.png'
import styles from './UserLayout.less'
import isPlustHost from '@/utils/isPlusHost'

export interface UserLayoutProps extends ConnectProps {
  breadcrumbNameMap: { [path: string]: MenuDataItem };
}

const UserLayout: React.SFC<UserLayoutProps> = props => {
  const { dispatch } = props
  useEffect(() => {
    if (dispatch) {
      dispatch({
        type: 'user/fetchSystemInfo',
      })
    }
  }, [])
  const {
    route = {
      routes: [],
    },
  } = props
  const { routes = [] } = route
  const {
    children,
    location = {
      pathname: '',
    },
  } = props
  const { breadcrumb } = getMenuData(routes)
  const isPlus = isPlustHost()
  return (
    <DocumentTitle
      title={getPageTitle({
        pathname: location.pathname,
        breadcrumb,
        formatMessage,
        ...props,
      })}
    >
      <div className={styles.container}>
        {false &&
        <div className={styles.lang}>
          <SelectLang />
        </div>}
        <div className={styles.content}>
          <div className={styles.top}>
            <div className={styles.header}>
              <Link to="/">
                <img alt="logo" className={styles.logo} src={isPlus ? logoPlus : logo} />
              </Link>
            </div>
            <div className={styles.desc}>欢迎来到阿基米德智能投研系统 {isPlus ? 'PLUS' : ''}</div>
          </div>
          {children}
        </div>
        <GlobalFooter />
      </div>
    </DocumentTitle>
  )
}

export default connect(({ settings }: ConnectState) => ({
  ...settings,
}))(UserLayout)
