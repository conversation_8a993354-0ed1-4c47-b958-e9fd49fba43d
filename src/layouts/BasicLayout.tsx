import ProLayout, {
  MenuDataItem,
  BasicLayoutProps as ProLayoutProps,
  Settings,
} from '@ant-design/pro-layout'
import _ from 'lodash'
import React, { useEffect } from 'react'
import Link from 'umi/link'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { formatMessage } from 'umi-plugin-react/locale'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/es/locale/zh_CN'
import localRoutes from '../json'
import Authorized from '@/utils/Authorized'
import RightContent from '@/components/GlobalHeader/RightContent'
import GlobalFooter from '@/components/GlobalFooter'
import { ConnectState } from '@/models/connect'
import logo from '@/assets/logo-mini.png'
import { cloneDeep } from 'lodash'
import Icon from './Icon'
import router from 'umi/router'
import isPlustHost from '@/utils/isPlusHost'

const defaultlocalRoutes = cloneDeep(localRoutes)


export interface BasicLayoutProps extends ProLayoutProps {
  breadcrumbNameMap: {
    [path: string]: MenuDataItem;
  };
  settings: Settings;
  dispatch: Dispatch;
}
export type BasicLayoutContext = { [K in 'location']: BasicLayoutProps[K] } & {
  breadcrumbNameMap: {
    [path: string]: MenuDataItem;
  };
};

/**
 * use Authorized check all menu item
 */
const menuDataRender = (menuList: MenuDataItem[]): MenuDataItem[] =>
  menuList.map(item => {
    const localItem = {
      ...item,
      children: item.children ? menuDataRender(item.children) : [],
    }
    return Authorized.check(item.authority, localItem, null) as MenuDataItem
  })

const BasicLayout: React.FC<BasicLayoutProps> = props => {
  const { dispatch, children, settings, currentUser, location, collapsed } = props
  /**
   * constructor
   */

  useEffect(() => {
    if (dispatch) {
      dispatch({
        type: 'user/fetchCurrent',
      })
      dispatch({
        type: 'settings/getSetting',
      })
      dispatch({
        type: 'global/changeLayoutCollapsed',
        payload: true,
      })
      dispatch({
        type: 'fund/fetchQueryOptions',
      })
      setTimeout(() => {
        dispatch({
          type: 'user/fetchSearchOptions',
          payload: true,
        })
      }, 1000)
    }
  }, [])

  /**
   * init variables
   */
  const handleMenuCollapse = (payload: boolean): void => {
    if (dispatch) {
      dispatch({
        type: 'global/changeLayoutCollapsed',
        payload,
      })
    }
  }

  const localMenuMap = defaultlocalRoutes.reduce((out, item) => {
    out[item.menuId] = _.omit(item, ['routes'])
    return out
  }, {})

  const toTree = (data) => {
    let result: any = []
    if (!Array.isArray(data)) {
      return result
    }
    data.forEach(item => {
      delete item.routes;
    });
    data.forEach(item => {
      const menuItem = localMenuMap[item.menuId]
      if (menuItem) {
        let parent = localMenuMap[menuItem.parentId];
        if (parent) {
          (parent.routes || (parent.routes = [])).push(menuItem);
        } else {
          result.push(menuItem);
        }
      }
    });
    return result.sort((fst, snd) => {
      return (fst.menuIndex || fst.menuId) > (snd.menuIndex || snd.menuId) ? 1 : -1
    });
  }

  const renderMenuItem = (data, type) => {
    let link = <Link to={data.path}>{data.name}</Link>
    if (data.path === '/manager/persona') {
      link = <a href={data.path}>{data.name}</a>
    }
    return <span className="ant-pro-menu-item" title={data.name}>
      {!data.icon ? undefined : <Icon icon={data.icon} />}
      <span className="ant-pro-menu-item-title">
        {
          type == 'sub' ?
            data.name : link
        }

      </span>
    </span >
  }

  const menus = (currentUser.menus || []).filter(item => item.menuType !== 'J')
  const routes = toTree(menus.map(item => {
    delete item.key
    delete item.title
    return item
  }).filter(ele => !(ele.type == 'page'))).filter(item => item.path) // 有 path 参数才显示到菜单

  const firstRoute = menus.filter(item => item.path)[0]
  const firstRoutePath = firstRoute ? firstRoute.path : '/user/login'

  const pathname = location?.pathname
  useEffect(() => {
    const isMenu = localRoutes.some(item => {
      return item.path === pathname
    })
    const hasPerm = menus.some(item => {
      return item.path === pathname
    })
    if (!currentUser.password_actived && pathname !== '/account/password') {
      return router.push('/account/password')
    }
    if (isMenu && !hasPerm) {
      console.log('hasPerm', hasPerm, isMenu, pathname)
      router.push(firstRoutePath)
    }
  }, [pathname])

  return (
    <ProLayout
      logo={<img src={logo} />}
      onCollapse={handleMenuCollapse}
      subMenuItemRender={(item) => {
        return renderMenuItem(item, 'sub')
      }}
      menuItemRender={(menuItemProps, defaultDom) => {
        if (menuItemProps.isUrl) {
          return defaultDom
        }
        return renderMenuItem(menuItemProps, 'menu')
      }}
      breadcrumbRender={(routers = []) => [
        {
          path: '/',
          breadcrumbName: formatMessage({
            id: 'menu.home',
            defaultMessage: 'Home',
          }),
        },
        ...routers,
      ]}
      itemRender={(route, params, routes, paths) => {
        const first = routes.indexOf(route) === 0
        return first ? (
          <Link to={paths.join('/')}>{route.breadcrumbName}</Link>
        ) : (
          <span>{route.breadcrumbName}</span>
        )
      }}
      footerRender={() => <GlobalFooter />}
      menuDataRender={menuDataRender}
      formatMessage={formatMessage}
      rightContentRender={rightProps => <RightContent {...rightProps} />}
      {...props}
      {...settings}
      route={{
        routes: routes,
      }}
      menuHeaderRender={(logo, title) => {
        return <a href='/'>
          <div style={{ position: 'relative' }}>
            {logo}
            {title}
            {isPlustHost() && !collapsed && <span style={{ fontSize: '20px', marginLeft: '5px', fontWeight: 500, top: '-5px', position: 'absolute' }}>+</span>}
          </div>
        </a>
      }}
    >
      <ConfigProvider locale={zhCN}>
        {children}
      </ConfigProvider>
    </ProLayout>
  )
}

export default connect(({ global, settings, user }: ConnectState) => ({
  collapsed: global.collapsed,
  settings,
  currentUser: user.currentUser,
}))(BasicLayout)
