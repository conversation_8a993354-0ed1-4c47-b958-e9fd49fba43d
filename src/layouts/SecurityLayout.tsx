import React from 'react'
import { connect } from 'dva'
import { Redirect } from 'umi'
import { ConnectState, ConnectProps } from '@/models/connect'
import { CurrentUser } from '@/models/user'
import PageLoading from '@/components/PageLoading'

interface SecurityLayoutProps extends ConnectProps {
  loading: boolean;
  currentUser: CurrentUser;
}

interface SecurityLayoutState {
  isReady: boolean;
}

class SecurityLayout extends React.Component<SecurityLayoutProps, SecurityLayoutState> {
  state: SecurityLayoutState = {
    isReady: false,
  };

  componentDidMount() {
    this.setState({
      isReady: true,
    })
    const { dispatch } = this.props
    if (dispatch) {
      dispatch({
        type: 'user/fetchCurrent',
      })
      dispatch({
        type: 'investpool/fetchGroupList',
      })
    }
    this.preloadImgs()
  }

  preloadImgs() {
    const images = [
      '/design/dashboard.png',
      '/design/fof_funds.png',
      '/design/fof_taa_airealtime.png',
      '/design/fof_taa_allocation.png',
      '/design/fof_typicalcustomerneeds.png',
      '/design/future_holding.png',
      '/design/future_smartfof.png',
      '/design/research_manager_altpersona.png',
      '/design/research_manager_database.png',
      '/design/research_manager_persona.png',
    ]
    images.forEach(item => {
      const image = new Image()
      image.src = item
    })
  }

  render() {
    const { isReady } = this.state
    const { children, loading, currentUser } = this.props
    if ((!currentUser._id && loading) || !isReady) {
      return <PageLoading />
    }
    if (!currentUser._id) {
      return <Redirect to="/user/login"></Redirect>
    }
    return children
  }
}

export default connect(({ user, loading }: ConnectState) => ({
  currentUser: user.currentUser,
  loading: loading.models.user,
}))(SecurityLayout)
