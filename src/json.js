
import { DashboardOutlined,FundProjectionScreenOutlined,
  HourglassOutlined,AppstoreOutlined ,BuildOutlined,BlockOutlined,
  ProfileOutlined,RiseOutlined,AuditOutlined,UserAddOutlined
} from '@ant-design/icons';
import { cloneDeep } from 'lodash';
const targeRoutes = [
  {
    path: '/dashboard',
    name: '首页',
    icon: <DashboardOutlined/>,
    component: './dashboard',
    menuId: 1,
    menuType:'M',
    parentId: 0,
  },
  {
    name: '深度研究',
    icon: <FundProjectionScreenOutlined/>,
    menuId: 2,
    parentId: 0,
    menuType:'C',
    routes: [
      {
        name: '基金产品',
        path: '/fund',
        menuType:'M',
        menuId: 21,
        parentId: 2,
        component: './fund/MutualFund',
      },
      {
        name: '基金经理',
        menuType:'M',
        path: '/manager/persona',
        component: './manager/list',
        menuId: 22,
        parentId: 2,
      },
      {
        name: '基金行业',
        menuType:'M',
        path: '/fundindustry',
        component: './fundindustry',
        menuId: 23,
        parentId: 2,
      },
      {
        name: '因子方案',
        menuType:'M',
        path: '/factorschema',
        menuId: 24,
        parentId: 2,
        component: './factorschema/List',
      },
    ]
  },
  {
    name: '资产配置',
    icon: <HourglassOutlined/>,
    menuId: 3,
    parentId: 0,
    menuType:'C',
    path: '/assetallocation',
    routes: [{
      name: '资本市场研究',
      path: '/research',
      menuId: 31,
      parentId: 3,
      menuType:'C',
      routes: [
        {
          name: '宏观框架',
          path: '/research/macrostrategy/framework',
          component: './macro/frameworks/List',
          menuId: 311,
          parentId: 3,
          menuType:'M',
        },
        {
          name: '风格分析',
          path: '/research/macrostrategy/style',
          menuId: 312,
          parentId: 3,
          menuType:'M',

        },
        {
          name: '情景/事件',
          path: '/research/macrostrategy/scenario',
          menuId: 313,
          parentId: 3,
          menuType:'M',

        },
      ],
    },
    {
      name: '经典大类资产配置方案',
      path: '/assetallocation/saa',
      menuId: 32,
      parentId: 3,
      menuType:'M',

    },
    {
      name: '配置方案池',
      path: '/assetallocation/saaschema',
      component: './portfolio/saaschema',
      menuId: 33,
      parentId: 3,
      menuType:'M',

    }],
  },
  {
    name: '组合构建',
    icon: <AppstoreOutlined/>,
    menuId: 4,
    parentId: 0,
    menuType:'C',
    path: '/fof',
    routes: [
      {
        name: '基于因子的组合构建',
        path: '/fof/factorbased',
        component: './portfolio/factorbased',
        menuId: 41,
        parentId: 4,
        menuType:'M',
      },
      {
        name: '基金组合池',
        path: '/fof/fundpool',
        component: './portfolio/fundpool',
        menuId: 42,
        parentId: 4,
        menuType:'M',

      },
      {
        name: '自上而下组合构建',
        path: '/fof/topdown',
        component: './portfolio/topdown',
        menuId: 43,
        parentId: 4,
        menuType:'M',

      },
    ],
  },
  {
    name: '主动管理',
    icon: <BuildOutlined/>,
    path: '/activefund',
    component: './fund/ActiveFund',
    menuId: 5,
    parentId: 0,
    menuType:'M',

  },
  {
    name: '风险管理',
    path: '/riskmonitoring',
    icon: <BlockOutlined/>,
    menuType:'C',
    menuId: 6,
    parentId: 0,
    routes: [{
      name: '费率监控',
      menuType:'M',
      path: '/riskmonitoring/mgtfee',
      component: './riskmonitoring/MonitoringMgtFee',
      menuId: 61,
      parentId: 6,
    }, {
      name: '指标监控',
      path: '/riskmonitoring/quotas',
      component: './riskmonitoring/index',
      menuId: 62,
      parentId: 6,
      menuType:'M',

    }, {
      name: '监控方案',
      path: '/riskmonitoring/monitoringrules',
      component: './riskmonitoring/MonitoringRuleList',
      menuId: 63,
      parentId: 6,
      menuType:'M',

    }],
  },
  {
    name: '工作报表',
    path: '/reportbuilder',
    icon: <ProfileOutlined/>,
    menuId: 7,
    parentId: 0,
    menuType:'C',
    routes: [
    {
      name: '报表模板',
      menuType:'M',
      path: '/reportbuilder/reporttemplate',
      component: './reportbuilder/Templates',
      menuId: 71,
      parentId: 7,
    },
    {
      name: '跟踪名单',
      menuType:'M',
      path: '/reportbuilder/trackinglist',
      component: './reportbuilder/TrackingList',
      menuId: 72,
      parentId: 7,
    },
     {
      name: '跟踪指标',
      menuType:'M',
      path: '/reportbuilder/quotatemplate',
      component: './reportbuilder/Templates',
      menuId: 73,
      parentId: 7,
    },
     {
      name: '编辑器',
      menuType:'M',
      path: '/reportbuilder/editor',
      component: './reportbuilder',
      menuId: 74,
      parentId: 7,
    },
    {
      name: '定制报表',
      menuType:'C',
      menuId: 75,
      parentId: 7,
      routes: [
      {
        name: '委外专户业绩追踪表',
        menuType:'M',
        path: '/reportbuilder/customreport/momperf',
        component: './customreport',
        menuId: 751,
        parentId: 75,
      }, {
        name: '委外专户VaR追踪表',
        path: '/reportbuilder/customreport/fundvar',
        component: './customreport/FundVar',
        menuId: 752,
        menuType:'M',
        parentId: 75,
      }, {
        name: 'MOM研究池',
        path: '/reportbuilder/customreport/momrespool',
        component: './momrespool',
        menuId: 753,
        menuType:'M',
        parentId: 75,
      }],
    }]
  },
  {
    name: '市场跟踪',
    path: '/market',
    icon: <RiseOutlined/>,
    parentId:0,
    menuType:'C',
    menuId: 8,
    routes: [
      {
        name: '可转债指数',
        menuType:'M',
        path: '/market/convbond',
        component: './market/ConvBondIndex',
        menuId: 81,
        parentId:8,
      },
      {
        name: '风格指数',
        path: '/market/style',
        component: './market/StyleIndex',
        menuId: 82,
        menuType:'M',
        parentId:8,
      }
    ]
  },
  {
    name: '智能尽调',
    icon: <AuditOutlined/>,
    path: '/duediligence',
    menuType:'C',
    menuId:9,
    parentId:0,
    routes: [
      {
        name: '问卷管理',
        menuType:'M',
        path: '/duediligence/history',
        component: './surveylist',
        menuId:91,
        parentId:9,
      },
      {
        name: '我的问卷',
        menuType:'M',
        path: '/duediligence/survey',
        component: './userSurvey',
        menuId:92,
        parentId:9,
      },
      {
        name: '文档管理',
        menuType:'M',
        path: '/duediligence/docs',
        component: './duedocs',
        menuId:93,
        parentId:9,
      },

    ],
  },
  {
    name: '用户管理',
    path: '/admin',
    icon: <UserAddOutlined/>,
    menuId:'a',
    menuType:'C',
    parentId:0,
    routes: [{
      name: '内网用户管理',
      path: '/admin/users',
      component: './admin/user',
      menuId:'a1',
      menuType:'M',
      parentId:'a',
    }, {
      name: '外网用户管理',
      path: '/admin/exusers',
      menuType:'M',
      component: './admin/user',
      menuId:'a2',
      parentId:'a',
    }, {
      name: '在线用户',
      path: '/admin/onlineusers',
      component: './admin/onlineuser',
      menuId:'a3',
      menuType:'M',
      parentId:'a',
    }]
  },
  {
    name: '角色管理',
    path: '/rolematrix',
    menuType:'M',
    icon: <AuditOutlined/>,
    menuId:'b',
    parentId:0,
  },
  {
    name: '操作日志',
    icon: <AuditOutlined/>,
    path: '/actionlogs',
    menuType:'M',
    component: './admin/actionlog',
    menuId:'c',
    parentId:0,
  },
  {
    name: '用户组管理',
    path: '/usergroups',
    component: './admin/usergroup',
    icon: <AuditOutlined/>,
    menuId:'usergroups',
    parentId:0,
    menuType:'M'
  },
];
const routes = [
  {
    path: '/',
    name: '首页',
    menuType:'M',
    component: './dashboard',
    icon: 'DashboardOutlined',
    menuId: 1,
    parentId: 0,
  },
  {
    name: '深度研究',
    menuId: 2,
    parentId: 0,
    menuType:'C',
    icon: 'FundProjectionScreenOutlined',
    path: '/deepresearch',
  },
  {
    name: '基金产品',
    path: '/fund',
    menuId: 21,
    menuType:'M',
    parentId: 2,
    component: './fund/MutualFund',
  },
  {
    name: '基金经理',
    menuType:'M',
    path: '/manager/persona',
    component: './manager/list',
    menuId: 22,
    parentId: 2,
  },
  {
    name: '基金行业',
    menuType:'M',
    path: '/fundindustry',
    component: './fundindustry',
    menuId: 23,
    parentId: 2,
  },
  {
    name: '因子方案',
    menuType:'M',
    path: '/factorschema',
    menuId: 24,
    parentId: 2,
    component: './factorschema/List',
  },
  {
    name: '资产配置',
    icon: 'HourglassOutlined',
    menuId: 3,
    menuType:'C',
    parentId: 0,
    path: '/assetallocation',
  },
  {
    name: '资本市场研究',
    menuId: 31,
    menuType:'C',
    parentId: 3,
    path: '/research',
  },
  {
    name: '宏观框架',
    path: '/research/macrostrategy/framework',
    component: './macro/frameworks/List',
    menuId: 311,
    menuType:'M',
    parentId: 31,
  },
  {
    name: '风格分析',
    menuType:'M',
    path: '/research/macrostrategy/style',
    menuId: 312,
    parentId: 31,
  },
  {
    name: '情景/事件',
    path: '/research/macrostrategy/scenario',
    menuId: 313,
    menuType:'M',
    parentId: 31,
  },
  {
    name: '集团TOPTAA观点',
    path: '/research/macrostrategy/toptaaview',
    menuId: 314,
    menuType:'M',
    parentId: 31,
  },
  {
    name: '经典大类资产配置方案',
    path: '/assetallocation/saa',
    menuId: 32,
    parentId: 3,
    menuType:'M',

  },
  {
    name: '配置方案池',
    path: '/assetallocation/saaschema',
    component: './portfolio/saaschema',
    menuId: 33,
    parentId: 3,
    menuType:'M',

  },
  {
    name: '组合构建',
    menuType:'C',
    icon: 'AppstoreOutlined',
    menuId: 4,
    parentId: 0,
    path: '/fof',
  },
  {
    name: '基于因子的组合构建',
    path: '/fof/factorbased',
    component: './portfolio/factorbased',
    menuId: 41,
    parentId: 4,
    menuType:'M',

  },
  {
    name: '基金组合池',
    path: '/fof/fundpool',
    component: './portfolio/fundpool',
    menuId: 42,
    parentId: 4,
    menuType:'M',
  },
  {
    name: '自上而下组合构建',
    path: '/fof/topdown',
    menuType:'M',
    component: './portfolio/topdown',
    menuId: 43,
    parentId: 4,
  },
  {
    name: '主动管理',
    icon: 'BuildOutlined',
    path: '/activefund',
    component: './fund/ActiveFund',
    menuId: 5,
    menuType:'M',
    parentId: 0,
  },
  {
    name: '风险管理',
    path: '/riskmonitoring',
    icon: 'BlockOutlined',
    menuId: 6,
    parentId: 0,
    menuType:'C',
  },
  {
    name: '费率监控',
    path: '/riskmonitoring/mgtfee',
    component: './riskmonitoring/MonitoringMgtFee',
    menuId: 61,
    parentId: 6,
    menuType:'M',

  }, {
    name: '指标监控',
    path: '/riskmonitoring/quotas',
    component: './riskmonitoring/index',
    menuId: 62,
    menuType:'M',
    parentId: 6,
  },
  {
    name: '监控方案',
    menuType:'M',
    path: '/riskmonitoring/monitoringrules',
    component: './riskmonitoring/MonitoringRuleList',
    menuId: 63,
    parentId: 6,
  },
  {
    name: 'MOM默认监控方案',
    menuType:'M',
    path: '/riskmonitoring/mommonitoringrules',
    component: './riskmonitoring/MonitoringRuleList',
    menuId: 64,
    parentId: 6,
  },
  {
    name: '工作报表',
    path: '/reportbuilder',
    icon: 'ProfileOutlined',
    menuId: 7,
    menuType:'C',
    parentId: 0,
  },
  {
    name: '报表模板',
    path: '/reportbuilder/reporttemplate',
    component: './reportbuilder/Templates',
    menuId: 71,
    menuType:'M',
    parentId: 7,
  },
  {
    name: '跟踪名单',
    path: '/reportbuilder/trackinglist',
    component: './reportbuilder/TrackingList',
    menuId: 72,
    parentId: 7,
    menuType:'M',

  },
  {
    name: '跟踪指标',
    menuType:'M',

    path: '/reportbuilder/quotatemplate',
    component: './reportbuilder/Templates',
    menuId: 73,
    parentId: 7,
  },
  {
    name: '编辑器',
    menuType:'M',
    path: '/reportbuilder/editor',
    component: './reportbuilder',
    menuId: 74,
    parentId: 7,
  },
  {
    name: '定制报表',
    menuId: 75,
    menuType:'C',
    parentId: 7,
    path: '/reportbuilder/customreport'
  },
  {
    name: '委外专户业绩追踪表',
    menuType:'M',
    path: '/reportbuilder/customreport/momperf',
    component: './customreport',
    menuId: 751,
    parentId: 75,
  }, {
    name: '委外专户VaR追踪表',
    menuType:'M',
    path: '/reportbuilder/customreport/fundvar',
    component: './customreport/FundVar',
    menuId: 752,
    parentId: 75,
  }, {
    name: 'MOM研究池',
    menuType:'M',
    path: '/reportbuilder/customreport/momrespool',
    component: './momrespool',
    menuId: 753,
    parentId: 75,
  },
  {
    name: '市场跟踪',
    path: '/market',
    menuType:'C',
    icon: 'RiseOutlined',
    parentId:0,
    menuId: 8,
  },
  {
    name: '可转债指数',
    path: '/market/convbond',
    menuType:'M',
    component: './market/ConvBondIndex',
    menuId: 81,
    parentId:8,
  },
  {
    name: '风格指数',
    path: '/market/style',
    menuType:'M',
    component: './market/StyleIndex',
    menuId: 82,
    parentId:8,
  },
  {
    name: '智能尽调',
    icon: 'AuditOutlined',
    path: '/duediligence',
    menuId:9,
    menuType:'C',
    parentId:0,
  },
  {
    name: '问卷管理',
    path: '/duediligence/history',
    component: './surveylist',
    menuId:91,
    menuType:'M',
    parentId:9,
  },
  {
    name: '我的问卷',
    path: '/duediligence/survey',
    component: './userSurvey',
    menuId:92,
    menuType:'M',
    parentId:9,
  },
  {
    name: '研究文档',
    menuId: 93,
    menuType:'C',
    parentId: 9,
    path: '/researchdoc',
  },
  {
    name: '文档管理',
    menuType:'M',
    path: '/duediligence/docs',
    component: './duedocs',
    menuId:931,
    parentId:93,
  },
  {
    name: '卖方报告',
    menuType:'M',
    path: '/duediligence/sellsidereport',
    component: './sellsidereport',
    menuId:932,
    parentId:93,
  },
  {
    name: '用户管理',
    path: '/admin',
    menuType:'C',
    icon: 'UserAddOutlined',
    menuId:'a',
    menuIndex: 2000,
    parentId:0,
  },
  {
    name: '内网用户管理',
    path: '/admin/users',
    component: './admin/user',
    menuId:'a1',
    parentId:'a',
    menuType:'M',
  }, {
    name: '外网用户管理',
    path: '/admin/exusers',
    component: './admin/user',
    menuId:'a2',
    parentId:'a',
    menuType:'M',
  }, {
    name: '在线用户',
    path: '/admin/onlineusers',
    component: './admin/onlineuser',
    menuId:'a3',
    parentId:'a',
    menuType:'M',
  },
  {
    name: '角色管理',
    path: '/rolematrix',
    icon: 'AuditOutlined',
    menuId:'b',
    menuIndex: 2100,
    parentId:0,
    menuType:'M'
  },
  {
    name: '用户组管理',
    path: '/usergroups',
    icon: 'AuditOutlined',
    menuId:'usergroups',
    menuIndex: 2200,
    component: './admin/usergroup',
    parentId:0,
    menuType:'M'
  },
  {
    name: '操作日志',
    icon: 'AuditOutlined',
    path: '/actionlogs',
    component: './admin/actionlog',
    menuId:'c',
    menuIndex: 2300,
    parentId:0,
    menuType:'M',
  },
  {
    name: '系统监控',
    path: '/system/monitor',
    menuId:'systemmonitor',
    icon: 'MonitorOutlined',
    parentId:0,
    menuType:'M',
    menuIndex: 2400,
  },
  {
    name: '基金详情因子评价',
    menuId: 'd',
    parentId: 0,
    menuType:'J',
  },
  {
    name: '基金经理详情因子评价',
    menuId: 'e',
    parentId: 0,
    menuType:'J',
  },
  {
    name: '数据下载',
    menuId: 'downloadData',
    parentId: 0,
    menuType:'J',
  },
  {
    name: 'MOM研究池',
    menuId: 'MRP',
    parentId: 0,
    menuType:'C',
  },
  {
    name: '权益池',
    menuId: 'MRP_equity',
    parentId: 'MRP',
    menuType:'C',
  },
  {
    name: '查看',
    menuId: 'MRP_equity_view',
    parentId: 'MRP_equity',
    menuType:'J',
  },
  {
    name: '更新提名名单',
    menuId: 'MRP_equity_nomi',
    parentId: 'MRP_equity',
    menuType:'J',
  },
  {
    name: '更新短/聘用名单',
    menuId: 'MRP_equity_shortemp',
    parentId: 'MRP_equity',
    menuType:'J',
  },
  {
    name: '固收池',
    menuId: 'MRP_bond',
    parentId: 'MRP',
    menuType:'C',
  },
  {
    name: '查看',
    menuId: 'MRP_bond_view',
    parentId: 'MRP_bond',
    menuType:'J',
  },
  {
    name: '更新提名名单',
    menuId: 'MRP_bond_nomi',
    parentId: 'MRP_bond',
    menuType:'J',
  },
  {
    name: '更新短/聘用名单',
    menuId: 'MRP_bond_shortemp',
    parentId: 'MRP_bond',
    menuType:'J',
  },
];




export default routes
