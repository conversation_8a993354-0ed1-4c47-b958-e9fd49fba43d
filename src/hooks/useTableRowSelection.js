import { useState } from 'react'

export default function useTableRowSelection(defaultSelectedRows) {
  const [selectedRows, setSelectedRows] = useState(defaultSelectedRows)
  const rowSelection = {
    onSelect: (record, selected) => {
      let newRows = []
      if (selected) {
        newRows = selectedRows.concat([record])
      } else {
        newRows = selectedRows.filter(item => item._id !== record._id)
      }
      setSelectedRows(newRows)
    },
    onSelectAll: (selected, _selectedRows, changeRows) => {
      let newRows = []
      if (selected) {
        newRows = selectedRows.concat(changeRows)
      } else {
        const ids = changeRows.map(item => item._id)
        newRows = selectedRows.filter(item => !ids.includes(item._id))
      }
      setSelectedRows(newRows)
    },
    selectedRowKeys: selectedRows.map(item => item._id),
  }
  return {
    selectedRows, rowSelection, setSelectedRows,
  }
}
