import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { Dropdown, <PERSON><PERSON>, But<PERSON> } from 'antd'
import classnames from 'classnames'
import downloadAsBlob from '../../utils/downloadAsBlob'
import formatFundQuota from '../../utils/formatFundQuota'
export default class GetCsvAndPng extends Component {
  static propTypes = {
    id: PropTypes.string.isRequired,
    className: PropTypes.string,
    chart: PropTypes.object,
    heads: PropTypes.array,
    results: PropTypes.array,
    quotas: PropTypes.array,
    data: PropTypes.array,
    noExpo: PropTypes.bool
  }

  onSelect = ({ key }) => {
    // 增加下载图片清晰度配置
    const SVGParam = {
      chart: {
        width: 1200,
        height: 600,
        cssWidth: 1200,
        cssHeight: 600,
        sourceWidth: 1200,
        sourceHeight: 600
      }
    }
    const { id, chart } = this.props
    console.log('char=====>t', chart)
    const chartCSV = chart.getCSV()
    const chartSVG = chart.getSVG(SVGParam)
    if (key === 'exportCSV') {
      this.seriesToCsv(id, chartCSV)
    }
    if (key === 'exportPNG') {
      this.svgToPng(id, chartSVG)
    }
  }

  svgToPng = (id, chartSVG) => {
    const cv = document.createElement('canvas')
    cv.style.display = 'none'
    document.getElementById(id).appendChild(cv)
    canvg(cv, chartSVG) //eslint-disable-line
    const pngData = cv
      .toDataURL('image/png')
      .replace('image/png', 'image/octet-stream')
    const aLink = document.createElement('a')
    aLink.download = `${id}.png`
    aLink.href = pngData
    aLink.click()
    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
      window.navigator.msSaveOrOpenBlob(cv.msToBlob(), `${id}.png`);
    }
  }

  seriesToCsv = (id, chartCSV) => {
    const { heads, results, quotas, data, noExpo } = this.props
    if (heads && results) {
      const csv = [heads, ...results].join('\n')
      return downloadAsBlob(csv, `${id}.csv`)
    }
    if (quotas && data) {
      const headers = quotas.map(item => item.name)
      const finalResult = data.map(item => {
        return quotas
          .map(quota =>
            quota.isNumeric || quota.format === 'commaNumber'
              ? item[quota.value]
              : formatFundQuota(quota, item)
          )
          .join(',')
      })
      finalResult.unshift(headers)
      const csv = finalResult.join('\n')
      return downloadAsBlob(csv, `${id}.csv`)
    }
    if (noExpo) {
      const lines = chartCSV.split('\n')
      const head = lines[0]
      const result = lines.slice(1).map(item => item.split(',').map(one => {
        const num = Number(one)
        if (isNaN(num)) {
          return one
        }
        return this.toNonExponential(num)
      }).join(','))
      const csv = [head, ...result].join('\n')
      return downloadAsBlob(csv, `${id}.csv`)
    }
    downloadAsBlob(chartCSV, `${id}.csv`)
  }

  toNonExponential = (num) => {
    const m = num.toExponential().match(/\d(?:\.(\d*))?e([+-]\d+)/)
    return num.toFixed(Math.max(0, (m[1] || '').length - m[2]))
  }

  render() {
    const { id } = this.props
    const styles = require('./GetCsvAndPng.less')
    const menu = (
      <Menu onClick={this.onSelect}>
        <Menu.Item key={'exportCSV'}>下载数据</Menu.Item>
        <Menu.Item key={'exportPNG'}>下载图片</Menu.Item>
      </Menu>
    )
    return (
      <div
        className={classnames(
          styles.buttonWapper,
          this.props.className,
          'js-hidden no-print'
        )}
      >
        <Dropdown overlay={menu} trigger={['hover']}>
          <Button size="small">
            <i className="fa fa-download" />
          </Button>
        </Dropdown>
      </div>
    )
  }
}
