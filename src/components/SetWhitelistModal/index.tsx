import PropTypes from 'prop-types'
import React, { Component } from 'react'
import InviteModal from '@/components/InviteModal'
import classnames from 'classnames'
import deduplication from '../../utils/deduplication'
import { Modal, Select, Button, Icon } from 'antd'

export default class SetWhitelistModal extends Component {
  static propTypes = {
    submit: PropTypes.func.isRequired,
    users: PropTypes.array.isRequired,
    show: PropTypes.bool.isRequired,
    close: PropTypes.func.isRequired,
    survey: PropTypes.object.isRequired,
    inviteJoin: PropTypes.func.isRequired,
    inviteResult: PropTypes.object,
    inviteError: PropTypes.object,
    resetAdminState: PropTypes.func.isRequired,
    currentUser: PropTypes.object
  }

  constructor(props) {
    super(props)
    console.log('survey', props.survey)
    this.state = {
      currentUsers: props.survey.whitelist
    }
  }

  componentWillUnmount() {
    this.props.resetAdminState({ invite: false, inviteError: null })
  }

  handleScaleChange = values => {
    console.log('values', values)
    // const users = this.getUsers()
    // const allUsers = []
    // values.forEach(item => {
    //   users.forEach(user => (user.tags.includes(item) || item.includes(user.email || user.contact)) && allUsers.push(user._id))
    // })
    this.setState({ currentUsers: values })
  }

  getUsers() {
    const isSolution = false
    return isSolution
      ? this.props.users
      : this.props.users.filter(
        user =>
          user.fof_status === 'actived' &&
          ~user.user_scopes.indexOf('researcher')
      )
  }

  getTags() {
    return deduplication(
      this.getUsers().reduce((out, user) => out.concat(user.tags), [])
    )
  }

  inviteJoin = (emails, subject, content, isCc) => {
    const { survey } = this.props
    this.props.inviteJoin(survey._id, { emails, subject, content, isCc })
  }

  submit = () => {
    const { currentUsers } = this.state
    this.props.submit({ whitelist: currentUsers })
    this.props.close()
  }
  render() {
    const styles = require('./index.less')
    const { inviteResult, resetAdminState, inviteError, users, currentUser, survey: { description } } = this.props
    const { currentUsers } = this.state
    const Option = Select.Option
    const tags = this.getTags()
    const otherUsers = this.getUsers()
    const tagObjects = tags.map(tag => ({
      _id: tag,
      isTag: true,
      name: tag,
      nickname: tag
    }))
    let options = users
    // if (inviteResult) {
    //   const { fundUsers } = inviteResult
    //   options = options.concat(fundUsers)
    // }
    console.log('currentUsers', currentUsers)
    // const currentUsersValue = currentUsers.map(item => {
    //   const theUser = otherUsers.find(user => user._id === item)
    //   return theUser && `${theUser.nickname}${theUser.email || theUser.contact}`
    // })
    return (
      <div>
        <Modal
          visible={this.props.show}
          onCancel={this.props.close}
          className={styles.setWhitelistModal}
          title='设置白名单'
          footer={[
            <Button onClick={this.props.close}>取消</Button>,
            <Button onClick={this.submit}>确定</Button>
          ]}
        >
          <div>
            <div className={classnames('form-group')}>
              <label htmlFor="tagInput">
                白名单设置
              </label>
              {/* <InviteModal
                className={styles.inviteButton}
                inviteJoin={this.inviteJoin}
                resetState={resetAdminState}
                {...{ inviteResult, inviteError, users, currentUser, description }}
              >
                <a style={{ color: 'orange' }}>邀请新用户</a>
              </InviteModal> */}
              <Select
                mode="multiple"
                placeholder='输入邮箱、姓名、公司进行搜索'
                onChange={this.handleScaleChange}
                className={styles.filterWapper}
                value={currentUsers}
              >
                {options.map(item => <Option key={item._id} value={item._id}>{item.isTag
                  ? <div><Icon type="tag" />{' '}<span>{item.name}</span></div>
                  : <div>
                    <div>{item.nickname}</div>
                    <div>{item.email || item.contact}</div>
                  </div>}</Option>)}
              </Select>
            </div>
          </div>
        </Modal>
      </div>
    )
  }
}
