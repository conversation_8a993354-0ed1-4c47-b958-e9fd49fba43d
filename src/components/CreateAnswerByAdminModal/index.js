import React from 'react'
import PropTypes from 'prop-types'
import { Modal, Button, Select } from 'antd'
import router from 'umi/router'

export default class CreateAnswerByAdminModal extends React.Component {
  static propTypes = {
    show: PropTypes.bool,
    users: PropTypes.array,
    close: PropTypes.func,
    survey: PropTypes.object,
  }

  state = {
    authorId: ''
  }

  handleChange = (authorId) => {
    this.setState({ authorId })
  }

  getUsers() {
    const isSolution = true
    return isSolution
      ? this.props.users
      : this.props.users.filter(
        user =>
          user.fof_status === 'actived' &&
          ~user.user_scopes.indexOf('researcher')
      )
  }

  submit = () => {
    const { authorId } = this.state
    const { survey } = this.props
    console.log('authorId', survey._id, authorId)
    router.push(`/duediligence/survey/${survey._id}?authorId=${authorId}`)
  }

  render() {
    const { show, close, survey: { whitelist } } = this.props
    const users = this.getUsers()
    console.log('users',users,whitelist)
    const options =users && whitelist && (users||[]).filter(user => !whitelist.includes(user._id))
    return (
      <Modal
        visible={show}
        onCancel={close}
        title='录入答案'
        footer={[
          <Button onClick={close}>取消</Button>,
          <Button onClick={this.submit}>确认</Button>
        ]}
      >
        <Select style={{ width: '100%' }} placeholder='请选择需要录入答案的用户' onChange={this.handleChange}>
          {(options||[]).map(option => (
            <Select.Option value={option._id}>
              {option.nickname}
            </Select.Option>
          ))}
        </Select>
      </Modal>
    )
  }
}
