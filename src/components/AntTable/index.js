import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { Table, Icon } from 'antd'
import sortQuotaFn from '../../utils/sortQuotaFn'
import renderFundQuota from '../../utils/renderFundQuota'
import AntTdModal from '../AntTdModal'
import styles from './AntTable.less'
import stringWidth from 'string-width'

// @connect(
//   state => ({
//     canBePrinted: state.fund.isPrint
//   }),
//   {}
// )

export default class AntTable extends Component {
  static propTypes = {
    quotas: PropTypes.array.isRequired, // quotas中的quota可以设置width、textAlign、fixed、hasSorter、hasFilter、filterDropdown、render、children来给对应列设置宽度、对齐方式、固定位置、排序、筛选、自定义表头、自定义单元格、表头分组
    data: PropTypes.array.isRequired, // data和以前data数据结构一样
    pageSize: PropTypes.number, // 给pageSize设置值table会按照这个值分页，不设置则不分页
    showHeader: PropTypes.bool, // 是否显示表头
    yScrollHeight: PropTypes.number, // 表格垂直滚动的高度
    canBePrinted: PropTypes.bool, // 打印模式下去掉X轴的滚动、单列宽度限制和位置固定
    isPrint: PropTypes.bool,
    title: PropTypes.any
  }

  constructor(props) {
    super(props)
    const { quotas } = props
    this.state = quotas
      .filter(quota => quota.filterDropdown)
      .reduce((out, quota) => {
        out[`visible-${quota.value}`] = false
        return out
      }, {})
  }

  getSum = items => {
    return items.reduce((sum, item) => {
      if (item.width) {
        sum += item.width // eslint-disable-line
      }
      return sum
    }, 0)
  }

  getOneWidth = () => {
    // 计算未设置width列的width
    const { quotas } = this.props
    const widthQuotas = quotas.filter(
      quota =>
        quota.width ||
        (quota.children && quota.children.some(child => child.width))
    )
    const widthQuotasSum = this.getSum(widthQuotas)
    const difWidth =
      (!this.hasXSCroll() && this.hasYScroll() && !this.hasSetAllWidth()
        ? 1125
        : 1140) - widthQuotasSum // 消除y滚动条所占宽度对水平总宽度的影响
    const difLength = quotas.length - widthQuotas.length
    if (difLength !== 0) {
      const oneWidth = difWidth / difLength
      return oneWidth
    }
    return 0
  }

  getXScrollValue = columns => {
    const xScrollSum = this.getSum(columns)
    const xScrollValue =
      xScrollSum > 1140 && xScrollSum < 1145 ? 1140 : xScrollSum // 去掉7、8、9列总宽度略大于1140产生的水平滚动条
    return xScrollValue
  }

  fundDataRender = (quota, width) => item => {
    return (
      <div style={{ maxWidth: (quota.width || width) - 17 }}>
        {renderFundQuota(quota, item)}
      </div>
    )
  }

  generateFinalQuotas = () => {
    const { quotas, data, canBePrinted, isPrint } = this.props
    const oneWidth = this.getOneWidth()
    const finalOneWidth = oneWidth < 114 && !canBePrinted && !isPrint ? 114 : Math.round(oneWidth)
    return quotas.map((quota, key) => {
      const finalQuota = {
        title: quota.name,
        key: `${quota.value}-${key}`,
        width: quota.width || finalOneWidth, // 根据quotas中的quota是否设置width来给table中的对应列设置宽度，未设置则系统自动计算
        render: quota.render
          ? quota.render
          : item => {
            // -17是为了给左右俩边一点边距，不至于让格子填的太慢
            if (
              !['date'].includes(quota.format) &&
              typeof quota.formatter !== 'function' &&
              typeof item[quota.value] === 'string' &&
              stringWidth(item[quota.value]) > 12
            ) {
              if (quota.isTextarea) {
                const textHtml = item[quota.value]
                return (
                  <AntTdModal text={textHtml} canBePrinted={canBePrinted || isPrint} isHtml>
                    <div
                      className={styles.textHtml}
                      style={{
                        maxWidth: (quota.width || finalOneWidth) - 17,
                        textAlign: quota.textAlign || 'inherit'
                      }}
                      dangerouslySetInnerHTML={{ __html: textHtml }}
                    />
                  </AntTdModal>
                )
              }
              return (
                <AntTdModal text={item[quota.value]} canBePrinted={canBePrinted || isPrint} >
                  <div
                    style={{
                      maxWidth: (quota.width || finalOneWidth) - 17,
                      textAlign: quota.textAlign || 'inherit',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis'
                    }}
                  >
                    {renderFundQuota(quota, item)}
                  </div>
                </AntTdModal>
              )
            }
            let value = renderFundQuota(quota, item)
            return (
              <div
                style={{
                  maxWidth: (quota.width || finalOneWidth) - 17,
                  textAlign: quota.textAlign || 'inherit'
                }}
              >
                {value}
              </div>
            )
          }
      }
      if (quota.fixed && !canBePrinted && !isPrint) {
        // 根据quotas中的quota是否设置fixed来给table中的对应列设置左右固定，fixed值可为'left'、'right'
        finalQuota.fixed = quota.fixed
      }
      if (quota.children) {
        finalQuota.width = this.getSum(quota.children)
        finalQuota.children = quota.children.map((child, index) => ({
          ...child,
          title: '其他',
          key: `${child.value}-${index}`,
          sorter:
            child.hasSorter || child.sortable
              ? sortQuotaFn(child, 'asc')
              : undefined,
          render: child.render ? child.render : this.fundDataRender(child, finalOneWidth)
        }))
      }
      if (quota.hasSorter || quota.sortable) {
        // 根据quotas中的quota是否设置hasSorter来给table中的对应列设置排序
        finalQuota.sorter = sortQuotaFn(quota, 'asc')
      }
      if (quota.hasFilter) {
        // 根据quotas中的quota是否设置hasFilter来给table中的对应列设置筛选器
        finalQuota.filters = [...new Set(data.map(dt => dt[quota.value]))].map(
          item => ({ text: item, value: item })
        )
        finalQuota.onFilter = (value, record) =>
          record[quota.value].indexOf(value) === 0
      }
      if (quota.filterDropdown) {
        finalQuota.filterDropdown = (
          <div className={styles.filterDropdown}>{quota.filterDropdown}</div>
        )
        finalQuota.filterIcon = <Icon type="question-circle-o" />
        finalQuota.filterDropdownVisible = this.state[`visible-${quota.value}`]
        finalQuota.onFilterDropdownVisibleChange = visible => {
          this.setState({ [`visible-${quota.value}`]: visible })
        }
      }
      return finalQuota
    })
  }

  hasXSCroll = () => {
    const { quotas } = this.props
    return quotas.length >= 10
  }

  hasYScroll = () => {
    const { data } = this.props
    return data.length >= 8
  }

  hasSetAllWidth = () => {
    const { quotas } = this.props
    return quotas.every(quota => {
      if (quota.width) {
        return true
      } else if (
        !quota.width &&
        quota.children &&
        quota.children.every(child => child.width)
      ) {
        return true
      }
      return false
    })
  }

  renderXScrollValue = () => {
    const { canBePrinted, isPrint } = this.props
    const columns = this.generateFinalQuotas()
    const xScrollValue = this.getXScrollValue(columns)
    if (canBePrinted || isPrint) {
      return undefined
    }
    if (!this.hasXSCroll() && this.hasYScroll() && !this.hasSetAllWidth()) {
      return 1125
    }
    return xScrollValue
  }

  render() {
    const { data, pageSize, showHeader, yScrollHeight, title } = this.props
    const columns = this.generateFinalQuotas()
    const dataSource = data.map((item, key) => ({
      ...item,
      key: `${item}-${key}`
    }))
    const xScrollValue = this.renderXScrollValue()
    return (
      <Table
        title={title}
        columns={columns}
        dataSource={dataSource}
        pagination={
          !pageSize || dataSource.length <= pageSize ? false : { pageSize }
        }
        scroll={{
          x: xScrollValue,
          y: this.hasYScroll() ? yScrollHeight || 480 : undefined
        }}
        bordered
        showHeader={showHeader === false ? showHeader : true}
      />
    )
  }
}
