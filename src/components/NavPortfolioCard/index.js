/* eslint-disable react/jsx-key */
import React, { Component } from 'react'
import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { Row, Col, Tooltip, Card, Statistic, Popconfirm } from 'antd';
import classnames from 'classnames'
import Chart from '@/components/Chart/Chart'
import chartTheme from '@/components/Chart/theme'
import NavPortfolioModal from '@/components/NavPortfolioModal'
import styles from './style.less'

const { Meta } = Card

export default class NavPortfolioCard extends Component {
  getActions() {
    const { portfolio, t } = this.props
    return [
      <Tooltip placement="top" title="概览">
        <a href={`/fof/portfolios/${portfolio._id}/invest_performance`} rel="noopener noreferrer" target="_blank">
          <EyeOutlined />
        </a>
      </Tooltip>,
      <NavPortfolioModal
        isEdit
        fundData={portfolio}
        t={t}
        fundType={portfolio.portfolioType}
        navPortfolioType={
          portfolio.portfolioType === 'nav' ? portfolio.navPortfolioType || 'nav' : undefined
        }
      >
        <Tooltip title="编辑">
          <EditOutlined />
        </Tooltip>
      </NavPortfolioModal>,
      <Popconfirm
        title={`${t('portfolio.delTip')}${portfolio.name}${t('portfolio.questionEnd')}？`}
        onConfirm={() => this.props.removePortfolio(portfolio._id)}
        onCancel={() => {}}
        okText={t('portfolio.confirm')}
        cancelText={t('portfolio.cancel')}
      >
        <Tooltip title="删除">
          <DeleteOutlined />
        </Tooltip>
      </Popconfirm>,
    ];
  }

  renderChart() {
    const {
      portfolio: { segments },
    } = this.props
    let data = []
    if (segments && segments.length) {
      const segment = segments[0]
      const { funds } = segment
      data = funds.map(item => {
        return {
          name: item.name,
          realVal: item.ratio,
          value: item.ratio,
          portfolioType: item.isSaa ? 'saa' : 'taa',
        }
      })
    }

    if (this.props.portfolio.portfolioType === 'css') {
      data = data.map(item => {
        item.displayName = item.name
        item.parent = item.portfolioType
        return item
      })
      data.unshift({
        id: 'saa',
        name: 'SAA',
        color: '#6975CD',
      })
      data.unshift({
        id: 'taa',
        name: 'TAA',
        color: '#D5896C',
      })
    } else {
      data = data
        .sort((fst, snd) => snd.value - fst.value)
        .map((item, index) => {
          item.displayName = index < 12 ? item.name : ''
          item.color = chartTheme.colors[index]
          return item
        })
    }
    const config = {
      chart: {
        type: 'treemap',
        height: 100,
        margin: [0, 1, 0, 1],
        borderRadius: 2,
      },
      tooltip: {
        shared: true,
        pointFormat: '{point.name}: <b>{point.realVal:.2f}%</b><br/>',
      },
      series: [
        {
          type: 'treemap',
          layoutAlgorithm: 'squarified',
          animation: false,
          dataLabels: {
            enabled: false,
            format: '{point.displayName} {point.realVal:.2f}%',
          },
          data: data,
        },
      ],
    }

    if (this.props.portfolio.portfolioType === 'css') {
      config.series[0].layoutAlgorithm = 'stripes'
      config.series[0].alternateStartingDirection = true
      config.series[0].levels = [
        {
          level: 1,
          layoutAlgorithm: 'sliceAndDice',
          dataLabels: {
            enabled: true,
            align: 'left',
            verticalAlign: 'top',
            format: '{point.name}',
            style: {
              fontSize: '15px',
              fontWeight: 'bold',
            },
          },
        },
      ]
    }
    return <Chart options={config} />
  }

  render() {
    const { portfolio, bordered } = this.props

    const quotas = [
      {
        name: '最新净值',
        value: 'unitNavEndOfTerm',
        format: 'number',
      },
      {
        name: '年化收益',
        value: 'yearReturn',
        format: 'percentage',
      },
      {
        name: '波动率',
        value: 'vol',
        format: 'valPercentage',
      },
    ]
    const getQuotaValue = (quota) => {
      let value = portfolio[quota.value]
      let suffix = ''
      let className = ''
      let precision = 4
      if (['percentage', 'valPercentage'].includes(quota.format)) {
        value = value * 100
        suffix = '%'
        precision = 2
        if (quota.format === 'percentage' && value) {
          className = value > 0 ? 'colorUpWrap' : 'colorDownWrap'
        }
      }
      return {
        value, suffix, className, precision,
      }
    }
    return (
      <Card
        style={{ marginBottom: '15px' }}
        className={classnames(styles.cardWapper, { [styles.bordered]: bordered })}
        cover={this.renderChart()}
        actions={this.getActions()}
      >
        <a
          href={`/fof/portfolios/${portfolio._id}/invest_performance`}
          rel="noopener noreferrer"
          target="_blank"
        >
          <Meta className={styles.meta} title={portfolio.name} description={<div></div>} />
        </a>
        <Row>
          {quotas.map(item => {
            const quotaValue = getQuotaValue(item)
            return (
              <Col key={`${item.value}`} span={8}>
                <Statistic title={item.name} value={quotaValue.value} suffix={quotaValue.suffix} precision={quotaValue.precision} className={quotaValue.className}/>
              </Col>
            )
          })}
        </Row>
      </Card>
    )
  }
}
