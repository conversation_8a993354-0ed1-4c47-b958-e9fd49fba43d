import PropTypes from 'prop-types'
import React, { Component } from 'react'
import DateFormatter from './DateFormatter'
import moment from 'moment'
import <PERSON><PERSON>enderer from './RowRenderer'
import uniqBy from 'lodash/uniqBy'
import { Dropdown, Menu, Tooltip, Button, Upload, Icon } from 'antd'
import styles from './index.less'
const ButtonGroup = Button.Group
import { getToken } from '@/utils/utils'


class NavGrid extends Component {
  static propTypes = {
    columns: PropTypes.array.isRequired,
    minHeight: PropTypes.number,
    rows: PropTypes.array.isRequired,
    extractMatrix: PropTypes.func.isRequired,
    extractResult: PropTypes.object,
    id: PropTypes.string.isRequired,
    resetSurveyState: PropTypes.func.isRequired,
    startDate: PropTypes.any,
    endDate: PropTypes.any,
    handleChange: PropTypes.func.isRequired,
    editable: PropTypes.bool,
    exceptRows: PropTypes.array.isRequired,
    isEN: PropTypes.bool
  }

  constructor(props) {
    super(props)
    const { columns, rows } = props
    const newRows = this.generateRows(rows)

    this.state = {
      rows: newRows,
      columnMap: columns.reduce((out, item) => {
        out[item.key] = item
        return out
      }, {})
    }
  }

  componentDidMount() {
    this.init()
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.extractResult) {
      this.fillExtractData(nextProps)
    }
  }

  generateRows(rows) {
    const minRowCount = 50
    const newRows = rows.map(item => ({
      ...item,
      date: moment(new Date(item.date)).format('YYYY-MM-DD')
    }))

    if (rows.length < minRowCount) {
      const tmp = minRowCount - rows.length
      const emptyRow = this.createEmptyRow()
      for (let idx = 0; idx < tmp; idx++) {
        newRows.push({ ...emptyRow })
      }
    }
    return newRows
  }

  createEmptyRow() {
    return this.props.columns.reduce((out, item) => {
      out[item.key] = undefined
      return out
    }, {})
  }

  init() {
    this.setState({ mounted: true })
  }

  fillExtractData = props => {
    const { startDate, endDate, isEN } = this.props
    const realStartDate = moment(new Date(startDate)).startOf('date')
    const realEndDate = moment(
      new Date(endDate === 'now' ? Date.now() : endDate)
    ).startOf('date')
    const { extractResult, id } = props
    let result = extractResult && extractResult[id]
    if (!result) {
      return
    }
    const rows = []
    result = result.slice(1)
    result
      .filter(item => !/^\s*$/.test(item[1]))
      .forEach(item => {
        let date = item[0]
        if (item[1]) {
          item[1] = item[1].replace(/[,\s]/g, '')
        }
        if (item[2]) {
          item[2] = item[2].replace(/[,\s]/g, '')
        }
        if (typeof date === 'number') {
          if ((date + '').length === 8) {
            date = moment(date + '')
          } else {
            date = moment(new Date(1900, 0, date - 1))
          }
        } else {
          date = moment(date)
        }
        if (
          !date.isValid() ||
          Number.isNaN(Number(item[1])) ||
          date < realStartDate ||
          date > realEndDate
        ) {
          return
        }
        rows.push({
          date: date.startOf('date').format('YYYY-MM-DD'),
          value: Number(item[1]),
          injection: Number(item[2]) || undefined
        })
      })
    if (!rows.length) {
      toastr.warning(`${isEN ? 'No data that meets the criteria' : '没有满足的条件'}`)
    } else {
      const newRows = this.generateRows(rows)
      this.setState({ rows: newRows })
      this.handleNavChange(newRows)
    }
    this.props.resetSurveyState({ extractResult: {} })
  }

  rowGetter = index => {
    return this.state.rows[index]
  }

  handleNavChange = data => {
    let nav = data.map(item => ({
      ...item,
      date: +moment(new Date(item.date)).startOf('date')
    }))
    nav = nav
      .filter(item => item.date && item.value)
      .sort((fst, snd) => fst.date - snd.date)
    nav = uniqBy(nav, 'date')
    this.props.handleChange(nav)
  }

  handleGridRowsUpdated = ({ fromRow, toRow, updated, cellKey }) => {
    const rows = this.state.rows.slice()
    const { columnMap } = this.state
    const column = columnMap[cellKey]
    let invalid

    for (let idx = fromRow; idx <= toRow; idx++) {
      const value = updated[cellKey]
      if (value && column.validator && !column.validator.validate(value)) {
        invalid = true
        toastr.warning(column.validator.message)
        break
      }
      const rowToUpdate = rows[idx]
      const updatedRow = Object.assign(rowToUpdate, updated)
      rows[idx] = updatedRow
    }
    if (!invalid) {
      this.setState({ rows })
      this.handleNavChange(rows)
    }
  }

  handleGridAction = (event) => {
    let eventKey = event.key
    const newRow = this.createEmptyRow()
    const { rows } = this.state
    if (eventKey === 'addNewTop') {
      rows.unshift(newRow)
    } else {
      rows.push(newRow)
    }
    this.setState({ rows })
  }

  handleGridSort = (sortColumn, sortDirection) => {
    const comparer = (fst, snd) => {
      let fstValue = fst[sortColumn]
      let sndValue = snd[sortColumn]
      let ret

      if (sortDirection === 'NONE') {
        return new Date(fst.date) - new Date(snd.date)
      }

      if (sortColumn === 'date') {
        ret =
          sortDirection === 'ASC'
            ? new Date(fstValue) - new Date(sndValue)
            : new Date(sndValue) - new Date(fstValue)
      }

      if (sortColumn === 'value') {
        ret =
          sortDirection === 'ASC'
            ? Number(fstValue) - Number(sndValue)
            : Number(sndValue) - Number(fstValue)
      }

      if (sortColumn === 'injection') {
        fstValue = fstValue === undefined ? -Number.MAX_VALUE : Number(fstValue)
        sndValue = sndValue === undefined ? -Number.MAX_VALUE : Number(sndValue)
        ret =
          sortDirection === 'ASC' ? fstValue - sndValue : sndValue - fstValue
      }

      return ret
    }
    const filter = item => item.date && item.value
    const rows = this.state.rows
      .slice(0)
      .filter(filter)
      .sort(comparer)
    this.setState({ rows: this.generateRows(rows) })
  }


  beforeUpload = file => {
    const hasTooLargeFile = file.size > 20 * 1024 * 1024
    if (hasTooLargeFile) {
      return toastr.error('超出上传文件20M的最大限制')
    }
    const { id } = this.props
    this.props.extractMatrix(id, file)
    return false
  }

  render() {
    const { columns, minHeight, isEN, editable, exceptRows } = this.props
    const { mounted } = this.state
    if (!mounted) {
      return false
    }
    const ReactDataGrid = require('react-data-grid')
    const header = { authorization: getToken() }
    return (
      <div className={styles.wapper}>
        {editable && (
          <ButtonGroup className={styles.buttons}>
            <div style={{ position: 'absolute', top: 0, right: 0, display: 'flex' }}>
              <Upload
                multiple={false}
                className={styles.uploadWrapper}
                name="attachment"
                showUploadList={false}
                action="/fake/url"
                accept=".csv,text/csv,.xls,application/vnd.ms-excel,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                headers={header}
                beforeUpload={this.beforeUpload}
              >
                <Tooltip
                  id={'upload'}
                  placement="top"
                  title={isEN ? 'CSV, Excel files allowed，import data from the second row' : '支持导入 Excel, CSV 文件，从文件第二行开始导入'}>
                  <Button>
                    <Icon type="upload" /> 上传
                  </Button>
                </Tooltip>
              </Upload>
              <Dropdown id={'grid-action-dropdown'} overlay={
                (<Menu onClick={this.handleGridAction}>
                  <Menu.Item key="addNewTop">
                    <i className="fa fa-arrow-up" /> {isEN ? 'Top' : '顶部'}
                  </Menu.Item>
                  <Menu.Item key="addNewBottom">
                    <i className="fa fa-arrow-down" /> {isEN ? 'Bottom' : '底部'}
                  </Menu.Item>
                </Menu>)
              }>
                <Tooltip id={'add-new-upload'} placement="top" title={isEN ? 'Add A New Line' : '新增一行'}>
                  <Button>
                    <i className="fa fa-plus-square-o" />
                  </Button>
                </Tooltip>
              </Dropdown>
            </div>
          </ButtonGroup>
        )
        }
        <ReactDataGrid
          enableCellSelect
          onGridSort={this.handleGridSort}
          columns={columns}
          rowGetter={this.rowGetter}
          rowsCount={this.state.rows.length}
          minHeight={minHeight || 500}
          rowRenderer={<RowRenderer exceptRows={exceptRows} />}
          onGridRowsUpdated={this.handleGridRowsUpdated}
        />
      </div >
    )
  }
}

NavGrid.DateFormatter = DateFormatter
NavGrid.VaueFormatter = ({ value }) => {
  if (!value) {
    return value
  }
  return `${Number(value).toFixed(4)}`
}

export default NavGrid
