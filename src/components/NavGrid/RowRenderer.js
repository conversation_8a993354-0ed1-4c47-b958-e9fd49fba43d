import PropTypes from 'prop-types'
import React, { Component } from 'react'
import moment from 'moment'

class <PERSON><PERSON><PERSON><PERSON> extends Component {
  static propTypes = {
    exceptRows: PropTypes.array,
    row: PropTypes.object
  }

  getRowStyle() {
    return {
      color: this.getRowBackground()
    }
  }

  getRowBackground() {
    const { exceptRows, row } = this.props
    const format = moment(row.date).format('YYYY-MM-DD')
    return ~exceptRows.indexOf(format) && 'tomato'
  }

  render() {
    const ReactDataGrid = require('react-data-grid')
    return (
      <div style={this.getRowStyle()}>
        <ReactDataGrid.Row ref="row" {...this.props} />
      </div>
    )
  }
}

export default RowRenderer
