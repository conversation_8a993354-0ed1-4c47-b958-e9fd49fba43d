import React from 'react'
import HighchartsReact from 'highcharts-react-official'
import Highcharts from './Highcharts'

export interface ChartProps {
  options?: Highcharts.Options;
  constructorType?: string;
}
const Chart: React.FC<ChartProps> = props => {
  const options: Highcharts.Options = {
    series: [
      {
        type: 'line',
        data: [1, 2, 3],
      },
      {
        type: 'line',
        data: [3, 1, 3],
      },
      {
        type: 'line',
        data: [2, 4, 6],
      },
    ],
  }

  return <HighchartsReact highcharts={Highcharts} options={options} {...props} />
}

export default Chart
