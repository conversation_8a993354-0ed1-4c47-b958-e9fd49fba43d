import React from 'react'
import HighchartsReact from 'highcharts-react-official'
import Highcharts from 'highcharts/highstock'
import HighchartsMain from 'highcharts'
import exporting from 'highcharts/modules/exporting'
import highchartsMore from 'highcharts/highcharts-more'
import solidgauge from 'highcharts/modules/solid-gauge'
import map from 'highcharts/modules/map'
import treemap from 'highcharts/modules/treemap'
import heatmap from 'highcharts/modules/heatmap'
import noData from 'highcharts/modules/no-data-to-display'
import mapData from '@/assets/mapdata/cn-all'
import exportCsv from 'highcharts-export-csv'

import theme from './theme'

exportCsv(Highcharts)
exporting(Highcharts)
highchartsMore(Highcharts)
solidgauge(Highcharts)
map(Highcharts)
heatmap(Highcharts)
treemap(Highcharts)
noData(Highcharts)
Highcharts.maps['countries/cn/custom/cn-all'] = mapData

const customOptions = {
  chart: {
    height: 300,
    pinchType: '',
  },
  credits: {
    enabled: false,
  },
  title: {
    text: '',
  },
  exporting: {
    enabled: false,
  },
  legend: {
    enabled: true,
    maxHeight: 40,
    layout: 'horizontal',
    align: 'center',
    verticalAlign: 'bottom',
  },
  tooltip: {
    shared: true,
    split: false,
    xDateFormat: '%A, %b %e, %Y',
    borderColor: '#444970',
    backgroundColor: '#282c47',
    pointFormat:
      '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.2f}%</b><br/>',
  },
  plotOptions: {
    series: {
      borderWidth: 0,
      dataGrouping: {
        enabled: false,
      },
      turboThreshold: 20000,
    },
    bar: {
      borderWidth: 0,
    },
    column: {
      borderWidth: 0,
    },
    heatmap: {
      borderWidth: 0,
    },
    pie: {
      borderWidth: 0,
      allowPointSelect: true,
      cursor: 'pointer',
      dataLabels: {
        enabled: false,
      },
      showInLegend: true,
    },
    area: {
      stacking: 'normal',
      lineWidth: 1,
      marker: {
        lineWidth: 2,
        lineColor: '#ffffff',
      },
    },
  },
}
const customHighStockOptions = {
  ...customOptions,
  rangeSelector: {
    enabled: false,
    selected: 1,
    inputDateFormat: '%Y-%m-%d',
    inputEditDateFormat: '%Y-%m-%d',
    inputEnabled: false,
    buttons: [
      {
        type: 'ytd',
        text: 'YTD',
      },
      {
        type: 'year',
        count: 1,
        text: '1y',
      },
      {
        type: 'year',
        count: 3,
        text: '3y',
      },
      {
        type: 'all',
        text: 'All',
      },
    ],
  },
  xAxis: {
    dateTimeLabelFormats: {
      second: '%Y-%m-%d<br/>%H:%M:%S',
      minute: '%Y-%m-%d<br/>%H:%M',
      hour: '%Y-%m-%d<br/>%H:%M',
      day: '%Y<br/>%m-%d',
      week: '%Y<br/>%m-%d',
      month: '%Y-%m',
      year: '%Y',
    },
    events: {},
  },
  yAxis: {
    title: {
      text: '',
      offset: -10,
    },
    opposite: false,
    labels: {
      format: '{value}%',
    },
  },
  time: {
    timezone: 'Asia/Shanghai',
  },
  global: { useUTC: false },
  noData: {
    useHTML: true,
  },
  lang: {
    loading: '加载中...',
    noData: '没有数据',
    thousandsSep: ',',
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    shortMonths: [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    weekdays: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
    invalidDate: '无效时间',
    exportButtonTitle: '导出',
    printChart: '打印图表',
    printButtonTitle: '打印',
    rangeSelectorFrom: '从',
    rangeSelectorTo: '到',
    rangeSelectorZoom: '缩放/时间范围',
    downloadPNG: '下载PNG格式',
    downloadJPEG: '下载JPEG格式',
    downloadPDF: '下载PDF格式',
    downloadSVG: '下载SVG格式',
  },
  navigator: {
    height: 20,
    handles: {
      backgroundColor: '#949fb6',
      borderColor: '#949fb6',
    },
    outlineColor: '#949fb6',
    maskFill: 'rgba(255,255,255,0.1)',
    series: {
      color: '#7798BF',
      lineColor: '#A6C7ED',
    },
    xAxis: {
      gridLineColor: '#505053',
    },
  },
  scrollbar: {
    barBackgroundColor: '#3a4353',
    barBorderColor: '#181f29',
    buttonArrowColor: '#949fb6',
    buttonBackgroundColor: '#3a4353',
    buttonBorderColor: '#181f29',
    rifleColor: '#FFF',
    trackBackgroundColor: '#252b35',
    trackBorderColor: '#181f29',
  },
}

HighchartsMain.setOptions(customHighStockOptions)
Highcharts.setOptions(theme)
Highcharts.setOptions(customHighStockOptions)
// Highstock.setOptions(theme)
// Highstock.setOptions(customHighStockOptions)

export interface ChartProps {
  options?: Highcharts.Options;
  constructorType?: string;
  refChart?: any;
  afterChartCreated?: any;
}
const Chart: React.FC<ChartProps> = props => {
  const legendOptions: Highcharts.Options = {
    legend: {
      enabled: true,
      maxHeight: 60,
      layout: 'horizontal',
      align: 'center',
      verticalAlign: 'bottom',
    },
  }
  const { options, afterChartCreated, ...rest } = props
  return (
    <HighchartsReact
      callback={afterChartCreated}
      highcharts={Highcharts}
      options={{
        ...legendOptions,
        ...options,
      }}
      {...rest}
    />
  )
}

Chart.Highcharts = Highcharts
export default Chart
