import React from 'react'
// import ReactEcharts from 'echarts-for-react'
// import echarts from 'echarts'
import ReactEchartsCore from 'echarts-for-react/lib/core'
import echarts from 'echarts/lib/echarts'
import 'echarts/lib/chart/pie'
import 'echarts/lib/chart/line'
import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/gauge'
import 'echarts/lib/chart/custom'
import 'echarts/lib/chart/scatter'
import 'echarts/lib/chart/treemap'
import 'echarts/lib/chart/effectScatter'
import 'echarts/lib/chart/pictorialBar'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/title'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/axis'
import 'echarts/lib/component/singleAxis'
import 'echarts/lib/component/visualMap'
import 'echarts/lib/component/markPoint'
import 'echarts/lib/component/calendar'
import 'echarts/lib/util/format'
import 'echarts/extension/dataTool'
// import 'echarts-gl'
import 'echarts-wordcloud'

import theme from './theme-echarts-chalk.json'

echarts.registerTheme('dark', theme)

export interface ChartProps {
  options?: any;
}
const ECharts: React.FC<ChartProps> = props => {
  const { options, ...rest } = props
  return <ReactEchartsCore echarts={echarts} option={options} theme="dark" {...rest} />
}

ECharts.echarts = echarts
export default ECharts
