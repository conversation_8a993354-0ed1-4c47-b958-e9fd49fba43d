import Highcharts from 'highcharts/highstock'
import exporting from 'highcharts/modules/exporting'
import highchartsMore from 'highcharts/highcharts-more'
import solidgauge from 'highcharts/modules/solid-gauge'
import map from 'highcharts/modules/map'
import treemap from 'highcharts/modules/treemap'
import heatmap from 'highcharts/modules/heatmap'
import mapData from '@/assets/mapdata/cn-all'
import exportCsv from 'highcharts-export-csv'

exportCsv(Highcharts)
exporting(Highcharts)
highchartsMore(Highcharts)
solidgauge(Highcharts)
map(Highcharts)
heatmap(Highcharts)
treemap(Highcharts)
Highcharts.maps['countries/cn/custom/cn-all'] = mapData

export default Highcharts

export const dateTimeFormat = {
  day: '%Y/%m/%d',
  week: '%Y/%m',
  month: '%Y/%m',
  year: '%Y',
}

const colors = function() {
  return ['#0ebf9c', '#e85655', '#e49831', '#46a3df', '#A171D3', '#CE9078', '#5e6ee3', '#8094bd']
}

const lineColors = function() {
  return colors().concat([
    '#3366CC',
    '#DC3912',
    '#FF9900',
    '#109618',
    '#990099',
    '#3B3EAC',
    '#0099C6',
    '#DD4477',
    '#66AA00',
    '#B82E2E',
    '#316395',
    '#994499',
    '#22AA99',
    '#AAAA11',
    '#6633CC',
    '#E67300',
    '#8B0707',
    '#329262',
    '#5574A6',
    '#3B3EAC',
    '#e2431e',
    '#e7711b',
    '#f1ca3a',
    '#6f9654',
    '#1c91c0',
    '#43459d',
    '#c12e34',
    '#e6b600',
    '#0098d9',
    '#2b821d',
    '#005eaa',
    '#ff7f00',
    '#2f4554',
    '#32a487',
    '#377eb8',
    '#e41a1c',
    '#4daf4a',
    '#984ea3',
    '#339ca8',
    '#ffff33',
    '#a65628',
    '#f781bf',
    '#8564A1',
    '#CA97D2',
    '#79CA82',
    '#B5CA92',
    '#A47D7C',
    '#92A8CD',
    '#DB843D',
    '#3D96AE',
    '#80699B',
    '#89A54E',
    '#AA4643',
    '#4572A7',
    '#ED5565',
    '#1ab394',
    '#f8ac59',
    '#c23531',
    '#1c84c6',
    '#cda819',
    '#61a0a8',
    '#d48265',
    '#91c7ae',
    '#749f83',
    '#ca8622',
    '#bda29a',
    '#6e7074',
    '#546570',
    '#c4ccd3',
  ])
}

const pieColors = function() {
  return lineColors()
}

/**
 * 导出颜色
 */
export const ChartsColor = {
  colors,
  lineColors,
}

export const rangeSelectorButtons = {
  enabled: false,
  buttons: [
    // {
    //   type: 'month',
    //   count: 1,
    //   text: '1个月',
    // }, {
    //   type: 'month',
    //   count: 3,
    //   text: '3个月',
    // }, {
    //   type: 'month',
    //   count: 6,
    //   text: '6个月',
    // },
    {
      type: 'year',
      count: 1,
      text: '1年',
    },
    {
      type: 'year',
      count: 3,
      text: '3年',
    },
    {
      type: 'ytd',
      text: '年初以来',
    },
    {
      type: 'all',
      text: '全部',
    },
  ],
  buttonTheme: {
    width: 'auto',
    fill: '#3a404c',
    stroke: '#000000',
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
      color: '#949fb6',
    },
    states: {
      hover: {
        fill: '#252B35',
        stroke: '#000000',
        style: {
          color: 'white',
        },
      },
      select: {
        fill: '#00b692',
        stroke: '#000000',
        style: {
          color: 'white',
        },
      },
    },
  },
  inputDateFormat: '%Y-%m-%d',
  inputEditDateFormat: '%Y-%m-%d',
  inputBoxBorderColor: '#505053',
  inputStyle: {
    backgroundColor: '#333',
    color: 'silver',
  },
  labelStyle: {
    color: 'silver',
  },
}

export const navigator = {
  handles: {
    backgroundColor: '#949fb6',
    borderColor: '#949fb6',
  },
  outlineColor: '#949fb6',
  maskFill: 'rgba(255,255,255,0.1)',
  series: {
    color: '#7798BF',
    lineColor: '#A6C7ED',
  },
  xAxis: {
    gridLineColor: '#505053',
  },
}

export const scrollbar = {
  barBackgroundColor: '#3a4353',
  barBorderColor: '#181f29',
  buttonArrowColor: '#949fb6',
  buttonBackgroundColor: '#3a4353',
  buttonBorderColor: '#181f29',
  rifleColor: '#FFF',
  trackBackgroundColor: '#252b35',
  trackBorderColor: '#181f29',
}

export function tooltipStyleMultiLine() {
  return {
    enabled: true,
    shared: true,
    borderWidth: 1,
    borderRadius: 2,
    borderColor: '#444970',
    shadow: false,
    useHTML: true,
    backgroundColor: '#282b47',
    style: {
      color: '#ffffff',
      fontSize: '12px',
      textAlign: 'left',
    },
    xDateFormat: '%A, %b %e, %Y',
    pointFormat:
      '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.2f}%</b><br/>',
  }
}

export function tooltipStyleSingleLine() {
  return {
    enabled: true,
    borderWidth: 1,
    borderRadius: 2,
    borderColor: '#444970',
    shadow: false,
    useHTML: true,
    backgroundColor: '#282b47',
    pointFormat: '{series.name}: <b>{point.y}</b><br/>',
    style: {
      color: '#ffffff',
      fontSize: '12px',
      textAlign: 'left',
    },
    xDateFormat: '%A, %b %e, %Y',
  }
}

export const zhCNLang = {
  loading: '加载中...',
  noData: '没有数据',
  thousandsSep: ',',
  months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  shortMonths: [
    '1月',
    '2月',
    '3月',
    '4月',
    '5月',
    '6月',
    '7月',
    '8月',
    '9月',
    '10月',
    '11月',
    '12月',
  ],
  weekdays: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
  invalidDate: '无效时间',
  exportButtonTitle: '导出',
  printChart: '打印图表',
  printButtonTitle: '打印',
  rangeSelectorFrom: '从',
  rangeSelectorTo: '到',
  rangeSelectorZoom: '缩放/时间范围',
  downloadPNG: '下载PNG格式',
  downloadJPEG: '下载JPEG格式',
  downloadPDF: '下载PDF格式',
  downloadSVG: '下载SVG格式',
}

export const defaultOptions = {
  time: {
    useUTC: false,
  },
  colors: lineColors(),
  title: {
    style: {
      textTransform: 'none',
    },
    text: null,
  },
  chart: {
    backgroundColor: {
      linearGradient: { x1: 0, y1: 0, x2: 1, y2: 1 },
      stops: [[0, '#181F29'], [1, '#181F29']],
    },
    style: {
      fontFamily: "'Unica One', sans-serif",
    },
    plotBorderColor: '#3A404C',
  },
  rangeSelector: rangeSelectorButtons,
  navigator: navigator,
  scrollbar: scrollbar,
  legend: {
    itemStyle: {
      fontSize: '11px',
      fontWeight: 'normal',
    },
    symbolWidth: 8,
    symbolHeight: 8,
  },
  // 全局配置
  exporting: {
    enabled: false,
  },
  credits: {
    enabled: false,
  },
  xAxis: {
    title: {
      text: null,
    },
    dateTimeLabelFormats: dateTimeFormat,
  },
  yAxis: {
    title: {
      text: null,
    },
  },
  tooltip: tooltipStyleMultiLine(),
  plotOptions: {
    series: {
      turboThreshold: 0,
    },
  },
  noData: {
    useHTML: true,
  },
  lang: zhCNLang,
}

export function chartsShareTooltip(title, dot, options) {
  options = options || {}
  dot = dot || options.valueDecimals || 2
  return Object.assign(
    {
      enabled: true,
      shared: true,
      borderWidth: 1,
      borderRadius: 2,
      borderColor: '#444970',
      shadow: false,
      useHTML: true,
      backgroundColor: '#282b47',
      style: {
        color: '#ffffff',
        fontSize: '12px',
        textAlign: 'left',
      },
      formatter: function() {
        if (this.points[0].y !== 0) {
          const x = Highcharts.dateFormat('%A, %b %e, %Y', this.x)
          const titleTxt = title ? `<span><small>${title}</small></span><br/>` : ''
          const pointHtml = this.points
            .map(point => {
              return `
            <span style="color:${point.series.options.tooltipColor || point.series.color}">
              <small>${point.series.name}:<strong>${Highcharts.numberFormat(point.y, dot)}${point
                .series.tooltipOptions.valueSuffix || ''}</strong></small>
            </span>
            <br/>
          `
            })
            .join('')
          return `
          <div>
            ${x}<br/>
            ${titleTxt}
            ${pointHtml.join('')}
          </div>
        `
        }
        return false
      },
    },
    options,
  )
}

export function chartsTopLeftLegend(options) {
  return Object.assign(
    {
      useHTML: false,
      align: 'top',
      verticalAlign: 'top',
      x: 40,
      y: 0,
      borderWidth: 0,
      itemMarginBottom: 10,
      symbolWidth: 8,
      itemHoverStyle: {
        color: '#0069dc',
      },
      itemStyle: {
        fontSize: '11px',
        fontWeight: 'normal',
      },
      labelFormatter: function() {
        const canRemove = this.options.canRemove
        const color = this.options.color || this.color
        const removeHtml = canRemove
          ? '<span style="padding-left:14px"><i class="el-icon-close"></i></span>'
          : ''
        return `
        <div>
          <span style="color:${color}">
            ${this.name}
          </span>
            ${removeHtml}
        </div>
      `
      },
    },
    options || {},
  )
}

export function chartsTopLeftOutsideLegend(options) {
  return Object.assign(
    {
      useHTML: true,
      align: 'left',
      verticalAlign: 'top',
      x: 30,
      y: -15,
      borderWidth: 0,
      itemMarginBottom: 0,
      itemMarginTop: 0,
      symbolWidth: 8,
      itemHoverStyle: {
        color: '#0069dc',
      },
      itemStyle: {
        fontSize: '11px',
        fontWeight: 'normal',
      },
      labelFormatter: function() {
        const canRemove = this.options.canRemove
        const color = this.options.color || this.color
        const removeHtml = canRemove
          ? '<span style="padding-left:14px"><i class="el-icon-close"></i></span>'
          : ''
        return `
        <div>
          <span style="color:${color}">
            ${this.name}
          </span>
          ${removeHtml}
        </div>
      `
      },
    },
    options || {},
  )
}

/**
 * @param {Color} startColor
 * @param {Array} alpha[0,0.8]
 * @param {Array} direction
 */
export function linearGrandient(startColor, alpha, direction) {
  alpha = (Array.isArray(alpha) && alpha.length >= 2 && alpha) || [0, 0.5, 1]
  direction = direction || [0, 0, 0, 1]
  return {
    linearGradient: {
      x1: direction[0],
      y1: direction[1],
      x2: direction[2],
      y2: direction[3],
    },
    stops: Array.from(alpha).map(function(a, k) {
      return [
        k,
        Highcharts.Color(startColor)
          .setOpacity(a)
          .get('rgba'),
      ]
    }),
  }
}

Highcharts.setOptions(defaultOptions)
Highcharts.generateShareTooltip = chartsShareTooltip
Highcharts.generateTopLeftLegend = chartsTopLeftLegend
Highcharts.generateTopLeftOutsideLegend = chartsTopLeftOutsideLegend
Highcharts.linearGrandient = linearGrandient

Highcharts.lineColors = lineColors
Highcharts.pieColors = pieColors
Highcharts.colors = colors
Highcharts.getOptions().plotOptions.pie.colors = pieColors()
Highcharts.tooltipMultiLine = tooltipStyleMultiLine()
Highcharts.tooltipSingleLine = tooltipStyleSingleLine()
