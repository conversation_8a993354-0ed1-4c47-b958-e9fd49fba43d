import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import downloadAsBlob from '@/utils/downloadAsBlob'
import formatFundQuota from '@/utils/formatFundQuota'
import { Tooltip } from 'antd'
import styles from './ExportTableData.less'
export default class ExportTableData extends Component {
  static propTypes = {
    children: PropTypes.object,
    result: PropTypes.array,
    id: PropTypes.string.isRequired,
    name: PropTypes.string,
    quotas: PropTypes.array,
    data: PropTypes.array,
    className: PropTypes.string,
    isParent: PropTypes.bool,
    hasData: PropTypes.bool
  }

  exportCsv = () => {
    const { hasData, result, id, name, data } = this.props
    if (hasData) {
      return downloadAsBlob(result.join('\n'), `${name || id}.csv`)
    }
    const quotas = this.props.quotas.reduce((out, item) => {
      if (!item.children) {
        out.push(item)
      } else {
        item.children.forEach(child => {
          child.name = `${item.value}-${child.value}`
          out.push(child)
        })
      }
      return out
    }, [])
    const headers = quotas.map(
      item => item.name
    )
    const finalResult = result || data.map(item => {
      return quotas
        .map(quota => {
          if (quota.getTextValue) {
            return quota.getTextValue(quota, item)
          }
          if (quota.render) {
            return item[quota.value]
          }
          const value = quota.isNumeric || quota.format === 'commaNumber'
            ? item[quota.value]
            : formatFundQuota(quota, item)
          if (quota.value === 'STOCK_CODE' || quota.value === 'code') {
            return `="${value}"`
          }
          return value
        }).map(item => {
          if (typeof item === 'string' && /\n/.test(item)) {
            return item.replace(/\n/g, '')
          }
          if (typeof item === 'string' && item.includes(',')) {
            return item.replace(/,/g, '')
          }
          return item
        })
        .join(',')
    })
    finalResult.unshift(headers)
    const csv = finalResult.join('\n')
    downloadAsBlob(csv, `${name || id}.csv`)
  }

  renderButton() {
    const { name, id, isParent, children } = this.props
    return isParent ? (
      <div>{children}</div>
    ) : (
      <div>
        <Tooltip id={`${name || id}-export-data`} placement="top" title='下载数据'>
          <span>
            <i className="fa fa-download" />
          </span>
        </Tooltip>
      </div>
    )
  }

  render() {
    const { className, name, id } = this.props
    return (
      <div
        className={classnames(className, styles.buttonWapper)}
        onClick={this.exportCsv}
      >
        {this.renderButton()}
      </div>
    )
  }
}
