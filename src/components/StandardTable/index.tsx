import React, { Component } from 'react'
import { Table } from 'antd'
import { SorterResult, ColumnProps, TableRowSelection, TableProps } from 'antd/es/table'
import renderFundQuota from '@/utils/renderFundQuota'

import styles from './index.less'

const getValue = (obj: { [x: string]: string[] }) =>
  Object.keys(obj)
    .map(key => obj[key])
    .join(',')

type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

export interface TableListItem {
  name: string;
  _id: string;
  originalFundId?: string;
  accReturn?: number;
}

export interface TableListPagination {
  total: number;
  pageSize: number;
  current: number;
}

export interface TableListData {
  list: TableListItem[];
  pagination: Partial<TableListPagination>;
}

export interface TableListParams {
  type?: string;
  sort: string;
  order: string;
  page: number;
  per_page: number;
  input?: string;
  portfolioType?: string;
}

export interface StandardTableProps<T> extends Omit<TableProps<T>, 'columns' | 'onChange'> {
  columns: StandardTableColumnProps[];
  data: {
    list: TableListItem[];
    pagination: StandardTableProps<TableListItem>['pagination'];
  };
  selectedRows: TableListItem[];
  onSelectRow: (rows: any) => void;
  onChange: (params: Partial<TableListParams>) => void;
  showAlertInfo?: boolean;
  disableRowSlection?: boolean;
  rowSelectionType?: 'checkbox' | 'radio';
}

export interface StandardTableColumnProps extends ColumnProps<TableListItem> {
  needTotal?: boolean;
  total?: number;
  value?: string;
  format?: string;
  sortable?: boolean;
}

interface StandardTableState {
  selectedRowKeys: string[];
  needTotalList: StandardTableColumnProps[];
}

class StandardTable extends Component<StandardTableProps<TableListItem>, StandardTableState> {
  handleTableChange = (
    pagination: Partial<TableListPagination>,
    filtersArg: Record<keyof TableListItem, string[]>,
    sorter: SorterResult<TableListItem>,
  ) => {
    const { onChange } = this.props
    const filters = Object.keys(filtersArg).reduce((obj, key) => {
      const newObj = { ...obj }
      newObj[key] = getValue(filtersArg[key])
      return newObj
    }, {})
    const params: Partial<TableListParams> = {
      page: pagination.current,
      per_page: pagination.pageSize,
      ...filters,
    }
    if (sorter.field) {
      params.sort = sorter.field
      params.order = sorter.order === 'ascend' ? 'asc' : 'desc'
    }
    if (onChange) {
      onChange(params)
    }
  };

  render() {
    const { selectedRows, data, rowKey, showAlertInfo, disableRowSlection, ...rest } = this.props
    let { list = [], pagination = false } = data || {}
    if (this.props.dataSource) {
      list = this.props.dataSource
    }
    if (this.props.pagination) {
      pagination = this.props.pagination
    }
    const columns = this.props.columns.map(item => {
      item.key = item.dataIndex
      if (item.format) {
        const { format, dataIndex } = item
        item.render = (text, row) => {
          return renderFundQuota({ format, value: dataIndex || '', dataIndex }, row)
        }
      }
      return item
    })
    const paginationProps = pagination
      ? {
          // showSizeChanger: true,
          showQuickJumper: true,
          ...pagination,
        }
      : false
    const rowSelection: TableRowSelection<TableListItem> = !disableRowSlection
      ? {
          onSelect: (record, selected) => {
            let newRows = []
            if (selected) {
              newRows =
                this.props.rowSelectionType === 'radio'
                  ? [record]
                  : this.props.selectedRows.concat([record])
            } else {
              newRows = this.props.selectedRows.filter(item => item._id !== record._id)
            }
            this.props.onSelectRow(newRows)
          },
          onSelectAll: (selected, selectedRows, changeRows) => {
            let newRows = []
            if (selected) {
              newRows = this.props.selectedRows.concat(changeRows)
            } else {
              const ids = changeRows.map(item => item._id)
              newRows = this.props.selectedRows.filter(item => !ids.includes(item._id))
            }
            this.props.onSelectRow(newRows)
          },
          selectedRowKeys: this.props.selectedRows
            ? this.props.selectedRows.map(item => item._id)
            : [],
        }
      : undefined
    if (rowSelection && this.props.rowSelectionType) {
      rowSelection.type = this.props.rowSelectionType
    }
    return (
      <div className={styles.standardTable}>
        <Table
          {...rest}
          columns={columns}
          rowKey={rowKey || 'key'}
          rowSelection={rowSelection}
          dataSource={list}
          pagination={paginationProps}
          onChange={this.handleTableChange}
        />
      </div>
    )
  }
}

export default StandardTable
