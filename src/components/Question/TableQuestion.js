import PropTypes from 'prop-types'
import React, { Component } from 'react'
import SubQuestion from './SubQuestion'
import { Table } from 'react-bootstrap'
import classnames from 'classnames'
import styles from './index.less'
import { UndoOutlined } from '@ant-design/icons';

export default class TableQuestion extends Component {
  static propTypes = {
    question: PropTypes.object.isRequired,
    createRangeError: PropTypes.func.isRequired,
    onSubQuestionChange: PropTypes.func.isRequired,
    inputError: PropTypes.object,
    handleInputError: PropTypes.func,
    isEdit: PropTypes.bool,
    readOnly: PropTypes.bool,
    answer: PropTypes.any,
    answers: PropTypes.object.isRequired,
    initialRows: PropTypes.number,
    extractResult: PropTypes.object,
    onResetTableAnswer: PropTypes.func
  }

  state = {
    newRows: 0,
    initialRows: this.props.initialRows
  }

  componentWillReceiveProps(nextProps) {
    const { question } = this.props
    const { extractResult } = nextProps
    if (extractResult && extractResult[question._id]) {
      this.props.onResetTableAnswer()
      const extractData = extractResult[question._id]
      const realLength = extractData.length - 1
      const newRows =
        realLength > question.rowSize ? realLength - question.rowSize : 0
      this.setState({
        newRows,
        initialRows: 0
      })
    }
  }

  getRealRowSize() {
    const {
      question: { rowSize, allowNewRow }
    } = this.props
    const { newRows, initialRows } = this.state
    if (allowNewRow) {
      return Math.max(initialRows, rowSize) + newRows
    }
    return rowSize
  }

  addNewRow = () => {
    this.setState({ newRows: this.state.newRows + 1 })
  }

  processExtractData() {
    const { extractResult, question } = this.props
    let result = extractResult && extractResult[question._id]
    const { options } = question
    if (!result) {
      return []
    }
    if (question.allowNewRow) {
      result = result.slice(1)
    } else {
      result = result.slice(1, question.rowSize + 1)
    }
    const data = []
    for (let index = 0; index < result.length; index++) {
      if (result[index] && result[index].some(item => !!item)) {
        const arr = result[index]
        data.push(
          options.reduce((out, option, idx) => {
            out[option.value] = arr[idx]
            return out
          }, {})
        )
      }
    }
    return data
  }

  render() {
    const {
      question: { type, options, allowNewRow },
      isEdit,
      readOnly,
      inputError,
      handleInputError
    } = this.props
    const answerList = Object.keys(this.props.answers).map(key => ({
      question: key,
      answer: this.props.answers[key]
    }))
    const extractData = this.processExtractData()
    const realRowSize = extractData.length || this.getRealRowSize()
    const renderTableBody = () => {
      const ret = []
      const sortedAnswers = (answerList || []).sort(
        (fst, snd) => Number(fst.question) - Number(snd.question)
      )
      const maxId = answerList.length
        ? Math.max.apply(
          null,
          answerList.map(item => item.question).map(Number)
        )
        : 0
      for (let index = 0; index < realRowSize; index++) {
        const subQuestion = {
          id: sortedAnswers[index]
            ? sortedAnswers[index].question
            : index + maxId,
          options: options,
          type: type,
          parentId: this.props.question._id
        }
        ret.push(
          <SubQuestion
            key={subQuestion.id}
            question={subQuestion}
            isEdit={isEdit}
            readOnly={readOnly}
            answer={(sortedAnswers[index] && sortedAnswers[index].answer) || ''}
            extractData={extractData[index]}
            inputError={inputError || {}}
            handleInputError={handleInputError}
            createRangeError={this.props.createRangeError}
            handleChange={this.props.onSubQuestionChange}
          />
        )
      }
      return ret
    }
    const hasDate = options.some(option => option.isDate)
    return (
      <div className={styles.tableQuestion}>
        <div
          className={classnames(styles.matrixOptions, {
            [styles.hasDate]: hasDate,
            [styles.autoScroll]: realRowSize > 5
          })}
        >
          <Table
            responsive
            striped
            bordered
            hover
            className={styles.surveyTable}
          >
            <thead>
              <tr>
                {options.map(option => (
                  <th key={option.id}>{option.value}</th>
                ))}
                <th className={styles.resetButton}>操作</th>
              </tr>
            </thead>
            <tbody>{renderTableBody()}</tbody>
          </Table>

          {allowNewRow && !isEdit && !readOnly && (
            <div className={styles.addNewRow}>
              <span onClick={this.addNewRow}>
                <i className="fa fa-plus-square" /> 增加一行
              </span>
            </div>
          )}
        </div>
      </div>
    )
  }
}
