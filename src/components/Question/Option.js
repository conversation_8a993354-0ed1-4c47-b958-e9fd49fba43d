import PropTypes from 'prop-types'
import React, { Component } from 'react'
import Input from '../../components/Input'

export default class Option extends Component {
  static propTypes = {
    option: PropTypes.object.isRequired,
    onChange: PropTypes.func.isRequired,
    onOtherAnswerChange: PropTypes.func,
    isEdit: PropTypes.bool,
    readOnly: PropTypes.bool,
    type: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    checked: PropTypes.bool,
    label: PropTypes.string,
    otherAnswer: PropTypes.string,
    otherError: PropTypes.object,
    placeholder: PropTypes.string
  }

  onChange = () => {
    const checked = this.refs.input.getChecked()
    this.props.onChange(this.props.option.value, checked)
  }

  render() {
    const {
      option,
      type,
      name,
      isEdit,
      readOnly,
      checked,
      label,
      otherAnswer,
      placeholder,
      otherError
    } = this.props
    return (
      <div>
        <Input
          type={type}
          label={label === undefined ? option.label || option.value : label}
          checked={checked}
          name={name}
          disabled={isEdit || readOnly}
          onChange={this.onChange}
          ref="input"
        />
        {option.type === 'other' && checked && (
          <Input
            type="text"
            placeholder={placeholder}
            onChange={this.props.onOtherAnswerChange}
            value={otherAnswer}
            disabled={isEdit || readOnly}
          />
        )}
      </div>
    )
  }
}
