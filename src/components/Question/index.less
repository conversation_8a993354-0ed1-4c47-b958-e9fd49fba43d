.fullEditModal {
  :global(.form-group) {
    margin-bottom: 0;
  }
}

.settingPopover {
  :global(.popover-content) {
    padding: 9px 5px;
  }
}

.examplePopover {
  .title {
    padding: 5px 10px;
    border-left: 2px solid;
    border-left-color: #1b809e;
    margin-bottom: 10px;
  }
}

.tableQuestion,
.question {
  padding: 10px 0;
  position: relative;
  zoom: 1;
  p {
    white-space: pre-wrap;
  }
  .upload {
    cursor: pointer;
    float: right;
  }
  .sliderWapper {
    padding: 20px 10px;
    margin-bottom: 20px;
  }
  .sliderAnswer {
    text-align: center;
    font-size: 16px;
    padding-bottom: 10px;
  }
  .fundUpload {
    cursor: pointer;
    display: inline-block;
  }
  .title {
    margin-bottom: 10px;
  }
  .isRequired:after {
    content: '*';
    font-weight: 700;
    color: #f00;
    margin-left: 5px;
  }
  .remark {
    font-size: 12px;
  }
  .textareaAnswer {
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: scroll;
    img {
      display: block;
      margin: 0 auto;
      max-width: 100%;
      max-height: 100%;
    }
  }
  .optionItem {
    padding: 3px 0 3px 3px;
    font-size: 14px;
    line-height: 1.8em;
    input {
      width: 100%;
    }
    input[type="radio"], input[type="checkbox"] {
      cursor: pointer;
    }
    :global(.form-group):hover {
      background-color: #565555;
    }
  }
  .textContent {
    position: relative;
    min-height: 100px;
    border: 1px solid #ccc;
    padding: 3px 0 3px 3px;
    border-radius: 4px;
    :global(.text-wrapper-relative) {
      display: block !important;
      position: relative !important;
      margin-bottom: 10px !important;
      min-height: 100px;
      padding: 3px 0 3px 3px;
      font-size: 14px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    :global(.text-toolbar-absolute) {
      position: absolute !important;
      top: -45px !important;
      left: 0px !important;
      border-radius: 3px !important;
      background: #f3f3f3 !important;
      border: 1px solid #e3e3e3 !important;
    }
  }
  .answerRemark {
    margin-top: 10px;
  }
  .sortOptions {
    .tipMessage {
      font-size: 12px;
      margin-bottom: 10px;
    }
    ul {
      counter-reset: sorting-counter;
    }
    li {
      cursor: move;
      background-color:#2d2d2d;
      color: white;
      border:1px solid #615f5f;
      &:before {
        content: counter(sorting-counter);
        counter-increment: sorting-counter;
        display: inline-block;
        text-align: center;
        color: #928e8e99;
        font-size: 14px;
        margin-right: 10px;
        width: 25px;
        height: 25px;
        line-height: 25px;
        border-radius: 50%;
        border: 1px solid rgb(152 151 151);
        letter-spacing: 0;
        font-weight: 600;
        font-style: normal;
      }
    }
  }
  .matrixOptions {
    table tr{
      border:1px solid #898989;
      th{
        border: 1px solid #6c6c6c
      }
      td{
        border: 1px solid #6c6c6c
      }
    }
    .surveyTable {
      white-space: pre-wrap;
      & > tbody > tr > td {
        min-width: 100px;
      }
      & > tbody > tr > th {
        text-align: center;
      }
    }
    input[type="radio"], input[type="checkbox"] {
      margin-left: 50%;
      margin-top: -5px;
    }
    input[type="number"] {
      border-radius: 0;
      min-width: 50px;
    }
    input[type="text"] {
      border-radius: 0;
      min-width: 150px;
    }
    :global(.form-group) {
      margin-bottom: 0;
    }
    :global(.table-hover > tbody > tr){
      background-color: #000000;
    }
    :global(.table-hover > tbody > tr:hover){
      background-color: #2a2929;
    }
    :global(.form-control[disabled]){
      background-color:#222222
    }
    .optionWapper {
      position: relative;
      .fullEdit {
        position: absolute;
        right: 5px;
        top: 10px;
        font-size: 12px;
        cursor: pointer;
      }
      input {
        padding-right: 20px;
        background-color: black ;
        border-color: #707070;
        color:white
      }
    }
  }
  .uploadZone,
  .matrixOptions.autoScroll {
    :global(.table-responsive) {
      max-height: 400px;
      overflow-y: scroll;
    }
  }
  .matrixOptions.hasDate {
    .dateTimeTd {
      width: 280px;
    }
  }
  .addNewRow {
    text-align: center;
    margin-bottom: 10px;
    cursor: pointer;
  }
  .addNewYear {
    :global(.input-group) {
      width: 180px;
      margin: 0 auto;
    }
  }
}

.uploadWapper {
  padding: 45px;
  width: 100%;
  text-align: center;
  border: 2px #e0e0e0 dashed;
  border-radius: 5px;
  cursor: pointer;
  .filename {
    font-size: 18px;
  }
  .downloadButton {
    display: none;
    margin-left: 30px;
  }
  .uploadText {
    margin-top: 10px;
    span {
      margin-right: 5px;
    }
  }
  .uploadIcon {
    font-size: 30px;
  }
  .uploadNote {
    margin-top: 20px;
    color: #181f29;
    opacity: .5;
  }
  &:hover {
    .downloadButton {
      display: inline-block;
    }
  }
}
.uploadWapper.editting {
  cursor: move;
}

.question.editting {
  cursor: move;
  min-height: 210px;
  color: white;
  &:hover {
    background-color:#000000;
    border-top:1px solid #464545;
    border-bottom:1px solid #464545;
    .control {
      transform:translateX(0);
    }
  }
  .control {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 48px;
    background-color: #232323;
    border-left: 1px solid #adadad;
    transform: translateX(60px);
    transition: transform .2s;
    list-style-type: none;
    padding-left: 0;
    font-size: 20px;
    li {
      display: block;
      height: 42px;
      line-height: 42px;
      text-align: center;
      cursor: pointer;
    }
    li:hover {
      background-color:orange;
      color:#fff;
    }
    .animateHeart {
      display: none;
      color: #ff7e82;
      font-size: 30px;
      &:global(.animated) {
        display: block;
      }
    }
  }
  .optionItem {
    label:hover, input:hover, textarea:hover {
      cursor: move;
    }
    label {
      span:hover {
        cursor: move;
      }
    }
    &:hover {
      background-color: #696969;
      cursor: move;
    }
  }
}

.fundQuestion {
  margin-bottom: 30px;
  input[type="number"], input[type="text"] {
    min-width: 70px;
  }
  .hasDate {
    :global(.table-responsive) {
      overflow-x: inherit;
    }
  }
  .fundNameInput {
    margin-bottom: 15px;
  }
  .fundNameWapper {
    position: relative;
    input {
      width: 60%;
    }
    button {
      position: absolute;
      top: 0;
      right: 0;
    }
    :global(.removeBtn) {
      right: 80px;
    }
  }
  .fundPeriodWapper {
    margin-bottom: 15px;
    .setLatest {
      background-color: #fff;
      cursor: pointer;
      border-right: 0;
    }
    .setLatest.active {
      background-color: #336699;
      color: #fff;
    }
    input[type="text"] {
      border-radius: 0;
    }
  }
  .fundTypeWapper {
    margin-bottom: 15px;
  }
  .panel {
    border-top: 0;
    border-radius: 0;
    border-color: #ddd;
  }
  .callout {
    padding: 5px 10px;
    margin: 10px 0 20px;
    border-left: 2px solid;
    border-left-color: #1b809e;
  }
  .actionButtons {
    float: right;
    margin-top: -30px;
    & > span {
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .uploadZone {
    :global(.table-responsive) {
      max-height: 400px;
      overflow-y: scroll;
    }
  }
  .scaleTabTip {
    padding: 10px 5px;
  }
}

.tomato {
  color: tomato;
}

.downloadIcon {
  float: right;
  margin-top: 3px;
  margin-right: 15px;
  cursor: pointer;
}

.relateFund {
  height: 20px;
  line-height: 10px;
}

.resetButton {
  width: 50px;
  min-width: 50px !important;
}

@media (max-width: 768px) {
  .question.editting {
    min-height: 130px;
  }
}

@media print {
  .question {
    .textareaAnswer {
      max-height: none;
      overflow-y: none;
    }
  }
}