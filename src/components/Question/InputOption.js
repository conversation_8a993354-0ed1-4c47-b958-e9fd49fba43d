import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import { Modal, Button, Input } from 'antd'
import styles from './index.less'

const { TextArea } = Input

export default class InputOption extends Component {
  static propTypes = {
    option: PropTypes.object.isRequired,
    onChange: PropTypes.func.isRequired,
    isEdit: PropTypes.bool,
    readOnly: PropTypes.bool,
    type: PropTypes.string.isRequired,
    answer: PropTypes.string,
    hasError: PropTypes.bool
  }

  state = {
    showModal: false
  }

  closeFullEdit = () => {
    this.setState({ showModal: false })
  }

  startFullEdit = () => {
    this.setState({ showModal: true })
  }

  render() {
    const { readOnly, answer, isEdit, option, hasError } = this.props
    if (readOnly) {
      return <div>{answer}</div>
    }

    return (
      <div className={styles.optionWapper}>
        <div className={classnames('form-group', { 'has-error': hasError })}>
          <input
            className="form-control"
            type="text"
            disabled={isEdit}
            placeholder={
              option.isNumeric ? '请输入数字' : ''
            }
            value={answer || ''}
            onChange={this.props.onChange}
          />
        </div>
        {!isEdit && !option.isNumeric && (
          <span className={styles.fullEdit} onClick={this.startFullEdit}>
            <i className="fa fa-expand" />
          </span>
        )}
        <Modal
          width={900}
          visible={this.state.showModal}
          onCancel={this.closeFullEdit}
          className={styles.fullEditModal}
          maskClosable={false}
          keyboard={false}
          footer={[
            <Button onClick={this.closeFullEdit}>
              保存
            </Button>
          ]}
        >
          <div>
            <TextArea
              onChange={this.props.onChange}
              value={answer}
              rows={12}
            />
          </div>
        </Modal>
      </div>
    )
  }
}
