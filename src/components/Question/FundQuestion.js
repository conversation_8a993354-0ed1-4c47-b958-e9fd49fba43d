import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import moment from 'moment'
import TableQuestion from './TableQuestion'
import NavGrid from '@/components/NavGrid'
import transformUnitNav from '@/utils/transformUnitNav'
import { Menu, Tooltip, Select, Radio, Col, Row, AutoComplete, Input } from 'antd'
import styles from './index.less'
export default class FundQuestion extends Component {
  static propTypes = {
    fundNatures: PropTypes.array.isRequired,
    fundStrategies: PropTypes.array.isRequired,
    benchmarkList: PropTypes.array.isRequired,
    question: PropTypes.object.isRequired,
    answer: PropTypes.string,
    isEdit: PropTypes.bool,
    readOnly: PropTypes.bool,
    handleChange: PropTypes.func.isRequired,
    addFundQuestion: PropTypes.func,
    removeFundQuestion: PropTypes.func,
    index: PropTypes.number.isRequired,
    extractMatrix: PropTypes.func,
    extractResult: PropTypes.object,
    resetSurveyState: PropTypes.func,
    disableAction: PropTypes.bool,
    disableNav: PropTypes.bool,
    fundType: PropTypes.string,
    isNotSurvey: PropTypes.bool,
    total: PropTypes.number,
    currentUser: PropTypes.object,
    fundInvestmentTypes: PropTypes.array,
    fundManagementTypes: PropTypes.array,
  }

  state = this.buildSate()

  onInputChange = type => year => month => event => {
    const value = event.target.value
    this.updateInput(type, year, month, value)
  }

  onFundNameChange = name => event => {
    const value = event.target.value
    this.saveAnswers({ [name]: value })
    this.setState({
      [name]: value.slice(0, 100)
    })
  }

  onManagerChange = (question, answer) => {
    this.state.managerAnswers[question] = answer
    const managers = Object.keys(this.state.managerAnswers)
      .filter(key => {
        let data = this.state.managerAnswers[key]
        if (!data) {
          return false
        }
        data = this.parseJSON(data)
        return (
          data['姓名'] &&
          data['开始时间'] &&
          data['结束时间']
        )
      })
      .map(key => {
        const data = this.parseJSON(this.state.managerAnswers[key])
        return {
          id: data['姓名']._id || data.id,
          name: data['姓名'].name,
          startDate: data['开始时间'],
          endDate: data['结束时间']
        }
      })
    this.saveAnswers({ managers })
    this.setState({ managers })
  }

  onUpload = files => {
    const { question } = this.props
    this.props.extractMatrix(`${question._id}-${question.id}`, files[0])
  }

  onChangeBeInvested = e => {
    const isInvestable = e.target.value
    this.saveAnswers({ isInvestable })
    this.setState({ isInvestable })
  }

  onChangeMotherChild = e => {
    const isMotherChildStructure = e.target.value
    this.saveAnswers({ isMotherChildStructure })
    this.setState({ isMotherChildStructure })
  }

  getDateValue = date => {
    if (!date) {
      return undefined
    }
    if (date === 'now') {
      return new Date()
    }
    return new Date(date)
  }

  setRemind = val => {
    return val
  }

  updateInput(type, year, month, value) {
    const answers = this.state[type]
    if (Number.isNaN(Number(value)) && value !== '-') {
      return
    }
    answers[year] = answers[year] || {}
    answers[year][month] = value
    this.saveAnswers({ [type]: answers })
    this.setState({ [type]: answers })
  }

  saveAnswers = data => {
    const {
      company,
      advisor,
      fundName,
      answers,
      fundStrategy,
      benchmark,
      benchmarkId,
      scaleAnswers,
      fundNature,
      fundInvestmentType,
      fundManagementType,
      managers,
      isInvestable,
      isMotherChildStructure
    } = { ...this.state, ...data }
    this.props.handleChange(this.props.question.id, {
      company,
      advisor,
      fundName,
      fundStrategy,
      benchmark,
      benchmarkId,
      fundNature,
      fundInvestmentType,
      fundManagementType,
      managers,
      answer: JSON.stringify(answers),
      scaleAnswers: JSON.stringify(scaleAnswers),
      isInvestable,
      isMotherChildStructure
    })
  }

  buildNavList(data) {
    return this.parseJSON(data, [])
  }

  buildSate() {
    const { question, benchmarkList, currentUser } = this.props
    const ret = {
      answers: [],
      managerAnswers: {},
      scaleAnswers: [],
      defaultStartText: '请选择起始日期',
      defaultEndText: '请选择结束日期',
      currentTab: 'basicInfo',
      showUploadBtn: false,
      benchmarkMap: benchmarkList.reduce((out, item) => {
        out[item._qutkeId || item._id] = item.name
        return out
      }, {})
    }
    if (currentUser.user_scopes.includes('researcher')) {
      ret.advisor = currentUser.company
      ret.company = currentUser.company
    }
    if (question.fundNature === '私募契约型') {
      ret.isInvestable = question.isInvestable
      ret.isMotherChildStructure = question.isMotherChildStructure
    }
    if (!question.answer) {
      return ret
    }
    const answers = this.buildNavList(question.answer)
    ret.fundName = question.question
    ret.fundStrategy = question.fundStrategy
    ret.fundNature = question.fundNature
    ret.fundInvestmentType = question.fundInvestmentType
    ret.fundManagementType = question.fundManagementType
    ret.benchmark = question.benchmark
    ret.benchmarkId = question.benchmarkId
    ret.company = question.company
    ret.advisor = question.advisor
    ret.answers = answers
    if (question.scaleAnswers) {
      ret.scaleAnswers = this.buildNavList(question.scaleAnswers)
    }
    ret.managers = question.managers
    ret.managerAnswers = (question.managers || []).reduce(
      (out, manager, index) => {
        out[index] = JSON.stringify({
          id: manager.id,
          ['姓名']: {
            id: manager.id,
            name: manager.name
          },
          ['开始时间']: manager.startDate,
          ['结束时间']: manager.endDate
        })
        return out
      },
      {}
    )
    return ret
  }

  parseJSON(str, fallbackValue) {
    let ret
    try {
      ret = JSON.parse(str)
    } catch (err) {
      console.log('parse error', err)
      ret = fallbackValue || {}
    }
    return ret
  }

  selectBenchmark = benchmarkId => {
    const { benchmarkMap } = this.state
    const benchmark = benchmarkMap[benchmarkId]
    this.saveAnswers({ benchmark, benchmarkId })
    this.setState({ benchmark, benchmarkId })
  }

  selectFundStrategy = fundStrategy => {
    this.saveAnswers({ fundStrategy })
    this.setState({ fundStrategy })
  }

  selectFundNature = fundNature => {
    this.saveAnswers({ fundNature })
    this.setState({ fundNature })
  }

  selectFundInvestmentType = value => {
    const fundInvestmentType = value.trim()
    this.saveAnswers({ fundInvestmentType })
    this.setState({ fundInvestmentType })
  }

  selectFundManageType = value => {
    const fundManagementType = value.trim()
    this.saveAnswers({ fundManagementType })
    this.setState({ fundManagementType })
  }

  handleNavChange = nav => {
    const { currentTab } = this.state
    const newState = { [currentTab]: nav }
    if (currentTab === 'scaleAnswers' && nav.length > 1) {
      const newNav = transformUnitNav(nav)
      newState.answers = newNav
    }
    this.setState(newState)
    this.saveAnswers(newState)
  }

  switchTab = tab => {
    if (typeof tab.key !== 'string') {
      return
    }
    this.setState({
      currentTab: tab.key,
      showUploadBtn: tab.key !== 'basicInfo'
    })
  }

  selectDropdown = (event, eventKey) => {
    if (eventKey === 'add') {
      this.props.addFundQuestion()
    }
    if (eventKey === 'delete') {
      this.props.removeFundQuestion(this.props.question.id)
    }
  }

  addNewFund = () => {
    this.props.addFundQuestion()
  }

  deleteFound = () => {
    this.props.removeFundQuestion(this.props.question.id)
  }

  isValidDate = (min, max) => currentDate => {
    return currentDate >= min && currentDate <= max
  }

  renderNav(tab, exceptRows) {
    const { answers, scaleAnswers, currentTab } = this.state
    const {
      extractMatrix,
      extractResult,
      question,
      resetSurveyState
    } = this.props
    const rows = tab === 'answers' ? answers : scaleAnswers
    const editable = currentTab !== 'answers' || !scaleAnswers.length
    const netWorth = '净值'
    const netAssets = '净资产'
    const columns = [
      {
        key: 'date',
        name: '日期',
        editable: editable,
        sortable: true,
        formatter: NavGrid.DateFormatter,
        validator: {
          validate: value => moment(new Date(value)).isValid(),
          message: '请输入一个合法的日期'
        }
      },
      {
        key: 'value',
        name: tab === 'answers' ? netWorth : netAssets,
        editable: editable,
        sortable: true,
        formatter: NavGrid.VaueFormatter,
        validator: {
          validate: value => !Number.isNaN(Number(value)),
          message: '请输入一个数字'
        }
      }
    ]
    if (tab === 'scaleAnswers') {
      columns.push({
        key: 'injection',
        name: '注资/撤资',
        editable: true,
        sortable: true,
        validator: {
          validate: value => !Number.isNaN(Number(value)),
          message: '请输入一个数字'
        }
      })
    }
    const handleChange = this.handleNavChange
    const minHeight = 400
    const id = `${question._id}-${question.id}`
    return (
      <NavGrid
        {...{
          id,
          resetSurveyState,
          rows,
          columns,
          minHeight,
          extractResult,
          extractMatrix,
          handleChange,
          editable,
          exceptRows
        }}
      />
    )
  }

  render() {
    const {
      question,
      isEdit,
      readOnly,
      disableAction,
      disableNav,
      fundType,
      fundNatures,
      fundStrategies,
      benchmarkList,
      isNotSurvey,
      total,
      currentUser,
      fundInvestmentTypes,
      fundManagementTypes
    } = this.props
    const { answers } = this.state
    const values = answers.map(item => Number(item.value))
    const exceptRows = values.reduce((res, cur, count, arr) => {
      if (
        count >= 1 &&
        (cur / arr[count - 1] > 1.1 || arr[count - 1] / cur > 1.1)
      ) {
        const format = moment(answers[count].date).format('YYYY-MM-DD')
        res.push(format)
      }
      return res
    }, [])
    const {
      fundStrategy,
      benchmarkId,
      fundName,
      fundInvestmentType,
      fundManagementType,
      fundNature,
      currentTab,
      showUploadBtn,
      company,
      advisor,
      isInvestable,
      isMotherChildStructure
    } = this.state
    const tableQuestion = {
      type: 'table',
      options: [
        {
          id: 'name',
          _id: 'name',
          value: '姓名',
          isManager: true,
          isNotSurvey
        },
        {
          id: 'startDate',
          _id: 'startDate',
          value: '开始时间',
          isDate: true
        },
        {
          id: 'endDate',
          _id: 'endDate',
          value: '结束时间',
          isDate: true
        }
      ],
      rowSize: 1,
      allowNewRow: true
    }
    const isNavTabDisabled = disableNav
    const isNotResearcher = !~currentUser.user_scopes.indexOf('researcher')
    const isPAYL = window.__appConfig && window.__appConfig.isPAYL
    return (
      <div className={classnames(styles.matrixOptions, styles.fundQuestion)}>
        <Menu
          mode="horizontal"
          selectedKeys={[currentTab]}
          defaultOpenKeys={[currentTab]}
          onClick={this.switchTab}
        >
          <Menu.Item key="basicInfo">基本信息</Menu.Item>
          <Menu.Item key="answers" disabled={isNavTabDisabled}>
            {isNavTabDisabled ? (
              <Tooltip id="answers-tooptip" placement="top" title='请先选择基金起始日期和结束日期'>
                <span>上传净值</span>
              </Tooltip>
            ) : (
              '上传净值'
            )}
          </Menu.Item>
          {isNotResearcher && isNotSurvey && fundType !== 'index' && (
            <Menu.Item key="scaleAnswers" disabled={isNavTabDisabled}>
              {isNavTabDisabled ? (
                <Tooltip id="netAssets-tooptip" placement="top" title='请先选择基金起始日期和结束日期'>
                  <span>净资产</span>
                </Tooltip>
              ) : (
                '净资产'
              )}
            </Menu.Item>
          )}
        </Menu>
        {!isEdit && !readOnly && (
          <div className={styles.actionButtons}>
            {!disableAction && !showUploadBtn && (
              <span onClick={this.addNewFund}>
                <i className="fa fa-plus-circle" />新建
              </span>
            )}
            {total !== 1 && !disableAction && !showUploadBtn && (
              <span onClick={this.deleteFound}>
                <i className="fa fa-trash" />删除
              </span>
            )}
          </div>
        )}
        <div
          className={classnames('panel', styles.panel, {
            hidden: currentTab !== 'basicInfo'
          })}
        >
          <div className="panel-body">
            <div className={classnames('row')}>
              <div className="col-sm-7">
                <Input
                  type="text"
                  placeholder='请输入名称'
                  value={fundName}
                  className={styles.fundNameInput}
                  onChange={this.onFundNameChange('fundName')}
                  disabled={isEdit || readOnly}
                />
              </div>
              {fundType !== 'index' && (
                <div className="col-sm-5">
                  {isPAYL ? <AutoComplete dataSource={fundInvestmentTypes} placeholder='请输入或选择产品投资类型' style={{ width: '100%' }} disabled={isEdit || readOnly} value={fundInvestmentType} filterOption={(inputValue, option) => option.props.children.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1} onChange={this.selectFundInvestmentType} /> : <Select style={{ width: '100%' }} value={fundNature} onChange={this.selectFundNature} placeholder='--请选择基金类型--' disabled={isEdit || readOnly} showSearch>
                    {(fundNatures || []).map(nature => (
                      <Select.Option value={nature} key={nature}>
                        {nature}
                      </Select.Option>
                    ))}
                  </Select>}
                </div>
              )}
            </div>
            {fundType !== 'index' && (
              <div className={classnames('row', styles.fundTypeWapper)}>
                <div className="col-xs-6">
                  {isPAYL ? <AutoComplete dataSource={fundManagementTypes} placeholder='请输入或选择管理资金类型' style={{ width: '100%' }} disabled={isEdit || readOnly} value={fundManagementType} filterOption={(inputValue, option) => option.props.children.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1} onChange={this.selectFundManageType} /> : <Select style={{ width: '100%' }} value={fundStrategy} onChange={this.selectFundStrategy} placeholder='请选择投资策略' disabled={isEdit || readOnly} showSearch>
                    {fundStrategies.map(nature => (
                      <Select.Option value={nature} key={nature}>
                        {nature}
                      </Select.Option>
                    ))}
                  </Select>}
                </div>
                <div className="col-xs-6">
                  <Select style={{ width: '100%' }} value={benchmarkId} onChange={this.selectBenchmark} optionFilterProp="children" placeholder='请选择投资基准' disabled={isEdit || readOnly} showSearch filterOption={(input, option) => option.props.children.indexOf(input) >= 0}>
                    {benchmarkList.map(item => (
                      <Select.Option value={item._qutkeId || item._id} key={item._qutkeId || item._id}>
                        {item.name}
                      </Select.Option>
                    ))}
                  </Select>
                </div>
              </div>
            )}
            {fundType !== 'index' && (
              <div className="row">
                <div className="col-sm-6">
                  <Input
                    type="text"
                    placeholder='请输入管理人名称'
                    value={company}
                    className={styles.fundNameInput}
                    onChange={this.onFundNameChange('company')}
                    disabled={isEdit || readOnly}
                  />
                </div>
                <div className="col-sm-6">
                  <Input
                    type="text"
                    placeholder='请输入投顾公司名称'
                    value={advisor}
                    className={styles.fundNameInput}
                    onChange={this.onFundNameChange('advisor')}
                    disabled={isEdit || readOnly}
                  />
                </div>
              </div>
            )}
            {fundNature === '私募契约型' && (
              <div>
                <Row>
                  <Col span={7}>
                    <label className="control-label">该基金目前是否可投资}</label>
                  </Col>
                  <Col span={17}>
                    <Radio.Group onChange={this.onChangeBeInvested} value={isInvestable}>
                      <Radio value={'Y'}>是</Radio>
                      <Radio value={'N1'}>否，有其他复制策略产品</Radio>
                      <Radio value={'N2'}>否，没有复制策略产品</Radio>
                    </Radio.Group>
                  </Col>
                </Row>
                <Row>
                  <Col span={7}>
                    <label className="control-label">该基金是否有母子基金结构</label>
                  </Col>
                  <Col span={17}>
                    <Radio.Group onChange={this.onChangeMotherChild} value={isMotherChildStructure}>
                      <Radio value={'Y'}>是</Radio>
                      <Radio value={'N'}>否</Radio>
                    </Radio.Group>
                  </Col>
                </Row>
              </div>
            )}
            {fundType !== 'index' && (
              <div>
                <div className={styles.callout}>基金经理</div>
                <TableQuestion
                  question={tableQuestion}
                  createRangeError={() => { }}
                  onSubQuestionChange={this.onManagerChange}
                  answers={this.state.managerAnswers}
                  answer={question.managers}
                  inputError={{}}
                  initialRows={(question.managers || []).length}
                  handleInputError={() => { }}
                  {...{ isEdit, readOnly }}
                />
              </div>
            )}
          </div>
        </div>
        {currentTab === 'scaleAnswers' && (
          <div className={styles.scaleTabTip}>
            <i className="fa fa-info-circle" /> 净资产可以不填写。如果您填写了净资产，系统将自动计算并覆盖原有单位净值。{' '}
            <br />
            {exceptRows.length > 0 && (
              <span>
                <i className={classnames('fa fa-info-circle', styles.tomato)} />
                &nbsp;提示：字体变为红色的那一行可能需要输入注资或撤资
              </span>
            )}
          </div>
        )}
        {currentTab === 'answers' && this.renderNav('answers', [])}
        {currentTab === 'scaleAnswers' &&
          this.renderNav('scaleAnswers', exceptRows)}
      </div>
    )
  }
}
