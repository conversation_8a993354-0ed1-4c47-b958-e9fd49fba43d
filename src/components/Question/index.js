import React, { Component } from 'react'
import PropTypes from 'prop-types'
import classnames from 'classnames'
import { Table } from 'react-bootstrap'
import QsOption from './Option'
import SubQuestion from './SubQuestion'
import FundQuestion from './FundQuestion'
import TableQuestion from './TableQuestion'
import moment from 'moment'
import generateFileIcon from '@/utils/generateFileIcon'
import downloadAsBlob from '@/utils/downloadAsBlob'
import WysiwygEditor from '@/components/WysiwygEditor'
import AddQuestionCategoryModal from '@/components/AddQuestionCategoryModal'
import { Tooltip, DatePicker, Upload, Input, Select, Slider, message } from 'antd'
import styles from './index.less'
import { getToken } from '@/utils/utils'
const { Option } = Select
const { Dragger } = Upload
const { TextArea } = Input;
export default class Question extends Component {
  static propTypes = {
    survey: PropTypes.object,
    question: PropTypes.object.isRequired,
    index: PropTypes.number,
    isEdit: PropTypes.bool,
    isPreview: PropTypes.bool,
    readOnly: PropTypes.bool,
    remove: PropTypes.func,
    edit: PropTypes.func,
    copy: PropTypes.func,
    favorite: PropTypes.func,
    addCategory: PropTypes.func,
    handleChange: PropTypes.func,
    handleRemarkChange: PropTypes.func,
    handleOtherChange: PropTypes.func,
    handleSort: PropTypes.func,
    answer: PropTypes.any,
    answerRemark: PropTypes.string,
    otherAnswer: PropTypes.string,
    inputError: PropTypes.object,
    handleInputError: PropTypes.func,
    extractMatrix: PropTypes.func,
    extractResult: PropTypes.object,
    uploadAttachment: PropTypes.func,
    // attachmentResult: PropTypes.object,
    handleAttachment: PropTypes.func,
    resetSurveyState: PropTypes.func,
    currentUser: PropTypes.object,
    categories: PropTypes.array
  }

  constructor(props) {
    super(props)
    this.state = this.buildState()
  }

  onContentStateChange = answerContent => {
    this.props.handleChange(this.props.question._id, answerContent)
    this.setState({ answerContent })
  }


  onSelectChange = (values) => {
    const value = values
    this.props.handleChange(this.props.question._id, value)
    this.setState({ value })
  }

  onInputChange = event => {
    const {
      question: { max, min, title, isNumeric }
    } = this.props
    const { question } = this.props
    const value = event.target.value
    if (isNumeric) {
      if (Number.isNaN(Number(value)) && value !== '-') {
        return
      }
      if (value !== '') {
        const number = Number(value)
        const isEmptyValue = item => item === null || item === undefined
        if (
          (!isEmptyValue(min) && number < min) ||
          (!isEmptyValue(max) && number > max)
        ) {
          this.props.handleInputError(
            `${question._id}`,
            this.createRangeError({ value: title, max, min })
          )
        } else {
          this.props.handleInputError(`${question._id}`, false)
        }
      } else {
        this.props.handleInputError(`${question._id}`, false)
      }
    }
    this.props.handleChange(this.props.question._id, value)
    this.setState({ value })
  }

  onDateChange = date => {
    let answer
    if (typeof date === 'object') {
      answer = date.format('YYYY-MM-DD')
      this.props.handleChange(this.props.question._id, answer)
      this.setState({ value: answer })
    } else {
      const dateObject = moment(new Date(date))
      if (dateObject.isValid()) {
        answer = dateObject.format('YYYY-MM-DD')
        this.props.handleChange(this.props.question._id, answer)
        this.setState({ value: answer })
      }
    }
  }

  onButtonClick = (value, checked) => {
    const {
      question: { type, options }
    } = this.props
    let answer
    if (type === 'radio') {
      answer = value
    } else {
      this.state.checkbox[value] = checked
      answer = options
        .filter(option => this.state.checkbox[option.value])
        .map(option => option.value)
        .join('\n')
      if (answer.length === 0) {
        answer = ''
      }
    }
    this.props.handleChange && this.props.handleChange(this.props.question._id, answer)
    this.setState({ value: answer })
  }

  onSubQuestionChange = (question, answer) => {
    this.state.answers[question] = answer
    const answers = Object.keys(this.state.answers)
      .filter(key => this.state.answers[key])
      .map(key => ({ question: key, answer: this.state.answers[key] }))
    this.props.handleChange && this.props.handleChange(this.props.question._id, answers)
  }

  onResetTableAnswer = () => {
    this.state.answers = {}
    this.props.handleChange && this.props.handleChange(this.props.question._id, [])
  }

  onFundQuestionChange = (id, answer) => {
    answer.question = answer.fundName
    this.state.answers[id] = answer
    const answers = Object.keys(this.state.answers)
      .map(key => this.state.answers[key])
      .filter(item => {
        if (
          item.answer === '[]' &&
          !item.fundName &&
          !item.fundNature &&
          !item.question &&
          !item.fundStrategy &&
          !item.benchmark &&
          (!item.managers || !item.managers.length)
        ) {
          return false
        }
        return true
      })
    this.props.handleChange(this.props.question._id, answers)
  }

  onAnswerRemakChange = event => {
    this.props.handleRemarkChange(this.props.question._id, event.target.value)
  }

  onOtherAnswerChange = event => {
    const value = event.target.value
    const {
      question: { isNumeric },
    } = this.props
    if (isNumeric && Number.isNaN(Number(value)) && value !== '-') {
      return this.setState({
        otherError: {
          error: '请输入数字',
          touched: true
        }
      })
    }
    this.props.handleOtherChange(this.props.question._id, value)
  }

  onSort = event => {
    const { oldIndex, newIndex } = event
    const options = this.reindexElement(this.state.options, oldIndex, newIndex)
    this.props.handleSort(
      this.props.question._id,
      options.map((option, index) => ({
        question: option.value,
        answer: index + 1
      }))
    )
    this.setState({ options })
  }

  onUpload = file => {
    const hasTooLargeFile = file.size > 20 * 1024 * 1024
    if (hasTooLargeFile) {
      return toastr.error('超出上传文件20M的最大限制')
    }
    const { question } = this.props
    this.props.extractMatrix(question._id, file)
    return false
  }

  onSliderChange = (value) => {
    let values = [value]
    const {
      answer,
      question: { scale }
    } = this.props
    this.setState({ sliderValues: value })
    const { snapPointsMap, snapPoints } = this.state
    const answerIndex = values[0]
    let newAnswer
    if (scale > 1) {
      const times = Math.round((answerIndex - snapPoints[0]) / scale)
      newAnswer = snapPoints[0] + times * scale
    } else {
      newAnswer = Math.round(snapPointsMap[answerIndex] * 10000) / 10000
    }
    if (this.props.handleChange) {
      this.props.handleChange(
        this.props.question._id,
        Number.isNaN(newAnswer) ? answer : newAnswer + ''
      )
    }
  }

  onDownload = () => {
    const { question } = this.props
    let resultCsv = ''
    question.options.map(item => {
      resultCsv += item.value + ','
    })
    if (question.type === 'table') {
      resultCsv = resultCsv.substring(0, resultCsv.length - 1) + '\n'
      for (let index = 0; index < question.rowSize; index++) {
        let item = ''
        let len = question.options.length
        while (len > 1) {
          item += ','
          len--
        }
        resultCsv += item + '\n'
      }
    } else {
      resultCsv = ',' + resultCsv.substring(0, resultCsv.length - 1) + '\n'
      for (let index = 0; index < question.questions.length; index++) {
        let item = question.questions[index].value
        let len = question.options.length
        while (len > 1) {
          item += ','
          len--
        }
        resultCsv += item + '\n'
      }
    }
    this.seriesToCsv(resultCsv, question.title)
  }

  onDownloadUploadFiles = event => {
    const token = getToken()
    const { survey, question } = this.props
    event.stopPropagation()
    event.preventDefault()
    window.open(
      `/api/survey/${survey._id}/attachment?token=${token.slice(7)}&questionId=${question._id
      }${survey.hasTemporaryAnswer ? `&isTemp=${survey.hasTemporaryAnswer}` : ''
      }`
    )
  }

  onDelUploadFiles = event => {
    event.stopPropagation()
    event.preventDefault()
    const { handleAttachment, question } = this.props
    handleAttachment(question._id, undefined)
  }

  isValidExcelFile = file => {
    return /.*(\d{4})[/,-]?(\d{2})[/,-]?(\d{2}).*\.(xlsx|xls|xlsm)$/i.test(
      file.name
    )
  }

  seriesToCsv = (resultCsv, id) => {
    downloadAsBlob(resultCsv, `${id}.csv`)
  }

  buildState = () => {
    this.fundQuestionId = 0
    const {
      question: { type, options },
      answer
    } = this.props
    const ret = {
      options,
      checkbox: {},
      answers: {},
      otherError: {},
      initialRows: this.props.question.rowSize,
      mounted: true,
      answerContent: answer || '',
      locale:
        window && window.__i18n && window.__i18n.locale === 'zh-CN'
          ? 'zh'
          : 'en'
    }
    if (type === 'slider') {
      const {
        question: { min, max, scale }
      } = this.props
      const count = Math.round((max - min) / scale) + 1
      const snapPointsMap = {}
      const multiple = Math.ceil(1 / scale)
      for (let index = 0; index < count; index++) {
        const point = Math.round((min + index * scale) * multiple)
        snapPointsMap[point] = min + scale * index
      }
      ret.snapPointsMap = snapPointsMap
      if (answer) {
        ret.sliderValues = [Math.round(Number(answer) * multiple)]
      } else {
        ret.sliderValues = min
      }
    }
    if (!answer || !answer.length) {
      ret.fundQuestions = [
        {
          id: ++this.fundQuestionId,
          _id: this.props.question._id
        }
      ]
      return ret
    }
    if (type === 'checkbox') {
      ret.checkbox = answer.split('\n').reduce((out, item) => {
        out[item] = true
        return out
      }, {})
    }
    if (~type.indexOf('matrix') || type === 'table') {
      ret.answers = answer.reduce((out, item) => {
        out[item.question] = item.answer
        return out
      }, {})
    }
    if (type === 'table') {
      ret.initialRows = answer.length
    }
    if (type === 'fund') {
      answer.forEach(item => {
        item._id = this.props.question._id
        item.id = ++this.fundQuestionId
        item.fundName = item.question
        item.startDate = +item.startDate
        item.endDate = item.endDate === 'now' ? 'now' : +item.endDate
      })
      ret.fundQuestions = answer
      ret.answers = answer.reduce((out, item) => {
        out[item.id] = item
        return out
      }, {})
    }
    return ret
  }

  createRangeError = option => {
    const isEmptyValue = item => item === null || item === undefined
    if (isEmptyValue(option.min)) {
      return `${option.value}: 请填写一个不大于${option.max}的数`
    }
    if (isEmptyValue(option.max)) {
      return `${option.value}: 请填写一个不小于${option.max}的数`
    }
    return `${option.value}: 请填写一个${min}-${max}的数`
  }

  reindexElement(arr, oldIndex, newIndex) {
    if (arr.length <= 1) {
      return arr
    }
    arr.splice(newIndex, 0, arr.splice(oldIndex, 1)[0])
    return arr
  }

  sortableOptionsDecorator = componentBackingInstance => {
    const { question, isEdit, readOnly } = this.props
    if (isEdit || readOnly) {
      return
    }
    const Sortable = require('../../libs/Sortable.js')
    if (componentBackingInstance) {
      const options = {
        animation: 200,
        group: `${question._id}-options`,
        onSort: this.onSort
      }
      Sortable.create(componentBackingInstance, options)
    }
  }

  remove = () => {
    this.props.remove(this.props.question._id)
  }

  edit = () => {
    this.props.edit(this.props.question._id)
  }

  copy = () => {
    this.props.copy(this.props.question)
  }

  favorite = () => {
    const { question } = this.props
    const { jQuery } = window
    jQuery(`#favorite-${this.props.question._id}`)
      .addClass('zoomOutUp animated')
      .one(
        'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend',
        function listener() {
          jQuery(this).removeClass('zoomOutUp animated')
        }
      )
    this.props.favorite({ ...question, visibility: 'private' })
    message.success('收藏成功')
  }

  showCategoryMode = () => {
    this.setState({ showMode: true })
  }

  closeCategoryModal = () => {
    this.setState({ showMode: false })
  }

  addFundQuestion = () => {
    this.setState({
      fundQuestions: [
        ...this.state.fundQuestions,
        { id: ++this.fundQuestionId, _id: this.props.question._id }
      ]
    })
  }
  beforeUpload = file => {
    const hasTooLargeFile = file.size > 20 * 1024 * 1024
    if (hasTooLargeFile) {
      return message.error('超出上传文件20M的最大限制')
    }
    const { question, isEdit, readOnly } = this.props
    if (isEdit || readOnly) {
      return false
    }
    if (question.type === 'valuation' && !this.isValidExcelFile(file)) {
      message.error('您上传的估值表格式不正确')
      return false
    }
    this.props.uploadAttachment(question._id, file)
    return false
  }

  removeFundQuestion = id => {
    const fundQuestions = this.state.fundQuestions.filter(
      question => question.id !== id
    )
    const { answers } = this.state
    delete answers[id]
    this.props.handleChange(
      this.props.question._id,
      Object.keys(answers).map(key => answers[key])
    )
    this.setState({ fundQuestions, answers })
  }

  processExtractData = (extractResult) => {
    const { question } = this.props
    let result = extractResult && extractResult[question._id]
    const { options } = question
    if (!result) {
      return []
    }
    if (question.allowNewRow) {
      result = result.slice(1)
    } else {
      result = result.slice(1, question.questions.length + 1)
    }
    const data = []
    for (let index = 0; index < result.length; index++) {
      if (result[index]) {
        const arr = result[index]
        data.push(
          options.reduce((out, option, idx) => {
            out[option.value] = arr[idx + 1]
            return out
          }, {})
        )
      }
    }
    return data
  }

  renderInputs = () => {
    const { answerContent } = this.state
    const {
      question: { type, options, questions, isNumeric, isDate, min, max, scale },
      survey,
      currentUser,
      isEdit,
      readOnly,
      answer,
      otherAnswer,
      inputError,
      handleInputError,
      extractResult,
      extractMatrix,
      isPreview,
    } = this.props
    const { question } = this.props
    if (type === 'paragraph') {
      return false
    } else if (type === 'select') {
      const selected =
        this.state.value !== undefined ? this.state.value : answer
      return (
        <div className={styles.optionItem} disabled={isEdit || readOnly} value={selected}>
          <Select onChange={this.onSelectChange} style={{ width: '100%' }}>
            {options.map(option => (
              <Option key={option.id} value={option.value}>
                {option.label || option.value}
              </Option>
            ))}
          </Select>
          {selected === '__other__' && (
            <Input
              placeholder={
                isNumeric
                  ? '请输入数字'
                  : '请输入'
              }
              onChange={this.onOtherAnswerChange}
              value={otherAnswer}
              disabled={isEdit || readOnly}
            />
          )}
        </div>
      )
    } else if (type === 'textarea') {
      if (readOnly) {
        return (
          <div
            className={styles.textareaAnswer}
            dangerouslySetInnerHTML={{
              __html: answer
            }}
          />
        )
      }
      return (
        <div className={styles.textContent}>
          <div className="inner">
            {this.state.mounted && (
              <WysiwygEditor
                config={{
                  toolbarInline: true,
                  charCounterCount: false,
                  toolbarVisibleWithoutSelection: false,
                  toolbarSticky: false,
                  heightMin: 100,
                  toolbarButtons: [
                    'bold',
                    'italic',
                    'underline',
                    'strikeThrough',
                    'subscript',
                    'superscript',
                    '|',
                    'fontFamily',
                    'fontSize',
                    'color',
                    '|',
                    'paragraphFormat',
                    'align'
                  ]
                }}
                model={answerContent}
                onModelChange={this.onContentStateChange}
              />
            )}
          </div>
        </div>
      )
    } else if (type === 'input') {
      if (isDate) {
        let dateValue
        if (this.state.value !== undefined) {
          dateValue = moment(this.state.value)
        } else if (answer !== undefined) {
          dateValue = moment(answer)
        }
        if (!moment(dateValue).isValid()) {
          dateValue = undefined
        }
        return (
          <DatePicker
            value={dateValue}
            placeholder='请选择日期'
            onChange={this.onDateChange}
          />
        )
      }
      return (
        <div className={styles.optionItem}>
          <div
            className={classnames('form-group', {
              'has-error': inputError && inputError[question._id]
            })}
          >
            <input
              className="form-control"
              type={type}
              disabled={isEdit || readOnly}
              placeholder={
                isNumeric
                  ? '请输入数字'
                  : '请输入'
              }
              value={this.state.value !== undefined ? this.state.value : answer}
              onChange={this.onInputChange}
            />
          </div>
        </div>
      )
    } else if (type === 'checkbox' || type === 'radio') {
      return options.map(option => {
        let checked
        if (isEdit) {
          checked = false
        } else if (this.state.value === undefined) {
          checked =
            type === 'checkbox'
              ? !!~answer.split('\n').indexOf(option.value)
              : option.value === answer
        } else {
          checked =
            type === 'checkbox'
              ? !!~(this.state.value
                ? this.state.value.split('\n')
                : []
              ).indexOf(option.value)
              : this.state.value === option.value
        }
        return (
          <div key={option.id} className={styles.optionItem}>
            <QsOption
              {...{ isEdit, readOnly, option }}
              otherAnswer={otherAnswer}
              type={type}
              name={question._id}
              onChange={this.onButtonClick}
              onOtherAnswerChange={this.onOtherAnswerChange}
              checked={checked}
              otherError={this.state.otherError}
              placeholder={
                isNumeric
                  ? '请输入数字'
                  : '请输入'
              }
            />
          </div>
        )
      })
    } else if (type === 'sort') {
      return (
        <div className={styles.sortOptions}>
          <div className={styles.tipMessage}>Tips：拖放列表进行排序</div>
          <ul
            className={classnames('list-group')}
            ref={this.sortableOptionsDecorator}
          >
            {this.state.options.map((option, index) => (
              <li className="list-group-item" key={option.id + index}>
                {option.value}
                <span style={{ float: 'right' }}>
                  <i className="fa fa-list" />
                </span>
              </li>
            ))}
          </ul>
        </div>
      )
    } else if (type === 'table') {
      return (
        <TableQuestion
          question={question}
          createRangeError={this.createRangeError}
          onSubQuestionChange={this.onSubQuestionChange}
          answers={this.state.answers}
          addNewRow={this.addNewRow}
          initialRows={this.state.initialRows}
          onResetTableAnswer={this.onResetTableAnswer}
          extractResult={extractResult}
          {...{ isEdit, readOnly, inputError, handleInputError, answer }}
        />
      )
    } else if (type === 'fund') {
      // const { fundQuestions } = this.state
      // let fundNatures
      // let fundStrategies
      // let benchmarkList
      // if (!survey || currentUser._id === survey.author || !survey.fofSettings) {
      //   fundNatures = window
      //     ? window.__systemSettings &&
      //     window.__systemSettings.fundNatures.map(item => t(item))
      //     : []
      //   fundStrategies = window
      //     ? window.__systemSettings &&
      //     window.__systemSettings.fundStrategies.map(item => t(item))
      //     : []
      //   benchmarkList = window
      //     ? window.__systemSettings && window.__systemSettings.benchmarkList
      //     : []
      // } else {
      //   benchmarkList = survey.fofSettings.benchmarkList || []
      // }
      // return fundQuestions.map((item, index) => (
      //   <FundQuestion
      //     key={item.id}
      //     fundNatures={fundNatures}
      //     fundStrategies={fundStrategies}
      //     benchmarkList={benchmarkList}
      //     question={item}
      //     t={t}
      //     index={index}
      //     total={fundQuestions.length}
      //     isEdit={isEdit}
      //     readOnly={readOnly}
      //     extractMatrix={extractMatrix}
      //     extractResult={extractResult}
      //     addFundQuestion={this.addFundQuestion}
      //     removeFundQuestion={this.removeFundQuestion}
      //     resetSurveyState={this.props.resetSurveyState}
      //     handleChange={this.onFundQuestionChange}
      //   />
      // ))
    } else if (type === 'attachment' || type === 'valuation') {
      const header = { authorization: getToken() }
      return (
        <div>
          {type === 'valuation' && question.relatedFund && (
            <div className={styles.relateFund}>
              请上传基金{question.relatedFund.name}的估值表
            </div>
          )}
          <Dragger
            className={classnames(styles.uploadWapper, {
              [styles.editting]: isEdit
            })}
            multiple={false}
            disabled={isEdit || readOnly}
            name="attachment"
            showUploadList={false}
            action="/fake/url"
            headers={header}
            beforeUpload={this.beforeUpload}

          >
            {!answer && (
              <div className={styles.uploadIcon}>
                <i className="fa fa-upload" />
              </div>
            )}
            {answer && (
              <div className={styles.filename}>
                <i className={generateFileIcon(answer)} /> {answer.name || answer || ''}
                {!isPreview &&
                  survey &&
                  (survey.hasAnswer || survey.hasTemporaryAnswer) &&
                  answerContent && (
                    <Tooltip
                      placement="top"
                      title='点击下载上一次上传的文件'
                    >
                      <span
                        className={styles.downloadButton}
                        onClick={this.onDownloadUploadFiles}
                      >
                        <i className="fa fa-download" />
                      </span>
                    </Tooltip>
                  )}
                {!isPreview &&
                  survey &&
                  (survey.hasAnswer || survey.hasTemporaryAnswer) &&
                  answerContent && (
                    <Tooltip
                      placement="top"
                      title='点击删除上一次上传的文件'
                    >
                      <span
                        className={styles.downloadButton}
                        onClick={this.onDelUploadFiles}
                      >
                        <i className="fa fa-trash" />
                      </span>
                    </Tooltip>
                  )}
              </div>
            )}
            <div className={styles.uploadNote}>
              {type === 'attachment'
                ? '点击或拖放文件到这里进行上传，文件大小不能超过20M'
                : '点击或拖放估值表excel文件到这里进行上传，合法的日期格式为20160101 2016-01-01 2016/01/01，合法的文件格式为xls xlsx xlsm'
              }
            </div>
          </Dragger>
        </div>
      )
    } else if (type === 'slider') {
      const { sliderValues } = this.state
      return (
        <div className={styles.sliderWapper}>
          {answer && (
            <div className={styles.sliderAnswer}>
              <span className="label label-success">{`当前值: ${answer}`}</span>
            </div>
          )}
          <Slider
            min={Number(min)}
            max={Number(max)}
            disabled={isEdit || readOnly}
            value={Number(sliderValues)}
            step={Number(scale)}
            onChange={this.onSliderChange}
          />
        </div>
      )
    }
    const hasDate = options.some(option => option.isDate)
    const extractData = this.processExtractData(extractResult)
    return (
      <div
        className={classnames(styles.matrixOptions, {
          [styles.hasDate]: hasDate
        })}
      >
        <Table responsive striped bordered hover className={styles.surveyTable}>
          <thead>
            <tr>
              <th />
              {options.map(option => (
                <th key={option.id}>{option.value}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {questions.map((item, index) => {
              const isNumericQuestion = item.isNumeric
              const subQuestion = {
                id: item.id,
                value: item.value,
                options: options.map(option => ({
                  ...option,
                  isNumeric: item.isNumeric || option.isNumeric,
                  isDate: item.isDate || option.isDate,
                  max: isNumericQuestion ? item.max : option.max,
                  min: isNumericQuestion ? item.min : option.min
                })),
                type: type,
                parentId: question._id
              }
              const getAnswer = () => {
                let ret = ''
                if (!answer) {
                  return ret
                }
                answer.some(ans => {
                  if (ans.question === item.value) {
                    ret = ans.answer
                    return true
                  }
                  return false
                })
                return ret
              }
              return (
                <SubQuestion
                  key={item.id}
                  question={subQuestion}
                  isEdit={isEdit}
                  readOnly={readOnly}
                  answer={getAnswer()}
                  inputError={inputError || {}}
                  handleInputError={handleInputError}
                  createRangeError={this.createRangeError}
                  handleChange={this.onSubQuestionChange}
                  extractData={extractData[index]}
                />
              )
            })}
          </tbody>
        </Table>
      </div>
    )
  }

  renderTitle = (number) => {
    const { question } = this.props
    if (question.type === 'paragraph') {
      return (
        <div
          dangerouslySetInnerHTML={{
            __html: question.title
          }}
        />
      )
    }
    return (
      <p
        className={classnames({ [styles.isRequired]: question.isRequired })}
      >{`${number}、${question.title}`}</p>
    )
  }

  render() {
    const {
      index,
      question,
      isEdit,
      answerRemark,
      answer,
      readOnly,
      currentUser,
      question: { templateFile },
      extractResult
    } = this.props
    const { showMode } = this.state
    const token = getToken()
    const header = { authorization: token }
    return (
      <div
        className={classnames(styles.question, { [styles.editting]: isEdit })}
        id={`question-${question._id}`}
      >
        <div className="inner">
          {!isEdit &&
            !readOnly &&
            !!~['table', 'matrix'].indexOf(question.type) && (
              <Upload
                name="attachment"
                showUploadList={false}
                action="/fake/url"
                headers={header}
                multiple={false}
                accept=".csv,text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                className={styles.upload}
                beforeUpload={this.onUpload}
              >
                <Tooltip id={`${question._id}-upload`} placement="top" title='支持导入 Excel, CSV 文件，从文件第二行开始导入'>
                  <span>
                    <i className="fa fa-upload" />
                  </span>
                </Tooltip>
              </Upload>
            )}
          {!isEdit &&
            !readOnly &&
            !!~['table', 'matrix'].indexOf(question.type) && (
              <Tooltip id={`${question._id}-download`} placement="top" title='导出问题模版'>
                <a
                  className={classnames('fa fa-download', styles.downloadIcon)}
                  onClick={() => { this.onDownload() }}
                />
              </Tooltip>
            )}
          <div className={styles.title}>{this.renderTitle(index)}</div>
          <div className={styles.remark}>
            {!!question.remark && (
              <div
                dangerouslySetInnerHTML={{
                  __html: question.remark
                }}
              />
            )}
            {question.type === 'attachment' && templateFile && (
              <p>
                <a
                  target="_blank"
                  href={`/api/survey/attachmentTemplate?token=${token.slice(7)}&key=${templateFile.key
                    }&name=${templateFile.name}`}
                >
                  <i className="fa fa-download" />{' '}
                  点击下载附件模版
                </a>
              </p>
            )}
          </div>
          <div className={styles.inputs}>{this.renderInputs()}</div>
          {!isEdit && (this.state.value || answer) && question.answerRemark && (
            <TextArea rows={4}
              placeholder='可不填写'
              className={styles.answerRemark}
              onChange={this.onAnswerRemakChange}
              disabled={isEdit || readOnly}
              value={answerRemark} />
          )}
        </div>
        {isEdit && (
          <ul className={styles.control}>
            <li onClick={this.edit}>
              <Tooltip id={`${question._id}-control-btn-edit`} placement="top" title='编辑'>
                <i className="fa fa-edit" />
              </Tooltip>
            </li>
            <li onClick={this.copy}>
              <Tooltip id={`${question._id}-control-btn-copy`} placement="top" title='复制'>
                <i className="fa fa-files-o" />
              </Tooltip>
            </li>
            <li onClick={this.remove}>
              <Tooltip id={`${question._id}-control-btn-delete`} placement="top" title='删除'>
                <i className="fa fa-trash" />
              </Tooltip>
            </li>
            <li onClick={this.favorite}>
              <Tooltip id={`${question._id}-control-btn-favorite`} placement="top" title='收藏'>
                <i className="fa fa-heart" />
              </Tooltip>
            </li>
            {!!~currentUser.admin_scopes.indexOf('fof_super_admin') && (
              <li>
                <AddQuestionCategoryModal
                  show={showMode}
                  save={this.props.addCategory}
                  close={this.closeCategoryModal}
                  question={this.props.question}
                  categories={this.props.categories}
                >
                  <Tooltip id={`${question._id}-control-btn-category`} placement="top" title='添加到题库'>
                    <i className="fa fa-tag" onClick={this.showCategoryMode} />
                  </Tooltip>
                </AddQuestionCategoryModal>
              </li>
            )}
            <li className={styles.animateHeart} id={`favorite-${question._id}`}>
              <i className="fa fa-heart" />
            </li>
          </ul>
        )}
      </div>
    )
  }
}
