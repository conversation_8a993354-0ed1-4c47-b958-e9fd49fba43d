import PropTypes from 'prop-types'
import React, { Component } from 'react'
import Option from './Option'
import InputOption from './InputOption'
import moment from 'moment'
import classnames from 'classnames'
import SelectManagerModal from '@/components/SelectManagerModal'
import { Tooltip, Button, Icon, DatePicker } from 'antd'
import styles from './index.less'
import { UndoOutlined } from '@ant-design/icons';

export default class SubQuestion extends Component {
  static propTypes = {
    question: PropTypes.object.isRequired,
    answer: PropTypes.string,
    isEdit: PropTypes.bool,
    readOnly: PropTypes.bool,
    handleChange: PropTypes.func.isRequired,
    inputError: PropTypes.object,
    handleInputError: PropTypes.func,
    createRangeError: PropTypes.func.isRequired,
    extractData: PropTypes.object
  }

  state = this.buildSate()

  componentDidMount() {
    this.fillExtractData(this.props)
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.extractData) {
      this.fillExtractData(nextProps)
    }
  }

  shouldComponentUpdate(nextProps) {
    return nextProps.answer !== this.props.answer
  }

  onButtonClick = (value, checked) => {
    const {
      question: { type, options }
    } = this.props
    let answer
    if (type === 'matrix_radio') {
      answer = value
    } else {
      this.state.checkbox[value] = checked
      answer = options
        .filter(option => this.state.checkbox[option.value])
        .map(option => option.value)
        .join('\n')
    }
    this.props.handleChange(this.props.question.value, answer)
    this.setState({ value: answer })
  }

  onInputChange = option => (data) => {
    let value = ''
    if (option.isManager) {
      value = data[0]
    } else {
      value = data.target.value
    }
    this.updateInput(option, value)
  }

  onDateChange = option => date => {
    if (typeof date === 'object') {
      this.updateAnswer(option, date.format('YYYY-MM-DD'))
    } else {
      const dateObject = moment(new Date(date))
      if (dateObject.isValid()) {
        this.updateAnswer(option, dateObject.format('YYYY-MM-DD'))
      }
    }
  }

  fillExtractData(props) {
    const {
      question: { options },
      extractData
    } = props
    if (extractData) {
      (options||[]).forEach(option => {
        let answer = extractData[option.value]
        if (!option.isDate) {
          if (option.isNumeric) {
            answer = answer.replace(/,/g, '')
          }
          this.updateInput(option, answer)
          return
        }
        let date =
          typeof answer === 'number'
            ? new Date(1900, 0, answer - 1)
            : new Date(answer)
        date = moment(date)
        if (date.isValid()) {
          this.updateAnswer(option, date.format('YYYY-MM-DD'))
        }
      })
    }
  }

  updateInput(option, value) {
    const { question } = this.props
    if (
      (question.type === 'matrix' || question.type === 'table') &&
      option.isNumeric
    ) {
      if (Number.isNaN(Number(value)) && value !== '-') {
        return
      }
      if (value !== '') {
        const number = Number(value)
        const { min, max } = option
        const isEmptyValue = item => item === null || item === undefined
        if (
          (!isEmptyValue(min) && number < min) ||
          (!isEmptyValue(max) && number > max)
        ) {
          this.props.handleInputError(
            `${question.parentId}-${question.id}-${option.id}`,
            this.props.createRangeError(option)
          )
        } else {
          this.props.handleInputError(
            `${question.parentId}-${question.id}-${option.id}`,
            false
          )
        }
      } else {
        this.props.handleInputError(
          `${question.parentId}-${question.id}-${option.id}`,
          false
        )
      }
    }
    this.updateAnswer(option, value)
  }

  updateAnswer(option, value) {
    const { question } = this.props
    const { input } = this.state
    input[option.value] = value
    this.props.handleChange(question.value || question.id, JSON.stringify(input))
    this.setState({ input })
  }

  buildSate() {
    const {
      answer,
      question: { type }
    } = this.props
    const ret = { checkbox: {}, input: {} }
    if (!answer) {
      return ret
    }
    if (type === 'matrix_checkbox') {
      ret.checkbox = answer.split('\n').reduce((out, item) => {
        out[item] = true
        return out
      }, {})
    }
    if (type === 'matrix' || type === 'table') {
      ret.input = this.parseJSON(answer)
    }
    return ret
  }

  parseJSON(str) {
    let ret
    try {
      ret = JSON.parse(str)
    } catch (err) {
      console.log('parse error', err, str)
      ret = {}
    }
    return ret
  }

  renderInput(option) {
    const {
      question: { type, id, parentId },
      isEdit,
      readOnly,
      answer,
      inputError
    } = this.props
    const answerMap =
      answer && (type === 'matrix' || type === 'table')
        ? this.parseJSON(answer)
        : {}
    if ((type === 'matrix' || type === 'table') && !option.isDate) {
      return (
        <InputOption
          type={type}
          isEdit={isEdit}
          readOnly={readOnly}
          answer={
            this.state.input[option.value] !== undefined
              ? this.state.input[option.value]
              : answerMap[option.value]
          }
          onChange={this.onInputChange(option)}
          option={option}
          hasError={!!inputError[`${parentId}-${id}-${option.id}`]}
        />
      )
    }
    if (option.isDate) {
      const textAnswer =
        this.state.input[option.value] !== undefined
          ? this.state.input[option.value]
          : answerMap[option.value]
      let dateValue
      if (textAnswer) {
        dateValue = textAnswer === 'now' ? new Date() : new Date(textAnswer)
      }
      if (readOnly) {
        return (
          <div>
            {textAnswer === 'now' ? '至今' : textAnswer}
          </div>
        )
      }
      return (
        <DatePicker
          placeholder='请选择日期'
          value={dateValue ? moment(dateValue) : undefined}
          onChange={this.onDateChange(option)}
        />
      )
    }
    let checked
    if (isEdit) {
      checked = false
    } else if (this.state.value === undefined) {
      checked =
        type === 'matrix_checkbox'
          ? !!~answer.split('\n').indexOf(option.value)
          : option.value === answer
    } else {
      checked =
        type === 'matrix_checkbox'
          ? !!~(this.state.value ? this.state.value.split('\n') : []).indexOf(
            option.value
          )
          : this.state.value === option.value
    }
    return (
      <Option
        {...{ isEdit, option, readOnly }}
        type={type === 'matrix_radio' ? 'radio' : 'checkbox'}
        name={id}
        onChange={this.onButtonClick}
        checked={checked}
        label=""
      />
    )
  }

  renderSelect(option) {
    let {
      answer
    } = this.props
    let manager = answer ? JSON.parse(answer) : ''
    return (
      <div className={styles.optionWapper}>
        <div className={classnames('form-group', {})}>
          <SelectManagerModal onChange={this.onInputChange(option)} >
            <button
              className="form-control"
            >
              {
                (manager[option.value] && manager[option.value].name) || ''
              }
            </button>
          </SelectManagerModal>
        </div>
      </div>
    )
  }

  handleResetAllInputs = () => {
    const {
      question: { options }
    } = this.props
    (options||[]).forEach(option => {
      this.updateInput(option, '')
    })
  }

  render() {
    const {
      question: { options, value, id, type },
    } = this.props
    return (
      <tr>
        {type !== 'table' && <td>{value}</td>}
        {options.map(option => (
          <td
            key={`${id}-${option.id}`}
            className={classnames({ [styles.dateTimeTd]: option.isDate })}
          >
            {option.isManager ? this.renderSelect(option) : this.renderInput(option)}
          </td>
        ))}
        {['fund', 'table'].includes(type) && <td className={styles.resetButton}>
          <Tooltip placement="top" title='重置该行答案'>
            <Button onClick={this.handleResetAllInputs}><UndoOutlined /></Button>
          </Tooltip>
        </td>}
      </tr>
    )
  }
}
