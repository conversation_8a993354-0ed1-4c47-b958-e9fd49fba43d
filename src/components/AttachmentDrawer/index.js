import React from 'react'
import PropTypes from 'prop-types'
import { Drawer, Tooltip, Progress } from 'antd'
import styles from './AttachmentDrawer.less'
export default class AttachmentDrawer extends React.Component {
  static propTypes = {
    list: PropTypes.array,
    sidebarOpen: PropTypes.bool,
    close: PropTypes.func
  }

  render() {
    const { list, sidebarOpen, close } = this.props
    return (
      <Drawer
        className={styles.drawer}
        title={`通知列表(${list.filter(user => user.percent === 100 || user.error).length} / ${list.length})`}
        placement="right"
        visible={sidebarOpen}
        onClose={close}
      >
        {list.map(item => {
          return (
            <li className="list-group-item" key={`task-${item.file ? item.file.name : item.nickname}-${item.index}`}>
              <div className={styles.filename}>
                <Tooltip id={`tooltip-name-${item.index}`} placement="top" title={item.file ? item.file.name : item.nickname}>
                  <span>{item.file ? item.file.name : item.nickname}</span>
                </Tooltip>
              </div>
              {item.error && (
                <div className={styles.error}>
                  <Tooltip id={`tooltip-${item.index}`} placement="top" title={item.error}>
                    <span className="fa fa-warning" />
                  </Tooltip>
                </div>
              )}
              {!item.error && (
                <div className={styles.progressCircle}>
                  <Progress status={item.percent === 100 ? 'success' : 'active'} percent={item.percent} size="small" />
                </div>
              )}
            </li>
          )
        })}
      </Drawer>
    )
  }
}
