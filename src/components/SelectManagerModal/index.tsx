import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import {
  Input,
  Card,
  Modal,
  Button,
  Table,
  Tabs,
} from 'antd'
import { queryManagers } from '@/services/manager'
import { styleTypeMap, compositTypeMap, companyTypeMap } from '@/utils/kymDefMapping'
import useTableRowSelection from '@/hooks/useTableRowSelection'
import styles from './style.less'

const Search = Input.Search
const TabPane = Tabs.TabPane

export default ({
  children,
  onChange,
}: {
  children: any,
  onChange: any,
}) => {
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [input, setInput] = useState('')
  const [refreshCount, setRefreshCount] = useState(0)
  const { selectedRows, rowSelection, setSelectedRows } = useTableRowSelection([])
  const [activeTab, setActiveTab] = useState('mutual')
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    p.isAll = 'Y'
    p.type = activeTab
    return queryManagers(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [input, refreshCount, activeTab],
  })
  const handleModalOpen = () => {
    setVisibleTrue()
    setRefreshCount(refreshCount + 1)
  }
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '基金公司',
      dataIndex: 'company_abbr_name',
      format: 'text',
      sorter: true,
    },
    {
      title: '公司类型',
      dataIndex: 'company_type',
      sorter: true,
      render: (text: string) => companyTypeMap[text] || text,
    },
    {
      title: '风格分类',
      dataIndex: 'style_type',
      sorter: true,
      render: (text: string) => styleTypeMap[text] || text,
    },
  ]

  return (
    <div style={{ display: 'inline-block', width: '100%' }}>
      <div onClick={handleModalOpen}>{children}</div>
      <Modal
        className={styles.modal}
        title={
          <>
            <span>请选择基金经理({selectedRows.length})</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={800}
        footer={[
          <Button type="primary" onClick={() => {
            setVisibleFalse()
            onChange(selectedRows)
            setSelectedRows([])
          }}>
            保存
          </Button>,
        ]}
      >
        <Tabs
          animated={false}
          activeKey={activeTab}
          onTabClick={setActiveTab}
          tabBarExtraContent={
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          }
        >
          <TabPane tab="公募基金经理" key="mutual" />
          <TabPane tab="专户基金经理" key="activeManager" />
        </Tabs>
        <Table
          {...tableProps}
          columns={columns}
          rowSelection={rowSelection}
          size="small"
          rowKey="_id"
          bordered
        />
      </Modal>
    </div>
  )
}
