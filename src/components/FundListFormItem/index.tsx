import React from 'react'
import _ from 'lodash'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Tooltip,
  List,
  Popconfirm,
} from 'antd'
import SelectFundModal from '@/components/SelectFundModal'
import t from '@/utils/t'
import classNames from 'classnames'
import styles from './style.less'

const FundListFormItem = ({
  onChange,
  value,
  defaultTabs,
  title,
  addTitle,
  hideEmpty,
} : {
  onChange?: any,
  value?: any,
  defaultTabs?: any,
  title?: string,
  addTitle?: string,
  hideEmpty?: boolean,
}) => {
  const data = value || []
  const handleSelectFunds = funds => {
    const newData = funds.map(item => {
      return { id: item._id, name: item.name, fundType: item._syncType }
    }).concat(data)
    onChange(_.uniqBy(newData, 'id'))
  }
  const handleRemoveFund = fund => () => {
    const newData = data.filter(item => item.id !== fund.id)
    onChange(newData)
  }
  return (
    <List
      className={classNames(styles.reportItem, { [styles.hideEmpty] : hideEmpty})}
      size="small"
      header={<div>{ title || '组合'} ({data.length})</div>}
      footer={
        <SelectFundModal style={{ width: '100%' }} onChange={handleSelectFunds} defaultTabs={defaultTabs}>
          <Button style={{ width: '100%', marginTop: 10 }} type="dashed" icon={<PlusOutlined />}>
            { addTitle || '添加组合' }
          </Button>
        </SelectFundModal>
      }
      bordered
      dataSource={data || []}
      renderItem={(item) => {
        const actions = [
          <Popconfirm
            title={`${t('portfolio.delTip')}${item.name}${t('portfolio.questionEnd')}？`}
            onConfirm={handleRemoveFund(item)}
            onCancel={() => {}}
            okText={t('portfolio.confirm')}
            cancelText={t('portfolio.cancel')}
          >
            <Tooltip title="删除">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>,
        ]
        return (
          <List.Item
            actions={actions}
          >
            <span>{item.name}</span>
          </List.Item>
        )
      }}
    />
  )
}

export default FundListFormItem
