import PropTypes from 'prop-types'
import React, { Component } from 'react'
import 'froala-editor/js/froala_editor.pkgd.min.js';
import 'froala-editor/css/froala_style.min.css';
import 'froala-editor/css/froala_editor.pkgd.min.css';
import 'font-awesome/css/font-awesome.css';
import FroalaEditor from 'react-froala-wysiwyg';
import { getToken } from '@/utils/utils'
export default class WysiwygEditor extends Component {
  static propTypes = {
    config: PropTypes.object,
    model: PropTypes.string,
    onModelChange: PropTypes.func.isRequired
  }

  shouldComponentUpdate(nextProps) {
    return false
  }

  render() {
    const {
      model, onModelChange
    } = this.props
    const token = getToken()
    const defaultConfig = {
      heightMin: 100,
      language: 'zh_cn',
      toolbarButtons: [
        'fullscreen',
        'bold',
        'italic',
        'underline',
        'strikeThrough',
        'subscript',
        'superscript',
        '|',
        'fontFamily',
        'fontSize',
        'color',
        '|',
        'paragraphFormat',
        'align',
        'formatOL',
        'formatUL',
        'lineHeight',
        'outdent',
        'indent',
        'quote',
        'insertLink',
        'insertImage',
        'insertTable',
        '|',
        'emoticons',
        'insertHR',
        'clearFormatting',
        '|',
        'undo',
        'redo'
      ],
      imageInsertButtons: ['imageBack', '|', 'imageUpload'],
      imageUploadURL: '/api/survey/upload/img',
      imageUploadParam: 'attachment',
      requestHeaders: {
        authorization: `${token}`
      },
      tableStyles: {
        table: 'Table'
      }
    }
    const customConfig = this.props.config

    return (
      <FroalaEditor
        config={{
          ...defaultConfig,
          ...customConfig
        }}
        model={model}
        onModelChange={onModelChange}
      />
    )
  }
}
