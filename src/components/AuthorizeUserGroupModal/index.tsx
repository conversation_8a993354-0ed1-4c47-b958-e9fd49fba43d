import React, { useState } from 'react'
import { Modal, Form, notification } from 'antd'
import SearchSelect from '@/components/SearchSelect'
import { useRequest } from '@umijs/hooks'

const AuthorizeUserGroupModal = ({ itemId, userGroups, children, initialValues, authorizeUserGroups }) => {
  const { run: doAuthorize } = useRequest((id, data) => {
    return authorizeUserGroups(id, data)
  }, {
    manual: true,
    onSuccess: () => {
      notification.success({
        message: '保存成功',
      })
      setVisible(false)
    },
  })
  const [visible, setVisible] = useState(false)
  const onCreate = (values: any) => {
    const { userGroupIds } = values
    const groups = userGroups
      .filter(item => userGroupIds.includes(item._id))
      .map(item => {
        return {
          id: item._id,
          name: item.name
        }
      })
    doAuthorize(itemId, { userGroups: groups, userGroupIds })
  }

  const showModal = () => {
    setVisible(true)
  }
  const [form] = Form.useForm()
  return (
    <>
      <span onClick={showModal}>{children}</span>
      <Modal
        visible={visible}
        title="授权用户组"
        okText="确定"
        cancelText="取消"
        onCancel={() => setVisible(false)}
        onOk={() => {
          form
            .validateFields()
            .then(values => {
              onCreate(values)
            })
            .catch(info => {
              console.log('Validate Failed:', info)
            })
        }}
      >
        <Form
          form={form}
          layout="horizontal"
          name="form_in_modal"
          initialValues={initialValues}
        >
          <Form.Item
            name="userGroupIds"
            label="可见用户组"
            rules={[
              {
                required: true,
                message: '请选择用户组',
              },
            ]}
          >
            <SearchSelect
              placeholder="请选择用户组"
              mode="multiple"
              options={userGroups.map(item => {
                return {
                  title: item.name,
                  dataIndex: item._id,
                }
              })}
              width="100%"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default AuthorizeUserGroupModal
