import React from 'react'
import {
  Select,
} from 'antd'

const { Option } = Select

export default ({
  date,
  dates,
  onChange,
  placeholder,
  width,
}: {
  date: string,
  dates: any,
  onChange: any,
  placeholder?: string,
  width?: any,
}) => {
  return (
    <Select
      showSearch
      size="small"
      placeholder={placeholder || '选择报告期'}
      style={{
        // marginTop: '5px',
        width: width || '120px',
      }}
      value={date}
      filterOption={(input, option) =>
        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
      }
      onChange={onChange}
    >
      {dates.map(item => {
        return (
          <Option value={item}>
            {item}
          </Option>
        )
      })}
    </Select>
  )
}
