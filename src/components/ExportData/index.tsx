import React from 'react'
import {
  Button, Tooltip,
} from 'antd'
import moment from 'moment'
import { connect } from 'dva'
import { DownloadOutlined } from '@ant-design/icons'
import { exportTableAsExcel } from '@/utils/exportAsExcel'

const ExportData = ({
  dataSource,
  columns,
  filename,
  children,
  getData,
  title,
  buttonShape,
  currentUser,
  onClick,
  style,
  isIcon,
  loading,
  addDateSuffix,
}: {
  dataSource?: any[],
  columns: any[],
  filename: string,
  children?: any,
  getData?: any,
  title?: string,
  buttonShape?: string,
  currentUser: any,
  onClick?: any,
  style?: any,
  isIcon?: boolean,
  loading?: boolean,
  addDateSuffix?: boolean,
}) => {
  if (!currentUser || !currentUser.canDownload) {
    return null
  }
  const handleClick = () => {
    if (onClick) {
      return onClick()
    }
    const data = getData ? getData() : dataSource
    const downloadName = addDateSuffix ? `${filename}-${moment().format('YYYYMMDD')}` : filename
    exportTableAsExcel(data, columns, downloadName)
  }
  if (isIcon) {
    return (
      <Tooltip title={title || '点击导出数据'}>
        <DownloadOutlined style={style} onClick={handleClick}/>
      </Tooltip>
    )
  }
  if (!children) {
    return (
      <Tooltip title={title || '点击导出数据'}>
        <Button loading={loading} size="small" style={style} shape={buttonShape} onClick={handleClick} icon={<DownloadOutlined />} />
      </Tooltip>
    )
  }
  return (
    <span onClick={handleClick}>
      {children}
    </span>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(ExportData)
