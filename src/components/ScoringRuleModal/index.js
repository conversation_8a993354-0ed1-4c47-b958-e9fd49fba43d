import PropTypes from 'prop-types'
import React, { Component } from 'react'
import ISwitch from '@/components/ISwitch'
import SurveyScoreOverlayTrigger from '@/components/SurveyScoreOverlayTrigger'
import { Table } from 'react-bootstrap'
import { PlusOutlined } from '@ant-design/icons'
import classnames from 'classnames'
import { Modal, Button, message, Input } from 'antd'
import styles from './style.less'
export default class ScoringRuleModal extends Component {
  static propTypes = {
    survey: PropTypes.object,
    show: PropTypes.bool,
    close: PropTypes.func,
    update: PropTypes.func
  }

  constructor(props) {
    super(props)
    const {
      survey: { isWeightedScoring, questions, customScoringRules }
    } = props
    this.state = {
      isWeightedScoring: isWeightedScoring || false,
      questions: questions ? this.buildQuestions(questions) : [],
      customScoringRules: customScoringRules
        ? this.buildCustomRules(customScoringRules)
        : []
    }
  }

  componentWillReceiveProps(nextProps) {
    const { survey, show } = nextProps
    if (survey && show) {
      const isWeightedScoring = survey.isWeightedScoring
        ? survey.isWeightedScoring
        : false
      const questions = this.buildQuestions(survey.questions)
      const customScoringRules = survey.customScoringRules
        ? this.buildCustomRules(survey.customScoringRules)
        : []
      this.setState({
        isWeightedScoring,
        questions,
        customScoringRules
      })
    }
  }

  onWeightChange = id => event => {
    const { questions } = this.state
    const weightedQuestions = questions.map(question => {
      if (question._id === id) {
        const value = event.target.value
        if (Number.isNaN(Number(value)) && value !== '-') {
          return question
        }
        question.weight = value
      }
      return question
    })
    this.setState({ questions: weightedQuestions })
  }

  onScoreChange = (id, opId) => event => {
    const { questions } = this.state
    const scoredQuestions = questions.map(question => {
      if (question._id === id) {
        question.options = question.options.map(option => {
          if (option.id === opId) {
            const value = event.target.value
            if (Number.isNaN(Number(value)) && value !== '-') {
              return option
            }
            option.score = value
          }
          return option
        })
      }
      return question
    })
    this.setState({ questions: scoredQuestions })
  }

  onCustomChange = (key, item) => event => {
    const { customScoringRules } = this.state
    const value = event.target.value
    if (item === 'weight') {
      if (Number.isNaN(Number(value)) && value !== '-') {
        return
      }
    }
    const changedCustomScoringRules = customScoringRules.map(
      (custom, customKey) => {
        if (customKey === key) {
          return {
            ...custom,
            [item]: value
          }
        }
        return custom
      }
    )
    this.setState({ customScoringRules: changedCustomScoringRules })
  }

  isExistedNumber(item) {
    return item === 0 || item
  }

  removeCustomRule = key => () => {
    const { customScoringRules } = this.state
    const copyCustomScoringRules = [...customScoringRules]
    copyCustomScoringRules.splice(key, 1)
    this.setState({ customScoringRules: copyCustomScoringRules })
  }

  buildQuestions(questions) {
    const filteredQuestions = questions.filter(
      question => question.type === 'radio' || question.type === 'select'
    )
    return filteredQuestions.map(question => ({
      ...question,
      isScoring: question.isScoring !== undefined ? question.isScoring : false,
      weight: this.isExistedNumber(question.weight)
        ? question.weight * 100
        : 100 / filteredQuestions.length,
      options: question.options.map(option => ({
        ...option,
        score: this.isExistedNumber(option.score) ? option.score : ''
      }))
    }))
  }

  buildCustomRules(rules) {
    return rules.map(rule => ({
      ...rule,
      weight: rule.weight ? rule.weight * 100 : 0
    }))
  }

  allFilled(questions, property) {
    let failedName
    const hasProperty = questions.some(question => {
      if (question.isScoring) {
        if (property === 'score') {
          if (
            question.options.some(
              option => !this.isExistedNumber(option[property])
            )
          ) {
            failedName = question.title
            return true
          }
          return false
        }
        return !this.isExistedNumber(question.weight)
      }
      return false
    })
    return property === 'weight' ? hasProperty : failedName
  }

  allWeightFilled() {
    const { questions, customScoringRules } = this.state
    return (
      this.allFilled(questions, 'weight') ||
      customScoringRules.some(
        customRule => !this.isExistedNumber(customRule.weight)
      )
    )
  }

  allWeightSumOne() {
    const { questions, customScoringRules } = this.state
    const questionsWeights = questions.reduce((sum, cur) => {
      if (cur.isScoring) {
        return sum + Number(cur.weight)
      }
      return sum
    }, 0)
    const customWeights = customScoringRules.reduce(
      (sum, cur) => sum + Number(cur.weight),
      0
    )
    return Math.abs(questionsWeights + customWeights - 100) >= 0.1
  }

  submit = () => {
    const { isWeightedScoring, questions, customScoringRules } = this.state
    const allOptionsFilled = this.allFilled(questions, 'score')
    if (allOptionsFilled) {
      return message.error(`请给${allOptionsFilled}的选项设定分值`)
    }
    if (customScoringRules.some(customRule => !customRule.name)) {
      return message.error('请给所有自定义规则的问题设定题目')
    }
    if (isWeightedScoring && this.allWeightFilled()) {
      return message.error('请给所有问题设定权重')
    }
    if (isWeightedScoring && this.allWeightSumOne()) {
      return message.error('请保证所有要打分的问题的权重之和为100%')
    }
    const copyQuestionsMap = questions
      .map(item => ({
        ...item,
        weight: item.weight / 100
      }))
      .reduce((out, item) => {
        out[item.title] = item
        return out
      }, {})
    const allQustions = this.props.survey.questions.map(item => {
      if (copyQuestionsMap[item.title]) {
        return copyQuestionsMap[item.title]
      }
      return item
    })
    const copyCustomScoringRules = customScoringRules.map(item => ({
      ...item,
      weight: item.weight / 100
    }))
    this.props.update(this.props.survey._id, {
      isWeightedScoring,
      questions: [...allQustions],
      customScoringRules: copyCustomScoringRules
    })
    this.props.close()
  }

  toggleWeighted = () => {
    const { isWeightedScoring } = this.state
    this.setState({ isWeightedScoring: !isWeightedScoring })
  }

  toggleScoring = id => () => {
    const { questions } = this.state
    const toggledQuestions = questions.map(question => {
      if (question._id === id) {
        question.isScoring = !question.isScoring
      }
      return question
    })
    this.setState({ questions: toggledQuestions })
  }

  closeSurveyScoreModal = id => () => {
    this.setState({ [`${id}`]: false })
  }

  addNewCustomRow = () => {
    const { customScoringRules } = this.state
    const copyCustomScoringRules = [...customScoringRules]
    copyCustomScoringRules.push({ name: '', weight: '' })
    this.setState({ customScoringRules: copyCustomScoringRules })
  }

  render() {
    const { show } = this.props
    const { isWeightedScoring, questions, customScoringRules } = this.state
    return (
      <div>
        <Modal
          visible={show}
          onCancel={this.props.close}
          title={
            <div style={{ textAlign: 'center' }}>设置打分规则</div>
          }
          footer={[
            <Button onClick={this.submit}>保存</Button>
          ]}
        >
          <div>
            <div className={styles.scoringRuleModal}>
              <div className={styles.setAllWeight}>
                <span>为每题设置权重&nbsp;&nbsp;</span>
                <ISwitch
                  active={isWeightedScoring}
                  label={''}
                  onToggle={this.toggleWeighted}
                />
              </div>
              {!!questions.length && (
                <div>
                  <Table responsive striped bordered hover>
                    <thead>
                      <tr>
                        <th>请输入问卷标题</th>
                        <th>是否打分</th>
                        {isWeightedScoring && <th>权重</th>}
                      </tr>
                    </thead>
                    <tbody>
                      {questions.map(question => (
                        <tr key={question._id}>
                          <td>{question.title}</td>
                          <td>
                            <ISwitch
                              active={question.isScoring}
                              label={''}
                              onToggle={this.toggleScoring(question._id)}
                            />
                            {question.isScoring && (
                              <span
                                className="fa fa-cog"
                                onClick={() => {
                                  this.setState({ [`${question._id}`]: true })
                                }}
                              />
                            )}
                            <SurveyScoreOverlayTrigger
                              show={this.state[`${question._id}`]}
                              close={this.closeSurveyScoreModal(question._id)}
                              question={question}
                              onScoreChange={this.onScoreChange}
                            />
                          </td>
                          {isWeightedScoring && (
                            <td>
                              {question.isScoring && (
                                <div>
                                  <Input
                                    value={question.weight}
                                    onChange={this.onWeightChange(question._id)}
                                  />
                                  <span className={styles.weightIcon}>
                                    &nbsp;&nbsp;%
                                  </span>
                                </div>
                              )}
                            </td>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              )}
              <div className={styles.addNewCustomRow}>
                <Button style={{ color: 'orange' }} onClick={this.addNewCustomRow}> <PlusOutlined />添加自定义打分</Button>
              </div>
              {!!customScoringRules.length && (
                <div>
                  <div className="row">
                    <span
                      className={classnames(
                        `col-md-${isWeightedScoring ? '8' : '10'}`,
                        styles.customRuleTitle
                      )}
                    >
                      请输入问卷标题
                    </span>
                    {isWeightedScoring && (
                      <span
                        className={classnames(
                          'col-md-2',
                          styles.customRuleTitle
                        )}
                      >
                        权重
                      </span>
                    )}
                    <span
                      className={classnames('col-md-2', styles.customRuleTitle)}
                    >
                      删除
                    </span>
                  </div>
                  {customScoringRules.map((rule, key) => (
                    <div className="row" key={`customScoringRule-${key}`}>
                      <span
                        className={`col-md-${isWeightedScoring ? '8' : '10'}`}
                      >

                        <Input
                          value={rule.name}
                          onChange={this.onCustomChange(key, 'name')}
                        />
                      </span>
                      {isWeightedScoring && (
                        <span
                          className={classnames(
                            'col-md-2',
                            styles.customWeight
                          )}
                        >
                          <Input
                            value={rule.weight}
                            onChange={this.onCustomChange(key, 'weight')}
                          />
                          <span className={styles.customWeightIcon}>
                            &nbsp;&nbsp;%
                          </span>
                        </span>
                      )}
                      <span
                        className={classnames(
                          'col-md-2',
                          styles.customRuleTrash
                        )}
                      >
                        <i
                          className="fa fa-trash"
                          onClick={this.removeCustomRule(key)}
                        />
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </Modal>
      </div>
    )
  }
}
