import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { Dropdown, Menu } from 'antd'
import router from 'umi/router'
import ConfirmModal from '@/components/ConfirmModal'

export default class SurveyActionDropDownList extends Component {
  static propTypes = {
    survey: PropTypes.object,
    pushState: PropTypes.func,
    update: PropTypes.func,
    copy: PropTypes.func,
    deleteSurvey: PropTypes.func,
    initialize: PropTypes.func,
    onShowModal: PropTypes.func,
    currentUser: PropTypes.object
  }

  onSelectMenu = (event) => {
    let eventKey = event.key
    const { survey } = this.props
    switch (eventKey) {
      case 'recycle':
        this.startRecycle()
        break
      case 'view':
        router.push(`/survey/${survey._id}`)
        break
      case 'stat':
        router.push(`/duediligence/stat/${survey._id}`)
        break
      case 'cross':
        router.push(`/duediligence/stat/cross?category=${survey.category}`)
        break
      case 'stick':
        this.toggleSticky()
        break
      case 'edit':
        router.push(`/duediligence/survey/edit?id=${survey._id}`)
        break
      case 'score':
        router.push(`/duediligence/survey/score?id=${survey._id}&category=${survey.category}`)
        break
      case 'copy':
        this.props.copy(survey._id)
        break
      case 'setting':
        this.props.onShowModal('showModal', survey._id)
        break
      case 'share':
        this.props.onShowModal('showShareModal', survey._id)
        break
      case 'authorize':
        // this.props.onShowModal('showAuthorizeModal', survey._id)
        break
      case 'email':
        this.props.onShowModal('showEmailModal', survey._id)
        break
      default:
        break
    }
  }

  startRecycle() {
    const { survey } = this.props
    const status = survey.status === 'on' ? 'off' : 'on'
    this.props.update(survey, { status })
  }

  toggleSticky() {
    const { survey } = this.props
    const isSticky = !survey.isSticky
    this.props.update(survey, { isSticky })
  }

  render() {
    const { survey, currentUser } = this.props
    if ((currentUser && currentUser._id) !== (survey && survey.author)) {
      return (
        <div >
          <Dropdown.Button
            overlay={
              (<Menu onClick={this.onSelectMenu}>
                <Menu.Item key="stat">统计分析</Menu.Item>
                {survey && survey.category && (
                  <Menu.Item key="cross">交叉分析</Menu.Item>
                )}
                <Menu.Item key="share">分享问卷</Menu.Item>
              </Menu>)
            }></Dropdown.Button>
        </div>
      )
    }
    return (
      <div >
        <Dropdown.Button
          style={{ position: 'relative', left: -30 }}
          overlay={
            (<Menu onClick={this.onSelectMenu}>
              <Menu.Item key="recycle">
                {survey && survey.status === 'off'
                  ? '开始回收'
                  : '暂停回收'}
              </Menu.Item>
              <Menu.Item key="stat">统计分析</Menu.Item>
              {/* {survey && survey.category && (
                <Menu.Item key="cross">交叉分析</Menu.Item>
              )} */}
              {/* <Menu.Item key="setting">问卷设置</Menu.Item>
              <Menu.Item key="email">邮件通知</Menu.Item>
              <Menu.Item key="share">分享问卷</Menu.Item> */}
              {/* {(currentUser && currentUser._id) === (survey && survey.author) && (
                <Menu.Item key="authorize">
                  授权用户
                </Menu.Item>
              )} */}
              {/* <Menu.Item key="score">问卷打分</Menu.Item> */}
              <Menu.Item key="stick">
                {survey && survey.isSticky ? '取消置顶' : '置顶'}
              </Menu.Item>
              <Menu.Item key="edit">编辑</Menu.Item>
              <Menu.Item key="copy">复制</Menu.Item>
              {(currentUser && currentUser._id) === (survey && survey.author) &&
                <Menu.Item>
                  <ConfirmModal
                    submit={() => this.props.deleteSurvey(survey._id)}
                    message={`确认删除评分${survey.title}吗？`}
                  >
                    删除
                  </ConfirmModal>
                </Menu.Item>
              }
            </Menu>)
          }>
        </Dropdown.Button >





      </div>
    )
  }
}
