import React, { useContext, useState, useEffect, useRef } from 'react'
import { Table, Form, InputNumber, Button, Space } from 'antd'
import SelectFundModal from '@/components/SelectFundModal'
import { CheckOutlined } from '@ant-design/icons'
import './style.css'

const EditableContext = React.createContext<any>();

interface Item {
  key: string;
  name: string;
  age: string;
  address: string;
}

interface EditableRowProps {
  index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditableCellProps {
  title: React.ReactNode;
  editable: boolean;
  children: React.ReactNode;
  dataIndex: string;
  record: Item;
  format?: string,
  handleSave: (record: Item) => void;
}

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  handleSave,
  format,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false);
  const inputRef = useRef();
  const form = useContext(EditableContext);

  useEffect(() => {
    if (editing) {
      inputRef.current && inputRef.current.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
    form.setFieldsValue({ [dataIndex]: record[dataIndex] });
  };

  const save = async () => {
    try {
      const values = await form.validateFields();

      toggleEdit();
      handleSave({ ...record, ...values });
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  let childNode = children;
  const value = children[1]
  if (editable) {
    if (format === 'selectFundPool') {
      childNode = (
        <Form.Item
          style={{ margin: 0 }}
          name={dataIndex}
          rules={[
            {
              required: true,
              message: `${title} is required.`,
            },
          ]}
        >
          <SelectFundModal
            title="请选择基金组合"
            key="1"
            defaultTabs={[{
              name: '基金组合',
              value: 'portfolio',
            }]}
            portfolioType="nav,factorbased"
            rowSelectionType="radio"
            onChange={(fund) => {
              save()
            }}
          >
            <Space>
              <span>{value && value[0].name}</span>
              <Button type="primary" size="small" icon={value ? <CheckOutlined/> : null}>请选择</Button>
            </Space>
          </SelectFundModal>
        </Form.Item>
      )
    } else {
      childNode = editing ? (
        <Form.Item
          style={{ margin: 0 }}
          name={dataIndex}
          rules={[
            {
              required: true,
              message: `${title} is required.`,
            },
          ]}
        >
          <InputNumber style={{ width: '100%' }} ref={inputRef} onPressEnter={save} onBlur={save} />
        </Form.Item>
      ) : (
        <div className="editable-cell-value-wrap" style={{ paddingRight: 24 }} onClick={toggleEdit}>
          {value ? value : <span style={{ color: '#8d96a2' }}>请输入</span>}
        </div>
      )
    }
  }

  return <td {...restProps}>{childNode}</td>;
};

const EditableTable = ({ columns, value, onChange, callback, ...restProps }) => {
  const [dataSource, setDataSource] = useState(value || [])
  useEffect(() => {
    setDataSource(value);
  }, [callback]);
  const handleSave = row => {
    const newData = [...dataSource];
    const index = newData.findIndex(item => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      ...row,
    });
    setDataSource(newData);
    if (onChange) {
      onChange(newData);
    }
  };
  const newColumns = columns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: record => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        format: col.format,
        handleSave: handleSave,
      }),
    };
  });
  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };
  return (
    <div>
      <Table
        bordered
        components={components}
        rowClassName={() => 'editable-row'}
        dataSource={dataSource}
        columns={newColumns}
        pagination={false}
        {...restProps}
      />
    </div>
  );
}

export default EditableTable
