import React from 'react'
import { Form } from '@ant-design/compatible'
import '@ant-design/compatible/assets/index.css'
import { Table, Slider, InputNumber, Input, Row, Col, Radio } from 'antd'
import styles from './style.less'

const EditableContext = React.createContext()

const EditableRow = ({ form, index, ...props }) => {
  return (
    <EditableContext.Provider value={form}>
      <tr {...props} />
    </EditableContext.Provider>
  )
}

const EditableFormRow = Form.create()(EditableRow)

class EditableCell extends React.Component {
  state = {
    editing: false,
  };

  save = value => {
    const { record, handleSave, dataIndex } = this.props
    this.form.validateFields((error, values) => {
      if (error) {
        return
      }
      handleSave({ ...record, [dataIndex]: value })
    })
  };

  renderCell = form => {
    this.form = form
    const { dataIndex, record, title, format, size, getCompProps } = this.props
    const compProps = getCompProps ? getCompProps(record) : { data: [] }
    if (format === 'sliderWithInput') {
      return (
        <Form.Item style={{ margin: 0 }} className={styles.formItemWrapper}>
          <Row gutter={0}>
            <Col span={18}>
              {form.getFieldDecorator(dataIndex, {
                rules: [
                  {
                    required: true,
                    message: `${title} is required.`,
                  },
                ],
                initialValue: record[dataIndex],
              })(<Slider min={0} max={100} step={0.01} size={size} onChange={this.save} />)}
            </Col>
            <Col span={6}>
              {form.getFieldDecorator(dataIndex, {
                rules: [
                  {
                    required: true,
                    message: `${title} is required.`,
                  },
                ],
                initialValue: record[dataIndex] || 0,
              })(
                <InputNumber
                  // value={lowers[record.index] * 100 || 0}
                  min={0}
                  max={100}
                  precision={2}
                  size={size}
                  onChange={this.save}
                />,
              )}
            </Col>
          </Row>
        </Form.Item>
      )
    }
    if (format === 'radioGroup') {
      return (
        <Form.Item style={{ margin: 0 }}>
          {form.getFieldDecorator(dataIndex, {
            rules: [
              {
                required: true,
                message: `${title} is required.`,
              },
            ],
            initialValue: record[dataIndex],
          })(
            <Radio.Group
              buttonStyle="solid"
              onChange={event => this.save(event.target.value)}
              {...compProps}
            >
              {compProps.data.map(item => (
                <Radio.Button key={item.value} value={item.value}>
                  {item.name}
                </Radio.Button>
              ))}
            </Radio.Group>,
          )}
        </Form.Item>
      )
    }
    if (format === 'inputNumber') {
      return (
        <Form.Item style={{ margin: 0 }}>
          {form.getFieldDecorator(dataIndex, {
            rules: [
              {
                required: true,
                message: `${title} is required.`,
              },
            ],
            initialValue: record[dataIndex] || 0,
          })(
            <InputNumber
              // value={lowers[record.index] * 100 || 0}
              size={size}
              min={0}
              precision={2}
              onChange={this.save}
              {...compProps}
            />,
          )}
        </Form.Item>
      )
    }
    return (
      <Form.Item style={{ margin: 0 }}>
        {form.getFieldDecorator(dataIndex, {
          rules: [
            {
              required: true,
              message: `${title} is required.`,
            },
          ],
          initialValue: record[dataIndex],
        })(<Input size={size} />)}
      </Form.Item>
    )
  };

  render() {
    const {
      editable,
      dataIndex,
      title,
      record,
      index,
      handleSave,
      format,
      children,
      ...restProps
    } = this.props
    return (
      <td {...restProps}>
        {editable ? (
          <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>
        ) : (
          children
        )}
      </td>
    )
  }
}

export default class EditableTable extends React.Component {
  handleSave = row => {
    const { dataSource } = this.props
    const newData = [...dataSource]
    const index = newData.findIndex(item => row.key === item.key)
    const item = newData[index]
    newData.splice(index, 1, {
      ...item,
      ...row,
    })
    this.props.onDataChange(newData)
  };

  render() {
    const { dataSource, columns, ...restPorps } = this.props
    const components = {
      body: {
        row: EditableFormRow,
        cell: EditableCell,
      },
    }
    const newColumns = columns.map(col => {
      if (!col.editable) {
        return col
      }
      return {
        ...col,
        onCell: record => ({
          record,
          editable: col.editable,
          dataIndex: col.dataIndex,
          title: col.title,
          format: col.format,
          getCompProps: col.getCompProps,
          handleSave: this.handleSave,
          size: this.props.size,
        }),
      }
    })
    return (
      <div>
        {/* <Button onClick={this.handleAdd} type="primary" style={{ marginBottom: 16 }}>
          Add a row
        </Button> */}
        <Table
          components={components}
          rowClassName={() => 'editable-row'}
          bordered
          dataSource={dataSource}
          columns={newColumns}
          {...restPorps}
        />
      </div>
    )
  }
}
