import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { Modal, Input } from 'antd'
export default class SurveyScoreOverlayTrigger extends Component {
  static propTypes = {
    show: PropTypes.bool,
    close: PropTypes.func,
    question: PropTypes.object,
    onScoreChange: PropTypes.func
  }

  render() {
    const { show, question } = this.props
    return (
      <div>
        <Modal
          visible={show}
          onCancel={this.props.close}
          title='设置选项分数'
        >
          <div>
            <div>
              {question.options.map(option => (
                <div className="row" key={option.id}>
                  <div className="col-xs-8">
                    {option.value === '__other__' ? option.label : option.value}
                  </div>
                  <div className="col-xs-4">
                    <Input
                      type="text"
                      value={option.score}
                      onChange={this.props.onScoreChange(
                        question._id,
                        option.id
                      )}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Modal>
      </div>
    )
  }
}
