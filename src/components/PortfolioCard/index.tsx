import React, { Component } from 'react'
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Row, Col, Card, Statistic, Divider, Tooltip } from 'antd';
import _ from 'lodash'
import classnames from 'classnames'
import Chart from '@/components/Chart/Chart'
import chartTheme from '@/components/Chart/theme'
import styles from './style.less'

interface ComponentProps {
  portfolio: any;
  bordered?: boolean;
  quotas?: boolean;
  gotoPrev?: any;
  gotoNext?: any;
  getCardActions?: any;
}

export default class NavPortfolioCard extends Component<ComponentProps> {
  renderChart() {
    const {
      portfolio: { segments, assetAllocation },
    } = this.props
    const assetTypes = [
      '现金', '股票', '期货', '债券', '基金', '权证', '理财产品', '买入返售', '衍生工具', '期权', '其他',
      '价值风格指数', '成长风格指数', '均衡风格指数',
      '信用选择风格指数', '久期择时风格指数', '可转债风格指数',
      '现金替代指数',
    ]
    let data = []
    if (segments && segments.length) {
      const segment = segments[0]
      const { funds } = segment
      data = funds.map(item => {
        return {
          name: item.name,
          realVal: item.ratio,
          value: item.ratio,
          portfolioType: item.isSaa ? 'saa' : 'taa',
        }
      })
    } else if (assetAllocation && assetAllocation.length) {
      data = assetAllocation.map(item => {
        return {
          name: item.name,
          realVal: item.ratio,
          value: item.ratio,
        }
      })
    }
    if (this.props.portfolio.portfolioType === 'css') {
      data = data.map(item => {
        item.displayName = item.name
        item.parent = item.portfolioType
        return item
      })
      data.unshift({
        id: 'saa',
        name: 'SAA',
        color: '#6975CD',
      })
      data.unshift({
        id: 'taa',
        name: 'TAA',
        color: '#D5896C',
      })
    } else if (['factorbased', 'nav', 'saa', 'topdown'].includes(this.props.portfolio.portfolioType)) {
      data = data
        .sort((fst, snd) => snd.value - fst.value)
        .map((item, index) => {
          let colorIndex = assetTypes.indexOf(item.name)
          if (colorIndex === -1) {
            colorIndex = index
          }
          item.displayName = index < 12 ? item.name : ''
          item.color = chartTheme.colors[colorIndex % chartTheme.colors.length]
          return item
        })
    } else {
      data = data
        .sort((fst, snd) => snd.value - fst.value)
        .map((item, index) => {
          item.displayName = index < 12 ? item.name : ''
          item.color = chartTheme.colors[assetTypes.indexOf(item.name)]
          return item
        })
    }
    const config = {
      chart: {
        type: 'treemap',
        height: 150,
        margin: [0, 0, 0, 0],
        borderRadius: 2,
      },
      tooltip: {
        shared: true,
        pointFormat: '{point.name}: <b>{point.realVal:.2f}%</b><br/>',
      },
      series: [
        {
          type: 'treemap',
          layoutAlgorithm: 'squarified',
          animation: false,
          dataLabels: {
            enabled: false,
            format: '{point.displayName} {point.realVal:.2f}%',
          },
          data: data,
        },
      ],
    }

    if (this.props.portfolio.portfolioType === 'css') {
      config.series[0].layoutAlgorithm = 'stripes'
      config.series[0].alternateStartingDirection = true
      config.series[0].levels = [
        {
          level: 1,
          layoutAlgorithm: 'sliceAndDice',
          dataLabels: {
            enabled: true,
            align: 'left',
            verticalAlign: 'top',
            format: '{point.name}',
            style: {
              fontSize: '15px',
              fontWeight: 'bold',
            },
          },
        },
      ]
    }
    return <Chart options={config} />
  }

  render() {
    const { portfolio, bordered, gotoPrev, gotoNext, getCardActions } = this.props
    const quotas = this.props.quotas || [
      {
        name: '最新净值',
        value: 'unitNavEndOfTerm',
        format: 'number',
      },
      {
        name: '年化收益',
        value: 'yearReturn',
        format: 'percentage',
      },
      {
        name: '波动率',
        value: 'vol',
        format: 'valPercentage',
      },
    ]
    let fundRouteType = 'activefund'
    if (portfolio.isPortfolio) {
      fundRouteType = 'fof/portfolios'
    } else if (portfolio._syncType === 'mutual') {
      fundRouteType = 'fund'
    }
    const getQuotaValue = (quota) => {
      let value = portfolio[quota.value]
      let suffix = ''
      let className = ''
      let precision = 4
      if (['percentage', 'valPercentage'].includes(quota.format)) {
        value = value * 100
        suffix = '%'
        precision = 2
        if (quota.format === 'percentage' && value) {
          className = value > 0 ? 'colorUpWrap' : 'colorDownWrap'
        }
      }
      return {
        value, suffix, className, precision,
      }
    }
    const rows = _.chunk(quotas, 3).map((list, index) => {
      return (
        <>
          {index !== 0 && <Divider dashed style={{ margin: '8px 0' }}/>}
          <Row>
            {list.map(item => {
              const quotaValue = getQuotaValue(item)
              return (
                <Col key={`${item.value}`} span={8}>
                  <Statistic title={item.name} value={quotaValue.value} suffix={quotaValue.suffix} precision={quotaValue.precision} className={quotaValue.className}/>
                </Col>
              )
            })}
          </Row>
        </>
      )
    })
    return (
      <Card
        title={
          <a
            href={`/${fundRouteType}/${portfolio._id}/invest_performance`}
            rel="noopener noreferrer"
            target="_blank"
          >
            {portfolio.name}
          </a>
        }
        actions={getCardActions ? getCardActions(portfolio) : []}
        className={classnames(styles.cardWapper, { [styles.bordered]: bordered })}
        cover={this.renderChart()}
        extra={gotoPrev &&
          <>
            <Tooltip title="上一个">
              <LeftOutlined onClick={gotoPrev} />
            </Tooltip>
            <Tooltip title="下一个">
              <RightOutlined style={{ marginLeft: 10 }} onClick={gotoNext} />
            </Tooltip>
          </>
        }
      >
        {rows}
      </Card>
    );
  }
}
