import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import deduplication from '../../utils/deduplication'
import { Modal, Select, Button, Switch } from 'antd'
import InviteModal from '@/components/InviteModal'
export default class SettingModal extends Component {
  static propTypes = {
    submit: PropTypes.func.isRequired,
    users: PropTypes.array.isRequired,
    show: PropTypes.bool.isRequired,
    close: PropTypes.func.isRequired,
    survey: PropTypes.object.isRequired,
    categories: PropTypes.array.isRequired,
    currentUser: PropTypes.object.isRequired,
  }

  constructor(props) {
    super(props)
    this.state = {
      resubmit: this.props.survey.resubmit,
      isOpen: this.props.survey.isOpen,
      isSpecial: this.props.survey.isSpecial,
      peerEvaluation: this.props.survey.peerEvaluation,
      category: props.survey.category ? [props.survey.category] : [],
      currentUsers: props.survey.whitelist
    }
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.survey != this.props.survey) {
      this.setState({
        category: (nextProps.survey && nextProps.survey.category) ? [nextProps.survey.category] : []
      })
    }
  }

  handleCategoryChange = values => {
    if (!values.length) {
      this.setState({ category: [] })
    } else {
      this.setState({ category: [values[values.length - 1]] })
    }
  }

  handleScaleChange = values => {
    const users = this.getUsers()
    const allUsers = []
    values.forEach(item => {
      users.forEach(user => (user.tags.includes(item) || item.includes(user.email || user.contact)) && allUsers.push(user._id))
    })
    this.setState({ currentUsers: [...new Set(allUsers)] })
  }

  getUsers() {
    const isSolution =false
    return isSolution
      ? this.props.users
      : this.props.users.filter(
        user =>
          user.fof_status === 'actived' &&
          ~user.user_scopes.indexOf('researcher')
      )
  }

  getTags() {
    return deduplication(
      this.getUsers().reduce((out, user) => out.concat(user.tags), [])
    )
  }

  toggleResubmit = () => {
    this.setState({
      resubmit: !this.state.resubmit
    })
  }

  toggleIsOpen = () => {
    this.setState({
      isOpen: !this.state.isOpen
    })
  }

  toggleIsSpecial = () => {
    this.setState({
      isSpecial: !this.state.isSpecial
    })
  }

  togglePeerEvaluation = () => {
    this.setState({
      peerEvaluation: !this.state.peerEvaluation
    })
  }

  submit = () => {
    const { category, currentUsers } = this.state
    const { survey } = this.props
    let whitelist = currentUsers
    const { resubmit, isOpen, isSpecial, peerEvaluation } = this.state
    if (isOpen) {
      whitelist = []
    }
    this.props.submit(survey, {
      whitelist,
      resubmit,
      category: category[0],
      isOpen,
      isSpecial,
      peerEvaluation
    })
    this.setState({ category: [] })
  }

  render() {
    const {
      categories,
      currentUser,
      users,
      survey: { title, description }
    } = this.props
    const { currentUsers, category } = this.state
    const Option = Select.Option
    const tags = this.getTags()
    const otherUsers = this.getUsers()
    const tagObjects = tags.map(tag => ({
      _id: tag,
      isTag: true,
      name: tag,
      nickname: tag
    }))
    let options = tagObjects.concat(otherUsers)
    let currentUsersValue = currentUsers.map(item => {
      const theUser = otherUsers.find(user => user._id === item)
      return theUser && `${theUser.nickname}${theUser.email || theUser.contact}`
    }).filter(item => item)
    return (
      <div>
        <Modal
          visible={this.props.show}
          onCancel={this.props.close}
          width={570}
          title='问卷设置'
          mask={false}
          footer={[
            <Button onClick={() => {
              this.setState({ categories: [] })
              this.props.close()
            }}>
              取消
            </Button>,
            <Button onClick={this.submit}>
              确定
            </Button>
          ]}
        >
          <div>
            <div className="form-group">
              <label htmlFor="category">
                分类
                <small>（可以对同一分类的问卷进行交叉分析</small>
              </label>
              <Select
                mode="tags"
                placeholder='请选择或输入一个新的分类，按回车确认'
                onChange={this.handleCategoryChange}
                value={category}
                style={{ width: '100%' }}
              >
                {categories.map(item => <Option key={item}>{item}</Option>)}
              </Select>
            </div>
            <div className="row">
              <div className="col-sm-4">
                <Switch
                  onClick={this.toggleResubmit}
                  checked={this.state.resubmit}
                />
                <span>允许重复提交问卷</span>
              </div>
              {!!~currentUser.admin_scopes.indexOf('fof_super_admin') && (
                <div className="col-sm-4">
                  <Switch
                    onClick={this.toggleIsSpecial}
                    checked={this.state.isSpecial}
                  />
                  <span>设置标准问卷</span>
                </div>
              )}
              {!!~currentUser.admin_scopes.indexOf('fof_super_admin') &&
                this.state.isSpecial && (
                  <div className="col-sm-4">
                    <Switch
                      onClick={this.togglePeerEvaluation}
                      checked={this.state.peerEvaluation}
                      label='同行评价'
                    />
                    <span>同行评价</span>
                  </div>
                )}
            </div>
            {!!~currentUser.admin_scopes.indexOf('fof_super_admin') &&
              !this.state.isSpecial && (
                <div style={{ marginTop: 15 }}>
                  <Switch
                    onClick={this.toggleIsOpen}
                    checked={this.state.isOpen}
                  />
                  <span>设为公开问卷</span>
                </div>
              )}
            <div
              className={classnames('form-group', {
                hidden: this.state.isSpecial || this.state.isOpen
              })}
            >
              <label htmlFor="tagInput">
                白名单
              </label>
              <Select
                mode="multiple"
                placeholder='输入邮箱、姓名、公司进行搜索'
                onChange={this.handleScaleChange}
                value={currentUsersValue}
                style={{ width: '100%' }}
              >
                {options.map(item => <Option key={item._id} value={item.isTag ? item.name : `${item.nickname}${item.email || item.contact}`}>{item.isTag
                  ? <div><Icon type="tag" />{' '}<span>{item.name}</span></div>
                  : <div>
                    <div>{item.nickname}</div>
                    <div>{item.email || item.contact}</div>
                  </div>}</Option>)}
              </Select>
            </div>
          </div>
        </Modal>
      </div>
    )
  }
}
