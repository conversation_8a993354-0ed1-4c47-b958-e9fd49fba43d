import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { Table } from 'react-bootstrap'
import { Modal, Button, Input, message } from 'antd'
import styles from './style.less'
export default class CustomScoreModal extends Component {
  static propTypes = {
    survey: PropTypes.object,
    answers: PropTypes.object,
    show: PropTypes.bool,
    close: PropTypes.func,
    update: PropTypes.func,
  }

  constructor(props) {
    super(props)
    const { survey, answers } = props
    this.state = {
      isWeightedScoring: survey.isWeightedScoring
        ? survey.isWeightedScoring
        : false,
      customScoring: this.buildCustomScoring(survey, answers)
    }
  }

  componentWillReceiveProps(nextProps) {
    const { survey, answers, show } = nextProps
    if (survey && show) {
      const isWeightedScoring = survey.isWeightedScoring
        ? survey.isWeightedScoring
        : false
      const customScoring = this.buildCustomScoring(survey, answers)
      this.setState({
        isWeightedScoring,
        customScoring
      })
    }
  }

  onCustomScoreChange = key => event => {
    const { customScoring } = this.state
    const value = event.target.value
    if (Number.isNaN(Number(value)) && value !== '-') {
      return
    }
    customScoring[key].score = value
    this.setState({ customScoring })
  }

  buildCustomScoring(survey, answers) {
    const answerAuthors = (answers.list || []).map(answer => answer.author)
    const customScoringRules = survey.customScoringRules || []
    const customScoring = survey.customScoring || []
    return answerAuthors.reduce((out, cur) => {
      customScoringRules.forEach(rule => {
        let score = ''
        const findScore = customScoring.find(
          cus => cus.name === rule.name && cus.userId === cur._id
        )
        if (findScore) {
          score = findScore.score
        }
        out.push({
          name: rule.name,
          weight: rule.weight,
          userId: cur._id,
          nickname: cur.nickname,
          company: cur.company,
          score: score
        })
      })
      return out
    }, [])
  }

  submit = () => {
    const {
      survey: { _id },
    } = this.props
    const { customScoring } = this.state
    if (customScoring.some(custom => !(custom.score === 0 || custom.score))) {
      return message.error('请给所有自定义问题的设定分值')
    }
    this.props.update(_id, { customScoring })
    this.props.close()
  }

  render() {
    const { show } = this.props
    const { isWeightedScoring, customScoring } = this.state
    return (
      <div>
        <Modal
          visible={show}
          onCancel={this.props.close}
          title={<h4 style={{ textAlign: 'center' }}>自定义问题打分</h4>}
          footer={[
            <Button onClick={this.submit}>保存</Button>
          ]}
        >
          <div>
            {!!customScoring.length && (
              <div className={styles.CustomScoreModal}>
                <Table responsive striped bordered hover>
                  <thead>
                    <tr>
                      <th>姓名</th>
                      <th>公司</th>
                      <th>请输入问卷标题</th>
                      {isWeightedScoring && <th>权重</th>}
                      <th>分数</th>
                    </tr>
                  </thead>
                  <tbody>
                    {customScoring.map((custom, key) => (
                      <tr key={`${custom.name}-${custom.userId}`}>
                        <td>{custom.nickname}</td>
                        <td>{custom.company}</td>
                        <td>{custom.name}</td>
                        {isWeightedScoring && <td>{custom.weight * 100}%</td>}
                        <td>
                          <Input
                            value={custom.score}
                            onChange={this.onCustomScoreChange(key)}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
            )}
          </div>
        </Modal>
      </div>
    )
  }
}
