import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { <PERSON><PERSON>, But<PERSON>, Select } from 'antd'
const { Option } = Select;

export default class AddQuestionCategoryModal extends Component {
  static propTypes = {
    children: PropTypes.object.isRequired,
    save: PropTypes.func.isRequired,
    close: PropTypes.func.isRequired,
    show: PropTypes.bool,
    question: PropTypes.object.isRequired,
    categories: PropTypes.array.isRequired
  }
  state = {
    categoryValue: undefined
  }

  onCategoryChange = (value) => {
    this.setState({ categoryValue: value })
  }

  save = () => {
    const { categoryValue } = this.state
    this.props.save({
      ...this.props.question,
      category: categoryValue
    })
    this.props.close()
  }

  render() {
    const { children, show, close, categories } = this.props
    const { categoryValue } = this.state
    return (
      <div>
        <div>{children}</div>
        <Modal
          visible={show}
          onCancel={close}
          title='设置分类'
          footer={[
            <Button onClick={this.save}>提交</Button>
          ]}
        >
          <div>
            <Select
              style={{ width: '100%' }}
              clearable={false}
              value={categoryValue}
              onChange={this.onCategoryChange}
              mode='tags'
            >
              {
                categories.length > 0 &&
                categories.map(item => <Option value={item}>{item}</Option>)
              }
            </Select>
          </div>
        </Modal>
      </div>
    )
  }
}
