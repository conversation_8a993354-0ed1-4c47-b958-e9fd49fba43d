import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import styles from './index.less'
export default class ISwitch extends Component {
  static propTypes = {
    onToggle: PropTypes.func.isRequired,
    active: PropTypes.bool,
    label: PropTypes.any.isRequired,
    className: PropTypes.string
  }

  render() {
    const { active, onToggle, label, className } = this.props
    return (
      <div className={classnames('form-group text-left', className)}>
        <label className={styles.iSwitch}>
          <input type="checkbox" checked={active} onChange={onToggle} />
          <i />
        </label>
        <span className={styles.label}>{label}</span>
      </div>
    )
  }
}
