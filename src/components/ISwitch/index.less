.label {
  margin-left: 5px;
}

.iSwitch {
  float: left;
  position: relative;
  display: inline-block;
  width: 35px;
  height: 20px;
  margin: 0;
  cursor: pointer;
  background-color: orange;
  border-radius: 30px;

  input {
    position: absolute;
    opacity: 0;
  }
  input:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
  }
  input:checked + i:before {
    top: 50%;
    right: 5px;
    bottom: 50%;
    left: 50%;
    border-width: 0;
    border-radius: 5px;
  }
  input:checked + i:after {
    margin-left: 16px;
  }
  i:before {
    border: 1px solid #CFDADD !important;
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    background-color: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 30px;
    content: "";
    transition: all 0.2s;
  }
  i:after {
    position: absolute;
    top: 1px;
    bottom: 1px;
    width: 18px;
    background-color: #fff;
    border-radius: 50%;
    content: "";
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
    transition: margin-left 0.3s;
  }
}
