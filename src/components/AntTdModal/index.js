import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { Popover, Tooltip } from 'antd'
import styles from './AntTdModal.less'
export default class AntTdModal extends Component {
  static propTypes = {
    children: PropTypes.object.isRequired,
    text: PropTypes.string.isRequired,
    isHtml: PropTypes.bool,
    canBePrinted: PropTypes.bool
  }

  state = {
    show: false
  }

  close = () => {
    this.setState({ show: false })
  }

  open = () => {
    this.setState({ show: true })
  }

  renderContent = text => {
    const { isHtml } = this.props
    if (isHtml) {
      return (
        <div
          dangerouslySetInnerHTML={{ __html: text }}
        />
      )
    }
    return <div className={styles.contentText}>{text}</div>
  }

  render() {
    const { children, text, canBePrinted } = this.props
    const content = this.renderContent(text)
    if (canBePrinted) {
      return (
        <div className="table-txt-content">
          {children}
        </div>
      )
    }
    return (
      <div onClick={this.open} >
        <Tooltip id={`antTd-${text}`} placement="top" title={text.length > 300 ? '点击查看详情' : content}>
          <Popover content={content} trigger="click" placement="right">
            <div className="table-txt-content">
              {children}
            </div>
          </Popover>
        </Tooltip>
      </div>
    )
  }
}
