import React, { useEffect, useRef, useState } from 'react';
import { Radio, Form, Col, Row, message, Tree, Switch, Space } from 'antd'
import localRoutes from '@/json'
import {
  ModalForm,
  ProFormText,
  ProFormDigit
} from '@ant-design/pro-form';
import { create, edit, getUserInfo } from '@/services/system/role'
import styles from './index.less'
import moment from 'moment';
import { cloneDeep } from 'lodash';
const { TreeNode } = Tree

export type RoleFromProps = {
  roleId?: string,
  visiable: Boolean,
  reload?: () => void;
  close: () => void
}

const toTree = (data) => {
  let result: any = []
  if (!Array.isArray(data)) {
    return result
  }
  data.forEach(item => {
    delete item.children;
  });
  let map = {};
  data.forEach(item => {
    map[item.menuId] = item;
  });
  data.forEach(item => {
    let parent = map[item.parentId];
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      result.push(item);
    }
  });
  return result;
}
const localTreeData = toTree(cloneDeep(localRoutes.map(item => {
  item.key = item.menuId
  item.title = item.name
  return item
})))
const RoleForm: React.FC<RoleFromProps> = (props) => {
  const { roleId, reload, visiable, close } = props
  const formRef = useRef();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>()
  const [halfCheck, sethalfCheck] = useState([])
  const [roleData, setroleData] = useState({})
  const handleSubmit = async (values: any) => {
    let menuList = cloneDeep(localRoutes).filter(item => checkedKeys?.includes(item.menuId)).map(item => {
      delete item.routes
      return item
    })
    let halfCheckMenus = cloneDeep(localRoutes).filter(item => halfCheck.includes(item.menuId)).map(item => {
      delete item.routes
      return item
    })
    if (roleId) {
      let data = { ...values, menuList: [...menuList, ...halfCheckMenus] }
      edit(roleId, { ...roleData, ...data, updated_at: moment() }).then(res => {
        if (!res || res.message)
          message.error(res.message)
      })
    } else {
      let data = { ...values, menuList: [...menuList, ...halfCheckMenus] }
      create({ ...data }).then(res => {
        if (!res || res.message)
          message.error(res.message)
      })
    }
    reload && reload()
  }

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  }
  const onCheck = (checkedKeysValue: React.Key[], e) => {
    console.log('ee==>', e)
    sethalfCheck(e.halfCheckedKeys)
    setCheckedKeys(checkedKeysValue);
  };

  useEffect(() => {
    if (roleId && roleId !== 0 && visiable) {
      getUserInfo(roleId).then(res => {
        if (res && !res.message) {
          const mMenuIds = localRoutes.filter(item => item.menuType === 'M' || item.menuType == 'J').map(item => item.menuId)
          let data = res.menuList?.filter(item => mMenuIds.includes(item.menuId)).map(item => item.menuId)
          setroleData(res)
          setCheckedKeys(data)
          formRef?.current?.setFieldsValue(res)
        } else {
          message.error(res.message || 56)
        }
      })
    } else {
      formRef?.current?.setFieldsValue({
        roleName: '',
        roleKey: '',
        roleSort: '',
        status: '0',
        menuId: roleId,
        remark: '',
      })
      setCheckedKeys([])
    }
  }, [roleId, visiable])

  const renderTreeNodes = data => {
    return data.map(item => {
      if (item.children) {
        return (
          <TreeNode selectable={true} title={item.name} key={item.menuId} >
            {renderTreeNodes(item.children)}
          </TreeNode>
        )
      }
      return <TreeNode selectable={true} title={item.name} key={item.menuId} />
    })
  }
  return <ModalForm
    title={roleId ? '修改角色' : '添加角色'}
    width={666}
    visible={visiable}
    autoFocusFirstInput
    formRef={formRef}
    modalProps={{
      destroyOnClose: true,
      onCancel: () => {
        close()
        setCheckedKeys([])
      }
    }}
    onFinish={async (values) => {
      await handleSubmit(values)
      close()
      setCheckedKeys([])
      reload && reload()
      return true;
    }}
  >
    <Row gutter={{ sm: 12, md: 24 }}>
      <Col span={12}>
        <ProFormText
          name="roleName"
          label={'角色名称'}
          required
          rules={[
            { required: true, message: '角色名称不能为空' },
          ]}
        />
      </Col>
      <Col span={12}>
        <ProFormText
          name="roleKey"
          label={'角色标识'}
          required
          rules={[
            { required: true, message: '角色标识不能为空' },
          ]}
        />
      </Col>
    </Row>
    <Row gutter={{ sm: 12, md: 24 }}>
      <Col span={12}>
        <ProFormDigit
          name="roleSort"
          label={'角色顺序'}
          required
          rules={[
            {
              required: true, message: '角色顺序不能为空'
            },
          ]}
        />
      </Col>
      <Col span={12}>
        <Form.Item name="status" label={'状态'}>
          <Radio.Group>
            <Radio value='0'>正常</Radio>
            <Radio value='1'>停用</Radio>
          </Radio.Group>
        </Form.Item>
      </Col>
    </Row>
    <Form.Item name="menuId" label={<Space size='large'>
      菜单权限
      <Switch size='small' checkedChildren="展开" unCheckedChildren="折叠" onChange={(checked) => {
        if (checked) {
          setExpandedKeys(localRoutes.map(item => item.menuId))
        } else {
          setExpandedKeys([])
        }

      }} />
      <Switch size='small' checkedChildren="全选" unCheckedChildren="全不选" onChange={(checked) => {
        if (checked) {
          console.log('localRoutes', localRoutes)
          setCheckedKeys(localRoutes.map(item => item.menuId))
        } else {
          setCheckedKeys([])
        }
      }} />
    </Space>}>
      <Tree
        disabled={roleId === 1 ? true : false}
        className={styles.tree}
        checkable
        expandedKeys={expandedKeys}
        onExpand={onExpand}
        onCheck={onCheck}
        checkedKeys={checkedKeys}
        treeData={localTreeData}
      />
    </Form.Item>
    <ProFormText
      name="remark"
      label='备注'
    />
  </ModalForm>
}

export default RoleForm
