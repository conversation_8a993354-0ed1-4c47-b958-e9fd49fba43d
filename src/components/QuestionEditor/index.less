.questionEditor {
  background-color: #000000;
  border-top:  1px solid #615b5b;
  border-bottom: 1px solid #615b5b;
  color:#a09c9c;
  :global(.remark-wrapper-relative) {
    display: block !important;
    position: relative !important;
    margin-bottom: 25px !important;
    height: 200px !important;
  }
  :global(.remark-toolbar-absolute) {
    position: absolute !important;
    top: -50px !important;
    width: 550px !important;
    border-radius: 3px !important;
    background: #f3f3f3 !important;
    border: 1px solid #e3e3e3 !important;
  }
  :global(.rdw-editor-main) {
    border: 1px solid #ccc;
    background-color: #fff;
  }
  :global(.ant-checkbox-wrappere) {
    color: #2a5992;
   }
  .row {
    margin-top: 5px;
    margin-bottom: 5px;
    label {
      padding-top: 7px;
      font-weight: normal;
    }
    input {
      border-radius: 0;
    }
    :global(.col-sm-11) {
      padding-left: 0;
      padding-right: 0;
      :global(input) {
        background-color: #181f29;
        color:white !important;
        border-color:#736d6d
      }
      :global(input:focus) {
        border-color: orange;
      }
    }
    :global(.remark) {
      margin-top: 45px;
      :global(.fr-wrapper) {
        border: 1px solid#736d6d;
        background-color: #181f29;
        :global(.fr-view:focus-visible){
          outline:none;
          color:white;
        }
      }
    }
  }
  .matrixQuestion h5 {
    padding: 0 15px;
  }
  .questionType {
    ul {
      margin-left: 53px;
      margin-bottom: 0;
    }
    li {
      margin-right: 10px;
    }
    li.rowSize {
      height: 20px;
      :global(.input-group) {
        width: 120px;
        :global(.form-control){
          background-color: #181f29;
          border-color: #736d6d;
          color:#bcc5cf
        }
        :global(.input-group-addon){
          color:#bcc5cf;
          background-color: #181f29;
          border-color: #736d6d;
          border:1px solid #736d6d;
          border-left: none;
        }
      }
    }
    select {
      height: 35px;
      margin-top: 10px;
      margin-bottom: 10px;
    }
    .uploadTemplate {
      a {
        text-decoration: none;
        cursor: pointer;
        color: orange;
      }
    }
  }
  .rangeSetting {
    padding-left: 8.3%;
    :global(.input-group-addon){
      color: #cad4df;
      border-color: #736d6d;
      background-color: #484747;
    }
    :global(.form-control){
      background-color: #181f29;
      border-color: #736d6d;
      color:#bdc6d0
    }
    :global(.form-control:focus){
      border-color:orange
    }
  }
  .optionList {
    padding-left: 0;
    ul {
      list-style: none;
      padding-left: 0;
    }
    .optionItem {
      position: relative;
      overflow: hidden;
      zoom: 1;
      margin: 3px 0 3px -3px;
      padding: 3px 0 3px 3px;
      font-size: 14px;
      line-height: 1.8em;
      list-style: none;
      .handle {
        float: left;
        padding-top: 2px;
        margin-left: 30px;
        font-size: 20px;
        cursor: move;
      }

      .input {
        margin-left: 58px;
        margin-right: 15px;
        :global(.form-control){
          color:white;
          background-color: #181f29;
          border: 1px solid #3A404C;
        }
      }
      .remove {
        position: absolute;
        top: 8px;
        right: 0;
        cursor: pointer;
      }
      .hasSetting {
        margin-right: 40px;
      }
      .setting {
        position: absolute;
        top: 8px;
        right: 20px;
        cursor: pointer;
      }
      .createOption {
        span {
          display: inline-block;
          zoom: 1;
          box-sizing: border-box;
          width: 100%;
          border: 1px solid #3A404C;
          padding: 6px 10px;
          background-color: #181f29;
          color:white;
          cursor: pointer;
          text-align: center;
          cursor: pointer;
        }
        
      }
      .addOtherOption {
        margin-left: 58px;
        margin-top: 10px;
        a {
          cursor: pointer;
          text-decoration: none;
        }
      }
      .disabled {
        a {
          color: #b2b2b2;
          &:hover {
            color: #b2b2b2;
          }
        }
      }
    }
  }
  .buttons {
    text-align: center;
    button {
      margin-left: 5px;
      margin-right: 5px;
    }
  }
}

.optionSettingPopover {
  width: 330px;
  max-width: 330px;
}

@media (max-width: 768px) {
  .questionEditor {
    .row {
      :global(.remark) {
        margin-top: 0;
      }
    }
  }
}
