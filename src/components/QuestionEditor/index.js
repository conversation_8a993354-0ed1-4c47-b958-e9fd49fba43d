import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import QsOption from './Option'
import normalizeFloat from '../../utils/normalizeFloat'
import WysiwygEditor from '@/components/WysiwygEditor'
import { Upload, Select, Checkbox, Button, Switch, message } from 'antd'
import styles from './index.less'
import { getToken } from '@/utils/utils'
const { Option } = Select

export default class QuestionEditor extends Component {
  static propTypes = {
    question: PropTypes.object.isRequired,
    cancel: PropTypes.func.isRequired,
    save: PropTypes.func.isRequired,
    createEmptyOption: PropTypes.func.isRequired,
    fundEnabled: PropTypes.bool.isRequired,
    uploadAttachment: PropTypes.func.isRequired,
    uploadResult: PropTypes.object,
    resetSurveyState: PropTypes.func.isRequired
  }

  state = this.buildState()

  componentDidMount() {
    this.initSortable()
  }

  componentWillReceiveProps(newProps) {
    const { question, uploadResult } = newProps
    if (question.type !== this.props.question.type) {
      this.setState({ question })
    }
    if (uploadResult && uploadResult._id === this.state.question._id) {
      this.props.resetSurveyState({ uploadResult: null })
      this.setState({
        question: {
          ...this.state.question,
          templateFile: uploadResult
        }
      })
    }
  }

  componentWillUnmount() {
    clearTimeout(this.state.timeout)
  }

  onSort = event => {
    const { oldIndex, newIndex } = event
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        options: this.reindexElement([...question.options], oldIndex, newIndex)
      }
    })
  }

  onQuestionSort = event => {
    const { oldIndex, newIndex } = event
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        questions: this.reindexElement(
          [...question.questions],
          oldIndex,
          newIndex
        )
      }
    })
  }

  onTitleChange = event => {
    this.setState({
      question: {
        ...this.state.question,
        title: event.target.value
      }
    })
  }

  onParaStateChange = contentState => {
    this.setState({
      question: {
        ...this.state.question,
        title: contentState
      }
    })
  }

  onRemarkStateChange = contentState => {
    this.setState({
      question: {
        ...this.state.question,
        remark: contentState
      }
    })
  }

  onEditorStateChange = editorState => {
    this.setState({
      editorState
    })
  }

  onMaxOrMinChange = type => event => {
    const value = event.target.value
    if (Number.isNaN(Number(value)) && value !== '-') {
      return
    }
    this.setState({
      question: {
        ...this.state.question,
        [type]: value
      }
    })
  }

  onScaleChange = event => {
    const value = event.target.value
    if (Number.isNaN(Number(value))) {
      return
    }
    this.setState({
      question: {
        ...this.state.question,
        scale: value
      }
    })
  }

  onUploadTemplate = file => {
    const hasTooLargeFile = file.size > 20 * 1024 * 1024
    if (hasTooLargeFile) {
      return message.error('超出上传文件20M的最大限制')
    }
    const { question } = this.props
    this.props.uploadAttachment(question._id, file)
    return false
  }

  buildState() {
    const { question } = this.props
    const options = question.options
    const newQuestion = Object.assign({}, question)
    let otherOption
    if (options && options.length) {
      newQuestion.options = options.filter(option => option.type !== 'other')
      otherOption = options.filter(option => option.type === 'other')[0]
      if (otherOption) {
        otherOption.value = otherOption.label
      }
    }
    return {
      question: newQuestion,
      otherOption,
      error: {}
    }
  }

  reindexElement(arr, oldIndex, newIndex) {
    if (arr.length <= 1) {
      return arr
    }
    arr.splice(newIndex, 0, arr.splice(oldIndex, 1)[0])
    return arr
  }

  sortableOptionsDecorator = componentBackingInstance => {
    const Sortable = require('../../libs/Sortable.js')
    // check if backing instance not null
    if (componentBackingInstance) {
      const options = {
        animation: 200,
        group: 'options',
        handle: '.handle',
        onSort: this.onSort
      }
      Sortable.create(componentBackingInstance, options)
    }
  }

  sortableQuestionDecorator = componentBackingInstance => {
    const Sortable = require('../../libs/Sortable.js')
    // check if backing instance not null
    if (componentBackingInstance) {
      const options = {
        animation: 200,
        group: 'questions',
        handle: '.handle',
        onSort: this.onQuestionSort
      }
      Sortable.create(componentBackingInstance, options)
    }
  }

  initSortable() {
    const { question } = this.state
    const state = { mounted: true, question }
    state.locale =
      (window.__i18n && window.__i18n.locale) === 'zh-CN' ? 'zh' : 'en'
    if (question.type === 'paragraph') {
      state.question.title = question.title ? question.title : ''
    } else {
      state.question.remark = question.remark ? question.remark : ''
    }
    this.setState(state)
  }

  addOtherOption = () => {
    if (this.state.otherOption) {
      return
    }
    const option = this.props.createEmptyOption()
    option.type = 'other'
    option.value = '其他'
    this.setState({
      otherOption: option
    })
  }

  updateOtherOption = (id, newProps) => {
    this.setState({
      otherOption: {
        ...this.state.otherOption,
        ...newProps
      }
    })
  }

  removeOtherOption = () => {
    this.setState({
      otherOption: undefined
    })
  }

  addNewOption = () => {
    this.setState({
      question: {
        ...this.state.question,
        options: [
          ...(this.state.question.options || []),
          this.props.createEmptyOption()
        ]
      }
    })
  }

  removeOption = id => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        options: question.options.filter(option => option.id !== id)
      }
    })
  }

  updateOption = (id, newProps) => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        options: question.options.map(option => {
          if (option.id === id) {
            return { ...option, ...newProps }
          }
          return option
        })
      },
      error: {}
    })
  }

  addNewQuestion = () => {
    this.setState({
      question: {
        ...this.state.question,
        questions: [
          ...(this.state.question.questions || []),
          this.props.createEmptyOption()
        ]
      }
    })
  }

  removeQuestion = id => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        questions: question.questions.filter(option => option.id !== id)
      }
    })
  }

  updateQuestion = (id, newProps) => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        questions: question.questions.map(option => {
          if (option.id === id) {
            return { ...option, ...newProps }
          }
          return option
        })
      },
      error: {}
    })
  }

  switchType = (values) => {
    const { question } = this.state
    const type = values
    this.setState({
      otherOption: null,
      question: {
        ...question,
        isRequired: type !== 'sort' ? question.isRequired : false,
        isNumeric: false,
        type
      }
    })
  }

  switchAnswerRemark = (checkedValue) => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        answerRemark: checkedValue
      }
    })
  }

  switchIsRequired = () => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        isRequired: !question.isRequired
      }
    })
  }

  switchIsNumeric = (checked) => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        isNumeric: checked,
        isDate: false
      }
    })
  }

  switchIsDate = (checked) => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        isDate: checked,
        isNumeric: false
      }
    })
  }

  switchAllowNewRow = () => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        allowNewRow: !question.allowNewRow
      }
    })
  }

  handleRowSizeChange = event => {
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        rowSize: Number(event.target.value)
      }
    })
  }

  validateQuestion(question) {
    const ret = {}
    const { options, questions, relatedFund } = question
    if (!question.title) {
      ret.error = '请输入题目'
      ret.touched = true
    }
    if (
      question.isNumeric &&
      options
        .filter(option => option.type !== 'other')
        .some(option => Number.isNaN(Number(option.value)))
    ) {
      ret.error = '选项只能包含数字'
      ret.touched = true
    }
    if (
      (question.type === 'radio' ||
        question.type === 'select' ||
        question.type === 'checkbox' ||
        question.type === 'matrix_radio') &&
      options.length < 2
    ) {
      ret.error = '请至少输入两个选项'
      ret.touched = true
    }
    if (question.type === 'valuation' && (!relatedFund || !relatedFund.id)) {
      ret.error = '请选择关联基金'
      ret.touched = true
    }
    if (question.type === 'table') {
      if (options.length < 1) {
        ret.error = "请至少输入一个选项",
          ret.touched = true
      }
      if (!question.rowSize || question.rowSize < 1 || question.rowSize > 99) {
        ret.error = "行数为 1-99 的整数",
          ret.touched = true
      }
    }
    if (
      (question.type === 'matrix_radio' ||
        question.type === 'matrix_checkbox' ||
        question.type === 'matrix') &&
      !questions.length
    ) {
      ret.error = '请至少输入一个问题'
      ret.touched = true
    }
    const duplicateOption = options
      .map(option => option.value)
      .some((item, pos, self) => self.indexOf(item) !== pos)
    if (duplicateOption) {
      ret.error = '请不要输入重复的选项'
      ret.touched = true
    }
    const duplicateQuestion = questions
      .map(option => option.value)
      .some((item, pos, self) => self.indexOf(item) !== pos)
    if (duplicateQuestion) {
      ret.error = "请不要输入重复的问题"
      ret.touched = true
    }
    if (question.type === 'slider') {
      let { min, max, scale } = question
      min = Number(min)
      max = Number(max)
      scale = Number(scale)
      if (Number.isNaN(scale)) {
        ret.error = "请输入增幅"
        ret.touched = true
      }
      if (Number.isNaN(max)) {
        ret.error = "请输入最大值"
        ret.touched = true
      }
      if (Number.isNaN(min)) {
        ret.error = "请输入最小值"
        ret.touched = true
      }
      const maxScale = normalizeFloat(Math.abs(max - min))
      if (maxScale < scale) {
        ret.error = `增幅不能大于${maxScale}`
        ret.touched = true
      }
      if ((maxScale * 100000) % (scale * 100000) !== 0) {
        ret.error = '增幅不能被整除，请重新输入'
        ret.touched = true
      }
    }
    return ret
  }

  save = () => {
    const swapMaxMin = item => {
      if (
        (item.min || item.min === 0) &&
        (item.max || item.max === 0) &&
        Number(item.min) > Number(item.max)
      ) {
        const temp = item.max
        item.max = item.min
        item.min = temp
      }
    }
    const { question, otherOption } = this.state
    question.title =
      question.type === 'paragraph'
        ? question.title
        : question.title && question.title.trim()
    question.options = (question.options || []).filter(option => !!option.value)
    question.questions = (question.questions || []).filter(
      option => !!option.value
    )
    if (otherOption && otherOption.value) {
      question.options.push({
        ...otherOption,
        label: otherOption.value,
        value: '__other__',
        type: 'other'
      })
    }
    const error = this.validateQuestion(question)
    if (error.touched) {
      if (otherOption) {
        question.options = question.options.filter(
          option => option.type !== 'other'
        )
      }
      this.setState({ error })
      this.state.timeout = setTimeout(() => {
        this.setState({ error: {} })
      }, 3000)
      message.error(error.error)
      return
    }
    question.options.forEach(swapMaxMin)
    question.questions.forEach(swapMaxMin)
    swapMaxMin(question)
    if (question.type === 'input' || question.type === 'textarea') {
      delete question.options
    }
    this.props.save(question)
  }

  showTabs = tabs => {
    return tabs.slice(0, 1)
  }

  relateFundsToValuation = funds => {
    if (!funds.length) {
      return
    }
    const fund = funds[0]
    const relatedFund = {
      name: fund.name,
      id: fund._id
    }
    const { question } = this.state
    this.setState({
      question: {
        ...question,
        relatedFund
      }
    })
  }

  renderOptions() {
    const {
      question: { options, questions, type },
      otherOption
    } = this.state
    const shouldEnableSetting = items => {
      const anySetting = items.some(item => item.isNumeric || item.isDate)
      return !anySetting && !!~['number', 'matrix', 'table'].indexOf(type)
    }

    if (
      ~[
        'input',
        'textarea',
        'paragraph',
        'fund',
        'attachment',
        'slider',
        'valuation'
      ].indexOf(type)
    ) {
      return false
    }

    if (~type.indexOf('matrix')) {
      return (
        <div className={classnames(styles.row, styles.matrixQuestion)}>
          <div className="row">
            <div className="col-md-6">
              <h5 className="h5">问题管理</h5>
              <div className={styles.optionList}>
                {this.state.mounted && (
                  <ul ref={this.sortableQuestionDecorator}>
                    {questions &&
                      questions.map((option, index) => (
                        <QsOption
                          isSubQuestion
                          hasSetting={shouldEnableSetting(options)}
                          key={option.id + index}
                          questionType={type}
                          option={option}
                          updateOption={this.updateQuestion}
                          removeOption={this.removeQuestion}
                        />
                      ))}
                  </ul>
                )}
                <div className={styles.optionItem}>
                  <div
                    className={classnames(styles.input, styles.createOption)}
                  >
                    <span onClick={this.addNewQuestion}>
                      新建问题
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-6">
              <h5 className="h5">选项管理</h5>
              <div className={styles.optionList}>
                {this.state.mounted && (
                  <ul ref={this.sortableOptionsDecorator}>
                    {options &&
                      options.map((option, index) => (
                        <QsOption
                          hasSetting={shouldEnableSetting(questions || [])}
                          key={option.id + index}
                          questionType={type}
                          option={option}
                          updateOption={this.updateOption}
                          removeOption={this.removeOption}
                        />
                      ))}
                  </ul>
                )}
                <div className={styles.optionItem}>
                  <div
                    className={classnames(styles.input, styles.createOption)}
                  >
                    <span onClick={this.addNewOption}>
                      新建选项
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className={classnames(styles.row)}>
        <div className={styles.optionList}>
          {this.state.mounted && (
            <ul ref={this.sortableOptionsDecorator}>
              {options &&
                options.map((option, index) => (
                  <QsOption
                    hasSetting={shouldEnableSetting(questions || [])}
                    key={option.id + index}
                    questionType={type}
                    option={option}
                    updateOption={this.updateOption}
                    removeOption={this.removeOption}
                  />
                ))}
            </ul>
          )}
          <div className={styles.optionItem}>
            <div className={classnames(styles.input, styles.createOption)}>
              <span onClick={this.addNewOption}>
                新建选项
              </span>
            </div>
            {otherOption && (
              <QsOption
                disableMove
                option={otherOption}
                questionType={type}
                updateOption={this.updateOtherOption}
                removeOption={this.removeOtherOption}
              />
            )}
            {type !== 'sort' && type !== 'table' && (
              <div
                className={classnames(styles.addOtherOption, {
                  [styles.disabled]: !!otherOption
                })}
              >
                <a onClick={this.addOtherOption} style={{ color: 'orange' }}>
                  添加「其他」选项
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  render() {
    const {
      question: {
        type,
        answerRemark,
        title,
        isRequired,
        isNumeric,
        isDate,
        allowNewRow,
        rowSize,
        min,
        max,
        scale,
        templateFile,
        remark,
        relatedFund
      },
      error
    } = this.state
    const { fundEnabled } = this.props
    const header = { authorization: getToken() }
    const renderTempFileTip = () => {
      if (!templateFile) {
        return (
          <a className={styles.uploadTemplate}>
            <i className="fa fa-upload" /> 上传模版
          </a>
        )
      }
      return (
        <a className={styles.uploadTemplate}>
          模版{templateFile.name}，点击重新上传
        </a>
      )
    }
    const renderRelatedFundTip = () => {
      if (!relatedFund) {
        return (
          <a className={styles.uploadTemplate}>
            <i className="fa fa-plus" /> 关联基金
          </a>
        )
      }
      return (
        <a className={styles.uploadTemplate}>{`关联基金：${relatedFund.name
          }，点击关联新的基金`}</a>
      )
    }
    if (type === 'paragraph') {
      return (
        <div className={classnames(styles.questionEditor)}>
          <div className="inner">
            <div className={classnames(styles.row, styles.title)}>
              <div className="form-group">
                <div className="col-sm-12">
                  {this.state.mounted && (
                    <WysiwygEditor
                      config={{
                        toolbarInline: true,
                        charCounterCount: false,
                        toolbarVisibleWithoutSelection: false,
                        toolbarSticky: false,
                        placeholderText: '请输入题目',
                        heightMin: 100,
                        toolbarButtons: [
                          'bold',
                          'italic',
                          'underline',
                          'strikeThrough',
                          'subscript',
                          'superscript',
                          '|',
                          'fontFamily',
                          'fontSize',
                          'color',
                          '|',
                          'paragraphFormat',
                          'align'
                        ]
                      }}
                      model={title}
                      onModelChange={this.onParaStateChange}
                    />
                  )}
                </div>
              </div>
            </div>
            <div className={classnames(styles.row, styles.buttons)} >
              <Button onClick={this.props.cancel}>
                取消
              </Button>
              <Button type='primary' onClick={this.save}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className={classnames(styles.questionEditor)}>
        <div className="inner">
          <div className={classnames(styles.row, styles.title)}>
            <div className="form-group">
              <label
                className="col-sm-1 control-label"
                style={{ marginLeft: '-10px', marginRight: '10px', color: '#cdcec9' }}
              >
                题目
              </label>
              <div className="col-sm-11">
                <input
                  type="text"
                  className="form-control"
                  placeholder='题目'
                  value={title}
                  onChange={this.onTitleChange}
                />
              </div>
            </div>
            <div className="form-group remark">
              <label
                className="col-sm-1 control-label"
                style={{ marginLeft: '-10px', marginRight: '10px', color: '#cdcec9' }}
              >
                备注
              </label>
              <div className="col-sm-11" style={{ marginTop: 10 }}>
                {this.state.mounted && (
                  <WysiwygEditor
                    config={{
                      toolbarInline: true,
                      charCounterCount: false,
                      toolbarVisibleWithoutSelection: false,
                      toolbarSticky: false,
                      placeholderText: '可不填写',
                      heightMin: 100,
                      toolbarButtons: [
                        'bold',
                        'italic',
                        'underline',
                        'strikeThrough',
                        'subscript',
                        'superscript',
                        '|',
                        'fontFamily',
                        'fontSize',
                        'color',
                        '|',
                        'paragraphFormat',
                        'align'
                      ]
                    }}
                    model={remark}
                    onModelChange={this.onRemarkStateChange}
                  />
                )}
              </div>
            </div>
          </div>
          <div className={classnames(styles.row, styles.questionType)}>
            <ul className="list-inline">
              <li>
                <Select
                  onChange={this.switchType}
                  value={type}
                  style={{ position: 'relative', zIndex: 10 }}
                >
                  <Option value="radio">单选题</Option>
                  <Option value="checkbox">多选题</Option>
                  <Option value="select">下拉题</Option>
                  <Option value="matrix_radio">
                    矩阵单选题
                  </Option>
                  <Option value="matrix_checkbox">
                    矩阵多选题
                  </Option>
                  <Option value="matrix">矩阵题</Option>
                  <Option value="sort">排序题</Option>
                  <Option value="input">单行文本</Option>
                  <Option value="textarea">多行文本</Option>
                  <Option value="paragraph">段落说明</Option>
                  <Option value="table">表格题</Option>
                  <Option value="attachment">附件题</Option>
                  <Option value="slider">滑块题</Option>
                </Select>
              </li>
              {type === 'table' && (
                <li className={styles.rowSize}>
                  <div className="input-group">
                    <input
                      type="number"
                      className="form-control"
                      value={rowSize}
                      onChange={this.handleRowSizeChange}
                    />
                    <span className="input-group-addon">
                      行
                    </span>
                  </div>
                </li>
              )}
              {type === 'table' && (
                <li>
                  <Checkbox
                    onChange={this.switchAllowNewRow}
                    defaultChecked={allowNewRow}>
                    允许新增行
                  </Checkbox>
                </li>
              )}
              {type !== 'sort' && (
                <li>
                  <Checkbox
                    onChange={this.switchIsRequired}
                    defaultChecked={isRequired}>
                    必填
                  </Checkbox>
                </li>
              )}
              {type === 'attachment' && (
                <li>
                  <Upload
                    className={classnames(styles.uploadTemplate, styles.upload)}
                    name="attachment"
                    multiple={false}
                    showUploadList={false}
                    action="/fake/url"
                    headers={header}
                    beforeUpload={this.onUploadTemplate}
                  >
                    {renderTempFileTip()}
                  </Upload>
                </li>
              )}
              {/* {type === 'valuation' && (
                <li>
                  <SelectFundModal
                    isRadio
                    className={styles.uploadTemplate}
                    showTabs={this.showTabs}
                    onChange={this.relateFundsToValuation}
                  >
                    {renderRelatedFundTip()}
                  </SelectFundModal>
                </li>
              )} */}
              {(type === 'radio' ||
                type === 'matrix_radio' ||
                type === 'select') && (
                  <li>
                    <Checkbox
                      onChange={this.switchIsNumeric}
                      defaultChecked={isNumeric}>
                      数值计算
                    </Checkbox>
                  </li>
                )}
              {type !== 'fund' &&
                type !== 'attachment' &&
                type !== 'valuation' && (
                  <li>
                    <Checkbox
                      onChange={this.switchAnswerRemark}
                      defaultChecked={answerRemark}>
                      允许答案备注
                    </Checkbox>
                  </li>
                )}
              {type === 'input' && (
                <div className="row">
                  <div className="col-sm-6" style={{ marginTop: 5 }}>
                    <Switch
                      style={{ position: 'relative', zIndex: 10 }}
                      onChange={this.switchIsNumeric}
                      checked={isNumeric}
                    />
                    <span>只允许填数字</span>
                  </div>
                  <div className="col-sm-6">
                    <Switch
                      style={{ position: 'relative', zIndex: 10 }}
                      onChange={this.switchIsDate}
                      checked={isDate}
                    />
                    <span>只允许填日期</span>
                  </div>
                </div>
              )}
            </ul>
            {((type === 'input' && isNumeric) || type === 'slider') && (
              <div className={classnames('row', styles.rangeSetting)}>
                <div className="col-sm-9" style={{ marginTop: 6 }}>
                  <div className={classnames('input-group')}>
                    <span className="input-group-addon">
                      数值范围
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      placeholder='最小值'
                      value={min === undefined ? '' : min}
                      onChange={this.onMaxOrMinChange('min')}
                    />
                    <span className="input-group-addon">-</span>
                    <input
                      type="text"
                      className="form-control"
                      placeholder='最大值'
                      value={max === undefined ? '' : max}
                      onChange={this.onMaxOrMinChange('max')}
                    />
                  </div>
                </div>
                {type === 'slider' && (
                  <div className="col-sm-3">
                    <div className={classnames('input-group')}>
                      <span className="input-group-addon">
                        增幅
                      </span>
                      <input
                        type="text"
                        className="form-control"
                        placeholder='增幅'
                        value={scale === undefined ? '' : scale}
                        onChange={this.onScaleChange}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
          {this.renderOptions()}
          <div className={classnames(styles.row, styles.buttons)} style={{ position: 'relative', zIndex: 10 }}>
            <Button onClick={this.props.cancel}>
              取消
            </Button>
            <Button type='primary' onClick={this.save}>
              保存
            </Button>
          </div>
        </div>
      </div>
    )
  }
}
