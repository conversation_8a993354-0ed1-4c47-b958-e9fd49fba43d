import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import { Popover, Switch } from 'antd'
import styles from './index.less'

export default class Option extends Component {
  static propTypes = {
    removeOption: PropTypes.func.isRequired,
    option: PropTypes.object.isRequired,
    updateOption: PropTypes.func.isRequired,
    disableMove: PropTypes.bool,
    questionType: PropTypes.string.isRequired,
    isSubQuestion: PropTypes.bool,
    hasSetting: PropTypes.bool
  }

  state = {
    showModal: false
  }

  onChange = (event) => {
    const { option } = this.props
    this.props.updateOption(option.id, { value: event.target.value })
  }

  onMaxOrMinChange = type => event => {
    const { option } = this.props
    const value = event.target.value
    if (Number.isNaN(Number(value)) && value !== '-') {
      return
    }
    this.props.updateOption(option.id, { [type]: value })
  }

  removeOption = () => {
    this.props.removeOption(this.props.option.id)
  }

  openSettingModal = () => {
    this.setState({ showModal: true })
  }

  closeSettingModal = () => {
    this.setState({ showModal: false })
  }

  toggleIsNumeric = () => {
    const { option } = this.props
    const isNumeric = !option.isNumeric
    this.props.updateOption(option.id, { isNumeric, isDate: false })
  }

  toggleIsDate = () => {
    const { option } = this.props
    this.props.updateOption(option.id, {
      isDate: !option.isDate,
      isNumeric: false
    })
  }

  render() {
    const { option, disableMove, questionType, hasSetting } = this.props
    const isNumericActive = option.isNumeric
    const popover = (
      <Popover
        id={option.id}
        trigger="click"
        placement="left"
        title='选项设置'
        content={
          <div className={styles.optionSettingPopover}>
            <div className="row">
              <div className="col-xs-6">
                <Switch
                  onChange={this.toggleIsNumeric}
                  checked={isNumericActive}

                />
                <span>只允许填数字</span>
              </div>
              {(questionType === 'table' || questionType === 'matrix') && (
                <div className="col-xs-6">
                  <Switch
                    onChange={this.toggleIsDate}
                    checked={option.isDate}
                  />
                  <span>只允许填数字</span>
                </div>
              )}
            </div>
            {isNumericActive && (
              <div className="input-group" style={{ marginTop: 6 }}>
                <span className="input-group-addon">
                  数值范围
                </span>
                <input
                  disabled={!isNumericActive}
                  type="text"
                  className="form-control"
                  placeholder='最小值'
                  value={option.min === undefined ? '' : option.min}
                  onChange={this.onMaxOrMinChange('min')}
                />
                <span className="input-group-addon">-</span>
                <input
                  disabled={!isNumericActive}
                  type="text"
                  className="form-control"
                  placeholder='最大值'
                  value={option.max === undefined ? '' : option.max}
                  onChange={this.onMaxOrMinChange('max')}
                />
              </div>
            )}
          </div>
        }
      >
        <span className={styles.setting} onClick={this.openSettingModal}>
          <i className="fa fa-cog" />
        </span>
      </Popover>
    )

    return (
      <li className={styles.optionItem}>
        {!disableMove && (
          <span className={classnames('handle', styles.handle)}>
            <i className="fa fa-hand-rock-o" />
          </span>
        )}
        <div
          className={classnames(styles.input, {
            [styles.hasSetting]: hasSetting
          })}
          style={{ position: 'relative', zIndex: 10 }}
        >
          <input
            type="text"
            className="form-control"
            placeholder='选项'
            value={option.value}
            onChange={this.onChange}
          />
        </div>
        <span className={styles.remove} onClick={this.removeOption}>
          <i className="fa fa-remove" />
        </span>
        {hasSetting && popover}
      </li>
    )
  }
}
