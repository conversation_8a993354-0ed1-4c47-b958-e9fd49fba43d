import PropTypes from 'prop-types'
import React, { Component } from 'react'
import Chart from '@/components/Chart/Chart'
import classnames from 'classnames'
import math from '../../utils/math'
import deduplication from '../../utils/deduplication'
import { Select } from 'antd'
import pickBy from 'lodash/pickBy'
import styles from './style.less'
import { includes } from 'lodash'
import stat from '../StatItem/translate.js'
const { Option } = Select
export default class CrossStatItem extends Component {
  static propTypes = {
    id: PropTypes.string.isRequired,
    categories: PropTypes.array.isRequired,
    title: PropTypes.string.isRequired,
    data: PropTypes.array.isRequired,
  }

  constructor(props) {
    super(props)
    this.state = {
      quota: 'mean',
      samples: ['全部'],
      type: 'line',
      sampleSelectId: `select-sample-${this.props.id}`,
      referSelectId: `select-refer-${this.props.id}`,
      chartId: `chart-cross-stat-${this.props.id}`,
      curNames: this.buildCurNames(this.buildList(this.listFilter(null), true)).slice(0, 5)
    }
  }

  componentDidMount() {
  }

  onQuotaChange = (value) => {
    const quota = value
    if (quota === 'custom') {
      return
    }
    this.setState({ quota })
  }

  onSampleChange = values => {
    const { sampleSelectId } = this.state
    const { jQuery } = window
    if (!values) {
      this.setState({ samples: [], curNames: [] })
    } else {
      const samples = values
      const curNames = this.buildCurNames(this.buildList(this.listFilter(samples), true))
      if (samples.length > 0 && samples.includes('全部')) {
        this.setState({ samples: ['全部'], curNames })
      } else {
        this.setState({ samples, curNames })
      }
    }
  }

  onReferChange = value => {
    this.setState({ referId: value })
  }

  onNameChange = curNames => {
    this.setState({ curNames })
  }

  getSelectOptions(defaultOption) {
    const { data } = this.props
    const options = []
    if (defaultOption) {
      options.push(defaultOption)
    }
    const authors = data
      .map(questions =>
        questions
          .map(question => question.answers.map(answer => answer.author))
          .reduce((out, item) => out.concat(item), [])
      )
      .reduce((out, item) => out.concat(item), [])
    authors.forEach(author => {
      const ids = options.map(sample => sample._id)
      if (!~ids.indexOf(author._id)) {
        options.push(author)
      }
    })
    return options
  }

  getReferName(id) {
    const options = this.getSelectOptions()
    let name
    options.some(option => {
      if (option._id === id) {
        name = option.nickname
        return true
      }
      return false
    })
    return name
  }

  switchChartType = type => () => {
    this.setState({ type })
  }

  generateReferConfig(name, data) {
    return {
      name,
      data,
      type: 'line',
      lineWidth: 0,
      states: {
        hover: {
          lineWidthPlus: 0
        }
      },
      marker: {
        enabled: true,
        lineWidth: 4,
        lineColor: null
      }
    }
  }

  listFilter = theSamples => item => {
    const samples = theSamples || (this.state && this.state.samples) || ['全部']
    if (samples.includes('全部')) {
      return true
    }
    if (samples.length === 0) {
      return false
    }
    return ~samples.indexOf(item.author._id)
  }

  buildList = (filter, isCalculate) => {
    const { data } = this.props
    const quota = (this.state && this.state.quota) || 'mean'
    const currentQuota = quota || 'mean'
    return data.map(questions => {
      return questions.reduce((out, question) => {
        if (!question.answers.length) {
          out[question.title] = null
          return out
        }
        const answers = question.answers
        const values = answers
          .filter(filter)
          .map(answer =>
            Number(
              answer.answer === '__other__' ? answer.other : answer.answer
            )
          )
          .filter(answer => !Number.isNaN(answer))
        if (!values.length) {
          out[question.title] = null
        } else {
          out[question.title] = isCalculate
            ? math[currentQuota](values)
            : values[0]
        }
        return out
      }, {})
    })
  }

  buildNames = list => deduplication(
    list
      .map(item => Object.keys(item))
      .reduce((out, keys) => out.concat(keys), [])
  )

  buildCurNames = list => deduplication(
    list.map(item => Object.keys(pickBy(item, val => val !== null))).reduce((out, keys) => out.concat(keys), [])
  )

  renderChart() {
    const { quota, chartId, samples, type, referId, curNames } = this.state
    const { categories, title } = this.props
    const config = {
      chart: {
        type: type,
        pinchType: '',
        height: 400
      },
      exporting: {
        enabled: false
      },
      title: {
        text: title
      },
      credits: {
        enabled: false
      },
      xAxis: {
        categories
      },
      yAxis: {
        title: {
          text: stat[quota]
        }
      },
      series: [],
      colors: [
        '#7cb5ec',
        '#90ed7d',
        '#f7a35c',
        '#8085e9',
        '#f15c80',
        '#e4d354',
        '#2b908f',
        '#f45b5b',
        '#91e8e1'
      ]
    }

    if (!samples.length) {
      return <Chart options={config} />
    }
    const list = this.buildList(this.listFilter(null), true)
    const names = this.buildCurNames(list).filter(name => curNames.includes(name))
    const series = names.map(name => {
      return {
        name: name,
        data: list.map(item => (item[name] === undefined ? null : item[name]))
      }
    })
    if (referId) {
      const referName = this.getReferName(referId)
      const referFilter = item => {
        return item.author._id === referId
      }
      const referList = this.buildList(referFilter)
      names.forEach(name => {
        series.push(
          this.generateReferConfig(
            `${referName}-${name}`,
            referList.map(item =>
              item[name] === undefined ? null : item[name]
            )
          )
        )
      })
    }
    config.series = series
    return <Chart options={config} />
  }

  render() {
    const { quota, type, sampleSelectId, chartId, referSelectId, curNames, samples, referId } = this.state
    const names = this.buildCurNames(this.buildList(this.listFilter(null), true))
    const stat = [
      {
        value: 'mean',
        name: '平均值'
      },
      {
        value: 'median',
        name: '中位数'
      },
      {
        value: 'std',
        name: '标准差'
      },
      {
        value: 'var',
        name: '方差'
      },
      {
        value: 'max',
        name: '最大值'
      },
      {
        value: 'min',
        name: '最小值'
      },
      {
        value: 'sum',
        name: '总和'
      }
    ]
    const sampleOptions = this.getSelectOptions({
      _id: '全部',
      nickname: '全部',
      company: 'All'
    })
    const refOptions = this.getSelectOptions()
    return (
      <div className={styles.main}>
        <div className={classnames('row', styles.selectContainer)}>
          <div className="col-sm-6">
            <div className={classnames('row', styles.selectContainer)}>
              <div className={classnames('col-sm-8')}>
                <Select
                  style={{ width: 200 }}
                  placeholder='请选择样本'
                  mode='multiple'
                  onChange={this.onSampleChange}
                  value={samples}
                >
                  {
                    sampleOptions.map(item => {
                      let label = `${item.nickname} <${item.company}>`
                      return <Option value={item._id}>{label}</Option>
                    })
                  }
                </Select>
              </div>
              <div className={classnames('col-sm-4')}>
                <Select
                  style={{ width: 150 }}
                  placeholder='请选择参照'
                  value={referId}
                  onChange={this.onReferChange}
                >
                  {
                    refOptions.map(item => {
                      let label = `${item.nickname} <${item.company}>`
                      return <Option value={item._id}>{label}</Option>
                    })
                  }
                </Select>
              </div>
            </div>
          </div>
          <div className="col-sm-6">
            <div className={classnames('btn-group'), styles.rightButton}>
              <button
                className={classnames('btn btn-default', {
                  active: type === 'line'
                })}
                onClick={this.switchChartType('line')}
              >
                <i className="fa fa-line-chart" />
              </button>
              <button
                className={classnames('btn btn-default', {
                  active: type === 'column'
                })}
                onClick={this.switchChartType('column')}
              >
                <i className="fa fa-bar-chart" />
              </button>
            </div>
            <div className={styles.quotaOptions}>
              <Select value={quota} onChange={this.onQuotaChange}>
                {stat.map(item => {
                  return <Option value={item.value}>{item.name}</Option>
                })}
              </Select>
            </div>
            <div className={styles.quotaOptions}>
              <Select
                mode="multiple"
                style={{ width: '26em' }}
                value={curNames}
                onChange={this.onNameChange}
              >
                {names.map(name => <Select.Option key={name}>{name}</Select.Option>)}
              </Select>
            </div>
          </div>
        </div>
        <div className={styles.chartWapper}>
          {this.renderChart()}
        </div>
      </div>
    )
  }
}
