.main {
  margin-top: 25px;
  background-color: #4c4b4b;
  .leftButton {
    float: left;
    margin-right: 10px;
  }
  .rightButton {
    float: right;
    :golbal(button){
      background-color: #282727 !important;
      color: white !important;
      border: none !important;
    }
  }
  .sampleSelect {
    min-width: 200px;
  }
  .chartWapper {
    height: 440px;
  }
  :global(.btn-group) {
    float: right;
    margin-right: 10px;
  }
  .quotaOptions {
    float: right;
    margin-right: 10px;
  }
  .selectContainer {
    margin-left: -5px;
    margin-right: -5px;
    color:white;
    :global(.ant-select-selector span){
      color: #d8d9db !important
    }
    :global(.btn.btn-default){
      color: #d8d9db !important;
      background-color: #424242 !important;
      outline: none;
    }
    :global(.btn.btn-default:focus){
      background-color:rgb(189, 126, 9) !important;
    }
    :global(.btn.btn-default:visited){
      background-color:rgb(189, 126, 9) !important;
    }
    :global(.btn.btn-default:target){
      background-color:rgb(189, 126, 9) !important;
    }
    :global(.btn.btn-default.active){
      background-color:rgb(189, 126, 9) !important;
    }
  }
}
