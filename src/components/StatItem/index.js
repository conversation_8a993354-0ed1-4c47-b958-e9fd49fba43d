import PropTypes from 'prop-types'
import React, { Component } from 'react'
import Chart from '@/components/Chart/Chart'
import classnames from 'classnames'
import { getToken } from '@/utils/utils'
import math from '@/utils/math'
import moment from 'moment'
import clone from 'lodash/clone'
import generateFileIcon from '@/utils/generateFileIcon'
import normalizeUnit from '@/utils/normalizeUnit'
import * as calculator from '@/utils/returnCalculator'
import WysiwygEditor from '@/components/WysiwygEditor'
import AntTable from '@/components/AntTable'
import GetCsvAndPng from '@/components/GetCsvAndPng'
import { Tooltip, Select } from 'antd'
import styles from './StatItem.less'
import stat from './translate.js'
const mathDimensitons = ['mean', 'median', 'std', 'var', 'max', 'min', 'sum']
const { Option } = Select
export default class StatItem extends Component {
  static propTypes = {
    question: PropTypes.object,
    index: PropTypes.number,
    survey: PropTypes.object,
    getFundId: PropTypes.func,
    currentFundId: PropTypes.string,
    currentValuation: PropTypes.object,
    getFundIdError: PropTypes.object,
    getValuationIdError: PropTypes.object,
    updateStatRemarks: PropTypes.func,
    isPrint: PropTypes.bool
  }

  constructor(props) {
    super(props)
    this.props = props // Fix IE 9-10?
    this.state = this.buildState()  //需要创建一个图标类型字段，切换更新图标
  }

  shouldComponentUpdate(nextProps, nextState) {
    return nextProps.question._id !== this.props.question._id ||
      nextState.quota !== this.state.quota ||
      nextState.referId !== this.state.referId ||
      nextState.samples.toString() !== this.state.samples.toString() ||
      nextState.description !== this.state.description ||
      (nextState.chartData && !this.state.chartData) || nextState.type !== this.state.type
  }


  onQuotaChange = (value) => {
    this.setState({
      quota: value,
      chartData: this.generateCalculatorChartData(value, this.state.samples)
    })
  }



  onSampleChange = values => {
    const { sampleSelectId } = this.state
    if (!values) {
      this.setState({ samples: [] })
    } else {
      const samples = values
      if (samples.length > 0 && samples.includes('全部')) {
        this.setState({ samples: ['全部'] })
      } else {
        this.setState({ samples })
      }
    }
  }

  onReferChange = value => {
    this.setState({ referId: value })
  }

  handleModelChange = descriptionContent => {
    this.setState({ descriptionContent })
  }

  getInitialType() {
    if (this.hasCalculator()) {
      return 'calculator'
    }
    return this.hasPieChart() ? 'pie' : 'bar'
  }

  getFundId = fund => () => {
    const { survey } = this.props
    this.props.getFundId(survey._id, {
      name: fund.question,
      userId: fund.author._id
    })
  }


  getSelectOptions(defaultOption) {
    const {
      question: { answers, type }
    } = this.props
    const options = []
    let authors
    if (type === 'matrix' || type == 'sort') {
      authors = Object.keys(answers)
        .reduce((out, key) => {
          return out.concat(answers[key])
        }, [])
        .map(answer => answer.author)
    } else {
      authors = (answers||[]).map(answer => answer.author)
    }
    authors.forEach(author => {
      const ids = options.map(option => option._id)
      if (author && !~ids.indexOf(author._id)) {
        options.push(author)
      }
    })
    if (defaultOption) {
      options.unshift(defaultOption)
    }
    return options
  }

  getReferName(id) {
    const options = this.getSelectOptions()
    let name
    options.some(option => {
      if (option._id === id) {
        name = option.nickname
        return true
      }
      return false
    })
    return name
  }


  buildState() {
    const { question, survey } = this.props
    const state = {
      type: this.getInitialType(),
      quota: 'mean',
      samples: ['全部'],
      sampleSelectId: `select-sample-${question._id}`,
      referSelectId: `select-refer-${question._id}`,
      descriptionContent: survey.statRemarks && survey.statRemarks[question._id] ? survey.statRemarks[question._id] : '',
      tableData: this.buildTableData()
    }
    if (this.hasAnswers() && this.hasCalculator()) {
      state.chartData = this.generateCalculatorChartData(state.quota, state.samples)
    }
    return state
  }

  hasAnswers() {
    const { question: { answers } } = this.props
    if (Array.isArray(answers)) {
      return !!answers.length
    }
    return !!Object.keys((answers||[])).length
  }

  hasCalculator() {
    const {
      question: { type }
    } = this.props
    return (
      (!!~['radio', 'select', 'matrix_radio', 'input'].indexOf(type) &&
        this.props.question.isNumeric) ||
      ((type === 'table' || type === 'matrix') && this.hasNumericData()) ||
      type === 'slider'
    )
  }

  hasPieChart() {
    return !~[
      'slider',
      'input',
      'table',
      'sort',
      'matrix',
      'matrix_radio',
      'matrix_checkbox'
    ].indexOf(this.props.question.type)
  }

  hasSwitchButton() {
    return !~['input', 'table', 'slider'].indexOf(this.props.question.type)
  }

  hasNumericQuestion() {
    return (this.props.question.questions||[]).filter(question => question.isNumeric)
      .length
  }

  hasNumericOption() {
    console.log('this.props.question',this.props.question)
    return (this.props.question.options || []).filter(option => option.isNumeric).length
  }

  hasNumericData() {
    return this.hasNumericOption() || this.hasNumericQuestion()
  }

  hasChart() {
    const {
      question: { type }
    } = this.props
    return (
      !this.isText() &&
      type !== 'fund' &&
      type !== 'valuation' &&
      type !== 'attachment' &&
      !(~['matrix', 'table'].indexOf(type) && !this.hasNumericData())
    )
  }

  hasPerChart() {
    return !!~['matrix_radio', 'radio', 'dropdown'].indexOf(this.props.question.type)
  }

  isText() {
    const {
      question: { type, isNumeric }
    } = this.props
    return (type === 'input' && !isNumeric) || type === 'textarea'
  }

  isTextarea() {
    const { question: { type } } = this.props
    return type === 'textarea'
  }

  switchChartType = type => () => {
    this.setState({ type: type })
  }

  buildFunds(answers) {
    return answers.map(answer => {
      const nets = JSON.parse(answer.answer)
      const returns = calculator.calculateReturns(nets)
      answer.nets = nets
      answer.scale = JSON.parse(answer.scaleAnswers)
      answer.returns = returns
      answer.name = answer.question
      if (answer.endDate === 'now') {
        answer.endToNow = true
      } else {
        answer.endDate = Number(answer.endDate)
      }
      answer.startDate = Number(answer.startDate)
      return answer
    })
  }

  generateAnswerFilter = answer => {
    const { samples } = this.state
    if (samples[0] === '全部') {
      return true
    }
    if (samples.length === 0) {
      return false
    }
    return ~samples.indexOf(answer.author._id)
  }

  generateOptionCountMap = answers => {
    return (answers || []).reduce((out, answer) => {
      out[answer.answer] = (out[answer.answer] || 0) + 1
      return out
    }, {})
  }

  generateQuestionCountMap = items => {
    return (items || []).reduce((out, item) => {
      out[item.answer] = out[item.answer] || {}
      out[item.answer][item.question] = (out[item.answer][item.question] || 0) + 1
      return out
    }, {})
  }

  generateSeriesWithRefer = (series, perType) => {
    const config = this.generateBaseChartConfig(perType)
    const { question } = this.props
    const { type, answers } = question
    const { referId } = this.state
    if (!referId) {
      return series
    }
    let hasMultiple = series && series.length !== 1
    if (type === 'matrix') {
      const hasNumericQuestion = this.hasNumericQuestion()
      const optionFilter = item => {
        if (hasNumericQuestion) {
          return true
        }
        return item.isNumeric
      }
      const questionFilter = item => {
        if (hasNumericQuestion) {
          return item.isNumeric
        }
        return true
      }
      const categories = question.options
        .filter(optionFilter)
        .map(option => option.value)
      referId.split(',').forEach(rid => {
        const referName = this.getReferName(rid)
        const questionOptionMapByUser = question.questions
          .filter(questionFilter)
          .reduce((ret, item) => {
            ret[item.value] = question.options
              .filter(optionFilter)
              .reduce((out, option) => {
                const numbers = (answers[item.value] || [])
                  .filter(answer => answer.author._id === rid)
                  .map(answer => Number(answer.answer[option.value]))
                  .filter(answer => !Number.isNaN(answer))
                out[option.value] = Math.round(numbers[0] * 100) / 100
                return out
              }, {})
            return ret
          }, {})
        question.questions.filter(questionFilter).forEach(item => {
          const userData = categories.map(option =>
            questionOptionMapByUser[item.value]
              ? questionOptionMapByUser[item.value][option] || null
              : null
          )
          series.push(
            this.generateReferConfig(
              `${referName}-${item.value}`,
              userData,
              hasMultiple,
              config.chart.type
            )
          )
        })
      })
    } else if (type === 'table') {
      referId.split(',').forEach(rid => {
        const referName = this.getReferName(rid)
        question.options
          .filter(option => option.isNumeric)
          .forEach(option => {
            const userAnswer = answers.filter(
              item => item.author._id === rid
            )[0]
            const userData = [Number(userAnswer.answer[option.value])]
            series.push(
              this.generateReferConfig(
                `${referName}-${option.value}`,
                userData,
                hasMultiple,
                config.chart.type
              )
            )
          })
      })
    } else if (['matrix_radio', 'matrix_checkbox'].includes(type)) {
      referId.split(',').forEach(rid => {
        const referName = this.getReferName(rid)
        const questionAnswersMapUser = answers
          .filter(item => item.author._id === rid)
          .reduce((out, answer) => {
            out[answer.question] = out[answer.question] || []
            out[answer.question].push(Number(answer.answer) || 0)
            return out
          }, {})
        question.questions.forEach(item => {
          series.push(
            this.generateReferConfig(
              `${referName}-${item.value}`,
              questionAnswersMapUser[item.value],
              hasMultiple,
              'column'
            )
          )
        })
      })
    } else {
      const getValues = (filter = () => true) => {
        return (answers || [])
          .filter(filter)
          .filter(
            item =>
              item.answer !== '__other__' ||
              (item.other !== '' && !Number.isNaN(item.other))
          )
          .map(item =>
            item.answer === '__other__'
              ? Number(item.other)
              : Number(item.answer)
          )
          .filter(item => !Number.isNaN(item))
      }
      referId.split(',').forEach(rid => {
        const referName = this.getReferName(rid)
        const userData = getValues(item => item.author._id === rid)
        series.push(
          this.generateReferConfig(
            `${referName}`,
            userData,
            hasMultiple,
            'column'
          )
        )
      })
    }
    return series
  }

  generateCalculatorChartData = (quota, samples) => {
    const { question } = this.props
    const generateAnswerFilter = answer => {
      if (samples[0] === '全部') {
        return true
      }
      if (samples.length === 0) {
        return false
      }
      return ~samples.indexOf(answer.author._id)
    }
    const { type, answers } = question
    let categories = [stat[quota]]
    let name, series
    if (type === 'sort') {
      name = '得分'
      const generalRank = !answers
        ? {}
        : Object.keys(answers).reduce((ret, key) => {
          const sum = answers[key].reduce(
            (out, item, index) => (out += item * (index + 1)),
            0
          ) // eslint-disable-line
          const total = answers[key].reduce((out, item) => (out += item), 0) // eslint-disable-line
          ret[key] = sum / total
          return ret
        }, {})
      question.options.forEach(
        option => (option.rank = generalRank[option.value] || null)
      )
      const sortedAscOptions = question.options.sort(
        (fst, snd) => fst.rank - snd.rank
      )
      categories = sortedAscOptions.map(option => option.value)
      const data = sortedAscOptions
        .map(
          option =>
            question.options.length - Number(generalRank[option.value]) || 0
        )
        .map(item => Math.round(item * 100) / 100)
      series = [{ name: '得分', data }]
    } else if (type === 'matrix') {
      name = stat[quota]
      const hasNumericQuestion = this.hasNumericQuestion()
      const optionFilter = item => {
        if (hasNumericQuestion) {
          return true
        }
        return item.isNumeric
      }
      const questionFilter = item => {
        if (hasNumericQuestion) {
          return item.isNumeric
        }
        return true
      }
      categories = question.options
        .filter(optionFilter)
        .map(option => option.value)
      const filteredQuestions = question.questions.filter(questionFilter)
      const filteredOptions = question.options.filter(optionFilter)
      if (samples.length !== 0) {
        const questionOptionMap = filteredQuestions
          .reduce((ret, item) => {
            const filteredAnswers = (answers[item.value] || []).filter(generateAnswerFilter)
            ret[item.value] = filteredOptions
              .reduce((out, option) => {
                const numbers = filteredAnswers
                  .map(answer => Number(answer.answer[option.value]))
                  .filter(answer => !Number.isNaN(answer))
                out[option.value] = Math.round(math[quota](numbers) * 100) / 100
                out[`${option.value}_statValues`] = mathDimensitons.map(mathQuota => Math.round(math[mathQuota](numbers) * 100) / 100)
                return out
              }, {})
            return ret
          }, {})
        series = filteredQuestions.map(item => {
          const values = categories.map(option =>
            questionOptionMap[item.value]
              ? questionOptionMap[item.value][option] || 0
              : 0
          )
          const statValues = categories.map(option =>
            questionOptionMap[item.value]
              ? questionOptionMap[item.value][`${option}_statValues`] || []
              : []
          )
          return {
            name: item.value,
            data: values,
            _statValues: statValues,
            _categories: categories
          }
        })
      } else {
        series = []
      }
    } else if (type === 'table') {
      name = stat[quota]
      if (samples.length !== 0) {
        const filteredAnswers = answers.filter(generateAnswerFilter)
        series = question.options
          .filter(option => option.isNumeric)
          .map(option => {
            const answerList = filteredAnswers
              .map(item => Number(item.answer[option.value]))
              .filter(item => !Number.isNaN(item))
            return {
              name: option.value,
              data: [Math.round(math[quota](answerList) * 100) / 100],
              _statValues: mathDimensitons.map(mathQuota => Math.round(math[mathQuota](answerList) * 100) / 100)
            }
          })
      } else {
        series = []
      }
    } else if (['matrix_radio', 'matrix_checkbox'].includes(type)) {
      name = stat[quota]
      if (samples.length !== 0) {
        const questionAnswersMap = answers
          .filter(generateAnswerFilter)
          .reduce((out, answer) => {
            out[answer.question] = out[answer.question] || []
            out[answer.question].push(Number(answer.answer) || 0)
            return out
          }, {})
        series = question.questions.map(item => {
          const values = questionAnswersMap[item.value]
          if (!values || !values.length) {
            return {
              name: item.value,
              data: [0]
            }
          }
          return {
            name: item.value,
            data: [Math.round(math[quota](values) * 100) / 100],
            _statValues: mathDimensitons.map(mathQuota => Math.round(math[mathQuota](values) * 100) / 100)
          }
        })
      } else {
        series = []
      }
    } else {
      name = stat[quota]
      categories = [question.title]
      const getValues = (filter = () => true) => {
        return (answers || [])
          .filter(filter)
          .filter(
            item =>
              item.answer !== '__other__' ||
              (item.other !== '' && !Number.isNaN(item.other))
          )
          .map(item =>
            item.answer === '__other__'
              ? Number(item.other)
              : Number(item.answer)
          )
          .filter(item => !Number.isNaN(item))
      }
      const answerList = getValues(generateAnswerFilter)
      if (answerList.length) {
        series = [
          {
            name,
            data: [Math.round(math[quota](answerList) * 100) / 100],
            _statValues: mathDimensitons.map(mathQuota => {
              return Math.round(math[mathQuota](answerList) * 100) / 100
            })
          }
        ]
      } else {
        series = []
      }
    }
    return {
      name,
      categories,
      series
    }
  }

  generateCalculatorTableData = () => {
    const { chartData } = this.state
    const { question } = this.props
    const { type } = question
    let data
    const quotas = mathDimensitons.map(value => ({
      name: stat[value],
      value,
      hasSorter: true,
      format: 'commaNumber'
    }))
    if (type === 'table' || type === 'matrix_radio') {
      quotas.unshift({
        name: '题目',
        value: 'question',
        format: 'text',
        hasSorter: true
      })
      data = chartData.series.map(item => {
        const res = mathDimensitons.reduce((out, dim, index) => {
          const statValues = item._statValues
          out[dim] = statValues && statValues[index].toString()
          return out
        }, {})
        res.question = item.name
        return res
      })
    } else if (type === 'matrix') {
      quotas.unshift({
        name: '选项',
        value: 'option',
        format: 'text',
        hasFilter: true,
        hasSorter: true
      })
      quotas.unshift({
        name: '题目',
        value: 'question',
        format: 'text',
        hasFilter: true,
        hasSorter: true
      })
      data = chartData.series.reduce((out, item) => {
        const subData = chartData.categories.map((category, index) => {
          const statValues = item._statValues[index]
          const res = mathDimensitons.reduce((out, dim, index) => {
            out[dim] = statValues && statValues[index].toString()
            return out
          }, {})
          res.question = item.name
          res.option = category
          return res
        })
        return out.concat(subData)
      }, [])
    } else {
      const statValues = chartData.series[0]._statValues
      data = [mathDimensitons.reduce((out, dim, index) => {
        out[dim] = statValues && statValues[index].toString()
        return out
      }, {})]
    }
    return {
      quotas,
      data
    }
  }

  generatePerChartData = (stateType) => {
    const { question } = this.props
    const { type, answers } = question
    const filteredAnswers = Array.isArray(answers) && answers.filter(this.generateAnswerFilter)
    let countMap, name, categories, series
    if (['matrix_radio', 'matrix_checkbox'].includes(type)) {
      if (stateType === 'column' || stateType === 'bar') {
        name = '百分比'
      } else {
        name = '总和'
      }
      countMap = this.generateQuestionCountMap(filteredAnswers)
      categories = question.questions.map(item => item.value)
      series = question.options.map(option => {
        const values = question.questions.map(item => countMap[option.value]
          ? countMap[option.value][item.value] || 0 : 0)
        return { name: option.value, data: values }
      })
    } else {
      countMap = this.generateOptionCountMap(filteredAnswers)
      const sortedOptions = question.options.sort((fst, snd) => (countMap[snd.value] || 0) - (countMap[fst.value] || 0))
      categories = sortedOptions.map(option => option.type === 'other' ? '其他' : option.value)
      const data = stateType === 'pie' ?
        sortedOptions.map((option, index) => {
          return {
            name: option.type === 'other' ? '其他' : option.value,
            y: countMap[option.value] || 0,
            sliced: !index, selected: !index
          }
        }) : sortedOptions.map(option => countMap[option.value] || 0)
      series = [{ name: '总和', data }]
    }
    return {
      name,
      categories,
      series
    }
  }

  generatePerTableData = () => {
    const { question: { type } } = this.props
    const { categories, series } = this.generatePerChartData('pie')
    const quotas = [{
      name: '选项',
      value: 'option',
      width: 120,
      format: 'text'
    }, {
      name: '占比',
      value: 'ratio',
      width: 120,
      format: 'percentage'
    }]
    if (type === 'matrix_radio') {
      quotas.unshift({
        name: '题目',
        value: 'question',
        width: 120,
        format: 'text',
        hasFilter: true
      })
    }
    const data = []
    series.forEach((sery, index) => {
      sery.data.forEach((item, key) => {
        if (type === 'matrix_radio') {
          const sum = series.reduce((ans, it) => {
            ans += it.data[index]
            return ans
          }, 0)
          data.push({
            question: categories[key],
            option: sery.name,
            ratio: item / sum
          })
        } else {
          const sum = sery.data.reduce((ans, it) => {
            ans += it.y
            return ans
          }, 0)
          data.push({
            option: categories[key],
            ratio: item.y / sum
          })
        }
      })
    })
    return {
      quotas,
      data
    }
  }

  transformMatrixResult = data => {
    const optionQuestionMap = Object.keys(data)
      .map(question => {
        return Object.keys(data[question]).map(option => ({
          question,
          option,
          value: data[question][option]
        }))
      })
      .reduce((out, items) => out.concat(items), [])
      .reduce((out, item) => {
        out[item.option] = out[item.option] || {}
        out[item.option][item.question] = item.value
        return out
      }, {})
    return optionQuestionMap
  }

  generateReferConfig(name, data, hasMultiple, type) {
    if (hasMultiple) {
      return {
        name,
        data,
        type
      }
    }
    return {
      name,
      data,
      type: 'line',
      lineWidth: 0,
      states: {
        hover: {
          lineWidthPlus: 0
        }
      },
      marker: {
        enabled: true,
        lineWidth: 4,
        lineColor: null
      }
    }
  }

  generateBaseChartConfig(perType) {
    const { question: { options } } = this.props
    const type = perType || this.state.type
    const categories = options.map(option => option.value)
    const config = {
      chart: {
        type,
        height: 400
      },
      exporting: {
        enabled: false
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            color: '#000000',
            connectorColor: '#000000',
            format: '<b>{point.name}</b>: {point.percentage:.1f} %'
          }
        },
        column: {
          dataLabels: {
            enabled: true,
            style: {
              // textShadow: '0 0 3px black'
            }
          }
        },
        series: {
          dataLabels: {
            enabled: true
          }
        }
      },
      title: {
        text: ''
      },
      credits: {
        enabled: false
      },
      xAxis: {
        categories
      },
      yAxis: {},
      colors: [
        '#7cb5ec',
        '#90ed7d',
        '#f7a35c',
        '#8085e9',
        '#f15c80',
        '#e4d354',
        '#2b908f',
        '#f45b5b',
        '#91e8e1'
      ]
    }
    return config
  }

  generateChartConfig(perType) {
    const { question } = this.props
    const type = perType || this.state.type
    const config = this.generateBaseChartConfig(perType)
    const hasCalculatorData = (['sort', 'matrix', 'table'].includes(question.type) || type === 'calculator') && this.state.chartData
    const data = hasCalculatorData
      ? this.state.chartData
      : this.generatePerChartData(type)
    const { name } = data
    let series = clone(data.series)
    if (hasCalculatorData) {
      series = this.generateSeriesWithRefer(series, perType)
    }
    config.xAxis.categories = data.categories
    if (question.type === 'matrix') {
      config.chart.type = type === 'calculator' ? 'column' : type
      config.tooltip = {
        shared: true
      }
    } else if (question.type === 'table') {
      config.chart.type = type === 'calculator' ? 'column' : type
    } else if (question.type === 'matrix_radio' || question.type === 'matrix_checkbox') {
      if (type === 'calculator') {
        config.chart.type = 'column'
      } else {
        if (type === 'bar') {
          config.plotOptions.series.stacking = 'percent'
          config.plotOptions.series.dataLabels.format =
            '{point.percentage:.0f}%'
        }
        config.plotOptions.column.stacking = 'percent'
        config.plotOptions.column.dataLabels.format = '{point.percentage:.0f}%'
        config.tooltip = {
          shared: true
        }
        if (type === 'column' || type === 'bar') {
          config.tooltip.pointFormat =
            '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.percentage:.0f}%)<br/>'
        }
      }
    } else {
      config.tooltip = {
        pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b><br/>'
      }
      if (type === 'calculator') {
        config.chart.type = 'column'
      }
    }
    if (type === 'pie') {
      config.tooltip = {
        pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
      }
    }
    config.yAxis = [
      {
        title: {
          text: name
        },
        labels: {
          format: '{value}'
        }
      }
    ]
    config.series = series
    return config
  }

  buildTableData() {
    const token = getToken()
    const {
      question: { type, options, answers, questions },
    } = this.props
    const hasNumericQuestion = (questions||[]).some(
      subQuestion => subQuestion.isNumeric
    )
    const getFormatter = quota => {
      const quotaValue = quota.value
      if (quota.isDate) {
        return item =>
          item[quotaValue] === 'now' ? '至今' : item[quotaValue]
      }
      return null
    }
    let data = []
    const quotas = [
      {
        name: '姓名',
        value: '__name',
        format: 'text',
        fixed: 'left',
        hasFilter: true,
        width: 114
      },
      {
        name: '公司',
        value: '__company',
        format: 'text',
        fixed: 'left',
        hasFilter: true,
        width: 114
      }
    ]
    if (this.isText() || ~['input', 'slider'].indexOf(type)) {
      const quota = {
        name: '答案',
        value: 'answer',
        format: 'text'
      }
      if (this.isTextarea()) quota.isTextarea = true
      quotas.push(quota)
      data = answers
    } else if (type === 'attachment') {
      quotas.push({
        name: '答案',
        value: 'answer',
        formatter: item => {
          if (!item.answer) {
            return <span>未上传</span>
          }
          return (
            <div>
              <i className={generateFileIcon(item.answer)} />
              <span className={styles.filename}>{`${item.answer
                } - ${normalizeUnit(item.fileSize)}`}</span>
              <a
                href={`/api/admin/download/attachment?token=${token.slice(7)}&surveyId=${this.props.survey._id
                  }&questionId=${this.props.question._id}&authorId=${item.author._id
                  }`}
              >
                <span
                  className={classnames(
                    'label label-success',
                    styles.downloadAttachment,
                    'js-pdf-ignore'
                  )}
                >
                  {' '}
                  下载
                </span>
              </a>
            </div>
          )
        }
      })
      data = answers
    } else if (type === 'valuation') {
      quotas.push(
        {
          name: '答案',
          value: 'answer',
          formatter: item => {
            if (!item.answer) {
              return <span>未上传</span>
            }
            return (
              <div>
                <i className={generateFileIcon(item.answer)} />
                <span className={styles.filename}>{`${item.answer
                  } - ${normalizeUnit(item.fileSize)}`}</span>
                <a
                  href={`/api/admin/download/attachment?token=${token.slice(7)}&surveyId=${this.props.survey._id
                    }&questionId=${this.props.question._id}&authorId=${item.author._id
                    }`}
                >
                  <span
                    className={classnames(
                      'label label-success',
                      styles.downloadAttachment,
                      'js-pdf-ignore'
                    )}
                  >
                    {' '}
                    下载
                  </span>
                </a>
              </div>
            )
          }
        }
      )
      data = answers
    } else if (type === 'sort') {
      quotas.shift()
      quotas.shift()
      quotas.push({
        name: '选项',
        value: 'option',
        format: 'text'
      })
      options.forEach((option, index) => {
        quotas.push({
          name: `No. ${index + 1}`,
          value: `No. ${index + 1}`,
          format: 'text'
        })
      })
      quotas.push({
        name: '排名',
        value: 'rank',
        format: 'text'
      })
      const generalRank = !answers
        ? {}
        : Object.keys(answers)
          .map(key => {
            const sum = answers[key].reduce(
              (out, item, index) => (out += item * (index + 1)),
              0
            ) // eslint-disable-line
            const total = answers[key].reduce((out, item) => (out += item), 0) // eslint-disable-line
            return {
              option: key,
              rank: Math.round((sum / total) * 100) / 100
            }
          })
          .sort((fst, snd) => fst.rank - snd.rank)
          .reduce((out, item, index) => {
            out[item.option] = index + 1
            return out
          }, {})
      if (!answers) {
        data = []
      } else {
        data = Object.keys(answers).map(key => {
          const ret = { option: key }
          answers[key].forEach((item, index) => {
            ret[`No. ${index + 1}`] = item
          })
          ret.rank = generalRank[key]
          return ret
        })
      }
    } else if (type === 'table') {
      options.forEach(item => {
        quotas.push({
          name: item.value,
          value: item.value,
          formatter: getFormatter(item),
          format: 'text',
          isNumeric: item.isNumeric
        })
      })
      data = (answers||[]).map(item => {
        return {
          author: item.author,
          ...(item.answer || {})
        }
      })
    } else if (type === 'matrix') {
      quotas.push({
        name: '问题',
        value: '__question',
        format: 'text',
        fixed: 'left',
        hasFilter: true,
        width: 114
      })
      options.forEach(item => {
        quotas.push({
          name: item.value,
          value: item.value,
          formatter: getFormatter(item),
          format: 'text',
          isNumeric: hasNumericQuestion || item.isNumeric
        })
      })
      questions.forEach(subQuestion => {
        ; (answers[subQuestion.value] || []).forEach(item => {
          data.push({
            __question: subQuestion.value,
            author: item.author,
            ...item.answer
          })
        })
      })
    } else if (type === 'matrix_radio' || type === 'matrix_checkbox') {
      quotas.push({
        name: '问题',
        value: '__question',
        format: 'text',
        fixed: 'left',
        hasFilter: true
      })
      options.forEach(option => {
        quotas.push({
          name: option.value,
          value: option.value,
          format: 'text'
        })
      })
      const answerByUser = answers.reduce((out, answer) => {
        const userId = answer.author._id
        out[userId] = out[userId] || []
        out[userId].push(answer)
        return out
      }, {})
      data = Object.keys(answerByUser).reduce((out, userId) => {
        const answerByQuestion = answerByUser[userId].reduce((ret, answer) => {
          const questionTitle = answer.question
          ret[questionTitle] = ret[questionTitle] || []
          ret[questionTitle].push(answer.answer)
          return ret
        }, {})
        const author = answerByUser[userId][0].author
        const list = Object.keys(answerByQuestion).map(key => {
          const ret = { author, __question: key }
          options.forEach(option => {
            if (~answerByQuestion[key].indexOf(option.value)) {
              ret[option.value] = <i className="fa fa-check-square-o" />
            }
          })
          return ret
        })
        return out.concat(list)
      }, [])
    } else if (type === 'fund') {
      quotas.push({
        name: '基金',
        value: 'question',
        formatter: item => {
          return (
            <div>
              {item.question}{' '}
              <button
                onClick={this.getFundId(item)}
                className={classnames(
                  'btn btn-primary btn-xs',
                  styles.actionButton,
                  'js-pdf-ignore'
                )}
              >
                <i className="fa fa-eye" />净值分析
              </button>
            </div>
          )
        }
      })
      quotas.push({
        name: '开始时间',
        value: 'startDate',
        format: 'text'
      })
      quotas.push({
        name: '结束时间',
        value: 'endDate',
        format: 'text'
      })
      quotas.push({
        name: '基金类型',
        value: 'fundNature',
        format: 'text'
      })
      quotas.push({
        name: '投资策略',
        value: 'fundStrategy',
        format: 'text'
      })
      data = (answers || []).map(item => {
        return {
          author: item.author,
          question: item.question,
          startDate: moment(Number(item.startDate)).format('YYYY-MM-DD'),
          endDate:
            item.endDate === 'now'
              ? '至今'
              : moment(Number(item.endDate)).format('YYYY-MM-DD'),
          fundNature: item.fundNature,
          fundType: item.fundType
        }
      })
    } else {
      (options||[]).forEach(option => {
        quotas.push({
          name: option.type === 'other' ? '其他' : option.value,
          value: option.value,
          format: 'text'
        })
      })

      const answerByUserSingle = (answers||[]).reduce((out, answer) => {
        const userId = answer.author._id
        out[userId] = out[userId] || []
        out[userId].push(answer)
        return out
      }, {})
      data = Object.keys(answerByUserSingle).map(key => {
        return answerByUserSingle[key].reduce((out, item) => {
          out.author = item.author
          if (item.answer) {
            out[item.answer] =
              item.answer === '__other__' && item.other ? (
                item.other
              ) : (
                <i className="fa fa-check-square-o" />
              )
          }
          return out
        }, {})
      })
    }
    const antQuotas = quotas.map(quota => {
      const ret = { ...quota, hasSorter: true }
      if (quotas.length <= 9) {
        delete ret.fixed
      }
      return ret
    })
    data = data.map(item => ({
      __name: item.author && item.author.nickname,
      __company: item.author && item.author.company,
      _id: item.author && item.author._id,
      ...item
    }))
    return { quotas: antQuotas, data }
  }

  cancelEditDescription = () => {
    this.setState({ description: 'show' })
  }

  editDescription = () => {
    this.setState({ description: 'edit' })
  }

  updateStatRemarks = () => {
    this.setState({ description: 'show' }, () => {
      const { survey, question } = this.props
      const { descriptionContent } = this.state
      this.props.updateStatRemarks(survey._id, { [question._id]: descriptionContent }, this.props.dispatch)
    })
  }

  renderChart = () => {
    const { question } = this.props
    if (!this.hasChart()) {
      return
    }
    const config = this.generateChartConfig()
    return <Chart options={config} />
  }

  renderPerChart = (perType) => {
    const { question } = this.props
    if (!this.hasPerChart()) {
      return
    }
    const newconfig = this.generateChartConfig(perType)

    return <Chart options={newconfig} />
  }

  render() {
    const { question, index, survey, isPrint } = this.props
    const { type, quota, sampleSelectId, referSelectId, chart, perChart, description, descriptionContent, tableData, samples, referId, chartData } = this.state
    const token = getToken()
    const hasSwitchButton = this.hasSwitchButton()
    const sampleOptions = this.getSelectOptions({
      _id: '全部',
      nickname: '全部',
      company: 'All'
    })
    const refOptions = this.getSelectOptions()
    return (
      <div>
        {((survey.statRemarks && survey.statRemarks[question._id]) || !!description) &&
          (description === 'edit' ? (
            <div className={styles.reportDescription}>
              <div className="inner">
                <WysiwygEditor
                  model={descriptionContent}
                  onModelChange={this.handleModelChange}
                />
              </div>
              <button
                className={classnames(
                  'btn btn-xs btn-primary',
                  styles.descriptionButton,
                  styles.cancel
                )}
                onClick={this.cancelEditDescription}
              >
                取消
              </button>
              <button
                className={classnames(
                  'btn btn-xs btn-primary',
                  styles.descriptionButton
                )}
                onClick={this.updateStatRemarks}
              >
                保存
              </button>
            </div>
          ) : (
            <div className={styles.reportDescription}>
              <div
                className="fr-element fr-view"
                dangerouslySetInnerHTML={{ __html: descriptionContent }}
              />
              <button
                className={classnames(
                  'btn btn-xs btn-primary',
                  styles.descriptionButton,
                  styles.descriptionEditButton
                )}
                onClick={this.editDescription}
              >
                编辑
              </button>
            </div>
          ))}
        <div
          className={classnames(styles.stateItem, 'panel panel-default')}
          key={question._id}
        >
          <div className="panel-heading">
            <div>{`${index + 1}、${question.title}`}</div>
            {question.remark && (
              <div
                className={styles.remark}
                dangerouslySetInnerHTML={{
                  __html: question.remark
                }}
              />
            )}
            <div style={{ float: 'right', display: 'flex' }}>
              <div
                className={styles.panelButtons}
                onClick={this.editDescription}
              >
                <Tooltip id="add-description" placement="top" title='添加备注'>
                  <span>
                    <i className="fa fa-pencil-square-o" />
                  </span>
                </Tooltip>
              </div>
              <div className={styles.panelButtons}>
                {question.type === 'fund' && (
                  <Tooltip id={`download-question-${question._id}`} placement="top" title='下载净值'>
                    <a
                      className={styles.button}
                      href={`/api/admin/stat/${survey._id}/questions/${question._id
                        }/csv?token=${token.slice(7)}&exportField=nets`}
                    >
                      <i className="fa fa-file-zip-o" />
                    </a>
                  </Tooltip>
                )}
                {question.type !== 'attachment' && (
                  <Tooltip id={`download-question-${question._id}`} placement="top" title='下载 CSV'>
                    <a
                      className={styles.button}
                      href={`/api/admin/stat/${survey._id}/questions/${question._id
                        }/csv?token=${token.slice(7)}`}
                    >
                      <i style={{ color: 'white' }} className="fa fa-download" />
                    </a>
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
          <AntTable {...tableData} isPrint={isPrint} />
          {this.hasAnswers() && this.hasChart() && !(!this.hasCalculator() && this.hasPerChart()) && (
            <div className={styles.chart}>
              <div className="row js-pdf-ignore">
                <div className="col-sm-6">
                  {this.hasCalculator() && (
                    <div className={classnames('row', styles.selectContainer)}>
                      <div className={classnames('col-sm-8')}>
                        {
                          this.hasCalculator() &&
                          <Select
                            style={{ width: 200 }}
                            placeholder='请选择样本'
                            mode='multiple'
                            onChange={this.onSampleChange}
                            value={samples}
                          >
                            {
                              sampleOptions.map(item => {
                                let label = `${item.nickname} <${item.company}>`
                                return <Option value={item._id}>{label}</Option>
                              })
                            }
                          </Select>
                        }
                      </div>
                      <div className={classnames('col-sm-4')}>
                        {type === 'calculator' && (
                          <div
                            className={classnames(
                              styles.quotaOptions,
                              'js-pdf-ignore'
                            )}
                          >
                            <Select
                              value={quota}
                              onChange={this.onQuotaChange}
                            >
                              {mathDimensitons.map(dim => <Option value={dim}>{stat[dim]}</Option>)}
                            </Select>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                <div className="col-sm-6">
                  <GetCsvAndPng id={`chart-${question._id}`} chart={chart} />
                  {!this.hasPerChart() && <div className="btn-group">
                    {this.hasCalculator() && (
                      <button
                        className={classnames('btn btn-sm btn-default', {
                          active: type === 'calculator'
                        })}
                        onClick={this.switchChartType('calculator')}
                      >
                        <i className="fa fa-calculator" />
                      </button>
                    )}
                    {this.hasPieChart() && (
                      <button
                        className={classnames('btn btn-sm btn-default', {
                          active: type === 'pie'
                        })}
                        onClick={this.switchChartType('pie')}
                      >
                        <i className="fa fa-pie-chart" />
                      </button>
                    )}
                    {hasSwitchButton && (
                      <button
                        className={classnames('btn btn-sm btn-default', {
                          active: type === 'bar'
                        })}
                        onClick={this.switchChartType('bar')}
                      >
                        <i className="fa fa-bars" />
                      </button>
                    )}
                    {hasSwitchButton && (
                      <button
                        className={classnames('btn btn-sm btn-default', {
                          active: type === 'column'
                        })}
                        onClick={this.switchChartType('column')}
                      >
                        <i className="fa fa-bar-chart" />
                      </button>
                    )}
                    {hasSwitchButton && (
                      <button
                        className={classnames('btn btn-sm btn-default', {
                          active: type === 'line'
                        })}
                        onClick={this.switchChartType('line')}
                      >
                        <i className="fa fa-line-chart" />
                      </button>
                    )}
                  </div>}
                  {this.hasCalculator() && (
                    <div className={styles.referSelectWapper}>
                      <Select
                        style={{ width: 150 }}
                        placeholder='请选择参照'
                        value={referId}
                        onChange={this.onReferChange}
                      >
                        {
                          refOptions.map(item => {
                            let label = `${item.nickname} <${item.company}>`
                            return <Option value={item._id}>{label}</Option>
                          })
                        }
                      </Select>
                    </div>
                  )}
                </div>
              </div>
              <div className={styles.chartWapper} >
                {this.renderChart()}
              </div>
            </div>
          )}
          {this.hasAnswers() && this.hasCalculator() && this.state.chartData && <AntTable {...this.generateCalculatorTableData()} isPrint={isPrint} />}
          {this.hasAnswers() && this.hasPerChart() && (
            <div className={styles.chart}>
              <div className="row">
                <div className={classnames(`col-sm-${isPrint && question.type === 'matrix_radio' ? '12' : '8'}`, styles.chartWapper)} style={{ position: 'relative', top: -30 }}>
                  <div className="row js-pdf-ignore">
                    <div className="col-sm-6 col-sm-offset-6">
                      <div className="btn-group">
                        <GetCsvAndPng id={`per-chart-${question._id}`} chart={perChart} />
                        {this.hasPieChart() && (
                          <button
                            className={classnames('btn btn-sm btn-default', {
                              active: type === 'pie'
                            })}
                            onClick={this.switchChartType('pie')}
                          >
                            <i className="fa fa-pie-chart" />
                          </button>
                        )}
                        {hasSwitchButton && (
                          <button
                            className={classnames('btn btn-sm btn-default', {
                              active: type === 'bar'
                            })}
                            onClick={this.switchChartType('bar')}
                          >
                            <i className="fa fa-bars" />
                          </button>
                        )}
                        {hasSwitchButton && (
                          <button
                            className={classnames('btn btn-sm btn-default', {
                              active: type === 'column'
                            })}
                            onClick={this.switchChartType('column')}
                          >
                            <i className="fa fa-bar-chart" />
                          </button>
                        )}
                        {hasSwitchButton && (
                          <button
                            className={classnames('btn btn-sm btn-default', {
                              active: type === 'line'
                            })}
                            onClick={this.switchChartType('line')}
                          >
                            <i className="fa fa-line-chart" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                  {this.renderPerChart(type)}
                </div>
                <div className={`col-sm-${isPrint && question.type === 'matrix_radio' ? '12' : '4'}`}>
                  <AntTable {...(question && this.generatePerTableData())} yScrollHeight={350} isPrint={isPrint} />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }
}
