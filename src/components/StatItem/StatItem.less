.stateItem {
  table
  :global(.table-responsive) {
    max-height: 400px;
    overflow-y: scroll;
  }
  :global(.ant-table-wrapper) {
    margin-bottom: 0px;
  }
  :global(.panel-heading) {
    color: white;
    background-color:#252b35;
    border-color:#767575
  }
  .panelButtons {
    float: right;
    margin-top: -20px;
    .button {
      cursor: pointer;
      display: inline-block;
      margin-left: 15px;
      color: #333333;
    }
  }
  .filename {
    margin: 0 5px;
  }
  .downloadAttachment {
    cursor: pointer;
  }
  .actionButton {
    float: right;
  }
  .chartWapper {
    margin-top: 10px;
    height: 400px;
  }
}
.table {
  border-bottom: 1px solid #ddd;
  p {
    white-space: pre-wrap;
  }
}

.chart {
  margin-top: 25px;
  position: relative;
  .referSelectWapper {
    float: right;
    margin-right: 10px;
    min-width: 120px;
  }
  :global(.btn-group) {
    float: right;
    margin-right: 10px;
  }
  .selectContainer {
    margin-left: -5px;
    margin-right: -5px;
  }
}

.remark {
  margin-top: 8px;
  font-size: 12px;
}

.reportDescription {
  position: relative;
  margin-bottom: 35px;
  :global(.fr-element) {
    p {
      white-space: normal !important;
      word-wrap: break-word !important;
      word-break: break-all !important;
    }
  }
  :global(.description-wrapper-relative) {
    display: block !important;
    position: relative !important;
    margin-top: 50px !important;
    margin-bottom: 10px !important;
    height: 160px !important;
  }
  :global(.description-toolbar-absolute) {
    position: absolute !important;
    top: -40px !important;
    border-radius: 3px !important;
    background: #f3f3f3 !important;
    border: 1px solid #e3e3e3 !important;
  }
  .descriptionButton {
    position: absolute;
    right: 0;
    top: 117px;
    z-index: 3;
    background-color: orange;
    outline: none;
  }
  .cancel {
    right: 39px;
    color: #fff;
    background-color: #aaa;
  }
  .descriptionContent {
    word-wrap: break-word;
    img {
      display: block;
      margin: 0 auto;
    }
    margin-bottom: 10px;
  }
  .descriptionEditButton {
    display: none;
    background-color: orange !important;
    outline: none;
  }
  &:hover {
    .descriptionEditButton {
      display: block;
      right: 3px;
      top: 3px;
    }
  }
}
:global(.btn-group .btn-default){
  color: white !important;
  background-color: #323232 !important;
  border-color:#707070;
}
@media print {
  .stateItem {
    :global(.ant-table-body) {
      max-height: none !important;
    }
  }
  .panelButtons {
    display: none;
  }
  :global(.js-pdf-ignore) {
    display: none;
  }
  :global(.table-txt-content) div {
    overflow: initial !important;
    white-space: initial !important;
  }
}
