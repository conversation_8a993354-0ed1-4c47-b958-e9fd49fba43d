import React from 'react'
import _ from 'lodash'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Tooltip,
  List,
  Popconfirm,
} from 'antd'
import SelectManagerModal from '@/components/SelectManagerModal'
import t from '@/utils/t'
import classNames from 'classnames'
import styles from './style.less'

const FundListFormItem = ({
  onChange,
  value,
  title,
  addTitle,
  hideEmpty,
} : {
  onChange?: any,
  value?: any,
  title?: string,
  addTitle?: string,
  hideEmpty?: boolean,
}) => {
  const data = value || []
  const handleSelectFunds = funds => {
    const newData = funds.map(item => {
      return { id:item._id, name: item.name }
    }).concat(data)
    onChange(_.uniqBy(newData, 'id'))
  }
  const handleRemoveFund = fund => () => {
    const newData = data.filter(item => item.id !== fund.id)
    onChange(newData)
  }
  return (
    <List
      className={classNames(styles.reportItem, { [styles.hideEmpty] : hideEmpty})}
      size="small"
      header={<div>{ title || '基金经理' }({data.length})</div>}
      footer={
        <SelectManagerModal style={{ width: '100%' }} onChange={handleSelectFunds}>
          <Button style={{ width: '100%', marginTop: 10 }} type="dashed" icon={<PlusOutlined />}>
            { addTitle || '添加基金经理' }
          </Button>
        </SelectManagerModal>
      }
      bordered
      dataSource={data || []}
      renderItem={(item) => {
        const actions = [
          <Popconfirm
            title={`${t('portfolio.delTip')}${item.name}${t('portfolio.questionEnd')}？`}
            onConfirm={handleRemoveFund(item)}
            onCancel={() => {}}
            okText={t('portfolio.confirm')}
            cancelText={t('portfolio.cancel')}
          >
            <Tooltip title="删除">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>,
        ]
        return (
          <List.Item
            actions={actions}
          >
            <span>{item.name}</span>
          </List.Item>
        )
      }}
    />
  )
}

export default FundListFormItem
