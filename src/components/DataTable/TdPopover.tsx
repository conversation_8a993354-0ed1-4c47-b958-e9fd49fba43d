import React, { Component } from 'react'
import { Popover, Tooltip } from 'antd'
import styles from './TdPopover.less'

interface TdPopoverProps {
  children?: any;
  text: string;
  isHtml?: boolean;
  canBePrinted?: boolean;
}
export default class TdPopover extends Component<TdPopoverProps> {
  state = {
    show: false,
  };

  close = () => {
    this.setState({ show: false })
  };

  open = () => {
    this.setState({ show: true })
  };

  renderContent = (text: string) => {
    const { isHtml } = this.props
    if (isHtml) {
      return <div dangerouslySetInnerHTML={{ __html: text }} />
    }
    return <div className={styles.contentText}>{text}</div>
  };

  render() {
    const { children, text, canBePrinted } = this.props
    const content = this.renderContent(text)
    if (canBePrinted) {
      return <div className="table-txt-content">{children}</div>
    }
    return (
      <div onClick={this.open}>
        <Tooltip placement="top" title={text.length > 300 ? '点击查看' : content}>
          <Popover content={content} trigger="click" placement="right">
            <div className="table-txt-content">{children}</div>
          </Popover>
        </Tooltip>
      </div>
    )
  }
}
