import React from 'react'
import moment from 'moment'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Popover, Table } from 'antd'

export default ({ managers }) => {
  const columns = [{
    title: '姓名',
    dataIndex: 'name',
  }, {
    title: '起始日期',
    dataIndex: 'startDate',
  }, {
    title: '结束日期',
    dataIndex: 'endDate',
  }]
  if (!managers || !managers.length) {
    return '-'
  }
  const managerData = managers.map(item => {
    return {
      name: item.name,
      startDate: moment(new Date(item.startDate)).format('YYYY-MM-DD'),
      endDate: item.endToNow ? '至今' : moment(new Date(item.endDate)).format('YYYY-MM-DD'),
      endDateTs: item.endToNow ? Date.now() : +moment(new Date(item.endDate)),
    }
  }).sort((fst, snd) => snd.endDateTs - fst.endDateTs)
  return (
    <>
      <span>{managerData[0].name}</span>
      <Popover content={
        <Table
          columns={columns}
          dataSource={managerData}
          pagination={false}
          size="small"
        />
      } placement="right">
        <span style={{ marginLeft: '5px' }}>
          <QuestionCircleOutlined />
        </span>
      </Popover>
    </>
  )
}
