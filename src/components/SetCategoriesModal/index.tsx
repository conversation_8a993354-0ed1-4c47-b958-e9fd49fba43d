import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { Modal, Select, Button } from 'antd'

export default class SetCategoriesModal extends Component {
  static propTypes = {
    submit: PropTypes.func.isRequired,
    show: PropTypes.bool.isRequired,
    close: PropTypes.func.isRequired,
    categories: PropTypes.array.isRequired,
    survey: PropTypes.object.isRequired
  }
  constructor(props) {
    super(props)
    this.state = {
      category: props.survey.category ? [props.survey.category] : []
    }
  }

  handleScaleChange = values => {
    if (!values.length) {
      this.setState({ category: [] })
    } else {
      this.setState({ category: [values[values.length - 1]] })
    }
  }

  submit = () => {
    this.props.submit({ category: this.state.category[0] })
    this.props.close()
  }

  render() {
    const { categories } = this.props
    const { category } = this.state
    const Option = Select.Option
    return (
      <div>
        <Modal
          visible={this.props.show}
          onCancel={this.props.close}
          title='分类设置'
          footer={[
            <Button onClick={this.props.close}>取消</Button>,
            <Button onClick={this.submit}>确定</Button>
          ]}
        >
          <div>
            <div className="form-group">
              <label htmlFor="category">
                分类
                {/* <small>（可以对同一分类的问卷进行交叉分析）</small> */}
              </label>
              <Select
                mode="tags"
                placeholder='请选择或输入一个新的分类，按回车确认'
                onChange={this.handleScaleChange}
                value={category}
                style={{ width: '100%' }}
              >
                {categories.map(item => <Option key={item}>{item}</Option>)}
              </Select>
            </div>
          </div>
        </Modal>
      </div>
    )
  }
}
