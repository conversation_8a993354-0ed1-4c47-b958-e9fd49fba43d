import React, { useState } from 'react'
import { Modal, Button, Space, Tooltip, Table } from 'antd'
import { useBoolean } from '@umijs/hooks'
import buildTableColumn from '@/utils/buildTableColumn'

const SelectedFundButton = ({
  selectedRows,
  onUnselectAll,
  isManager,
}) => {
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const factorColumns = [{
    title: '收益类',
    dataIndex: 'incomeFactorScore',
    format: 'zeroNotNumber',
  }, {
    title: '风险类',
    dataIndex: 'riskFactorScore',
    format: 'zeroNotNumber',
  }, {
    title: '归因类',
    dataIndex: 'attributionFactorScore',
    format: 'zeroNotNumber',
  }, {
    title: '策略类',
    dataIndex: 'strategyFactorScore',
    format: 'zeroNotNumber',
  }, {
    title: '基金公司类',
    dataIndex: 'companyFactorScore',
    format: 'zeroNotNumber',
    width: 100,
  }, {
    title: '基金经理类',
    dataIndex: 'managerFactorScore',
    format: 'zeroNotNumber',
    width: 100,
  }, {
    title: '持仓类',
    dataIndex: 'positionFactorScore',
    format: 'zeroNotNumber',
  }, {
    title: '综合得分',
    dataIndex: 'totalFactorScore',
    format: 'zeroNotNumber',
    width: 90,
  }, {
    title: '综合得分排名',
    dataIndex: 'totalFactorScoreRank',
    format: 'zeroNotNumber',
    width: 120,
  }]
  const fundColumns = [{
    title: '名称',
    dataIndex: 'name',
    fixed: 'left',
    width: 150,
  }, {
    title: '成立时间',
    dataIndex: 'startDate',
    format: 'date',
    align: 'right',
    width: 100,
  }, {
    title: '净值日期',
    dataIndex: 'navEndDate',
    align: 'right',
    format: 'date',
    width: 100,
  },{
    title: '基金经理',
    dataIndex: 'managers',
    format: 'curManagerList',
  }, ...factorColumns]
  const managerColumns = [{
    title: '姓名',
    dataIndex: 'name',
    fixed: 'left',
    width: 80,
  },
  {
    title: '基金公司',
    dataIndex: 'company_abbr_name',
    format: 'text',
    sorter: true,
    width: 100,
  }, ...factorColumns]
  const columns = (isManager ? managerColumns : fundColumns).map(item => {
    const ret = {
      ...item,
      hasSorter: item.dataIndex !== 'managers',
      width: item.width || 80,
    }
    return buildTableColumn(ret)
  })
  return (
    <div>
      <Button
        ghost
        type="primary"
        size="small"
      >
        <Space>
          <Tooltip title="点击查看已选列表">
            <span onClick={setVisibleTrue}>已选({selectedRows.length})</span>
          </Tooltip>
          <a onClick={onUnselectAll}>清空选择</a>
        </Space>
      </Button>
      <Modal
        title={
          <>
            <span>已选{isManager ? '基金经理' : '基金'}({selectedRows.length})</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={1200}
        footer={[
          <Button type="primary" onClick={() => {
            setVisibleFalse()
          }}>
            关闭
          </Button>,
        ]}
      >
        <Table
          bordered
          columns={columns}
          dataSource={selectedRows}
          size="small"
          scroll={{ x: 700 }}
        />
      </Modal>
    </div>
  )
}

export default SelectedFundButton
