import PropTypes from 'prop-types'
import React, { Component } from 'react'
import TagsInput from './ReactTagsInput'
import classnames from 'classnames'
import { isEmail } from 'validator'
import { <PERSON><PERSON>, Button } from 'antd'
import WysiwygEditor from '@/components/WysiwygEditor'
export default class InviteModal extends Component {
  static propTypes = {
    children: PropTypes.object.isRequired,
    className: PropTypes.string,
    inviteJoin: PropTypes.func.isRequired,
    inviteResult: PropTypes.object,
    inviteError: PropTypes.object,
    resetState: PropTypes.func.isRequired,
    users: PropTypes.array,
    currentUser: PropTypes.object,
    description: PropTypes.string
  }

  constructor(props) {
    super(props)
    this.state = {
      show: false,
      tags: [],
      error: {},
      subject: this.getSubjectFromProps(props) || '',
      content: props.description || ''
    }
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.inviteResult && this.state.show) {
      this.close()
    }
  }

  componentWillUnmount() {
    this.setState({
      error: {}
    })
    this.props.resetState({ inviteError: null })
  }

  onSubjectChange = event => {
    this.setState({ subject: event.target.value })
  }

  onContentChange = content => {
    this.setState({ content })
  }

  getSubjectFromProps = (props) => {
    const { currentUser } = props
    const company = currentUser.company
    return `来自${company}的邀请`
  }

  close = () => {
    this.setState({ show: false, error: {}, tags: [] })
    this.props.resetState({ inviteError: null })
  }

  open = () => {
    this.setState({ show: true })
  }

  handleChange = (tags) => {
    this.setState({
      tags: tags.reduce((out, item) => out.concat(item.split(',')), [])
    })
  }

  isEmail = (input) => {
    if (!input) {
      return false
    }
    const emails = input.split(',')
    // return emails.every(email => isEmail(email))
  }

  submit = () => {
    const { users } = this.props
    const emails = (users || []).map(user => user.email)
    const { tags, subject, content } = this.state
    const isCc = this.refs.ccInput.checked
    const existEmail = []
    tags.forEach(tag => {
      if (~emails.indexOf(tag)) {
        existEmail.push(tag)
      }
    })
    if (existEmail.length) {
      this.setState({
        error: {
          message: `${existEmail.join(',')}已经在您的列表中`
        }
      })
      return
    }
    if (!tags.length) {
      this.setState({
        error: {
          message: '请输入邮箱，按回车确认'
        }
      })
      return
    }
    this.props.inviteJoin(tags, subject, content, isCc)
  }

  render() {
    const styles = require('./InviteModal.less')
    const { children, className, inviteError } = this.props
    const { tags, error, content, subject } = this.state
    const inputClass = !tags.length
      ? styles.tagsInputBig
      : styles.tagsInputSmall
    const placeholder = !tags.length
      ? '请输入邮箱，按回车确认'
      : ''

    return (
      <div className={className}>
        <div onClick={this.open}>{children}</div>
        <Modal
          className={styles.inviteModal}
          visible={this.state.show}
          onCancel={this.close}
          title='邀请新用户'
          footer={[
            <div>
              <div className={styles.cc}>
                <input type="checkbox" ref="ccInput" />
                &nbsp;
                <span className={styles.des}>
                  是否将邀请邮件发送到我的邮箱
                </span>
              </div>
              <Button onClick={this.submit}>
                保存
              </Button>
            </div>
          ]}
        >
          <div>
            <div className="form-group">
              <label>发送邮箱</label>
              <TagsInput
                onlyUnique
                validate={this.isEmail}
                value={this.state.tags}
                onChange={this.handleChange}
                tagProps={{
                  className: classnames('react-tagsinput-tag', styles.inputTag),
                  classNameRemove: classnames(
                    'react-tagsinput-remove',
                    styles.inputTagRemove
                  )
                }}
                inputProps={{
                  placeholder: placeholder,
                  className: classnames('react-tagsinput-input', inputClass)
                }}
              />
            </div>
            <div className="form-group">
              <label>邮件主题</label>
              <input value={subject} onChange={this.onSubjectChange} placeholder='请输入邮件主题' className="form-control" />
            </div>
            <div className="form-group">
              <label>邮件内容</label>
              <WysiwygEditor
                config={{
                  toolbarInline: false,
                  charCounterCount: false,
                  toolbarSticky: true,
                  placeholderText: '请输入' + '...',
                  heightMin: 100,
                  heightMax: 260,
                  toolbarButtons: [
                    'bold',
                    'italic',
                    'underline',
                    'strikeThrough',
                    '|',
                    'fontSize',
                    'color',
                    '|',
                    'paragraphFormat',
                    'align',
                    'formatOL',
                    'formatUL',
                    'outdent',
                    'indent',
                    'quote',
                    'insertLink'
                  ]
                }}
                model={content}
                onModelChange={this.onContentChange}
              />
            </div>
          </div>
        </Modal>
      </div>
    )
  }
}
