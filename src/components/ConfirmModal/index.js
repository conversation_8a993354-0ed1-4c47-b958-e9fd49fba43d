import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { Modal, Button } from 'antd'
export default class ConfirmModal extends Component {
  static propTypes = {
    submit: PropTypes.func.isRequired,
    children: PropTypes.object,
    itemDeleted: PropTypes.bool,
    className: PropTypes.string,
    message: PropTypes.string,
    title: PropTypes.string,
    showModal: PropTypes.bool,
    close: PropTypes.func
  }

  state = {
    show: false
  }

  close = () => {
    this.setState({ show: false })
  }

  open = () => {
    this.setState({ show: true })
  }

  submit = () => {
    this.props.submit()
    this.close()
  }

  hasChildren = () => this.props.showModal === undefined

  render() {
    const styles = require('./ConfirmModal.less')
    const { children, className, message, title, showModal, close } = this.props
    const { show } = this.state
    const showChildren = this.hasChildren()
    return (
      [
        showChildren ? <div key={`${title}-children`} className={className} onClick={this.open}>{children}</div> : null,
        <Modal
          key={`${title}-modal`}
          title={title || '确认删除'}
          width={800}
          visible={showChildren ? show : showModal}
          onCancel={showChildren ? this.close : close}
          footer={[
            <Button key="cancel" onClick={showChildren ? this.close : close}>取消</Button>,
            <Button key="confirm" onClick={this.submit}>保存</Button>
          ]}
        >
          <span className={styles.confirmMessage}>
            <i className="fa fa-exclamation-triangle" />{' '}{message || '删除后将不可恢复，确认删除吗'}
          </span>
        </Modal>
      ]
    )
  }
}
