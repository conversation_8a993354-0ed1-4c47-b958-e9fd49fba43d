import React, { PureComponent, Fragment } from 'react'
import { PlusOutlined, CheckOutlined, CloseOutlined, EditOutlined, DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import {
  Table,
  Button,
  Input,
  message,
  Popconfirm,
  Divider,
  DatePicker,
  InputNumber,
  Tooltip,
  Select,
  Cascader,
  Space,
} from 'antd'
import isEqual from 'lodash/isEqual'
import t from '@/utils/t'
import { getValueRangeDesc } from '@/components/FilterPanel/filterPanelHelper'
import styles from './style.less'

const Option = Select.Option

class TableForm extends PureComponent {
  cacheOriginData = {};

  constructor(props) {
    super(props)
    const index = props.value && props.value.length
    this.state = {
      data: props.value || [],
      value: props.value,
      index,
    }
    this.index = index
  }

  static getDerivedStateFromProps(nextProps, preState) {
    if (isEqual(nextProps.value, preState.value)) {
      return null
    }
    return {
      data: nextProps.value,
      value: nextProps.value,
    }
  }

  getRowByKey(key, newData) {
    const { data } = this.state
    return (newData || data).filter(item => item.key === key)[0]
  }

  toggleEditable = (e, key) => {
    e.preventDefault()
    const { data } = this.state
    const newData = data.map(item => ({ ...item }))
    const target = this.getRowByKey(key, newData)
    if (target) {
      // 进入编辑状态时保存原始数据
      if (!target.editable) {
        this.cacheOriginData[key] = { ...target }
      }
      target.editable = !target.editable
      this.setState({ data: newData })
    }
  };

  newMember = () => {
    const { data } = this.state
    const newData = data.map(item => ({ ...item }))
    newData.push({
      ...this.props.defaultNewValues,
      key: `NEW_TEMP_ID_${Date.now()}`,
      editable: true,
      isNew: true,
    })
    this.index += 1
    this.setState({ data: newData })
  };

  remove(key) {
    const { data } = this.state
    const { onChange } = this.props
    const newData = data.filter(item => item.key !== key)
    this.setState({ data: newData })
    onChange(newData)
  }

  handleKeyPress(e, key) {
    if (e.key === 'Enter') {
      this.saveRow(e, key)
    }
  }

  handleFieldChange = (value, fieldName, key, refOption) => {
    const { data } = this.state
    const newData = data.map(item => ({ ...item }))
    const target = this.getRowByKey(key, newData)
    if (target) {
      target[fieldName] = value
      if (refOption && !refOption.disableRef) {
        target._refOption = refOption
      }
      this.setState({ data: newData })
    }
  };

  saveRow(e, key, index) {
    e.persist()
    if (this.clickedCancel) {
      this.clickedCancel = false
      return
    }
    const target = this.getRowByKey(key) || {}
    const { validateRow } = this.props
    const validateResult = validateRow && validateRow(target, index)
    if (validateResult) {
      message.error(validateResult)
      e.target.focus()
      return
    }
    delete target.isNew
    delete target.editable
    const { data } = this.state
    const { onChange } = this.props
    onChange(data)
    const newData = data.map(item => ({ ...item }))
    this.setState({ data: newData })
  }

  cancel(e, key) {
    this.clickedCancel = true
    e.preventDefault()
    const { data } = this.state
    const newData = data.map(item => ({ ...item }))
    const target = this.getRowByKey(key, newData)
    if (this.cacheOriginData[key]) {
      Object.assign(target, this.cacheOriginData[key])
      delete this.cacheOriginData[key]
    }
    target.editable = false
    this.setState({ data: newData })
    this.clickedCancel = false
  }

  renderActionItem(text, icon) {
    const { iconAction } = this.props
    if (!iconAction) {
      return text
    }
    const getIconComponent = (iconName) => {
      if (iconName === 'check') {
        return <CheckOutlined/>
      } else if (iconName === 'delete') {
        return <DeleteOutlined/>
      } else if (iconName === 'edit') {
        return <EditOutlined/>
      } else if (iconName === 'close') {
        return <CloseOutlined/>
      }
      return <span/>
    }
    return (
      <Tooltip title={text}>
        {getIconComponent(icon)}
      </Tooltip>
    )
  }

  render() {
    const columns = [
      ...this.props.tableColumns.map(col => {
        return {
          ...col,
          render: (text, record, index) => {
            if (col.render) {
              return col.render(record, col, this.handleFieldChange)
            }
            if (record.editable) {
              if (col.format === 'RangePicker') {
                return (
                  <DatePicker.RangePicker
                    value={text}
                    onChange={dates => this.handleFieldChange(dates, col.key, record.key)}
                  />
                )
              }
              if (col.format === 'InputNumber') {
                return (
                  <InputNumber
                    value={text}
                    formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => value.replace(/\$\s?|(,*)/g, '')}
                    onChange={value => this.handleFieldChange(value, col.key, record.key)}
                  />
                )
              }
              if (col.format === 'DatePicker') {
                return (
                  <DatePicker
                    value={text}
                    onChange={value => this.handleFieldChange(value, col.key, record.key)}
                  />
                )
              }
              if (col.format === 'Select' || col.format === 'LogicalOperatorSelect') {
                if (col.format === 'LogicalOperatorSelect' && index === 0) {
                  return null
                }
                let selectOptions = col.options || []
                // if (col.dataIndex === 'reportDate' && col.dateType === 'factor') {
                //   const factorValues = record.factor || []
                //   const factorCode = factorValues[factorValues.length - 1]
                //   if (!factorCode) {
                //     selectOptions = []
                //   } else if (factorCode.includes('_Q_')) {
                //     selectOptions = selectOptions.filter(item => {
                //       const suffix = item.dataIndex.slice(-4)
                //       return ['0331', '0630', '0930', '1231'].includes(suffix)
                //     })
                //   } else if (factorCode.includes('_H_')) {
                //     selectOptions = selectOptions.filter(item => {
                //       const suffix = item.dataIndex.slice(-4)
                //       return ['0630', '1231'].includes(suffix)
                //     })
                //   }
                // }
                return (
                  <Select
                    showSearch
                    placeholder={`请选择${col.title}`}
                    style={{
                      // marginTop: '5px',
                      width: '90%',
                    }}
                    value={text}
                    filterOption={(input, option) => {
                      const optionData = selectOptions.find(item => item.dataIndex === option.value) || {}
                      return (`${optionData.title}${optionData.dataIndex}`).toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }}
                    onChange={value => this.handleFieldChange(value, col.key, record.key, selectOptions.find(op => op.dataIndex === value))}
                  >
                    {selectOptions.map(item => {
                      let optionDiv = item.title
                      if (col.optionDisplayRender) {
                        optionDiv = col.optionDisplayRender(item.dataIndex)
                      } else if (item.tooltip) {
                        optionDiv = (
                          <Tooltip title={item.tooltip} placement="right">
                            <div style={{ width: '100%' }}>{optionDiv}</div>
                          </Tooltip>
                        )
                      }
                      return (
                        <Option value={item.dataIndex}>
                          {optionDiv}
                        </Option>
                      )
                    })}
                  </Select>
                )
              }
              if (col.format === 'NumberRange') {
                const rangeValues = record[col.key] || []
                return (
                  <div className={styles.numberRange}>
                    <InputNumber
                      defaultValue={rangeValues[0]}
                      placeholder="下限"
                      onChange={val => this.handleFieldChange([val, rangeValues[1]], col.key, record.key)}
                    />
                    <span style={{ display: 'inline-block', width: '24px', textAlign: 'center' }}>-</span>
                    <InputNumber
                      defaultValue={rangeValues[1]}
                      placeholder="上限"
                      onChange={val => this.handleFieldChange([rangeValues[0], val], col.key, record.key)}
                    />
                  </div>
                )
              }
              if (col.format === 'Cascader') {
                return (
                  <Cascader
                    showSearch
                    options={col.options}
                    expandTrigger="hover"
                    value={text}
                    displayRender={(label) => label[label.length - 1]}
                    onChange={value => this.handleFieldChange(value, col.key, record.key, col.options.find(op => op.dataIndex === value))}
                  />
                )
              }
              return (
                <Input
                  value={text}
                  autoFocus
                  onChange={e => this.handleFieldChange(e.target.value, col.key, record.key)}
                  onKeyPress={e => this.handleKeyPress(e, record.key)}
                  placeholder={col.title}
                />
              )
            }
            if (!text) {
              return text
            }
            if (col.format === 'RangePicker') {
              return `${text[0].format('YYYY-MM-DD')} ~ ${text[1].format('YYYY-MM-DD')}`
            } else if (col.format === 'DatePicker') {
              return text.format('YYYY-MM-DD')
            } else if (col.format === 'Select' || col.format === 'LogicalOperatorSelect') {
              const option = col.options.find(item => item.dataIndex === text) || {}
              if (col.format === 'LogicalOperatorSelect' && index === 0) {
                return null
              }
              return col.optionDisplayRender ? col.optionDisplayRender(option.dataIndex) : option.title
            } else if (col.format === 'NumberRange') {
              let refDataIndex
              if (col.refColumn) {
                const refColumn = this.props.tableColumns.find(item => item.dataIndex === col.refColumn) || {}
                refDataIndex = record[refColumn.dataIndex]
              }
              return getValueRangeDesc(text, record._refOption || {}, refDataIndex)
            } else if (col.format === 'Cascader') {
              const option = col.refOptions.find(item => item.dataIndex === text[text.length - 1]) || {}
              return option.title
            }
            return text
          },
        }
      }),
      {
        title: t('component.actions'),
        key: 'action',
        align: 'center',
        width: this.props.iconAction ? 60 : undefined,
        render: (text, record, index) => {
          if (!this.props.isEdditing) {
            return false
          }
          if (record.editable) {
            if (record.isNew) {
              return (
                <span>
                  <a onClick={e => this.saveRow(e, record.key, index)}>
                    {this.renderActionItem(t('component.save'), 'check')}
                  </a>
                  <Divider type="vertical" />
                  <Popconfirm
                    title={t('component.confirmDeleteRow')}
                    onConfirm={() => this.remove(record.key)}
                  >
                    <a>{this.renderActionItem(t('component.delete'), 'delete')}</a>
                  </Popconfirm>
                </span>
              )
            }
            return (
              <span>
                <a onClick={e => this.saveRow(e, record.key, index)}>
                  {this.renderActionItem(t('component.save'), 'check')}
                </a>
                <Divider type="vertical" />
                <a onClick={e => this.cancel(e, record.key)}>
                  {this.renderActionItem(t('component.cancel'), 'close')}
                </a>
              </span>
            )
          }
          return (
            <span>
              <a onClick={e => this.toggleEditable(e, record.key)}>
                {this.renderActionItem(t('component.edit'), 'edit')}
              </a>
              <Divider type="vertical" />
              <Popconfirm
                title={t('component.confirmDeleteRow')}
                onConfirm={() => this.remove(record.key)}
              >
                <a>{this.renderActionItem(t('component.delete'), 'delete')}</a>
              </Popconfirm>
            </span>
          )
        },
      },
    ]

    const { data } = this.state
    const { newItemText, hideEmptyTable } = this.props
    return (
      <Fragment>
        {(data.length !== 0 || !hideEmptyTable) &&
        <Table
          bordered
          columns={columns}
          dataSource={data}
          pagination={false}
          rowClassName={record => (record.editable ? styles.editable : '')}
          size={this.props.size}
        />}
        {this.props.isEdditing && (
          <Button style={{ width: '100%', marginTop: 10 }} type="dashed" onClick={this.newMember} icon={<PlusOutlined />}>
            {newItemText || t('component.addNewRow')}
          </Button>
        )}
      </Fragment>
    )
  }
}

export default TableForm
