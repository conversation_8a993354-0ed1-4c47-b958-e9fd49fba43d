import PropTypes from 'prop-types'
import React, { Component } from 'react'
import shallowEqualArrays from '@/utils/shallowEqualArrays'
import { Modal, Select, Button } from 'antd'

export default class EditTagsModal extends Component {
  static propTypes = {
    children: PropTypes.object.isRequired,
    save: PropTypes.func.isRequired,
    items: PropTypes.array.isRequired,
    selectedItems: PropTypes.array.isRequired,
    id: PropTypes.string.isRequired,
    hideTags: PropTypes.bool,
  }

  state = {
    show: false,
    tags: this.props.selectedItems
  }

  componentWillReceiveProps(nextProps) {
    if (
      !shallowEqualArrays(nextProps.selectedItems || [], this.props.selectedItems || [])
    ) {
      this.setState({ tags: nextProps.selectedItems })
    }
  }

  onChange = tags => {
    this.setState({ tags })
  }

  close = () => {
    this.setState({ show: false })
  }

  open = () => {
    this.setState({ show: true })
  }

  save = () => {
    this.close()
    const { tags } = this.state
    this.props.save(tags)
  }

  render() {
    const { children, items, t } = this.props
    const { tags } = this.state
    return (
      <div style={{ display: 'inline-block' }}>
        <div onClick={this.open}>{children}</div>
        <Modal
          visible={this.state.show}
          onCancel={this.close}
          title='编辑标签'
          footer={[
            <Button onClick={this.save}>保存</Button>
          ]}
        >
          <div>
            <Select
              mode="tags"
              placeholder='请选择或输入一个新的标签，按回车确认'
              onChange={this.onChange}
              value={tags}
              style={{ width: '100%' }}
            >
              {items.map(item => <Select.Option key={item}>{item}</Select.Option>)}
            </Select>
          </div>
        </Modal>
      </div>
    )
  }
}
