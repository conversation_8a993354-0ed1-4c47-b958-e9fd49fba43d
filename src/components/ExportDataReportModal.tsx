import React, { useState } from 'react'
import moment from 'moment'
import { connect } from 'dva'
import { Modal, Button, Form, Radio, DatePicker } from 'antd'
import { FileExcelOutlined } from '@ant-design/icons'
import { getToken } from '@/utils/utils'
import SearchSelect from '@/components/SearchSelect'

const { RangePicker } = DatePicker

interface Values {
  type: string
  dateRange: []
}

interface DataReportFormProps {
  initialValues: any
  dateRanges: any
  visible: boolean
  onCreate: (values: Values) => void
  onCancel: () => void
}

const DataReportForm: React.FC<DataReportFormProps> = ({
  initialValues,
  dateRanges,
  visible,
  onCreate,
  onCancel,
  benchmarkList,
}) => {
  const [form] = Form.useForm()
  // console.log(dateRanges)
  return (
    <Modal
      visible={visible}
      title="导出数据分析报告"
      okText="确定"
      cancelText="取消"
      onCancel={onCancel}
      onOk={() => {
        form
          .validateFields()
          .then(values => {
            onCreate(values)
          })
          .catch(info => {
            console.log('Validate Failed:', info)
          })
      }}
    >
      <Form
        form={form}
        layout="vertical"
        name="form_in_modal"
        initialValues={initialValues}
      >
        <Form.Item name="type" label="报告类型" className="collection-create-form_last-form-item">
          <Radio.Group>
            <Radio value="stock">权益</Radio>
            <Radio value="convtBond">转债</Radio>
            <Radio value="bond">固收</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item name="dateRange" label="时间范围">
          <RangePicker
            ranges={dateRanges}
          />
        </Form.Item>
        <Form.Item name="benchmarkId" label="业绩基准">
          <SearchSelect
            placeholder="请选择基准"
            options={benchmarkList.map(item => {
              return {
                title: item.name,
                dataIndex: item._qutkeId === '000300.SH' ? item._qutkeId : item._id,
              }
            })}
            width="150px"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

const DataReportFormWrapper = connect(
  ({
    manager,
  }: {
    manager: any;
  }) => ({
    benchmarkList: manager.benchmarkList,
  }),
)(DataReportForm)

const ExportDataReportModal = ({ fund }) => {
  const startDate = moment(fund.navStartDate)
  const endDate = moment(fund.navEndDate)
  const ranges = (fund.managers || []).reduce((out, manager) => {
    const startMgtDate = moment(manager.startDate)
    const endMgtDate = moment(manager.endToNow ? new Date : manager.endDate)
    const key = `${manager.name}(${startMgtDate.format('YYYY/MM/DD')}-${endMgtDate.format('YYYY/MM/DD')})`
    out[key] = [startMgtDate, endMgtDate]
    return out
  }, {})
  const [visible, setVisible] = useState(false)

  const onCreate = (values: any) => {
    const { type, dateRange, benchmarkId } = values
    const startDate = dateRange[0].format('YYYYMMDD')
    const endDate = dateRange[1].format('YYYYMMDD')
    const token = getToken()
    const href = `/api/products/${fund._id}/download/mutualreport?type=${type}&startDate=${startDate}&endDate=${endDate}&benchmarkId=${benchmarkId}&token=${token.slice(7)}`
    window.open(href)
    setVisible(false)
  }

  const showModal = () => {
    setVisible(true)
  }

  return (
    <>
      <Button size="small" icon={<FileExcelOutlined />} onClick={showModal}>
        数据分析报告
      </Button>
      <DataReportFormWrapper
        initialValues={{
          type: 'stock',
          dateRange: [startDate, endDate],
          benchmarkId: '000300.SH',
        }}
        dateRanges={ranges}
        visible={visible}
        onCreate={onCreate}
        onCancel={() => {
          setVisible(false)
        }}
      />
    </>
  )
}

export default ExportDataReportModal
