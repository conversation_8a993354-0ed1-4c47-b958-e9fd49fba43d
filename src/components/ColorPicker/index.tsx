import React from 'react'
import { SketchPicker } from 'react-color'
import { Popover } from 'antd'
import styles from './style.less'

const ColorPicker = ({
  value,
  onChange,
  children,
}: {
  value?: string;
  onChange?: any;
  children?: any;
}) => {
  const content = children || (
    <div className={styles.content}>
      <div className={styles.colorBar} style={{ background: value }}/>
    </div>
  )
  return (
    <Popover
      content={
        <SketchPicker
          disableAlpha
          color={value}
          onChangeComplete={(color: any) => onChange && onChange(color.hex)}
        />
      }
    >
      {content}
    </Popover>
  )
}

export default ColorPicker
