import React from 'react'
import _ from 'lodash'
import Echart from '@/components/Chart/Echarts'

const getChartOptions = (data, name, format, visualDimension) => {
  const formatUtil = Echart.echarts.format
  const levelOption = [
    {
      itemStyle: {
        normal: {
          borderColor: '#777',
          borderWidth: 0,
          gapWidth: 1,
        },
      },
      upperLabel: {
        normal: {
          show: false,
        },
      },
      colorMappingBy: 'value',
      visualDimension,
      color: ['#0EBF9C', '#e49732', '#e85654'],
    },
    {
      itemStyle: {
        normal: {
          borderColor: '#252b35',
          borderWidth: 3,
          gapWidth: 1,
        },
        emphasis: {
          borderColor: '#ddd',
        },
      },
      colorMappingBy: 'value',
      visualDimension,
      color: ['#0EBF9C', '#e49732', '#e85654'],
    },
    {
      itemStyle: {
        normal: {
          borderColor: '#252b35',
          borderWidth: 3,
          gapWidth: 1,
        },
        emphasis: {
          borderColor: '#ddd',
        },
      },
      colorMappingBy: 'value',
      visualDimension,
      // color: ['#4caf50', '#fff', '#f44336'],
      color: ['#0EBF9C', '#e49732', '#e85654'],
    },
    {
      itemStyle: {
        normal: {
          borderColor: '#252b35',
          borderWidth: 0,
          gapWidth: 0,
        },
        emphasis: {
          borderColor: '#ddd',
        },
      },
    },
  ]
  const unit = format === 'integer' ? '' : '%'
  const options = {
    backgroundColor: '#252b35',
    grid: {
      backgroundColor: '#252b35',
    },
    tooltip: {
      formatter: function (info) {
        const value = info.value[1] || 0
        const treePathInfo = info.treePathInfo
        const treePath = []
        for (let i = 1; i < treePathInfo.length; i++) {
          treePath.push(treePathInfo[i].name)
        }
        return [
          '<div class="tooltip-title">' + formatUtil.encodeHTML(treePath.join('/')) + '</div>',
          name + ': ' + value.toFixed(2) + unit,
        ].join('')
      },
    },
    series: [{
      name: name,
      type: 'treemap',
      width: '100%',
      height: '100%',
      visibleMin: 300,
      // visualMin: -2,
      // visualMax: 2,
      leafDepth: 2,
      colorMappingBy: 'value',
      visualDimension,
      color: ['#0EBF9C', '#e49732', '#e85654'],
      label: {
        normal: {
          show: true,
          color: '#252b35',
          formatter: function (info) {
            const { value } = info
            return [info.name, `${_.round(value[1], 2)}${unit}`].filter(Boolean).join('\n\n')
          },
        },
      },
      upperLabel: {
        show: true,
        height: 30,
        formatter: param => `${param.name}: ${_.round(param.value[1], 2)}%`,
      },
      itemStyle: {
        normal: {
          borderColor: '#fff',
          backgroundColor: '#252b35',
        },
        borderColor: '#252b35',
      },
      levels: levelOption,
      data: data,
    }],
  }
  return options
}

export default ({
  data,
  name,
  format,
  visualDimension,
  onEvents,
  height,
}: {
  data: any,
  name: any,
  format?: string,
  visualDimension?: any,
  onEvents?: any,
  height?: any,
}) => {
  const options = getChartOptions(data, name, format, visualDimension || 1)
  return (
    <Echart style={{ height: height || '450px' }} options={options} onEvents={onEvents}/>
  )
}
