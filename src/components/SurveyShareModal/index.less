.linkModal {
  .qrCode {
    width: 270px;
    margin: auto;
  }
  .weixinShare {
    .txt {
      color: #fff;
      font-size: 17px;
      text-align: center;
      margin-top: 320px;
      line-height: 30px;
    }
    img {
      position: absolute;
      right: 40px;
      top: -300px;
    }
  }
  .textWapper {
    padding: 10px;
    .expireTip {
      position: relative;
      .actions {
        position: absolute;
        right: 0;
        top: 0;
      }
      .copyText {
        cursor: pointer;
        margin-left: 15px;
      }
      .qrcode {
        color: #4eaf46;
        cursor: pointer;
      }
    }
    .text {
      margin-top: 25px;
      border: 1px solid #ddd;
      padding: 10px;
      p {
        margin: 0;
        white-space: pre-wrap;
      }
    }
  }
  @media (min-width: 768px) {
    .qrCode {
      width: 250px;
      &> div {
        width: 100%;
      }
    }
  }
}
