import React, { Component } from 'react'
import PropTypes from 'prop-types'
import QRCode from 'qrcode.react'
import { Button, Modal, notification } from 'antd'
import { CopyOutlined } from '@ant-design/icons';
import ClipboardJS from 'clipboard'
import styles from './index.less'
export default class SurveyShareModal extends Component {
  static propTypes = {
    show: PropTypes.bool.isRequired,
    close: PropTypes.func.isRequired,
    url: PropTypes.string.isRequired
  }

  copyData = () => {
    notification.success({ message: '复制成功！' })
  }
  render() {
    const { show, close, url } = this.props
    new ClipboardJS('.copy-btn', {
      text: function () {
        return url
      }
    })
    return (
      <div>
        <Modal
          visible={show}
          onCancel={close}
          className={styles.linkModal}
          title='标题'
        >
          <div>
            <div className={styles.qrCode}>
              <QRCode value={encodeURI(url)} size={220} />
            </div>
            <div className={styles.textWapper}>
              <div className={styles.expireTip}>
                <div className={styles.actions}>
                  <span
                    className={styles.copyText}
                  >
                    <span className="copy-btn" onClick={this.copyData}><CopyOutlined key="Icon" style={{ color: 'orange' }} /> 复制</span>
                  </span>
                </div>
              </div>
              <div className={styles.text}>
                <p>
                  链接：
                  <a data-clipboard-text={url} href={url} target="_blank">
                    {url}
                  </a>
                </p>
              </div>
            </div>
          </div>
        </Modal >
      </div >
    )
  }
}
