import React, { Component } from 'react'
import { Tag } from 'antd'
import { stateDistribute, distributeFromState } from '@/utils/allocation'
import chartTheme from '@/components/Chart/theme'
import Chart from '@/components/Chart/Chart'

export default class HistoryStateChart extends Component {
  componentWillReceiveProps(newProps) {
    if (newProps.collapsed !== this.props.collapsed) {
      setTimeout(() => {
        // this.chartRef.reflow()
      }, 3000)
    }
  }

  afterChartCreated = chart => {
    this.chartRef = chart
  };

  getCondition = state =>
    this.props.stateType === 'date' ? state.dateConditions : state.conditions;

  getDateConditions = state => {
    const dateConditions = []
    for (let i = 0; i < state.conditions.length; i += 2) {
      dateConditions.push([state.conditions[i], state.conditions[i + 1]])
    }
    return dateConditions
  };

  getDistribution = () => {
    const { quotas, series, stateType } = this.props
    const endDate = series && Math.max(...series.map(fund => fund.data[fund.data.length - 1][0]))
    const states = this.props.states.map(item => {
      return {
        ...item,
        dateConditions: this.getDateConditions(item),
      }
    })
    const distribute =
      stateType === 'date' ? distributeFromState(states) : stateDistribute(quotas, states)
    distribute.fix(endDate)
    const colors = [...chartTheme.colors].reverse()
    const colorSet = {}
    distribute.forEach(interval => {
      if (!colorSet[interval[0]]) {
        colorSet[interval[0]] = colors[Object.keys(colorSet).length]
      }
    })
    return { distribute, colorSet }
  };

  getSeriesData() {
    const { series, states } = this.props
    if (series && series.length) {
      return series.map(fund => ({
        data: fund.data,
        name: fund.name,
        lineWidth: 2,
      }))
    }
    const allDates = states.reduce((out, item) => {
      return out.concat(item.conditions.map(con => con.restrict))
    }, [])
    let startDate = Math.min.apply(null, allDates)
    const endDate = Math.max.apply(null, allDates)
    const ts = 24 * 60 * 60 * 1000
    const data = []
    while (startDate <= endDate) {
      data.push([startDate, null])
      startDate = startDate + ts
    }
    return {
      data,
      name: 'x',
      lineWidth: 0,
      // visible: false,
      showInLegend: false,
    }
  }

  renderChart(distribute, colorSet) {
    const config = {
      chart: {
        type: 'line',
      },
      lang: {
        noData: '',
      },
      yAxis: [
        {
          title: {
            text: '',
          },
          labels: {
            format: '{value}%',
          },
        },
      ],
      xAxis: {
        plotBands: distribute.map(interval => {
          return {
            from: interval[1],
            to: interval[2],
            color: colorSet[interval[0]],
          }
        }),
      },
      series: this.getSeriesData(),
    }
    return <Chart options={config} constructorType="stockChart" />
  }

  render() {
    const { distribute, colorSet } = this.getDistribution()
    return (
      <div>
        {Object.keys(colorSet).map(key => (
          <Tag color={colorSet[key]} key={key}>
            {key}
          </Tag>
        ))}
        {this.renderChart(distribute, colorSet)}
      </div>
    )
  }
}
