import React, { Component } from 'react'
import PropTypes from 'prop-types'
import deduplication from '../../utils/deduplication'
import { Modal, Select, Button, Icon } from 'antd'
import styles from './EmailNotificationModal.less'
import validator from 'validator'
import superagent from 'superagent'
import { getToken } from '@/utils/utils'
import WysiwygEditor from '@/components/WysiwygEditor'
import AttachmentDrawer from '@/components/AttachmentDrawer'

export default class EmailNotificationModal extends Component {
  static propTypes = {
    show: PropTypes.bool.isRequired,
    close: PropTypes.func.isRequired,
    users: PropTypes.array.isRequired,
    survey: PropTypes.object.isRequired,
    subject: PropTypes.string
  }

  constructor(props) {
    super(props)
    this.state = {
      subject: props.subject || '',
      content: props.survey.description || '',
      sidebarOpen: false,
      currentUsers: [],
      list: []
    }
  }

  onSubjectChange = event => {
    this.setState({ subject: event.target.value })
  }

  onContentChange = content => {
    this.setState({ content })
  }

  handleScaleChange = values => {
    const users = this.getUsers()
    const allUsers = []
    values.forEach(item => {
      users.forEach(user => (user.tags.includes(item) || item.includes(user.email || user.contact)) && allUsers.push(user._id))
    })
    this.setState({ currentUsers: [...new Set(allUsers)] })
  }

  getUsers() {
    const {
      survey: { whitelist }
    } = this.props
    console.log('survey', this.props.survey)
    console.log('this.props.users', this.props.users)
    const isValidUser = user => (user.fof_status === 'actived' && ~user.user_scopes.indexOf('researcher'))
    return this.props.users.filter(
      user => isValidUser(user) && ~whitelist.indexOf(user._id)
    )
  }

  getTags() {
    return deduplication(
      this.getUsers().reduce((out, user) => out.concat(user.tags), [])
    )
  }

  submit = () => {
    const users = this.getUsers()
    const { subject, content, currentUsers } = this.state
    if (!subject || !validator.isLength(subject, 5)) {
      return toastr.warning('邮件主题不能少于 5 个字符')
    }
    if (!content || !validator.isLength(content, 5)) {
      return toastr.warning('邮件内容不能少于 5 个字符')
    }
    this.setState({ sidebarOpen: true, list: !currentUsers.length ? users.map((user, index) => ({ index, percent: 0, ...user })) : currentUsers.map((user, index) => ({ index, percent: 0, _id: user })) }, () => {
      this.props.close()
      const { list } = this.state
      list.forEach(item => {
        setTimeout(() => this.upload(item), item.index * 500)
      })
    })
  }

  upload(item) {
    const { survey } = this.props
    const { subject, content } = this.state
    const { index, _id } = item
    const data = { subject, content }
    data.userIds = [_id]
    const request = superagent.post(`/api/admin/survey/${survey._id}/notification`)
    const token = getToken()
    request.send(data)
    request.set('authorization', token)
    request.on('progress', this.handleProgress(index))
    request.end(this.handleResult(index))
  }

  handleProgress = index => event => {
    const { list } = this.state
    list.forEach(item => {
      if (item.index === index && event.percent) {
        item.percent = event.percent === 100 ? 99 : event.percent
      }
    })
    this.setState({ list })
  }

  handleResult = index => (err, { body }) => {
    if (err) {
      console.log('Upload file error', err)
    }
    const { list } = this.state
    list.forEach(item => {
      if (item.index === index) {
        if (body && body.message) {
          item.error = body.message
        }
        item.percent = 100
      }
    })
    this.setState({ list })
  }

  toggleSidebar = () => {
    this.setState({
      sidebarOpen: !this.state.sidebarOpen
    })
  }

  isUploading() {
    const { list } = this.state
    const done = list.filter(
      item => item.percent === 100 || item.error
    )
    return done.length !== list.length
  }

  renderUploadProgress() {
    const { list } = this.state
    const done = list.filter(
      item => item.percent === 100 || item.error
    )
    return (
      <span>
        <i className="fa fa-spinner fa-spin" />{' '}
        {`${done.length}/${list.length}`}
      </span>
    )
  }

  renderSendButton() {
    const { t } = this.props
    const uploading = this.isUploading()
    return uploading ? (<Button onClick={this.toggleSidebar}>{this.renderUploadProgress()}</Button>) : (<Button onClick={this.submit}>
      发送
    </Button>)
  }

  render() {
    const { currentUsers, subject, content, sidebarOpen, list } = this.state
    const Option = Select.Option
    const tags = this.getTags()
    const users = this.getUsers()
    const tagObjects = tags.map(tag => ({
      _id: tag,
      isTag: true,
      name: tag,
      nickname: tag
    }))
    const options = tagObjects.concat(users)
    const currentUsersValue = currentUsers.map(item => {
      const theUser = users.find(user => user._id === item)
      return theUser && `${theUser.nickname}${theUser.email || theUser.contact}`
    })
    return (
      <div>
        <Modal
          width={720}
          visible={this.props.show}
          onCancel={this.props.close}
          title='邮箱通知'
          footer={[
            <Button onClick={this.props.close}>
              取消
            </Button>,
            this.renderSendButton()
          ]}
        >
          <div className={styles.wapper}>
            <div className="form-group">
              <label>邮件主题</label>
              <input value={subject} onChange={this.onSubjectChange} placeholder='请输入邮件通知列表主题' className="form-control" />
            </div>
            <div className="form-group">
              <label>邮件内容</label>
              <WysiwygEditor
                config={{
                  toolbarInline: false,
                  charCounterCount: false,
                  toolbarSticky: true,
                  placeholderText: '请输入...',
                  heightMin: 100,
                  heightMax: 260,
                  toolbarButtons: [
                    'bold',
                    'italic',
                    'underline',
                    'strikeThrough',
                    '|',
                    'fontSize',
                    'color',
                    '|',
                    'paragraphFormat',
                    'align',
                    'formatOL',
                    'formatUL',
                    'outdent',
                    'indent',
                    'quote',
                    'insertLink'
                  ]
                }}
                model={content}
                onModelChange={this.onContentChange}
              />
            </div>
            <div className="form-group">
              <label htmlFor="tagInput">通知列表</label>
              <Select
                mode="multiple"
                placeholder='默认通知所有填写问卷的用户'
                onChange={this.handleScaleChange}
                className={styles.filterWapper}
                value={currentUsersValue}
              >
                {options.map(item => <Option key={item._id} value={item.isTag ? item.name : `${item.nickname}${item.email || item.contact}`}>{item.isTag
                  ? <div><Icon type="tag" />{' '}<span>{item.name}</span></div>
                  : <div>
                    <div>{item.nickname}</div>
                    <div>{item.email || item.contact}</div>
                  </div>}</Option>)}
              </Select>
            </div>
          </div>
        </Modal>
        <AttachmentDrawer list={list} sidebarOpen={sidebarOpen} close={this.toggleSidebar} />
      </div>
    )
  }
}
