import React, { useState } from 'react'
import { Modal, Button, Form, Tooltip } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import { getToken } from '@/utils/utils'
import SearchSelect from '@/components/SearchSelect'
import { useRequest } from '@umijs/hooks'
import { getDailyReviewDates } from '@/services/fund'

const ExportForm = ({
  initialValues,
  dates,
  visible,
  onCreate,
  onCancel,
}) => {
  const [form] = Form.useForm()
  return (
    <Modal
      visible={visible}
      title="下载日报数据"
      okText="确定"
      cancelText="取消"
      onCancel={onCancel}
      onOk={() => {
        form
          .validateFields()
          .then(values => {
            onCreate(values)
          })
          .catch(info => {
            console.log('Validate Failed:', info)
          })
      }}
    >
      <Form
        form={form}
        layout="vertical"
        name="form_in_modal"
        initialValues={initialValues}
      >
        <Form.Item
          name="bizDate"
          label="日报日期"
        >
          <SearchSelect
            placeholder="请选择日期"
            options={dates.map(item => {
              return {
                title: item,
                dataIndex: item,
              }
            })}
            width="150px"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

const ExportDailyReviewModal = ({ networkSegment }) => {
  const { loading, data: dates = [] } = useRequest(() => {
    return getDailyReviewDates()
  })
  const [visible, setVisible] = useState(false)

  const onCreate = (values: any) => {
    const { bizDate } = values
    const href = `/api/products/download/dailyreviewbydate?bizDate=${bizDate}&token=${getToken().slice(7)}`
    window.open(href)
    setVisible(false)
  }

  const showModal = () => {
    setVisible(true)
  }

  const dailyReviewButton = networkSegment === 'trading'
    ?
      <Button icon={<DownloadOutlined />} size="small" onClick={showModal} loading={loading}>
        日报
      </Button>
    : <Tooltip title="由于合规要求，办公网暂不支持日报下载功能">
      <Button icon={<DownloadOutlined />} size="small" disabled>
        日报
      </Button>
    </Tooltip>

  return (
    <>
      {dailyReviewButton}
      {!loading &&
      <ExportForm
        initialValues={{
          bizDate: dates[0],
        }}
        dates={dates}
        visible={visible}
        onCreate={onCreate}
        onCancel={() => {
          setVisible(false)
        }}
      />}
    </>
  )
}

export default ExportDailyReviewModal
