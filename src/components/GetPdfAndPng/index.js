import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import { Dropdown, Menu, Button, Icon } from 'antd'
import { getToken } from '@/utils/utils'
import { DownOutlined } from '@ant-design/icons'

export default class GetPdfAndPng extends Component {
  static propTypes = {
    id: PropTypes.string.isRequired,
    className: PropTypes.string
  }

  onSelect = (event) => {
    let eventKey = event.key
    const token = getToken()
    const href = window.location.href
    const exportServerUrl = 'http://export.fofinvesting.com/pdf'
    const url = `${exportServerUrl}?token=${token.slice(7)}&url=${href}&format=A4&type=${eventKey}`
    window.open(url)
  }

  render() {
    const filters = [
      { name: '导出PDF', value: 'pdf' },
      { name: '导出图片', value: 'png' }
    ]
    const usePrint = true
    if (usePrint) {
      return (
        <div className={classnames(this.props.className)}>
          <Button onClick={() => window.print()}>
            打印
          </Button>
        </div>
      )
    }
    return (
      <div className={classnames(this.props.className)}>
        <Dropdown overlay={
          <Menu onClick={this.onSelect}>
            {filters.map(item => (
              <Menu.Item key={item.value}>
                {item.name}
              </Menu.Item>
            ))}
          </Menu>
        }>
          <Button style={{ marginRight: 8 }}>
            导出 <DownOutlined />
          </Button>
        </Dropdown>
      </div>
    )
  }
}
