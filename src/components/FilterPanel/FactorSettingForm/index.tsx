import React, { useEffect } from 'react'
import {
  Form,
  Row,
  Col,
} from 'antd'
import SearchSelect from '@/components/SearchSelect'

const FactorSettingForm = ({
  factorDateOptions,
  stockTypeOptions,
  bondTypeOptions,
  allocTypeOptions,
  passiveTypeOptions,
  onChange,
  value,
  disableDate,
}) => {
  const [form] = Form.useForm()
  useEffect(() => {
    form.setFieldsValue(value || {})
  }, [value])
  const colWidth = disableDate ? 8 : 7
  return (
    <Form
      hideRequiredMark
      form={form}
      layout="vertical"
      onValuesChange={(changedValues, allValues) => {
        onChange(allValues)
      }}
    >
      <Row gutter={8}>
        {!disableDate &&
        <Col span={3} sm={3} xs={24}>
          <Form.Item
            label="评价日期"
            name="factorDate"
            rules={[
              {
                required: true,
                message: '请选择评价日期',
              },
            ]}
          >
            <SearchSelect placeholder='评价日期' options={factorDateOptions || []} isFullWidth/>
          </Form.Item>
        </Col>}
        <Col span={colWidth} sm={colWidth} xs={24}>
          <Form.Item
            label="纯股型方案"
            name="stockTypeId"
            rules={[
              {
                required: true,
                message: '请选择纯股型方案',
              },
            ]}
          >
            <SearchSelect placeholder='纯股型方案' options={stockTypeOptions || []} isFullWidth/>
          </Form.Item>
        </Col>
        <Col span={colWidth} sm={colWidth} xs={24}>
          <Form.Item
            label="纯债型方案"
            name="bondTypeId"
            rules={[
              {
                required: true,
                message: '请选择纯债型方案',
              },
            ]}
          >
            <SearchSelect placeholder='纯债型方案' options={bondTypeOptions || []} isFullWidth/>
          </Form.Item>
        </Col>
        <Col span={colWidth} sm={colWidth} xs={24}>
          <Form.Item
            label="配置型方案"
            name="allocTypeId"
            rules={[
              {
                required: true,
                message: '请选择配置型方案',
              },
            ]}
          >
            <SearchSelect placeholder='配置型方案' options={allocTypeOptions || []} isFullWidth/>
          </Form.Item>
        </Col>
        {/* <Col span={5} sm={5} xs={24}>
          <Form.Item
            label="被动型方案"
            name="passiveTypeId"
            rules={[
              {
                required: true,
                message: '请选择被动型方案',
              },
            ]}
          >
            <SearchSelect placeholder='被动型方案' options={passiveTypeOptions || []} isFullWidth/>
          </Form.Item>
        </Col> */}
      </Row>
    </Form>
  )
}

export default FactorSettingForm
