import { Card, Select, Tabs, Divider, Tooltip, Tag, Switch, Form, Space } from 'antd'
import { DeleteOutlined, PlusOutlined, SaveOutlined, DownOutlined, UpOutlined } from '@ant-design/icons'
import React, { useState } from 'react'
import _ from 'lodash'
import classnames from 'classnames'
import { FormProps } from 'antd/es/form'
import StandardFormRow from './StandardFormRow'
import TagSelect from './TagSelect'
import FactorSettingForm from './FactorSettingForm'
import InvestPoolModal from '@/components/investpool/InvestPoolModal'
import TableForm from '@/components/TableForm'
import { getFilterDisplayValues, getDefaultFilterValues, checkFilterValue } from './filterPanelHelper'
import styles from './style.less'

const { Option, OptGroup } = Select
const FormItem = Form.Item
const { TabPane } = Tabs

interface ComponentProps extends FormProps {
  filters?: any;
  filterValues?: any;
  onChange?: any;
  dispatch?: any;
  currentInvestPool?: any;
  disableInvestPool?: boolean;
  investPoolDataType?: string;
}

export interface FilterItem {
  name: string;
  type: string;
  formKey: string;
  data?: any;
  tabs: any;
  componentProps?: any;
}

const FilterPanel: React.FC<ComponentProps> = props => {
  const {
    filters,
    filterValues,
    currentInvestPool,
    disableInvestPool,
    onChange,
  } = props
  const [form] = Form.useForm()
  const [collapsed, setCollapsed] = useState(
    localStorage.getItem('qutke:filterpanel:collapsed') === 'Y'
  )
  const allFilters = filters.reduce((out, item) => {
    if (item.type === 'tabs') {
      return out.concat(item.tabs)
    }
    return out.concat([item])
  }, [])
  const clearAllFilters = () => {
    onChange({})
    const emptyValues = getDefaultFilterValues(allFilters)
    form.setFieldsValue(emptyValues)
  }

  const toggleCollapsed = () => {
    const collapsedNew = !collapsed
    setCollapsed(collapsedNew)
    localStorage.setItem('qutke:filterpanel:collapsed', collapsedNew ? 'Y' : 'N')
  }

  const handleValuesChange = (changedValues, allValues) => {
    const values = Object.keys(allValues).reduce((out, key) => {
      const values = allValues[key]
      if (values && values.length || !_.isEmpty(values) || key.includes('OverlayLogic') || typeof values === 'boolean') {
        out[key] = values
      }
      return out
    }, {})
    onChange(values)
  }

  const renderFilterItem = (filterItem: FilterItem) => {
    const formItemLayout = {
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 24 },
        md: { span: 24 },
      },
    }
    if (filterItem.type === 'tagSelect') {
      let filterItemData = filterItem.data
      if (
        filterItem.formKey === 'style_type' &&
        filterValues &&
        filterValues.asset_type &&
        filterValues.asset_type.length
      ) {
        const assetTypeValues = filterValues.asset_type
        filterItemData = filterItemData.filter(item => {
          const prefix = item.value[0]
          return assetTypeValues.includes(prefix)
        })
      }
      return (
        <FormItem name={filterItem.formKey}>
          <TagSelect expandable={filterItemData.length > 10}>
            {filterItemData.map(item => (
              <TagSelect.Option key={item.value} value={item.value}>
                {item.name}
              </TagSelect.Option>
            ))}
          </TagSelect>
        </FormItem>
      )
    } else if (filterItem.type === 'groupByInitialsSelect') {
      return (
        <FormItem {...formItemLayout} name={filterItem.formKey} className={styles.formItemWrapper}>
          <Select
            showSearch
            mode="multiple"
            placeholder={`请选择${filterItem.name}`}
            style={{
              // marginTop: '5px',
              width: '70%',
            }}
          >
            {filterItem.data.map(item => {
              return (
                <OptGroup label={item.letter} key={item.letter}>
                  {item.value.map(val => (
                    <Option key={val.name} value={val.name}>
                      {val.name}
                    </Option>
                  ))}
                </OptGroup>
              )
            })}
          </Select>
        </FormItem>
      )
    } else if (filterItem.type === 'tableForm') {
      const tableFormValues = form.getFieldValue(filterItem.formKey)
      return (
        <>
          {!filterItem.disableOverlayLogic &&
          <div style={{
            textAlign: 'right',
            marginBottom: 10,
            display: tableFormValues && tableFormValues.length > 0 ? 'block' : 'none',
          }}>
            <FormItem {...{
              labelCol: { span: 19 },
              wrapperCol: { span: 5 },
              name: `${filterItem.formKey}OverlayLogic`,
              initialValue: true,
              valuePropName: 'checked',
            }} label="指标叠加逻辑：" style={{ display: 'inline-block', marginRight: 40 }}>
              <Switch checkedChildren="与" unCheckedChildren="或"/>
            </FormItem>
          </div>}
          <FormItem
            name={filterItem.formKey}
            initialValue={[]}
            {...formItemLayout }
            className={styles.formItemWrapper}
          >
            <TableForm
              iconAction
              isEdditing
              hideEmptyTable
              newItemText="添加指标"
              size="small"
              {...filterItem.componentProps}
            />
          </FormItem>
        </>
      )
    } else if (filterItem.type === 'switchList') {
      return (
        <Space size="large">
          {filterItem.switchList.map(formKey => {
            return (
              <FormItem {...{
                labelCol: { span: 19 },
                wrapperCol: { span: 5 },
                name: formKey.value,
                initialValue: true,
                valuePropName: 'checked',
              }} label={`${formKey.name}：`} style={{ display: 'inline-block', marginRight: 40 }}>
                <Switch size="small"/>
              </FormItem>
            )
          })}
        </Space>
      )
    } else if (filterItem.type === 'factorSetting') {
      return (
        <FormItem {...formItemLayout} name={filterItem.formKey} className={styles.formItemWrapper}>
          <FactorSettingForm {...filterItem.componentProps} />
        </FormItem>
      )
    }
    return null
  }

  const renderClearTags = () => {
    return allFilters.map((filter: FilterItem) => {
      const displayValues = getFilterDisplayValues(filter, filterValues)
      if (!displayValues || !displayValues.length) {
        return false
      }
      const filterName = filter.name ? `${filter.name}: ` : ''
      const name = `${filterName}${displayValues[0]}${displayValues.length > 1 ? '...' : ''}`
      return (
        <Tooltip title={displayValues.join(',')} key={filter.formKey}>
          <Tag
            closable
            onClose={() => {
              const emptyValues = {[filter.formKey]: []}
              if (filter.type === 'switchList') {
                filter.switchList.forEach(item => {
                  emptyValues[item.value] = true
                })
              }
              form.setFieldsValue(emptyValues)
              const newValues = form.getFieldsValue()
              onChange(newValues)
            }}
          >
            {name}
          </Tag>
        </Tooltip>
      )
    })
  }
  const hasFilterValue = checkFilterValue(filters, filterValues)
  return (
    <>
      <Card
        className={classnames({[styles.collapsedCard]: collapsed})}
        title={
          <div>
            <span style={{ marginRight: '15px' }}>筛选条件</span>
            {renderClearTags()}
          </div>
        }
        extra={
          <>
            {hasFilterValue && (
              <a onClick={clearAllFilters}>
                <DeleteOutlined /> 清空条件
              </a>
            )}
            {!disableInvestPool && <Divider type="vertical" />}
            {hasFilterValue && !currentInvestPool && !disableInvestPool && (
              <InvestPoolModal
                type="dynamic"
                dispatch={props.dispatch}
                config={JSON.stringify(filterValues)}
                filters={filters}
                dataType={props.investPoolDataType}
              >
                <Tooltip title="保存为动态跟踪列表">
                  <a>
                    <PlusOutlined /> 动态跟踪列表
                  </a>
                </Tooltip>
              </InvestPoolModal>
            )}
            {hasFilterValue && currentInvestPool && !disableInvestPool && (
              <InvestPoolModal
                type="dynamic"
                dispatch={props.dispatch}
                config={JSON.stringify(filterValues)}
                filters={filters}
                investpool={currentInvestPool}
              >
                <Tooltip title="更新跟踪列表筛选条件">
                  <a>
                    <SaveOutlined /> 动态跟踪列表
                  </a>
                </Tooltip>
              </InvestPoolModal>
            )}
            <Tooltip title={collapsed ? '展开筛选条件' : '收起筛选条件'}>
              <a onClick={toggleCollapsed}>
                <span style={{ marginLeft: 15, marginRight: 5 }}>{collapsed ? <DownOutlined /> : <UpOutlined />} </span>
                {collapsed ? '展开' : '收起'}
              </a>
            </Tooltip>
          </>
        }
      >
        {!collapsed && (
          <Form
            layout="inline"
            onValuesChange={handleValuesChange}
            form={form}
            initialValues={filterValues}
          >
            {filters.filter(tab => {
              if (tab.formKey === 'style_type' && (
                !filterValues || !filterValues.asset_type || !filterValues.asset_type.length
                || filterValues.asset_type.every(assetType => !['1', '2', '3'].includes(assetType))
              )) {
                return false
              }
              return true
            }).map((filterItem: FilterItem) => {
              if (filterItem.type === 'tabs') {
                return (
                  <StandardFormRow title={filterItem.name} block style={{ paddingBottom: 11 }}>
                    <Tabs animated={false} size="small" defaultActiveKey="1" onChange={() => {}}>
                      {filterItem.tabs.map((tab: FilterItem, index: number) => {
                        return (
                          <TabPane tab={tab.name} key={`${index + 1}`}>
                            {renderFilterItem(tab)}
                          </TabPane>
                        )
                      })}
                    </Tabs>
                  </StandardFormRow>
                )
              }
              return (
                <StandardFormRow
                  key={filterItem.name}
                  title={filterItem.name}
                  block
                  style={{ paddingBottom: 11 }}
                >
                  {renderFilterItem(filterItem)}
                </StandardFormRow>
              )
            })}
          </Form>
        )}
      </Card>
    </>
  )
}

export default FilterPanel
