import { FilterItem } from './index'

export const getValueRangeDesc = (valueRange: any, option: any, dataIndex?: any, refOption) => {
  const isNullValue = value => value === undefined || value === null
  let valueDesc = ''
  if (!valueRange || !valueRange.length) {
    return valueDesc
  }
  let unit = '%'
  if ((option.dataIndex || dataIndex) === 'positionDuration') {
    unit = ' 年'
  } else if (
    (dataIndex || option.dataIndex) === 'value_rank'
    || option.format === 'number'
    || (refOption && refOption.dataIndex === 'value_rank')
  ) {
    unit = ''
  }
  if (isNullValue(valueRange[0]) && !isNullValue(valueRange[1])) {
    valueDesc = `小于 ${valueRange[1]}${unit}`
  } else if (!isNullValue(valueRange[0]) && isNullValue(valueRange[1])) {
    valueDesc = `大于 ${valueRange[0]}${unit}`
  } else {
    valueDesc = `介于 ${valueRange[0]}${unit} ~ ${valueRange[1]}${unit} 之间`
  }
  return valueDesc
}

const getTableFormDispalyValues = (values, tableColumns, nameQuota) => {
  const column = tableColumns.find(item => item.dataIndex === nameQuota)
  return values.map(item => {
    let dataIndexKey = item[nameQuota]
    if (Array.isArray(dataIndexKey)) {
      dataIndexKey = dataIndexKey[dataIndexKey.length - 1]
    }
    const option = (column.refOptions || column.options).find(option => option.dataIndex === dataIndexKey)
    const valueDesc = getValueRangeDesc(item.valueRange, option, null, item._refOption || {})
    if (item.reportDate) {
      return `[${item.reportDate}] ${option && option.title}${valueDesc}`
    }
    return `${option && option.title}${valueDesc}`
  })
}

export function getFilterDisplayValues(filter: FilterItem, filterValues: any) {
  if (filter.type === 'switchList') {
    const switches = filter.switchList.filter(item => filterValues[item.value] === false)
    return switches.map(item => `不显示${item.name}`)
  }
  const values = filterValues[filter.formKey]
  if (!values || !values.length) {
    return []
  }
  let displayValues = []
  if (filter.type === 'groupByInitialsSelect') {
    displayValues = values
  } else if (filter.type === 'tableForm') {
    const tableColumns = filter.componentProps.tableColumns
    if (filter.formKey === 'quotas') {
      displayValues = getTableFormDispalyValues(values, tableColumns, 'quota')
    } else if (filter.formKey === 'industryRatio') {
      displayValues = getTableFormDispalyValues(values, tableColumns, 'industry')
    } else if (filter.formKey === 'industryBoardRatio') {
      displayValues = getTableFormDispalyValues(values, tableColumns, 'industryBoard')
    } else if (filter.formKey === 'stockRatio') {
      displayValues = getTableFormDispalyValues(values, tableColumns, 'stock')
    } else if (filter.formKey === 'factors') {
      displayValues = getTableFormDispalyValues(values, tableColumns, 'factor')
    } else if (filter.formKey === 'factorDims') {
      displayValues = getTableFormDispalyValues(values, tableColumns, 'factor')
    } else if (filter.formKey === 'fundTags') {
      displayValues = getTableFormDispalyValues(values, tableColumns, 'factor')
    }
  } else {
    displayValues = filter.data.filter(item => values.includes(item.value)).map(item => item.name)
  }
  return displayValues
}

export function getDefaultFilterValues(filters: FilterItem[]) {
  return filters.reduce((out, filter: FilterItem) => {
    if (filter.type === 'switchList') {
      filter.switchList.forEach(item => {
        out[item.value] = true
      })
    } else {
      out[filter.formKey] = []
    }
    return out
  }, {})
}

export function checkFilterValue(filters, filterValues) {
  const switchFilter = filters && filters.filter(item => item.type === 'switchList')[0]
  const booleanKeys = switchFilter ? switchFilter.switchList.map(item => item.value) : []
  return Object
    .keys(filterValues)
    .filter(key => {
      if (booleanKeys.includes(key)) {
        return filterValues[key] === false
      }
      return !key.includes('OverlayLogic') && filterValues[key] && filterValues[key].length !== 0
    })
    .length !== 0
}
