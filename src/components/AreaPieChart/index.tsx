import React from 'react'
import { Row, Col } from 'antd'
import Chart from '@/components/Chart/Chart'
import theme from '@/components/Chart/theme'
import moment from 'moment'
import getSeriesChartData from '@/utils/getSeriesChartData'

const AreaPieChart = ({
  rows,
  nameKey,
  valueKey,
  nameMap = {},
  disablePie,
  height,
  pctValueKey,
  autoMaxYHeight,
  hideBalance,
  showNavigator,
  seriesNames,
  stackingValue,
}: {
  rows: any;
  nameKey: string;
  valueKey: string;
  nameMap?: any;
  disablePie?: boolean;
  height?: number,
  pctValueKey?: string,
  autoMaxYHeight?: boolean,
  hideBalance?: boolean,
  showNavigator?: boolean,
  seriesNames?: any,
  stackingValue?: boolean,
}) => {
  const latestDate = String(Math.max.apply(null, rows.map((item: any) => item.BIZ_DATE)))
  const latestData = rows.filter((item: any) => item.BIZ_DATE === latestDate)
  const colors = theme.colors
  const series = getSeriesChartData(rows, nameKey, valueKey, nameMap, pctValueKey).map((serie: any, index: number) => {
    return {
      ...serie,
      color: colors[index],
    }
  }).sort((fst, snd) => {
    if (seriesNames && seriesNames.length) {
      return seriesNames.indexOf(fst.name) - seriesNames.indexOf(snd.name)
    }
    return fst.name > snd.name ? 1 : -1
  })

  const colorMap = series.reduce((out, item) => {
    out[item.name] = item.color
    return out
  }, {})
  const pieChartConfig = {
    chart: {
      type: 'pie',
      height: height || 260,
    },
    legend: {
      layout: 'vertical',
      align: 'right',
      verticalAlign: 'middle',
      maxHeight: 200,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.percentage:.1f}%</b> ({point.y:,.0f} 万)<br/>',
    },
    series: [
      {
        name: '占比',
        innerSize: '60%',
        data: latestData
          .sort((fst, snd) => snd[valueKey] - fst[valueKey])
          .map(item => {
            const name = nameMap[item[nameKey]] || item[nameKey]
            return {
              name,
              color: colorMap[name],
              y: item[valueKey] / 10000,
            }
          }),
      },
    ],
  }
  let tooltipFormat = hideBalance
    ? '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%<br/>'
    : '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%</b> ({point.yValue:,.0f} 万)<br/>'
  if (stackingValue) {
    tooltipFormat = '<span style="color:{series.color}">{series.name}</span>: <b>{point.yValue:,.0f} 万<br/>'
  }
  const chartConfig = {
    chart: {
      type: 'column',
      height: height || 260,
    },
    yAxis: {
      max: 100,
    },
    navigator: {
      enabled: !!showNavigator,
    },
    scrollbar: {
      enabled: false,
    },
    tooltip: {
      pointFormat: tooltipFormat,
    },
    plotOptions: {
      area: {
        stacking: 'normal',
      },
      column: {
        stacking: 'normal',
      },
    },
    series,
  }
  if (autoMaxYHeight) {
    delete chartConfig.yAxis.max
  }
  if (stackingValue) {
    chartConfig.yAxis = {
      labels: {
        format: '{value}',
      },
    }
  }
  if (disablePie) {
    return <Chart options={chartConfig} constructorType="stockChart" />
  }
  return (
    <Row gutter={16}>
      <Col lg={18} md={24}>
        <Chart options={chartConfig} constructorType="stockChart" />
      </Col>
      {latestData.length !== 0 &&
      <Col lg={6} md={24}>
        <h4>{moment(latestDate).format('YYYY-MM-DD')}</h4>
        <Chart options={pieChartConfig} />
      </Col>}
    </Row>
  )
}

export default AreaPieChart
