import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import styles from './index.less'
import { message } from 'antd'
export default class SurveyTab extends Component {
  static propTypes = {
    tabs: PropTypes.array.isRequired,
    currentTab: PropTypes.string.isRequired,
    isEdit: PropTypes.bool,
    updateTab: PropTypes.func,
    removeTab: PropTypes.func,
    addTab: PropTypes.func,
    switchSurveyTab: PropTypes.func,
    onTabSort: PropTypes.func,
  }

  state = {
    inputs: this.props.tabs.reduce((out, tab) => {
      out[tab.id] = tab.name
      return out
    }, {})
  }

  componentDidMount() {
    this.initSortable()
  }

  onInputChange = id => event => {
    this.setState({
      inputs: {
        ...this.state.inputs,
        [id]: event.target.value
      }
    })
  }

  onSelectTab = (id) => () => {
    if (id === 'add') {
      this.setState({ adding: id })
    } else {
      this.props.switchSurveyTab(id)
    }
  }

  initSortable() {
    this.setState({ mounted: true })
  }

  switchTab = tab => {
    if (typeof tab.key === 'object') {
      return
    }
    const adding = tab.key === 'addTab'
    this.setState({ adding })
    if (!adding) {
      this.props.switchSurveyTab(tab.key)
    }
  }

  startEdit = id => () => {
    this.props.updateTab(id, { editting: true })
  }

  stopEdit = id => () => {
    this.props.updateTab(id, { editting: false })
  }

  cancelAdd = event => {
    event.stopPropagation()
    event.preventDefault()
    this.setState({ adding: false })
  }

  removeTab = id => () => {
    this.props.removeTab(id)
  }

  confirmAdd = event => {
    event.stopPropagation()
    event.preventDefault()
    const { inputs } = this.state
    const name = inputs.addTab
    if (!name) {
      message.warning('请输入名称')
      return
    }
    this.setState(
      {
        adding: false,
        inputs: {
          ...inputs,
          addTab: undefined
        }
      },
      () => {
        this.props.addTab({ name })
      }
    )
  }

  confirmUpdate = id => () => {
    const name = this.state.inputs[id]
    if (!name) {
      message.warning('请输入名称')
      return
    }
    this.props.updateTab(id, { name, editting: false })
  }

  renderTab = (item, index) => {
    const { isEdit, currentTab } = this.props
    const { inputs } = this.state
    if (!item.editting) {
      if (!isEdit) {
        return <li
          className={classnames(styles.tabLi, { [styles.selected]: item.id === currentTab, tab: isEdit })}
          onClick={this.onSelectTab(item.id)}
        >
          <div>{item.name}</div>
        </li>
      }
      return <Draggable key={item.id} draggableId={item.id} index={index}>
        {(draggableProvided, draggableSnapshot) => (
          <li
            ref={draggableProvided.innerRef}
            {...draggableProvided.draggableProps}
            {...draggableProvided.dragHandleProps}
            className={classnames(styles.tabLi, { [styles.selected]: item.id === currentTab, tab: isEdit })}
            onClick={this.onSelectTab(item.id)}
          >
            <div>{item.name}</div>
            {this.props.tabs.length > 1 && (
              <i
                className="fa fa-close tabBtn right-top"
                onClick={this.removeTab(item.id)}
              />
            )}
            <i
              className="fa fa-pencil tabBtn right-bottom"
              onClick={this.startEdit(item.id)}
            />
          </li>
        )}
      </Draggable>
    }
    if (!isEdit) {
      return <li
        className={classnames(styles.tabLi, styles.edittingLi, styles.editting, { [styles.selected]: item.id === currentTab })}
        onClick={this.onSelectTab(item.id)}
      >
        <input
          type="text"
          value={inputs[item.id]}
          className="form-control"
          onChange={this.onInputChange(item.id)}
        />
        <button
          className={classnames('btn btn-default btn-xs', styles.save)}
          onClick={this.confirmUpdate(item.id)}
        >
          <i className="fa fa-check" />
        </button>
        <button
          className={classnames('btn btn-default btn-xs', styles.cancel)}
          onClick={this.stopEdit(item.id)}
        >
          <i className="fa fa-close" />
        </button>
      </li>
    }
    return (
      <Draggable key={item.id} draggableId={item.id} index={index}>
        {(draggableProvided, draggableSnapshot) => (
          <li
            ref={draggableProvided.innerRef}
            {...draggableProvided.draggableProps}
            {...draggableProvided.dragHandleProps}
            className={classnames(styles.tabLi, styles.edittingLi, styles.editting, { [styles.selected]: item.id === currentTab })}
            onClick={this.onSelectTab(item.id)}
          >
            <input
              type="text"
              value={inputs[item.id]}
              className="form-control"
              onChange={this.onInputChange(item.id)}
            />
            <button
              className={classnames('btn btn-default btn-xs', styles.save)}
              onClick={this.confirmUpdate(item.id)}
            >
              <i className="fa fa-check" />
            </button>
            <button
              className={classnames('btn btn-default btn-xs', styles.cancel)}
              onClick={this.stopEdit(item.id)}
            >
              <i className="fa fa-close" />
            </button>
          </li>
        )}
      </Draggable>
    )
  }

  renderAddTab() {
    const { inputs } = this.state
    if (this.state.adding) {
      return (
        <li key="addTab" className={classnames(styles.tabLi, styles.editting)}>
          <input
            type="text"
            value={inputs.addTab}
            className="form-control"
            onChange={this.onInputChange('addTab')}
          />
          <button
            className={classnames('btn btn-default btn-xs', styles.save)}
            onClick={this.confirmAdd}
          >
            <i className="fa fa-check" />
          </button>
          <button
            className={classnames('btn btn-default btn-xs', styles.cancel)}
            onClick={this.cancelAdd}
          >
            <i className="fa fa-close" />
          </button>
        </li>
      )
    }
    return (
      <li key="addTab" onClick={this.onSelectTab('add')} className={styles.tabLi}>
        <i className="fa fa-plus" />
      </li>
    )
  }

  render() {
    const { tabs, isEdit } = this.props
    if (!this.state.mounted) {
      return false
    }
    if (!isEdit) {
      return <ul className={classnames(styles.tabContainer, styles.surveyTab)}>
        {tabs.map(this.renderTab)}
      </ul>
    }
    return (
      <DragDropContext onDragEnd={this.props.onTabSort}>
        <Droppable droppableId="droppable" direction="horizontal">
          {(droppableProvided, droppableSnapshot) => (
            <ul ref={droppableProvided.innerRef} {...droppableProvided.droppableProps} className={classnames(styles.tabContainer, styles.surveyTab)}>
              {tabs.map(this.renderTab)}
              {this.renderAddTab()}
              {droppableProvided.placeholder}
            </ul>
          )}
        </Droppable>
      </DragDropContext>
    )
  }
}
