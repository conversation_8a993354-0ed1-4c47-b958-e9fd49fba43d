.tabContainer {
  line-height: 46px;
  white-space: nowrap;
  -webkit-box-shadow: none;
  box-shadow: none;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-size: 14px;
  font-variant: tabular-nums;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  color: rgba(0, 0, 0, 0.65);
  list-style: none;
  background: rgb(0, 0, 0,0.5);
  outline: none;
  -webkit-transition: background 0.3s, width 0.2s;
  transition: background 0.3s, width 0.2s;
  zoom: 1;
}

.surveyTab {
  .tabLi {
    position: relative;
    top: 1px;
    display: inline-block;
    vertical-align: bottom;
    border-bottom: 2px solid transparent;
    margin-top: -1px;
    margin: 0;
    padding: 0 20px;
    white-space: nowrap;
    cursor: grab;
    background-color: #000;
    color:orange;
    transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), border-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), padding 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    &:hover {
      color: #ff9800;
      border-bottom: 2px solid #ff9800;
      background-color: rgb(92, 92, 92) !important;
    }
  }
  .edittingLi {
    height: 48px;
  }
  .selected {
    color: #ddd;
    background-color:#222;
    border: 1px solid #ff9800;
    border-bottom: 2px solid #ff9800;
  }
  li > a {
    border-radius: 0;
    padding-right: 30px;
    padding-left: 30px;
    position: relative;
  }
  li:global(.tab) {
    a {
      cursor: move !important;
    }
  }
  li.editting {
    height: 48px;
    a {
      padding-bottom: 0;
      padding-top: 5px;
      padding-left: 10px;
      padding-right: 10px;
    }
    input {
      margin-top: 6px;
      width: 65%;
    }
    button.save {
      position: absolute;
      right: 40px;
      top: 10px;
    }
    button.cancel {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }
  li:hover {
    :global(.tabBtn) {
      display: block;
    }
  }
  :global(.tabBtn) {
    display: none;
    position: absolute;
    cursor: pointer;
    font-size: 12px;
  }
  :global(.tabBtn):hover {
    display: block;
  }
  :global(.right-top) {
    right: 2px;
    top: 2px;
  }
  :global(.right-bottom) {
    right: 2px;
    bottom: 2px;
  }
  :global(.ant-menu-item-selected) {
    border: 1px solid #ddd;
    background-color: #fff;
    border-bottom-color: transparent;
  }
  :global(.ant-menu-item-active) {
    background-color: #eee !important;
  }
}
