import React from 'react'
import {
  Select,
} from 'antd'

const { Option } = Select

export default ({
  value,
  options,
  onChange,
  placeholder,
  width,
  isFullWidth,
  mode,
}: {
  value: string,
  options: any,
  onChange: any,
  placeholder?: string,
  width?: string,
  isFullWidth?: boolean,
  mode?: string,
}) => {
  return (
    <Select
      showSearch
      mode={mode}
      placeholder={placeholder || '选择报告期'}
      style={{
        // marginTop: '5px',
        width: isFullWidth ? undefined : width || '120px',
      }}
      value={value}
      filterOption={(input, option) =>
        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
      }
      onChange={onChange}
    >
      {options.map(item => {
        return (
          <Option value={item.dataIndex}>
            {item.title}
          </Option>
        )
      })}
    </Select>
  )
}
