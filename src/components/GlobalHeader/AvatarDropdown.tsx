import { LockOutlined, LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Menu, Spin } from 'antd';
import { ClickParam } from 'antd/es/menu'
import { FormattedMessage } from 'umi-plugin-react/locale'
import React from 'react'
import { connect } from 'dva'
import router from 'umi/router'

import { ConnectProps, ConnectState } from '@/models/connect'
import { CurrentUser } from '@/models/user'
import HeaderDropdown from '../HeaderDropdown'
import styles from './index.less'

export interface GlobalHeaderRightProps extends ConnectProps {
  currentUser?: CurrentUser;
  menu?: boolean;
}

class AvatarDropdown extends React.Component<GlobalHeaderRightProps> {
  onMenuClick = (event: ClickParam) => {
    const { key } = event

    if (key === 'logout') {
      const { dispatch } = this.props
      if (dispatch) {
        dispatch({
          type: 'login/logout',
        })
      }
      return
    }
    router.push(key)
  };

  render(): React.ReactNode {
    const { currentUser = { avatar: '', nickname: '', admin_scopes: [] }, menu } = this.props

    const menuHeaderDropdown = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={this.onMenuClick}>
        {menu && (
          <Menu.Item key="center">
            <UserOutlined />
            <FormattedMessage id="menu.account.center" defaultMessage="account center" />
          </Menu.Item>
        )}
        <Menu.Item key="/account/password">
          <LockOutlined />
          修改密码
        </Menu.Item>
        {currentUser.admin_scopes && currentUser.admin_scopes.includes('admin') && false &&
        <Menu.Item key="/admin/users">
          <UserOutlined />
          用户管理
        </Menu.Item>}
        {currentUser.admin_scopes && currentUser.admin_scopes.includes('admin') && false &&
        <Menu.Item key="/admin/exusers">
          <UserOutlined />
          外部用户管理
        </Menu.Item>}
        <Menu.Divider />
        <Menu.Item key="logout">
          <LogoutOutlined />
          <FormattedMessage id="menu.account.logout" defaultMessage="logout" />
        </Menu.Item>
      </Menu>
    )

    return currentUser && currentUser.nickname ? (
      <HeaderDropdown overlay={menuHeaderDropdown}>
        <span className={`${styles.action} ${styles.account}`}>
          <Avatar
            style={{ backgroundColor: '#607d8b' }}
            size="small"
            className={styles.avatar}
            alt="avatar"
          >
            {currentUser.nickname.slice(0, 1)}
          </Avatar>
          <span className={styles.name}>{currentUser.nickname}</span>
        </span>
      </HeaderDropdown>
    ) : (
      <Spin size="small" style={{ marginLeft: 8, marginRight: 8 }} />
    )
  }
}
export default connect(({ user }: ConnectState) => ({
  currentUser: user.currentUser,
}))(AvatarDropdown)
