import React from 'react'
import {
  Affix,
  Tabs,
} from 'antd'
import router from 'umi/router'

const { TabPane } = Tabs

const SystemMonitor = ({ location, tabBarExtraContent, tabs, offsetTop }) => {
  const { pathname } = location
  const splits = pathname.split('/')
  let activeTab = splits.pop()
  const pathPrefix = splits.join('/')
  const handleTabChange = currentTab => {
    router.push(`${pathPrefix}/${currentTab}`)
  }
  return (
    <Affix offsetTop={offsetTop}>
      <Tabs
        animated={false}
        activeKey={activeTab}
        onChange={handleTabChange}
        tabBarExtraContent={tabBarExtraContent}
        style={{
          marginBottom: 15,
          background: 'rgb(24, 31, 41)',
        }}
      >
        {tabs.map(item => <TabPane tab={item.name} key={item.tab}/>)}
      </Tabs>
    </Affix>
  )
}

export default SystemMonitor
