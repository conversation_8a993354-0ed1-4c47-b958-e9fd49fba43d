import React from 'react'
import {
  Table,
} from 'antd'
import _ from 'lodash'

const normalizeFixedRange = (x, minX, maxX, min, max) => {
  const diffX = maxX - minX
  const diffY = max - min
  return min + ((x - minX) * diffY) / diffX
}

const renderWeightProgress = (weight: number, value: number, format) => {
  if (value === undefined || value === null) {
    return '-'
  }
  const progressColor = value > 0 ? '#e85555' : '#3ec09d'
  const leftMargin = value < 0 ? 50 - weight / 2 : 50
  const positiveRadius = '0 10px 10px 0'
  const negtiveRadius = '10px 0px 0px 10px'
  return (
    <div
      style={{ paddingRight: 20 }}
      className="ant-progress ant-progress-line ant-progress-status-normal ant-progress-show-info ant-progress-default"
    >
      <div>
        <div className="ant-progress-outer" style={{ width: '70px' }}>
          <div className="ant-progress-inner" style={{ backgroundColor: '#373e4a' }}>
            <div
              className="ant-progress-bg"
              style={{
                left: `${leftMargin}%`,
                width: `${Math.abs(weight / 2)}%`,
                height: '8px',
                backgroundColor: progressColor,
                borderRadius: value > 0 ? positiveRadius : negtiveRadius,
              }}
            ></div>
          </div>
        </div>
        <span className="ant-progress-text" title="60%">
          {format === 'number' ? value.toFixed(4) : (value * 100).toFixed(2) + '%'}
        </span>
      </div>
    </div>
  )
}

export default ({ tableData, tableColumns }) => {
  const columns = tableColumns.map(col => {
    if (!col.isProgressBar) {
      return col
    }
    const key = col.dataIndex
    const keyN = `${col.dataIndex}_normalized`
    return {
      ...col,
      render: (text: any, record: any) => {
        return renderWeightProgress(
          record[keyN],
          record[key],
          col.format,
        )
      },
    }
  })
  const data = tableData.map(fund => {
    columns.forEach(col => {
      const value = fund[col.dataIndex]
      if (col.isProgressBar && value !== undefined) {
        const vlaues = tableData
          .map(item => item[col.dataIndex])
          .filter(item => item !== undefined)
          .map(item => Math.abs(item))
        fund[`${col.dataIndex}_normalized`] = normalizeFixedRange(
          Math.abs(value),
          0,
          Math.max.apply(null, vlaues),
          0,
          80,
        )
      }
    })
    return fund
  })
  return (
    <Table
      size="small"
      columns={columns}
      pagination={false}
      dataSource={data}
      scroll={{ y: 500 }}
      data={{ list: data, pagination: false }}
    />
  )
}
