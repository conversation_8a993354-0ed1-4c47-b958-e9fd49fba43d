exports.__esModule = true

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : { default: obj }
}

const _react = require('react')

const _react2 = _interopRequireDefault(_react)

const _common = require('./common')

/**
 * Checks whether a prop provides a type of element.
 *
 * The type of element can be provided in two forms:
 * - tag name (string)
 * - a return value of React.createClass(...)
 *
 * @param props
 * @param propName
 * @param componentName
 * @returns {Error|undefined}
 */

function validate(props, propName, componentName) {
  const errBeginning = _common.errMsg(
    props,
    propName,
    componentName,
    '. Expected an Element `type`'
  )

  if (typeof props[propName] !== 'function') {
    if (_react2.default.isValidElement(props[propName])) {
      return new Error(errBeginning + ', not an actual Element')
    }

    if (typeof props[propName] !== 'string') {
      return new Error(
        errBeginning +
          ' such as a tag name or return value of React.createClass(...)'
      )
    }
  }
}

exports.default = _common.createChainableTypeChecker(validate)
module.exports = exports.default
