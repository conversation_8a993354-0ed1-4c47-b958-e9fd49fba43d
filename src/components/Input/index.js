const _inherits = require('babel-runtime/helpers/inherits').default

const _classCallCheck = require('babel-runtime/helpers/class-call-check')
  .default

const _interopRequireDefault = require('babel-runtime/helpers/interop-require-default')
  .default

const _interopRequireWildcard = require('babel-runtime/helpers/interop-require-wildcard')
  .default

exports.__esModule = true

const _react = require('react')

const _react2 = _interopRequireDefault(_react)

const _propTypes = require('prop-types')

const _propTypes2 = _interopRequireDefault(_propTypes)

const _InputBase2 = require('./InputBase')

const _InputBase3 = _interopRequireDefault(_InputBase2)

const _FormControls = require('./FormControls')

const FormControls = _interopRequireWildcard(_FormControls)

const _utilsDeprecationWarning = require('./utils/deprecationWarning')

const _utilsDeprecationWarning2 = _interopRequireDefault(
  _utilsDeprecationWarning
)

const Input = (function(_InputBase) {
  // eslint-disable-line
  _inherits(Input, _InputBase) // eslint-disable-line

  function Input() {
    // eslint-disable-line
    _classCallCheck(this, Input)

    _InputBase.apply(this, arguments)
  }

  Input.prototype.render = function render() {
    if (this.props.type === 'static') {
      _utilsDeprecationWarning2.default(
        'Input type=static',
        'FormControls.Static'
      )
      return _react2.default.createElement(FormControls.Static, this.props)
    }

    return _InputBase.prototype.render.call(this)
  }

  return Input
})(_InputBase3.default)

Input.propTypes = {
  type: _propTypes2.default.string
}

exports.default = Input
module.exports = exports.default
