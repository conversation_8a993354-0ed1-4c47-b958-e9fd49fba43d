const _extends = require('babel-runtime/helpers/extends').default

const _interopRequireDefault = require('babel-runtime/helpers/interop-require-default')
  .default

exports.__esModule = true

const _react = require('react')

const _react2 = _interopRequireDefault(_react)

const _propTypes = require('prop-types')

const _propTypes2 = _interopRequireDefault(_propTypes)

const createReactClass = require('create-react-class')

const _classnames = require('classnames')

const _classnames2 = _interopRequireDefault(_classnames)

const Glyphicon = createReactClass({
  displayName: 'Glyphicon',

  propTypes: {
    /**
     * bootstrap className
     * @private
     */
    bsClass: _propTypes2.default.string,
    /**
     * An icon name. See e.g. http://getbootstrap.com/components/#glyphicons
     */
    glyph: _propTypes2.default.string.isRequired,
    /**
     * Adds 'form-control-feedback' class
     * @private
     */
    formControlFeedback: _propTypes2.default.bool
  },

  getDefaultProps: function getDefaultProps() {
    return {
      bsClass: 'glyphicon',
      formControlFeedback: false
    }
  },

  render: function render() {
    let _classNames

    const className = _classnames2.default(
      this.props.className,  // eslint-disable-line
      ((_classNames = {}),
      (_classNames[this.props.bsClass] = true),
      (_classNames['glyphicon-' + this.props.glyph] = true),
      (_classNames['form-control-feedback'] = this.props.formControlFeedback),
      _classNames)
    )

    return _react2.default.createElement(
      'span',
      _extends({}, this.props, { className: className }),
      this.props.children // eslint-disable-line
    )
  }
})

exports.default = Glyphicon
module.exports = exports.default
