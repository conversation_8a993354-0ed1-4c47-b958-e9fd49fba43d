const _inherits = require('babel-runtime/helpers/inherits').default

const _classCallCheck = require('babel-runtime/helpers/class-call-check')
  .default

const _objectWithoutProperties = require('babel-runtime/helpers/object-without-properties')
  .default

const _extends = require('babel-runtime/helpers/extends').default

const _interopRequireDefault = require('babel-runtime/helpers/interop-require-default')
  .default

exports.__esModule = true

const _react = require('react')

const _react2 = _interopRequireDefault(_react)

const _classnames = require('classnames')

const _classnames2 = _interopRequireDefault(_classnames)

const _InputBase2 = require('../InputBase')

const _InputBase3 = _interopRequireDefault(_InputBase2)

const _utilsChildrenValueInputValidation = require('../utils/childrenValueInputValidation')

const _utilsChildrenValueInputValidation2 = _interopRequireDefault(
  _utilsChildrenValueInputValidation
)

const _reactPropTypesLibElementType = require('../lib/elementType')

const _reactPropTypesLibElementType2 = _interopRequireDefault(
  _reactPropTypesLibElementType
)

const Static = (function(_InputBase) {
  // eslint-disable-line
  _inherits(Static, _InputBase) // eslint-disable-line

  function Static() {
    // eslint-disable-line
    _classCallCheck(this, Static)

    _InputBase.apply(this, arguments)
  }

  Static.prototype.getValue = function getValue() {
    const _props = this.props
    const children = _props.children
    const value = _props.value

    return children || value
  }

  Static.prototype.renderInput = function renderInput() {
    const _props2 = this.props
    const ComponentClass = _props2.componentClass

    const props = _objectWithoutProperties(_props2, ['componentClass'])

    return _react2.default.createElement(
      ComponentClass,
      _extends({}, props, {
        className: _classnames2.default(props.className, 'form-control-static'),
        ref: 'input',
        key: 'input'
      }),
      this.getValue()
    )
  }

  return Static
})(_InputBase3.default)

Static.propTypes = {
  value: _utilsChildrenValueInputValidation2.default,
  /**
   * You can override the default 'p' with a custom element
   */
  componentClass: _reactPropTypesLibElementType2.default,
  children: _utilsChildrenValueInputValidation2.default
}

Static.defaultProps = {
  componentClass: 'p'
}

exports.default = Static
module.exports = exports.default
