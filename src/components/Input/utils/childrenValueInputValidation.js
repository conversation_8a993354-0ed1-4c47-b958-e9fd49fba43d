const _interopRequireDefault = require('babel-runtime/helpers/interop-require-default')
  .default

exports.__esModule = true

const _propTypes = require('prop-types')

const _propTypes2 = _interopRequireDefault(_propTypes)

const _reactPropTypesLibSinglePropFrom = require('../lib/singlePropFrom')

const _reactPropTypesLibSinglePropFrom2 = _interopRequireDefault(
  _reactPropTypesLibSinglePropFrom
)

function valueValidation(props, propName, componentName) {
  let error = _reactPropTypesLibSinglePropFrom2.default('children', 'value')(
    props,
    propName,
    componentName
  )

  if (!error) {
    error = _propTypes2.default.node(props, propName, componentName)
  }

  return error
}

exports.default = valueValidation

module.exports = exports.default
