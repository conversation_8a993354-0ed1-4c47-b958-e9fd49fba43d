const _inherits = require('babel-runtime/helpers/inherits').default

const _classCallCheck = require('babel-runtime/helpers/class-call-check')
  .default

const _interopRequireDefault = require('babel-runtime/helpers/interop-require-default')
  .default

exports.__esModule = true

const _warning = require('warning')

const _warning2 = _interopRequireDefault(_warning)

const warned = {}

function deprecationWarning(oldname, newname, link) {
  let message

  if (typeof oldname === 'object') {
    message = oldname.message
  } else {
    message = oldname + ' is deprecated. Use ' + newname + ' instead.'

    if (link) {
      message += '\nYou can read more about it at ' + link
    }
  }

  if (warned[message]) {
    return
  }

  process.env.NODE_ENV !== 'production' // eslint-disable-line
    ? _warning2.default(false, message) // eslint-disable-line
    : undefined // eslint-disable-line
  warned[message] = true
}

deprecationWarning.wrapper = function(Component) {
  // eslint-disable-line
  for (
    var _len = arguments.length,
      args = Array(_len > 1 ? _len - 1 : 0),
      _key = 1;
    _key < _len;
    _key++
  ) {
    // eslint-disable-line
    args[_key - 1] = arguments[_key]
  }

  return (function(_Component) {
    // eslint-disable-line
    _inherits(DeprecatedComponent, _Component) // eslint-disable-line

    function DeprecatedComponent() {
      _classCallCheck(this, DeprecatedComponent)

      _Component.apply(this, arguments)
    }

    DeprecatedComponent.prototype.componentWillMount = function componentWillMount() {
      deprecationWarning.apply(undefined, args) // eslint-disable-line

      if (_Component.prototype.componentWillMount) {
        let _Component$prototype$componentWillMount

        for (
          var _len2 = arguments.length, methodArgs = Array(_len2), _key2 = 0;
          _key2 < _len2;
          _key2++
        ) {
          // eslint-disable-line
          methodArgs[_key2] = arguments[_key2]
        }

        ;(_Component$prototype$componentWillMount =
          _Component.prototype.componentWillMount).call.apply(
          _Component$prototype$componentWillMount,
          [this].concat(methodArgs)
        ) // eslint-disable-line
      }
    }

    return DeprecatedComponent
  })(Component)
}

exports.default = deprecationWarning
module.exports = exports.default
