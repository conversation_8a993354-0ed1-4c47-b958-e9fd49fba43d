const _inherits = require('babel-runtime/helpers/inherits').default

const _classCallCheck = require('babel-runtime/helpers/class-call-check')
  .default

const _extends = require('babel-runtime/helpers/extends').default

const _interopRequireDefault = require('babel-runtime/helpers/interop-require-default')
  .default

exports.__esModule = true

const _classnames = require('classnames')

const _classnames2 = _interopRequireDefault(_classnames)

const _react = require('react')

const _react2 = _interopRequireDefault(_react)

const _propTypes = require('prop-types')

const _propTypes2 = _interopRequireDefault(_propTypes)

const _FormGroup = require('./FormGroup')

const _FormGroup2 = _interopRequireDefault(_FormGroup)

const _Glyphicon = require('./Glyphicon')

const _Glyphicon2 = _interopRequireDefault(_Glyphicon)

const InputBase = (function(_React$Component) {
  // eslint-disable-line
  _inherits(InputBase, _React$Component) // eslint-disable-line

  function InputBase() {
    // eslint-disable-line
    _classCallCheck(this, InputBase)

    _React$Component.apply(this, arguments)
  }

  InputBase.prototype.getInputDOMNode = function getInputDOMNode() {
    return this.refs.input
  }

  InputBase.prototype.getValue = function getValue() {
    if (this.props.type === 'static') {
      return this.props.value
    } else if (this.props.type) {
      if (this.props.type === 'select' && this.props.multiple) {
        return this.getSelectedOptions()
      }
      return this.getInputDOMNode().value
    }
    throw new Error('Cannot use getValue without specifying input type.')
  }

  InputBase.prototype.getChecked = function getChecked() {
    return this.getInputDOMNode().checked
  }

  InputBase.prototype.getSelectedOptions = function getSelectedOptions() {
    const values = []

    Array.prototype.forEach.call(
      this.getInputDOMNode().getElementsByTagName('option'),
      function(option) {
        // eslint-disable-line
        if (option.selected) {
          const value = option.getAttribute('value') || option.innerHtml
          values.push(value)
        }
      }
    )

    return values
  }

  InputBase.prototype.isCheckboxOrRadio = function isCheckboxOrRadio() {
    return this.props.type === 'checkbox' || this.props.type === 'radio'
  }

  InputBase.prototype.isFile = function isFile() {
    return this.props.type === 'file'
  }

  InputBase.prototype.renderInputGroup = function renderInputGroup(children) {
    const addonBefore = this.props.addonBefore
      ? _react2.default.createElement(
        'span',
        { className: 'input-group-addon', key: 'addonBefore' },
        this.props.addonBefore
      )
      : null

    const addonAfter = this.props.addonAfter
      ? _react2.default.createElement(
        'span',
        { className: 'input-group-addon', key: 'addonAfter' },
        this.props.addonAfter
      )
      : null

    const buttonBefore = this.props.buttonBefore
      ? _react2.default.createElement(
        'span',
        { className: 'input-group-btn' },
        this.props.buttonBefore
      )
      : null

    const buttonAfter = this.props.buttonAfter
      ? _react2.default.createElement(
        'span',
        { className: 'input-group-btn' },
        this.props.buttonAfter
      )
      : null

    let inputGroupClassName
    switch (this.props.bsSize) {
    case 'small':
      inputGroupClassName = 'input-group-sm'
        break // eslint-disable-line
    case 'large':
      inputGroupClassName = 'input-group-lg'
        break // eslint-disable-line
    default:
    }

    return addonBefore || addonAfter || buttonBefore || buttonAfter
      ? _react2.default.createElement(
        'div',
        {
          className: _classnames2.default(inputGroupClassName, 'input-group'),
          key: 'input-group'
        },
        addonBefore,
        buttonBefore,
        children,
        addonAfter,
        buttonAfter
      )
      : children
  }

  InputBase.prototype.renderIcon = function renderIcon() {
    if (this.props.hasFeedback) {
      if (this.props.feedbackIcon) {
        return _react2.default.cloneElement(this.props.feedbackIcon, {
          formControlFeedback: true
        })
      }

      switch (this.props.bsStyle) {
      case 'success':
        return _react2.default.createElement(_Glyphicon2.default, {
          formControlFeedback: true,
          glyph: 'ok',
          key: 'icon'
        })
      case 'warning':
        return _react2.default.createElement(_Glyphicon2.default, {
          formControlFeedback: true,
          glyph: 'warning-sign',
          key: 'icon'
        })
      case 'error':
        return _react2.default.createElement(_Glyphicon2.default, {
          formControlFeedback: true,
          glyph: 'remove',
          key: 'icon'
        })
      default:
        return _react2.default.createElement('span', {
          className: 'form-control-feedback',
          key: 'icon'
        })
      }
    } else {
      return null
    }
  }

  InputBase.prototype.renderHelp = function renderHelp() {
    return this.props.help
      ? _react2.default.createElement(
        'span',
        { className: 'help-block', key: 'help' },
        this.props.help
      )
      : null
  }

  InputBase.prototype.renderCheckboxAndRadioWrapper = function renderCheckboxAndRadioWrapper(
    children
  ) {
    const classes = {
      checkbox: this.props.type === 'checkbox',
      radio: this.props.type === 'radio'
    }

    return _react2.default.createElement(
      'div',
      { className: _classnames2.default(classes), key: 'checkboxRadioWrapper' },
      children
    )
  }

  InputBase.prototype.renderWrapper = function renderWrapper(children) {
    return this.props.wrapperClassName
      ? _react2.default.createElement(
        'div',
        { className: this.props.wrapperClassName, key: 'wrapper' },
        children
      )
      : children
  }

  InputBase.prototype.renderLabel = function renderLabel(children) {
    const classes = {
      'control-label': !this.isCheckboxOrRadio()
    }
    classes[this.props.labelClassName] = this.props.labelClassName

    return this.props.label
      ? _react2.default.createElement(
        'label',
        {
          htmlFor: this.props.id,
          className: _classnames2.default(classes),
          key: 'label'
        },
        children,
        this.props.label
      )
      : children
  }

  InputBase.prototype.renderInput = function renderInput() {
    if (!this.props.type) {
      return this.props.children
    }

    switch (this.props.type) {
    case 'select':
      return _react2.default.createElement(
        'select',
        _extends({}, this.props, {
          className: _classnames2.default(
            this.props.className,
            'form-control'
          ),
          ref: 'input',
          key: 'input'
        }),
        this.props.children
      )
    case 'textarea':
      return _react2.default.createElement(
        'textarea',
        _extends({}, this.props, {
          className: _classnames2.default(
            this.props.className,
            'form-control'
          ),
          ref: 'input',
          key: 'input'
        })
      )
    case 'static':
      return _react2.default.createElement(
        'p',
        _extends({}, this.props, {
          className: _classnames2.default(
            this.props.className,
            'form-control-static'
          ),
          ref: 'input',
          key: 'input'
        }),
        this.props.value
      )
    default:
      const className =
          this.isCheckboxOrRadio() || this.isFile() ? '' : 'form-control'
      return _react2.default.createElement(
        'input',
        _extends({}, this.props, {
          className: _classnames2.default(this.props.className, className),
          ref: 'input',
          key: 'input'
        })
      )
    }
  }

  InputBase.prototype.renderFormGroup = function renderFormGroup(children) {
    return _react2.default.createElement(
      _FormGroup2.default,
      this.props,
      children
    )
  }

  InputBase.prototype.renderChildren = function renderChildren() {
    return !this.isCheckboxOrRadio()
      ? [
        this.renderLabel(),
        this.renderWrapper([
          this.renderInputGroup(this.renderInput()),
          this.renderIcon(),
          this.renderHelp()
        ])
      ]
      : this.renderWrapper([
        this.renderCheckboxAndRadioWrapper(
          this.renderLabel(this.renderInput())
        ),
        this.renderHelp()
      ])
  }

  InputBase.prototype.render = function render() {
    const children = this.renderChildren()
    return this.renderFormGroup(children)
  }

  return InputBase
})(_react2.default.Component)

InputBase.propTypes = {
  type: _propTypes2.default.string,
  label: _propTypes2.default.node,
  help: _propTypes2.default.node,
  addonBefore: _propTypes2.default.node,
  addonAfter: _propTypes2.default.node,
  buttonBefore: _propTypes2.default.node,
  buttonAfter: _propTypes2.default.node,
  bsSize: _propTypes2.default.oneOf(['small', 'medium', 'large']),
  bsStyle: _propTypes2.default.oneOf(['success', 'warning', 'error']),
  hasFeedback: _propTypes2.default.bool,
  feedbackIcon: _propTypes2.default.node,
  id: _propTypes2.default.oneOfType([
    _propTypes2.default.string,
    _propTypes2.default.number
  ]),
  groupClassName: _propTypes2.default.string,
  wrapperClassName: _propTypes2.default.string,
  labelClassName: _propTypes2.default.string,
  multiple: _propTypes2.default.bool,
  disabled: _propTypes2.default.bool,
  value: _propTypes2.default.any
}

InputBase.defaultProps = {
  disabled: false,
  hasFeedback: false,
  multiple: false
}

exports.default = InputBase
module.exports = exports.default
