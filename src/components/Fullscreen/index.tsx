import React from 'react'
import { useFullscreen } from '@umijs/hooks'
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'

export default () => {
  const body = document.getElementsByTagName('html')[0]
  const { ref, isFullscreen, setFull, exitFull } = useFullscreen<HTMLDivElement>({
    dom: () => body,
  })
  const handleExitFull = () => {
    body.className = ''
    exitFull()
  }
  const handleFull = () => {
    setFull()
    body.className = 'js-fullscreen'
  }
  // const userAgent = navigator.userAgent
  // const isSafari = userAgent.indexOf('Safari') !== -1 && userAgent.indexOf('Chrome') === -1
  // if (isSafari) {
  //   return false
  // }
  return (
    <span ref={ref} style={{ fontSize: 16, marginRight: 5 }}>
      {isFullscreen ?
      <Tooltip title="退出全屏"><FullscreenExitOutlined onClick={handleExitFull} /></Tooltip> :
      <Tooltip title="全屏"><FullscreenOutlined onClick={handleFull} /></Tooltip>}
    </span>
  )
}
