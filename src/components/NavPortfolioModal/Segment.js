import React, { Component } from 'react'
import classnames from 'classnames'
import moment from 'moment'
import SelectFundModal from '@/components/SelectFundModal'
import RatioSelector from './RatioSelector'
import math from '../../utils/math'
import { PlusOutlined, HourglassOutlined, UploadOutlined } from '@ant-design/icons'
import { Dropdown, Menu, List, Button, Input, DatePicker, Row, Col, Divider, Space, Popover, Upload, notification } from 'antd'
import thousandFormatter from '../../utils/thousandFormatter'
import { getToken } from '@/utils/utils'
import styles from './style.less'

const InputGroup = Input.Group

export default class Segment extends Component {
  shouldComponentUpdate(nextProps) {
    const { activeKey, index } = nextProps
    return activeKey === `${index + 1}`
  }

  onSelectAllocation = value => () => {
    const { index } = this.props
    if (value === 'avg') {
      this.props.avgAllocation(index)
    } else {
      this.props.scaleAllocation(index)
    }
  };

  getUploadProps = () => {
    const props = {
      name: 'attachment',
      action: '/api/portfolios/net/single_weight_file',
      headers: {
        authorization: getToken(),
      },
      showUploadList: false,
      onChange: info => {
        if (info.file.status === 'done') {
          notification.success({ message: '上传成功' })
          this.props.handleUploadSegmentFunds(this.props.index, info.file.response)
        } else if (info.file.status === 'error') {
          notification.error({
            message: info.file.response.message,
          })
        }
      },
    }
    return props
  };

  isCashPortfolio = () => {
    return this.props.navPortfolioType === 'cash'
  };

  render() {
    const {
      item,
      index,
      fundLabel,
      activeKey,
      isIndex,
      fundType,
      navPortfolioType,
      t,
      defaultTabs,
    } = this.props
    if (activeKey !== `${index + 1}`) {
      return false
    }
    const values = (item.funds || []).map(fund => Number(item.inputValues[fund._id]))
    const ratioSum = math.sum(values).toFixed(2) || 0
    const allocationMap = [
      { name: t('portfolio.equalDistributed'), value: 'avg' },
      { name: t('portfolio.allocatedByScale'), value: 'scale' },
    ]
    const allocationMenu = (
      <Menu selectable defaultSelectedKeys={isIndex() ? ['scale'] : ['avg']}>
        {allocationMap &&
          allocationMap.map(allocation => (
            <Menu.Item key={allocation.value}>
              <div onClick={this.onSelectAllocation(allocation.value)}>{allocation.name}</div>
            </Menu.Item>
          ))}
      </Menu>
    )
    const isCashPortfolio = this.isCashPortfolio()
    const weightFilePopoverConcent = (
      <div>
        <p>
        您可以通过上传文件更新组合调仓信息，文件示例如下：
        </p>
        <div className="ant-table ant-table-small ant-table-bordered ant-table-scroll-position-left">
          <div className="ant-table-content">
            <div className="ant-table-body">
              <table>
                <thead className="ant-table-thead">
                  <tr>
                    <th>{t('portfolio.fundCode')}</th>
                    <th>{!isCashPortfolio ? t('portfolio.weights') : t('portfolio.fundAmount')}</th>
                  </tr>
                </thead>
                <tbody className="ant-table-tbody">
                  <tr>
                    <td>202001</td>
                    <td>{!isCashPortfolio ? '50%' : '10000'}</td>
                  </tr>
                  <tr>
                    <td>040001</td>
                    <td>{!isCashPortfolio ? '30%' : '30000'}</td>
                  </tr>
                  <tr>
                    <td>000001</td>
                    <td>{!isCashPortfolio ? '20%' : '20000'}</td>
                  </tr>
                  <tr>
                    <td>202101</td>
                    <td>{!isCashPortfolio ? '50%' : '15000'}</td>
                  </tr>
                  <tr>
                    <td>000001</td>
                    <td>{!isCashPortfolio ? '50%' : '35000'}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    )
    return (
      <div>
        <List
          className={styles.fundList}
          itemLayout="horizontal"
          header={
            <List.Item
              className={styles.listHeader}
              extra={
              <Space>
                <Row key="0" className={styles.dateRange}>
                  <Col span={10}>
                    <DatePicker
                      style={{ width: '110px' }}
                      size="small"
                      value={item.startDate && moment(new Date(item.startDate))}
                      onChange={this.props.selectStartDate(index)}
                    />
                    <span style={{ marginLeft: 10 }}>-</span>
                  </Col>
                  <Col span={14}>
                    <InputGroup compact>
                      <DatePicker
                        style={{ width: '110px' }}
                        size="small"
                        value={item.endDate && moment(new Date(item.endDate))}
                        onChange={this.props.selectEndDate(index)}
                      />
                      <Button
                        size="small"
                        className={{ active: item.endToNow }}
                        onClick={this.props.handleSetLatest(index)}
                      >
                        至今
                      </Button>
                    </InputGroup>
                  </Col>
                </Row>
                <SelectFundModal
                  title={`请选择${fundLabel}`}
                  key="1"
                  isNav
                  defaultTabs={
                    fundType === 'css'
                      ? [
                          {
                            name: 'TAA策略',
                            value: 'taaStrategy',
                          },
                        ]
                      : undefined
                  }
                  isStock={this.props.isStock()}
                  isIndex={this.props.isIndex()}
                  showTabs={this.props.showTabs}
                  isSelectable={this.props.isSelectable}
                  onChange={this.props.onSelectFunds(index)}
                  defaultTabs={defaultTabs}
                >
                  <Button type="primary" icon={<PlusOutlined />} size="small">{`添加${fundLabel}`}</Button>
                </SelectFundModal>
                <Popover title={null} content={weightFilePopoverConcent}>
                  <Button
                    type="primary"
                    icon={
                      <Upload {...this.getUploadProps()}>
                        <UploadOutlined />
                      </Upload>
                    }
                    size="small"
                  />
                </Popover>
                {fundType !== 'css' && navPortfolioType !== 'cash' ? (
                  <Dropdown placement="bottomCenter" overlay={allocationMenu}>
                    <Button type="primary" icon={<HourglassOutlined />} size="small"/>
                  </Dropdown>
                ) : (
                  false
                )}
              </Space>
              }
            >
              <List.Item.Meta
                title={
                  <span>
                    已选
                    {fundLabel}（
                    {item.funds &&
                      item.funds.filter(fund => fundType !== 'css' || !fund.isSaa).length}
                    ）
                  </span>
                }
              />
            </List.Item>
          }
          dataSource={item.funds.filter(fund => fundType !== 'css' || !fund.isSaa)}
          renderItem={fund => (
            <RatioSelector
              fund={fund}
              inputValue={+item.inputValues[fund._id]}
              sliderValue={item.sliderValues[fund._id]}
              onSliderChange={this.props.onSliderChange(fund._id, index)}
              onInputChange={this.props.onInputChange}
              removeFund={this.props.removeFund}
              fundLabel={fundLabel}
              index={index}
              navPortfolioType={navPortfolioType}
              t={this.props.t}
            />
          )}
        />
        {fundType === 'css' && <Divider />}
        {fundType === 'css' && (
          <List
            className={styles.fundList}
            itemLayout="horizontal"
            header={
              <List.Item
                className={styles.listHeader}
                extra={[
                  <SelectFundModal
                    key="1"
                    isNav
                    isRadio
                    defaultTabs={[
                      {
                        name: 'SAA策略',
                        value: 'saaStrategy',
                      },
                    ]}
                    isStock={this.props.isStock()}
                    isIndex={this.props.isIndex()}
                    showTabs={this.props.showTabs}
                    isSelectable={this.props.isSelectable}
                    onChange={this.props.onSelectFunds(index, true)}
                    className={styles.actionButton}
                    rowSelectionType="radio"
                    defaultTabs={defaultTabs}
                  >
                    <Button type="primary" icon={<PlusOutlined />} size="small">{`添加SAA策略`}</Button>
                  </SelectFundModal>,
                ]}
              >
                <List.Item.Meta title={t('portfolio.chooseSaaStrategy')} />
              </List.Item>
            }
            dataSource={item.funds.filter(fund => fund.isSaa)}
            renderItem={fund => (
              <RatioSelector
                fund={fund}
                inputValue={item.inputValues[fund._id]}
                sliderValue={item.sliderValues[fund._id]}
                onSliderChange={this.props.onSliderChange(fund._id, index)}
                onInputChange={this.props.onInputChange}
                removeFund={this.props.removeFund}
                fundLabel={fundLabel}
                index={index}
                t={this.props.t}
              />
            )}
          />
        )}
        <div className={classnames(styles.ratioSum)}>
          {t('portfolio.total')}：{thousandFormatter(ratioSum)}
          {navPortfolioType !== 'cash' ? '%' : ''}
        </div>
      </div>
    );
  }
}
