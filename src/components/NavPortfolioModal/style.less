@import '~antd/es/style/themes/default.less';

.modal {
  .segmentHeader {
    position: relative;
    .delSegment {
      font-size: 14px;
      position: absolute;
      cursor: pointer;
      color: #337ab7;
      right: 10px;
      bottom: 0px;
    }
  }
  .addSegment {
    color: @primary-color;
  }
  .listHeader {
    font-size: 16px;
    :global(.ant-list-item-content) {
      justify-content: center;
    }
    .avgAllocation {
      cursor: pointer;
      color: @primary-color;
    }
  }
  .dateRange {
    width: 330px;
    :global(.ant-btn) {
      border: 1px solid rgba(255, 255, 255, 0.25);
    }
    :global(.ant-btn.active) {
      border-color: @primary-color;
    }
  }
  .icon {
    position: relative;
    right: 5px;
    cursor: pointer;
  }
  .inNum {
    width: 35px;
    height: 20px;
  }
  .fundList {
    max-height: 250px;
    overflow-y: scroll;
    :global(.ant-list-header) {
      padding-top: 0px;
    }
    :global(.ant-list-item) {
      padding: 0px;
      height: 32px;
    }
  }
  .actionButtons {
    float: right;
    margin-right: 30px;
  }
  .autoAllocate {
    margin-right: 50px;
  }
  .ratioSum {
    font-size: 12px;
    text-align: right;
    margin-top: 15px;
  }
  .dropzone {
    position: relative;
    :global(.anticon) {
      line-height: 1;
    }
  }
  .slider {
    width: 160px;
  }
  .switchBenchmark {
    button {
      overflow: hidden;
      width: 200px;
      text-overflow: ellipsis;
    }
  }
  .label {
    color: rgba(0, 0, 0, 0.85);
  }
}
