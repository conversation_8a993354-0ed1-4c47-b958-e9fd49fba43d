
import React, { Component } from 'react'
import classnames from 'classnames'
import { Input, DatePicker, Button, Row, Col } from 'antd'
import moment from 'moment'

const InputGroup = Input.Group

export default class TimeRangeSelector extends Component {
  render() {
    const styles = require('./TimeRangeSelector.less')
    const { startDate, endDate, endToNow } = this.props
    return (
      <div className={classnames(styles.periodWapper)}>
        <Row>
          <Col span={11}>
            <InputGroup compact>
              <Button className={styles.label}>开始时间</Button>
              <DatePicker
                value={startDate && moment(new Date(startDate))}
                onChange={this.props.selectStartDate}
              />
            </InputGroup>
          </Col>
          <Col span={13}>
            <InputGroup compact>
              <Button className={styles.label}>结束时间</Button>
              <Button className={{ active: endToNow }} onClick={this.props.handleSetLatest}>
                至今
              </Button>
              <DatePicker
                value={endDate && moment(new Date(endDate))}
                onChange={this.props.selectEndDate}
              />
            </InputGroup>
          </Col>
        </Row>
      </div>
    )
  }
}
