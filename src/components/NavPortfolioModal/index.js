import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { HourglassOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons'
import {
  Modal,
  Button,
  Popover,
  Input,
  Select,
  DatePicker,
  Collapse,
  Row,
  Col,
  Switch,
  Popconfirm,
  notification,
  Spin,
  Upload,
  Form,
  InputNumber,
  Card,
} from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import throttle from 'lodash/throttle'
import uniq from 'lodash/uniq'
import flatten from 'lodash/flatten'
import uniqBy from 'lodash/uniqBy'
import Segment from './Segment'
import requestUtils from '@/utils/request'
import { getToken } from '@/utils/utils'
import styles from './style.less'

const ButtonGroup = Button.Group
const Option = Select.Option
const InputGroup = Input.Group
const Panel = Collapse.Panel

@connect(({ fund, loading }) => ({
  portfolio: fund.portfolio,
  addNetPortfolioSuccess: fund.addNetPortfolioSuccess,
  editNetPortfolioSuccess: fund.editNetPortfolioSuccess,
  loading: loading.models.fund,
}))
export default class NavPortfolioModal extends Component {
  static propTypes = {
    t: PropTypes.func,
    children: PropTypes.object.isRequired,
    className: PropTypes.string,
    funds: PropTypes.array,
    portfolio: PropTypes.object,
    fundType: PropTypes.string,
    defaultWeights: PropTypes.object,
    loading: PropTypes.bool,
    isEdit: PropTypes.bool,
    fundData: PropTypes.object,
    features: PropTypes.object.isRequired,
    autoAllocate: PropTypes.bool,
    name: PropTypes.string,
    navPortfolioType: PropTypes.string,
    dispatch: PropTypes.func.isRequired,
    addNetPortfolioSuccess: PropTypes.bool,
    editNetPortfolioSuccess: PropTypes.bool,
    show: PropTypes.bool,
    defaultTabs: PropTypes.array,
    isReloadAfterCreated: PropTypes.bool,
  };

  constructor(props) {
    super(props)
    const state = {
      show: false,
      autoBalance: false,
      activeKey: '1',
      segment: [
        {
          funds: [],
        },
      ],
      cycleWeeks: [
        {
          name: this.props.t('portfolio.monday'),
          value: '1',
        },
        {
          name: this.props.t('portfolio.tuesday'),
          value: '2',
        },
        {
          name: this.props.t('portfolio.wednesday'),
          value: '3',
        },
        {
          name: this.props.t('portfolio.thursday'),
          value: '4',
        },
        {
          name: this.props.t('portfolio.friday'),
          value: '5',
        },
        {
          name: this.props.t('portfolio.saturday'),
          value: '6',
        },
        {
          name: this.props.t('portfolio.sunday'),
          value: '0',
        },
      ],
      cycleDays: [
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
        28,
        29,
        30,
        31,
      ].map(item => ({ name: item, value: item })),
    }
    this.state = state
  }

  componentDidMount() {
    if (this.props.show) {
      this.open()
    }
  }

  componentWillReceiveProps(nextProps) {
    const { addNetPortfolioSuccess, editNetPortfolioSuccess, isEdit, portfolio, isReloadAfterCreated } = nextProps
    if (addNetPortfolioSuccess && this.state.show) {
      if (isReloadAfterCreated) {
        window.location.reload()
      }
      this.close()
    }
    if (editNetPortfolioSuccess && this.state.show) {
      this.close()
    }

    if (isEdit && !this.props.portfolio && portfolio && this.state.show) {
      const segment = portfolio.segments[0]
      const name = portfolio.name
      const autoBalance = segment.autoBalance
      const cycle = segment.cycle
      const balanceAt = segment.balanceAt
      const description = portfolio.description
      const navPortfolioType = portfolio.navPortfolioType
      portfolio.segments.forEach(item => {
        const funds = navPortfolioType !== 'cash' ? item.funds : item.injections
        const sliderValues = funds.reduce((out, fund) => {
          out[fund._id] = navPortfolioType !== 'cash' ? fund.ratio : fund.injection
          return out
        }, {})
        const inputValues = { ...sliderValues }
        item.sliderValues = sliderValues
        item.inputValues = inputValues
      })
      const segments = portfolio.segments.map(item => {
        if (navPortfolioType === 'cash') {
          item.funds = item.injections
        }
        return item
      })
      this.setState({
        segment: segments,
        show: true,
        activeKey: '1',
        name,
        autoBalance,
        cycle,
        balanceAt,
        description,
      })
    }
  }

  onSliderChange = (id, index) => value => {
    const { segment } = this.state
    if (segment[index].sliderValues[id] === value) {
      return
    }
    const restSum = Object.keys(segment[index].sliderValues)
      .filter(key => key !== id)
      .reduce((out, key) => segment[index].sliderValues[key] + out, 0)
    if (restSum + value > 100) {
      value = 100 - restSum
    }
    let inputValue = segment[index].inputValues[id]
    const roundValue = Math.round(segment[index].inputValues[id])
    if (value !== roundValue) {
      inputValue = value
    }
    segment[index].sliderValues = {
      ...segment[index].sliderValues,
      [id]: value,
    }
    segment[index].inputValues = {
      ...segment[index].inputValues,
      [id]: inputValue,
    }
    this.setState({ segment })
  };

  onInputChange = (ivalue, id, index) => {
    const { t } = this.props
    let value = String(ivalue)
    const { segment } = this.state
    if (Number.isNaN(Number(value))) {
      notification.warning({ message: '请输入数字' })
    } else {
      const svalue = Math.floor(value)
      if (value.indexOf('.') !== -1 && value.split('.')[1].length > 2) {
        value = Number(value).toFixed(4)
      }
      segment[index].sliderValues = {
        ...segment[index].sliderValues,
        [id]: svalue,
      }
      segment[index].inputValues = {
        ...segment[index].inputValues,
        [id]: value,
      }
      this.setState({ segment })
    }
  };

  onNameChange = event => {
    this.setState({ name: event.target.value })
  };

  onDescriptionChange = event => {
    this.setState({ description: event.target.value })
  };

  onSelectFunds = (index, isSaa) => items => {
    const { navPortfolioType } = this.props
    const { segment } = this.state
    const fundIds = segment[index].funds.map(fund => fund._id)
    let funds = segment[index].funds
    items.forEach(item => {
      if (!~fundIds.indexOf(item._id)) {
        const newFund = {
          ...item,
          isSaa,
          ratio: this.isStock()
            ? this.calculateScale(segment[index].funds, item)
            : Math.round(100 / items.length),
          injection: 0,
        }
        if (isSaa) {
          funds = funds.filter(fund => !fund.isSaa)
        }
        funds.push(newFund)
        if (navPortfolioType === 'cash') {
          segment[index].sliderValues[newFund._id] = 0
          segment[index].inputValues[newFund._id] = 0
        }
      }
    })
    segment[index].funds = funds
    if (navPortfolioType === 'cash') {
      this.setState({ segment })
      return
    }
    const avg = this.calculateAvg(segment[index].funds)
    const avgValues = segment[index].funds.reduce((out, fund) => {
      out[fund._id] = avg
      return out
    }, {})
    const scaleValues = segment[index].funds.reduce((out, fund) => {
      out[fund._id] = this.calculateScale(segment[index].funds, fund)
      return out
    }, {})
    const finalValues = this.isStock() ? scaleValues : avgValues
    segment[index].sliderValues = { ...finalValues }
    segment[index].inputValues = { ...finalValues }
    this.setState({ segment })
  };

  onClickAutoAllocate = () => {
    const { segment } = this.state
    const funds = segment.reduce((out, item) => {
      return out.concat(item.funds)
    }, [])
    const newSegments = this.autoAllocateSegments(funds)
    this.setState({ segment: newSegments, activeKey: `${newSegments.length}` })
  };

  onUpload = files => {
    const { t } = this.props
    const file = files[0]
    if (!/(csv|xlsx|xls|xlsm)$/.test(file.name)) {
      notification.warning({ message: t('portfolio.uploadCsvOrExcel') })
      return
    }
    const request = requestUtils.post('/api/portfolios/net/weight_file')
    request.attach('attachment', file, file.name)
    const formData = new FormData()
    formData.append('attachment', file, file.name)
    request('/api/portfolios/net/weight_file', {
      method: 'post',
      body: formData,
      requestType: 'form',
    })
      .then(({ data, response }) => {
        const segments = response.map(item => {
          const sliderValues = item.funds.reduce((out, fund) => {
            out[fund._id] = this.isCashPortfolio() ? fund.injection : fund.ratio
            return out
          }, {})
          item.sliderValues = { ...sliderValues }
          item.inputValues = { ...sliderValues }
          return item
        })
        this.setState({ segment: segments })
      })
      .catch(err => {
        const response = err.response
        if (response) {
          notification.error({
            message: `请求错误`,
          })
        }
      })
  };

  getUploadProps = () => {
    const props = {
      name: 'attachment',
      action: '/api/portfolios/net/weight_file',
      headers: {
        authorization: getToken(),
      },
      showUploadList: false,
      onChange: info => {
        if (info.file.status === 'done') {
          notification.success({ message: '上传成功' })
          const segments = info.file.response.map(item => {
            const sliderValues = item.funds.reduce((out, fund) => {
              out[fund._id] = this.isCashPortfolio() ? fund.injection : fund.ratio
              return out
            }, {})
            item.sliderValues = { ...sliderValues }
            item.inputValues = { ...sliderValues }
            return item
          })
          this.setState({ segment: segments })
        } else if (info.file.status === 'error') {
          notification.error({
            message: info.file.response.message,
          })
        }
      },
    }
    return props
  };

  onBenchmarkChange = benchmarks => {
    this.setState({ benchmark: benchmarks[0] })
  };

  getFundTypeName = type => {
    const { t } = this.props
    // todo: i18n
    if (type === 'index') {
      return t('portfolio.indexDatabase')
    } else if (type === 'stock') {
      return t('portfolio.stock')
    } else if (type === 'css') {
      return t('portfolio.taaStrategy')
    } else if (type === 'saa') {
      return '资产'
    }
    return t('portfolio.fund')
  };

  getModalTitle = () => {
    // todo: i18n
    const { fundType, isEdit, t } = this.props
    const action = isEdit ? t('portfolio.edit') : t('portfolio.create')
    let name
    if (fundType === 'index') {
      name = t('portfolio.newIndexPortfolio')
    } else if (fundType === 'stock') {
      name = t('portfolio.stockPortfolio')
    } else if (fundType === 'saa') {
      name = t('portfolio.saaStrategy')
    } else if (fundType === 'taa') {
      name = t('portfolio.taaStrategy')
    } else if (fundType === 'css') {
      name = t('portfolio.configuration')
    } else {
      name = !this.isCashPortfolio()
        ? t('portfolio.netPortfolio')
        : t('portfolio.fixedInvestPortfolio')
    }
    return `${action}${name}`
  };

  autoAllocateSegments(fundList) {
    const funds = fundList.map(fund => {
      const rangeStart = fund.navStartDate
      const rangeEnd = fund.navEndDate
      if (rangeStart) {
        fund.rangeStart = +moment(new Date(rangeStart)).startOf('date')
      }
      if (rangeEnd) {
        fund.rangeEnd = +moment(new Date(rangeEnd)).startOf('date')
      }
      return fund
    })
    const numbers = uniq(flatten(funds.map(item => [item.rangeStart, item.rangeEnd]))).sort(
      (fst, snd) => fst - snd,
    )
    const dayInSeconds = 24 * 60 * 60 * 1000
    const newRanges = []
    for (let idx = 0; idx < numbers.length - 1; idx++) {
      newRanges.push([numbers[idx], numbers[idx + 1]])
    }
    return newRanges
      .filter(range => range[1] - range[0] >= dayInSeconds)
      .map((range, index, arr) => {
        const segmentFunds = uniqBy(
          funds.filter(fund => range[0] >= fund.rangeStart && range[1] <= fund.rangeEnd),
          '_id',
        )
        const ret = { funds: [] }
        if (segmentFunds.length) {
          const avg = this.calculateAvg(segmentFunds)
          const avgValues = segmentFunds.reduce((out, fund) => {
            out[fund._id] = avg
            return out
          }, {})
          const scaleValues = segmentFunds.reduce((out, fund) => {
            out[fund._id] = this.calculateScale(segmentFunds, fund)
            return out
          }, {})
          const finalValues = this.isStock() ? scaleValues : avgValues
          ret.sliderValues = { ...finalValues }
          ret.inputValues = { ...finalValues }
          ret.funds = segmentFunds.map(item => ({
            ...item,
            ratio: this.isStock() ? this.calculateScale(segmentFunds, item) : avg,
          }))
          ret.startDate = range[0]
          if (index === arr.length - 1) {
            ret.endDate = range[1] + dayInSeconds
          } else {
            ret.endDate = range[1]
          }
        }
        return ret
      })
      .filter(item => item.funds.length > 0)
  }

  close = () => {
    this.setState({ show: false, segment: [], name: '', description: '' }, () => {
      this.resetFundState({
        portfolio: null,
        addNetPortfolioSuccess: false,
        editNetPortfolioSuccess: false,
      })
    })
  };

  open = () => {
    const autoBalance = false
    const cycle = 'month'
    const balanceAt = 1
    // defaultWeights comes from fund allocation page
    const { fundData, defaultWeights, autoAllocate, name } = this.props
    let funds = this.props.funds
    if (fundData) {
      this.getNavPortfolio(fundData._id)
      this.setState({ show: true })
      return
    }
    if (funds === undefined) {
      this.setState({ show: true, autoBalance, cycle, balanceAt, segment: [] }, this.addSegment)
      return
    }
    if (autoAllocate) {
      this.setState({
        name,
        show: true,
        segment: this.autoAllocateSegments(funds),
      })
      return
    }
    let sliderValues
    const startDate = Math.max.apply(
      null,
      funds.map(item => +new Date(item.startDate || item.navStartDate)),
    )
    const endDate = Math.max.apply(
      null,
      funds.map(item => (item.endToNow ? Date.now() : +new Date(item.endDate || item.navEndDate))),
    )
    const avg = this.calculateAvg(funds)
    sliderValues = funds.reduce((out, fund) => {
      out[fund._id] = avg
      return out
    }, {})
    const scaleValues = funds.reduce((out, fund) => {
      out[fund._id] = this.calculateScale(funds, fund)
      return out
    }, {})
    if (this.isStock()) {
      sliderValues = { ...scaleValues }
    }
    if (defaultWeights) {
      sliderValues = { ...defaultWeights }
    }
    const inputValues = { ...sliderValues }
    funds = funds.map(fund => {
      return {
        ...fund,
        ratio: sliderValues[fund._id],
        injection: sliderValues[fund._id],
      }
    })
    this.setState({
      autoBalance,
      cycle,
      balanceAt,
      name,
      segment: [{ funds, startDate, endDate, sliderValues, inputValues }],
      show: true,
    })
  };

  avgAllocation = index => {
    const { segment } = this.state
    const funds = segment[index].funds
    const avg = this.calculateAvg(funds)
    const avgValues = funds.reduce((out, fund) => {
      out[fund._id] = avg
      return out
    }, {})
    segment[index].sliderValues = { ...avgValues }
    segment[index].inputValues = { ...avgValues }
    this.setState(segment)
  };

  handleUploadSegmentFunds = (index, funds) => {
    const { segment } = this.state
    const weightValues = funds.reduce((out, fund) => {
      out[fund._id] = fund.ratio
      return out
    }, {})
    segment[index].funds = funds
    segment[index].sliderValues = { ...weightValues }
    segment[index].inputValues = { ...weightValues }
    this.setState(segment)
  }

  calculateAvg = funds => {
    return !this.isCashPortfolio() ? +(100 / funds.length).toFixed(6) : 0
  };

  scaleAllocation = index => {
    const { segment } = this.state
    const funds = segment[index].funds
    const scaleValues = funds.reduce((out, fund) => {
      out[fund._id] = this.calculateScale(funds, fund)
      return out
    }, {})
    segment[index].sliderValues = { ...scaleValues }
    segment[index].inputValues = { ...scaleValues }
    this.setState(segment)
  };

  calculateScale = (funds, fund) => {
    if (!fund.latestScale) {
      return 0
    }
    const scaleSum = funds.reduce((sum, item) => {
      if (!item.latestScale) {
        return sum
      }
      sum += item.latestScale; // eslint-disable-line
      return sum
    }, 0)
    return +((fund.latestScale * 100) / scaleSum).toFixed(6)
  };

  selectCycle = cycle => {
    this.setState({
      cycle,
      balanceAt: undefined,
    })
  };

  selectBalanceAt = balanceAt => {
    this.setState({ balanceAt })
  };

  toggleAutoBalance = checked => {
    this.setState({
      autoBalance: checked,
      cycle: undefined,
      balanceAt: undefined,
    })
  };

  selectDate = date => {
    if (date === 'Invalid date') {
      return
    }
    this.setState({ balanceAt: +date })
  };

  handleSetLatest = index => () => {
    const { segment } = this.state
    segment[index].endDate = Date.now()
    segment[index].endToNow = true
    this.setState({ segment })
  };

  selectStartDate = index => date => {
    if (date === 'Invalid date') {
      return
    }
    const { segment } = this.state
    segment[index].startDate = +date
    this.setState({ segment })
  };

  selectEndDate = index => date => {
    if (date === 'Invalid date') {
      return
    }
    const { segment } = this.state
    segment[index].endDate = date
    segment[index].endToNow = false
    this.setState({ segment })
  };

  _save = () => {
    const { fundData, fundType, requesting, t, navPortfolioType } = this.props
    const { segment, name, cycle, autoBalance, balanceAt, description, benchmark } = this.state
    const fundLabel = this.getFundTypeName(fundType)
    if (requesting) {
      return
    }
    segment.map(item => {
      const ratios = item.inputValues
      return item.funds.map(fund => {
        fund.ratio = parseFloat(ratios[fund._id])
        fund.injection = parseFloat(ratios[fund._id])
      })
    })
    const isCashPortfolio = this.isCashPortfolio()
    if (!name) {
      notification.error({ message: t('portfolio.enterPortfolioName') })
      return
    }
    const segmentInvalid = segment.some((item, index) => {
      let invalidMessage
      const dateContinuousValidate =
        index === 0 ||
        moment(item.startDate)
          .startOf('date')
          .diff(moment(segment[index - 1].endDate).startOf('date'), 'day') >= 0
      const funds = item.funds
      const sum = funds.reduce((out, fund) => out + fund.ratio, 0)
      const ratioValid = Math.abs(Number(sum.toFixed(8)) - 100) < 1e-2
      if (fundType === 'css' && item.funds.filter(fund => !fund.isSaa).length === 0) {
        invalidMessage = t('portfolio.leastOneTaaStrategy')
      } else if (fundType === 'css' && item.funds.filter(fund => fund.isSaa).length === 0) {
        invalidMessage = t('portfolio.leastOneStrategy')
      } else if (fundType !== 'css' && item.funds.length === 0) {
        invalidMessage = t('portfolio.selectLeastOneFundLabel', { fundLabel })
      } else if (!item.startDate) {
        invalidMessage = t('portfolio.selectStartDate')
      } else if (!item.endDate) {
        invalidMessage = t('portfolio.endDateError')
      } else if (moment(item.startDate) >= moment(item.endDate)) {
        invalidMessage = t('portfolio.startDateLessThanEnd')
      } else if (!dateContinuousValidate) {
        invalidMessage = t('portfolio.duplicateDateTip')
      } else if (!ratioValid && !isCashPortfolio) {
        invalidMessage = t('portfolio.sumNotEqualOneHundred', { fundLabel })
      } else if (isCashPortfolio && funds.some(fund => fund.radio === 0)) {
        invalidMessage = t('portfolio.fillAllCashesTip')
      }
      if (invalidMessage) {
        notification.error({ message: invalidMessage })
        this.setState({ activeKey: `${index + 1}` })
        return true
      }
      return false
    })
    if (segmentInvalid) {
      return
    }
    if (autoBalance) {
      if (!cycle) {
        notification.error({ message: `${t('portfolio.chooseRebalanceCycle')}` })
        return
      }
      if (!balanceAt) {
        notification.error({ message: `${t('portfolio.datePlaceholder')}` })
        return
      }
    }
    const data = {
      name,
      description,
      segments: segment.map(item => ({
        autoBalance,
        cycle,
        balanceAt,
        startDate: item.startDate,
        endDate: item.endDate,
        endToNow: item.endToNow,
        [`${!isCashPortfolio ? 'funds' : 'injections'}`]: item.funds.map(fund => ({
          isSaa: !isCashPortfolio ? fund.isSaa : undefined,
          name: fund.name,
          _id: fund._id,
          [`${!isCashPortfolio ? 'ratio' : 'injection'}`]: fund[
            `${!isCashPortfolio ? 'ratio' : 'injection'}`
          ],
        })),
      })),
    }
    if (benchmark) {
      data.benchmarkId = benchmark._id
    }
    if (fundData) {
      this.editNavPortfolio(fundData._id, data)
    } else {
      const params = { type: fundType }
      if (fundType === 'nav') {
        params.navPortfolioType = navPortfolioType
      }
      this.addNavPortfolio(data, params)
    }
  };

  editNavPortfolio = (id, data) => {
    const { dispatch } = this.props
    dispatch({
      type: 'fund/editNavPortfolio',
      payload: {
        id,
        data,
      },
    })
  };

  addNavPortfolio = (data, params) => {
    const { dispatch } = this.props
    dispatch({
      type: 'fund/addNavPortfolio',
      payload: {
        data,
        params,
      },
    })
  };

  getNavPortfolio = id => {
    const { dispatch } = this.props
    dispatch({
      type: 'fund/fetchNavPortfolio',
      payload: {
        id,
      },
    })
  };

  resetFundState = payload => {
    const { dispatch } = this.props
    dispatch({
      type: 'fund/save',
      payload,
    })
  };

  save = throttle(this._save, 3000);

  removeFund = (fund, index) => () => {
    const { t } = this.props
    const fundLabel = this.getFundTypeName(this.props.fundType)
    const { segment } = this.state
    if (segment[index].funds.length === 1) {
      notification.warning({ message: `${t('portfolio.noLessOne')}${fundLabel}` })
      return
    }
    segment[index].funds = segment[index].funds.filter(item => item._id !== fund._id)
    if (this.isCashPortfolio()) {
      this.setState({ segment })
      return
    }
    const avg = Math.round(100 / segment[index].funds.length)
    const inputAvg = parseFloat(100 / segment[index].funds.length)
    segment[index].sliderValues = segment[index].funds.reduce((out, item) => {
      out[item._id] = this.isStock() ? this.calculateScale(segment[index].funds, item) : avg
      return out
    }, {})
    Object.keys(segment[index].inputValues).map(item => {
      segment[index].inputValues[item] = this.isStock()
        ? this.calculateScale(segment[index].funds, item)
        : inputAvg
    })
    this.setState({ segment })
  };

  addSegment = () => {
    const { t } = this.props
    const isCashPortfolio = this.isCashPortfolio()
    const { segment } = this.state
    const lastEndToNow = segment.length >= 1 && segment[segment.length - 1].endToNow
    let lastDate = segment.length >= 1 && segment[segment.length - 1].endDate
    if (lastEndToNow) {
      segment[segment.length - 1].endToNow = false
      segment[segment.length - 1].endDate = +moment().subtract(1, 'day')
      lastDate = +moment()
    } else if (lastDate === undefined) {
      notification.error({ message: t('portfolio.selectDateFirst') })
      return
    } else {
      lastDate = +moment(lastDate)
    }
    let sliderValues = {}
    let funds = []
    if (segment.length && !isCashPortfolio) {
      funds = segment[0].funds.map(fund => {
        return {
          _id: fund._id,
          name: fund.name,
          ratio: this.calculateAvg(segment[0].funds),
        }
      })
      sliderValues = funds.reduce((out, fund) => {
        out[fund._id] = fund.ratio
        return out
      }, {})
    }
    const inputValues = { ...sliderValues }
    const newSegment = {
      startDate: segment.length >= 1 ? lastDate : undefined,
      endDate: undefined,
      funds: funds,
      sliderValues,
      inputValues,
    }
    segment.push(newSegment)
    this.setState({ segment, activeKey: segment.length + '' })
  };

  delSeg = index => () => {
    const { segment } = this.state
    segment.splice(index, 1)
    this.setState({ segment })
  };

  handleSelect = activeKey => {
    this.setState({ activeKey })
  };

  showTabs = tabs => {
    const { features } = this.props
    let filterTabs = tabs.filter(
      item => (item.value !== 'portfolio' && item.value !== 'stock') || features.researchEnabled,
    )
    if (this.props.fundType === 'nav') {
      filterTabs = filterTabs.filter(item => item.value !== 'portfolio')
    }
    if (this.props.fundType === 'stock') {
      filterTabs = tabs.filter(item => item.value === 'stock')
    }
    if (this.props.fundType === 'index') {
      filterTabs = tabs.filter(item => item.value === 'index')
    }
    if (this.props.fundType === 'css') {
      return []
    }
    return filterTabs
  };

  isStock = () => {
    return this.props.fundType === 'stock'
  };

  isIndex = () => {
    return this.props.fundType === 'index'
  };

  isSelectable = fund => {
    const { features, fundType } = this.props
    if (fundType === 'css') {
      return fund.isPortfolio
    }
    return (
      (!(fund.isPortfolio && fund.portfolioType !== 'manager') || features.researchEnabled) &&
      fund.accReturn !== undefined
    )
  };

  isCashPortfolio = () => {
    return this.props.navPortfolioType === 'cash'
  };

  renderDate = date => {
    if (date === undefined) {
      return ''
    }
    return moment(date).format('YYYY-MM-DD')
  };

  render() {
    const { children, className, loading, fundType, t, navPortfolioType, defaultTabs } = this.props
    const {
      autoBalance,
      cycle,
      show,
      cycleDays,
      cycleWeeks,
      balanceAt,
      name,
      segment,
      activeKey,
      description,
    } = this.state
    const cycleItems = cycle === 'week' ? cycleWeeks : cycleDays
    const fundLabel = this.getFundTypeName(fundType)
    const isCashPortfolio = this.isCashPortfolio()
    const weightFilePopoverConcent = (
      <div>
        <p>
          {!isCashPortfolio
            ? t('portfolio.createPortfolioByUploading')
            : t('portfolio.createCashPortfolioByUploading')}
        </p>
        <div className="ant-table ant-table-small ant-table-bordered ant-table-scroll-position-left">
          <div className="ant-table-content">
            <div className="ant-table-body">
              <table>
                <thead className="ant-table-thead">
                  <tr>
                    <th>{t('portfolio.date')}</th>
                    <th>{t('portfolio.fundCode')}</th>
                    <th>{!isCashPortfolio ? t('portfolio.weights') : t('portfolio.fundAmount')}</th>
                  </tr>
                </thead>
                <tbody className="ant-table-tbody">
                  <tr>
                    <td>2017/01/01</td>
                    <td>202001</td>
                    <td>{!isCashPortfolio ? '50%' : '10000'}</td>
                  </tr>
                  <tr>
                    <td>2017/01/01</td>
                    <td>040001</td>
                    <td>{!isCashPortfolio ? '30%' : '30000'}</td>
                  </tr>
                  <tr>
                    <td>2017/01/01</td>
                    <td>000001</td>
                    <td>{!isCashPortfolio ? '20%' : '20000'}</td>
                  </tr>
                  <tr>
                    <td>2017/06/01</td>
                    <td>202101</td>
                    <td>{!isCashPortfolio ? '50%' : '15000'}</td>
                  </tr>
                  <tr>
                    <td>2017/06/01</td>
                    <td>000001</td>
                    <td>{!isCashPortfolio ? '50%' : '35000'}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    )
    return (
      <div style={{ display: 'inline-block' }} className={className}>
        <div onClick={this.open}>{children}</div>
        <Modal
          width={900}
          visible={show}
          onCancel={this.close}
          className={styles.modal}
          title={
            <div>
              {this.getModalTitle()}
              <ButtonGroup className={styles.actionButtons}>
                {fundType !== 'css' &&
                  !isCashPortfolio &&
                  segment &&
                  !!segment.filter(item => item.funds && item.funds.length).length && (
                    <Popover
                      title={null}
                      content={<p>{t('portfolio.rebalanceInterAutoCreated')}</p>}
                    >
                      <Button
                        type="primary"
                        icon={<HourglassOutlined />}
                        size="small"
                        onClick={this.onClickAutoAllocate}
                      />
                    </Popover>
                  )}
                {fundType !== 'css' && (
                  <Popover title={null} content={weightFilePopoverConcent}>
                    <Button
                      type="primary"
                      icon={
                        <Upload {...this.getUploadProps()}>
                          <UploadOutlined />
                        </Upload>
                      }
                      size="small"
                    />
                  </Popover>
                )}
              </ButtonGroup>
            </div>
          }
          footer={[
            <Row key="1">
              <Col span={4} style={{ textAlign: 'left' }}>
                <Switch
                  checkedChildren="开"
                  unCheckedChildren="关"
                  onChange={this.toggleAutoBalance}
                  checked={autoBalance}
                />
                <span
                  style={{
                    marginRight: '5px',
                    marginLeft: '5px',
                    marginTop: '5px',
                    display: 'inline-block',
                  }}
                >
                  {isCashPortfolio
                    ? t('portfolio.openTheCast')
                    : t('portfolio.automaticRebalancing')}
                </span>
              </Col>
              <Col span={14} style={{ textAlign: 'left' }}>
                {autoBalance && (
                  <>
                    <InputGroup compact>
                      <Select
                        placeholder={t('portfolio.chooseRebalanceCycle')}
                        onChange={this.selectCycle}
                        value={cycle}
                        style={{ width: 100 }}
                        disabled={!autoBalance}
                      >
                        <Option value="week">{t('portfolio.weekly')}</Option>
                        <Option value="month">{t('portfolio.monthly')}</Option>
                        <Option value="year">{t('portfolio.yearly')}</Option>
                      </Select>
                      {cycle && cycle !== 'year' && (
                        <Select
                          placeholder={t('portfolio.datePlaceholder')}
                          onChange={this.selectBalanceAt}
                          value={balanceAt}
                          style={{ width: 200 }}
                          disabled={!autoBalance}
                        >
                          {cycleItems.map(item => (
                            <Option key={`${cycle}-${item.value}`} value={item.value}>
                              {item.name}
                            </Option>
                          ))}
                        </Select>
                      )}
                      {cycle && cycle === 'year' && (
                        <DatePicker
                          format="YYYY/MM/DD"
                          value={balanceAt ? moment(new Date(balanceAt)) : undefined}
                          onChange={this.selectDate}
                          allowClear={false}
                          placeholder={t('portfolio.datePlaceholder')}
                          style={{ width: 200 }}
                          disabled={!autoBalance}
                        />
                      )}
                    </InputGroup>
                  </>
                )}
              </Col>
              <Col span={6}>
                <Button loading={loading} key="submit" type="primary" onClick={this.save}>
                  {t('portfolio.save')}
                </Button>
              </Col>
            </Row>,
          ]}
        >
          {segment && show && (
            <Spin spinning={!!loading}>
              <Form layout="vertical">
                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item label="名称">
                      <Input
                        type="text"
                        placeholder={t('portfolio.enterPortfolioName')}
                        value={name}
                        onChange={this.onNameChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={18}>
                    <Form.Item label="描述">
                      <Input
                        type="text"
                        placeholder={t('portfolio.enterDescription')}
                        value={description}
                        onChange={this.onDescriptionChange}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
              <Collapse accordion activeKey={activeKey} onChange={this.handleSelect}>
                {segment &&
                  segment.map((item, index) => (
                    <Panel
                      key={index + 1 + ''}
                      header={
                        <div className={styles.segmentHeader}>
                          {moment(item.startDate).format('YYYY-MM-DD')}{' '}
                          {t('portfolio.toPlaceholder')}{' '}
                          {item.endToNow
                            ? moment(new Date()).format('YYYY-MM-DD')
                            : this.renderDate(item.endDate)}
                          {segment.length > 1 && (
                            <div style={{ float: 'right' }} onClick={ev => ev.stopPropagation()}>
                              <Popconfirm
                                title={t('portfolio.sureWantDel')}
                                onConfirm={this.delSeg(index)}
                                onCancel={() => {}}
                                okText={t('portfolio.confirm')}
                                cancelText={t('portfolio.cancel')}
                              >
                                <span>{t('portfolio.delete')}</span>
                              </Popconfirm>
                            </div>
                          )}
                        </div>
                      }
                    >
                      <Segment
                        key={`segment-${index}`}
                        selectStartDate={this.selectStartDate}
                        selectEndDate={this.selectEndDate}
                        handleSetLatest={this.handleSetLatest}
                        isStock={this.isStock}
                        isIndex={this.isIndex}
                        showTabs={this.showTabs}
                        onSelectFunds={this.onSelectFunds}
                        onInputChange={this.onInputChange}
                        isSelectable={this.isSelectable}
                        avgAllocation={this.avgAllocation}
                        scaleAllocation={this.scaleAllocation}
                        onSliderChange={this.onSliderChange}
                        removeFund={this.removeFund}
                        t={t}
                        handleUploadSegmentFunds={this.handleUploadSegmentFunds}
                        {...{ item, index, fundLabel, activeKey, fundType, navPortfolioType, defaultTabs }}
                      />
                    </Panel>
                  ))}
              </Collapse>
              <div style={{ margin: '15px 0' }} />
              {fundType !== 'css' && (
                <Button
                  className={styles.addSegment}
                  icon={<PlusOutlined />}
                  type="dashed"
                  block
                  onClick={this.addSegment}
                >
                  {t('portfolio.addRebalanceInterval')}
                </Button>
              )}
            </Spin>
          )}
          {false &&
          <Card
            title="费率设置"
            style={{ marginTop: 15 }}
            className="bordered"
            size="small"
          >
            <Row>
              <Col span={12}>
                <Form.Item
                  label="非债型申购"
                  name="noneBondBuyFee"
                  rules={[{ required: true, message: '请输入' }]}
                >
                  <InputNumber step={0.1}/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="非债型赎回"
                  name="noneBondSellFee"
                  rules={[{ required: true, message: '请输入' }]}
                >
                  <InputNumber step={0.1}/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="债券型申购"
                  name="bondBuyFee"
                  rules={[{ required: true, message: '请输入' }]}
                >
                  <InputNumber step={0.1}/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="债券型赎回"
                  name="bondSellFee"
                  rules={[{ required: true, message: '请输入' }]}
                >
                  <InputNumber step={0.1}/>
                </Form.Item>
              </Col>
            </Row>
          </Card>}
        </Modal>
      </div>
    )
  }
}
