.periodWapper {
  margin-bottom: 15px;
  :global(.ant-calendar-picker) {
    width: 150px;
  }
  .label {
    padding: 0 8px;
    &:hover,
    &:focus {
      color: rgba(0, 0, 0, 0.65);
      background-color: #fff;
      border-color: #d9d9d9;
    }
  }
  .setLatest {
    background-color: #fff;
    cursor: pointer;
    border-right: 0;
  }
  .setLatest.active {
    background-color: #336699;
    color: #fff;
  }
  input[type='text'] {
    border-radius: 0;
  }
  .left {
    :global(.rdtPicker) {
      left: 0;
    }
  }
  .right {
    :global(.rdtPicker) {
      right: 0;
    }
  }
}
