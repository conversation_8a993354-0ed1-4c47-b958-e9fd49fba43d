
import React, { Component } from 'react'
import { DeleteOutlined } from '@ant-design/icons';
import { Slider, Tooltip, Popconfirm, List, InputNumber } from 'antd';
import thousandFormatter from '../../utils/thousandFormatter'
import styles from './style.less'

export default class RatioSelector extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  onSliderChange = value => {
    this.props.onSliderChange(value)
  };

  render() {
    const { fund, inputValue, sliderValue, fundLabel, index, t, navPortfolioType } = this.props
    return (
      <List.Item
        actions={[
          <span key="1">
            {navPortfolioType !== 'cash' ? (
              <InputNumber
                size="small"
                min={0}
                max={100}
                value={inputValue}
                precision={4}
                onChange={value => this.props.onInputChange(value, fund._id, index)}
              />
            ) : (
              <InputNumber
                style={{ width: '150px' }}
                size="small"
                value={inputValue}
                formatter={value => thousandFormatter(value)}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
                onChange={value => this.props.onInputChange(value, fund._id, index)}
              />
            )}
            {navPortfolioType !== 'cash' && <i>%</i>}
          </span>,
          <Popconfirm
            key="2"
            title={`${t('portfolio.delTip')}${fundLabel}${fund.name}${t(
              'portfolio.questionEnd',
            )}？`}
            onConfirm={this.props.removeFund(fund, index)}
            onCancel={() => {}}
            okText={t('portfolio.confirm')}
            cancelText={t('portfolio.cancel')}
          >
            <Tooltip title={t('portfolio.delete')} placement="right">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>,
        ]}
      >
        <List.Item.Meta title={fund.name} />
        {navPortfolioType !== 'cash' && (
          <Slider
            min={0}
            max={100}
            step={0.01}
            value={sliderValue}
            onChange={this.onSliderChange}
            className={styles.slider}
          />
        )}
      </List.Item>
    );
  }
}
