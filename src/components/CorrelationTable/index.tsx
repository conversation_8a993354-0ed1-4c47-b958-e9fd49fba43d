import React from 'react'
import { Table, Avatar } from 'antd'

interface ComponentProps {
  funds?: any;
  cor?: any;
  useIdAsKey?: boolean;
}

const CorrelationTable: React.SFC<ComponentProps> = props => {
  const { funds, cor, useIdAsKey } = props
  const renderAvatar = avatar => (
    <Avatar
      size={16}
      style={{ color: '#D5DFEB', background: 'none', border: '1px solid #D5DFEB', fontSize: 12 }}
    >
      {avatar}
    </Avatar>
  )
  const columns = funds.map((item, index) => {
    return {
      title: renderAvatar(index + 1),
      dataIndex: `cor${index}`,
      format: 'number',
      align: 'right',
      width: 50,
    }
  })
  columns.unshift({
    title: '相关系数',
    dataIndex: 'name',
    width: 130,
    fixed: 'left',
    ellipsis: true,
    render: (text, record, index) => {
      return (
        <span>
          {renderAvatar(index + 1)}
          <span style={{ marginLeft: 8 }}>{text}</span>
        </span>
      )
    },
  })
  const data = funds.map((fund1, ii) => {
    const corData = funds.reduce((out, fund2, jj) => {
      const key1 = useIdAsKey ? fund1._id : jj
      const key2 = useIdAsKey ? fund2._id : ii
      let value
      if (ii === jj) {
        value = 1
      } else if (ii < jj) {
        value = ''
      } else {
        value = cor[`${key1}-${key2}`] && cor[`${key1}-${key2}`].toFixed(2)
      }
      out[`cor${jj}`] = value
      return out
    }, {})
    return {
      name: fund1.name,
      ...corData,
    }
  })
  return (
    <Table columns={columns} dataSource={data} pagination={false} size="small" bordered={false} scroll={{ y: 500 }} />
  )
}

export default CorrelationTable
