import React from 'react'
import { Button, Modal, Input, message, Tag, Select, Form } from 'antd'
import FilterDescTable from './FilterDescTable'
const TextArea = Input.TextArea

interface ComponentProps {
  investpool?: any;
  dispatch: any;
  type?: string;
  dataType?: string;
  config?: string;
  filters?: any;
  groupList?: any;
}

interface ComponentState {
  show?: boolean;
  name?: string;
  description?: string;
  userGroupIds?: any;
  userGroups?: any;
}

class InvestPoolModal extends React.Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    this.state = {
      show: false,
    }
  }

  handleNameChange = event => {
    this.setState({ name: event.target.value })
  };

  handleDescriptionChange = event => {
    this.setState({ description: event.target.value })
  };

  handleUserGroupChange = userGroupIds => {
    const userGroups = this.props.groupList
    .filter(item => userGroupIds.includes(item._id))
    .map(item => {
      return {
        name: item.name,
        id: item._id,
      }
    })
    this.setState({ userGroups, userGroupIds })
  }

  close = () => {
    this.setState({ show: false })
  };

  open = () => {
    const { investpool } = this.props
    const newState: ComponentState = { show: true }
    if (investpool) {
      newState.name = investpool.name
      newState.description = investpool.description
      newState.userGroupIds = investpool.userGroupIds
    }
    this.setState(newState)
  };

  save = () => {
    const { name, description, userGroups, userGroupIds } = this.state
    if (!name) {
      message.error('请填写名字')
      return
    }
    const { dispatch, type, dataType, investpool, config } = this.props
    const data = {
      name,
      description,
      userGroups,
      userGroupIds,
    }
    const payload = {}
    let action = 'investpool/add'
    if (investpool) {
      payload.id = investpool._id
      action = 'investpool/edit'
    } else {
      data.type = type
      data.dataType = dataType
    }
    if (type === 'dynamic' && config) {
      data.config = config
    }
    payload.data = data
    dispatch({
      type: action,
      payload,
    })
    this.close()
  };

  render() {
    const { children, investpool, config, filters, groupList, typeName } = this.props
    const title = typeName || '跟踪列表'
    const { name, description, userGroupIds } = this.state
    return (
      <div style={{ display: 'inline-block' }}>
        <div onClick={this.open}>{children}</div>
        <Modal
          title={
            <>
              <span>{investpool ? '编辑' + title : '创建' + title}</span>
            </>
          }
          visible={this.state.show}
          onCancel={this.close}
          width={700}
          footer={[
            <Button type="primary" onClick={this.save}>
              保存
            </Button>,
          ]}
        >
          <Form layout="vertical">
            <Form.Item label="名称">
              <Input placeholder="名称" value={name} onChange={this.handleNameChange} />
            </Form.Item>
            <Form.Item label="描述">
              <TextArea
                placeholder="描述"
                value={description}
                onChange={this.handleDescriptionChange}
              />
            </Form.Item>
            <Form.Item
              label="可见用户组"
            >
              <Select
                showSearch
                value={userGroupIds}
                mode="multiple"
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                onChange={this.handleUserGroupChange}
              >
                {(groupList || []).map(item => <Select.Option key={item._id}>{`${item.name}`}</Select.Option>)}
              </Select>
            </Form.Item>
          </Form>
          {config && filters && filters.length !== 0 &&
          <h4>筛选条件</h4>}
          <FilterDescTable {...{config, filters}} />
        </Modal>
      </div>
    )
  }
}

export default InvestPoolModal
