import React from 'react'
import { connect } from 'dva'
import { ModelState } from '@/models/investpool'
import { Button, Modal, message } from 'antd'
import { Dispatch } from 'redux'
import { TableListParams, TableListItem } from '@/components/StandardTable'
import InvestPoolList from './List'
import styles from './style.less'

interface ComponentProps {
  dispatch?: Dispatch<any>;
  loading?: boolean;
  investPoolListData?: any;
  onSelectRow?: any;
  children?: any;
  dataType?: any;
  disabled?: boolean,
}

interface ComponentState {
  selectedRows: any;
  show?: boolean;
}

@connect(
  ({
    investpool,
    loading,
    user,
  }: {
    investpool: ModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
    user: any;
  }) => ({
    investPoolListData: investpool.investPoolListData,
    loading: loading.effects['investpool/fetch'],
    currentUser: user.currentUser,
    groupList: investpool.groupList,
  }),
)
class SelectInvestPoolModal extends React.Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    this.state = {
      show: false,
      selectedRows: [],
    }
  }

  loadData = (params: Partial<TableListParams>) => {
    const { dispatch, dataType } = this.props
    dispatch({
      type: 'investpool/fetch',
      payload: {
        ...params,
        dataType,
        type: 'static',
      },
    })
  }

  handleSelectRows = (rows: TableListItem[]) => {
    this.setState({
      selectedRows: rows,
    })
    console.log(rows)
  };

  close = () => {
    this.setState({ show: false })
  };

  open = () => {
    if (this.props.disabled) {
      return
    }
    this.loadData({})
    this.setState({ show: true })
  };

  save = () => {
    const { selectedRows } = this.state
    if (!selectedRows.length) {
      message.warning('请选择跟踪列表')
      return
    }
    this.props.onSelectRow(selectedRows)
    this.close()
  };

  render() {
    const { children, dispatch, investPoolListData, loading, dataType, currentUser, groupList } = this.props
    const { selectedRows } = this.state

    return (
      <div style={{ display: 'inline-block' }}>
        <div onClick={this.open}>{children}</div>
        <Modal
          title={
            <>
              <span>选择跟踪列表</span>
            </>
          }
          visible={this.state.show}
          onCancel={this.close}
          width={900}
          footer={[
            <Button type="primary" onClick={this.save}>
              确定
            </Button>,
          ]}
          className={styles.selectInvestPoolModal}
        >
          <InvestPoolList
            {...{ dispatch, investPoolListData, loading, currentUser, groupList }}
            loadData={this.loadData}
            type="static"
            rowSelectionType="radio"
            onSelectRow={this.handleSelectRows}
            selectedRows={selectedRows}
            dataType={dataType}
          />
        </Modal>
      </div>
    )
  }
}

export default SelectInvestPoolModal
