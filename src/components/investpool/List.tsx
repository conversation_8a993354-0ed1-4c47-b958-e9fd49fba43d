import { DeleteOutlined, EditOutlined, PlusOutlined, QuestionCircleOutlined, DownOutlined, ShareAltOutlined } from '@ant-design/icons';
import {
  Card,
  Input,
  Tooltip,
  Divider,
  Popconfirm,
  Popover,
  Menu,
  Dropdown,
  Space,
} from 'antd';
import React, { Component } from 'react'
import { Dispatch } from 'redux'
import moment from 'moment'
import StandardTable, {
  StandardTableColumnProps,
  TableListItem,
  TableListParams,
} from '@/components/StandardTable'
import InvestPoolModal from './InvestPoolModal'
import FilterDescTable from './FilterDescTable'

const { Search } = Input

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  investPoolListData: any;
  loadData: any;
  type: string;
  dataType: string;
  rowSelectionType?: 'checkbox' | 'radio';
  onSelectRow?: any;
  selectedRows?: TableListItem[];
  mutualFundFilters?: any;
  isMixedList?: boolean;
  groupList?: any;
  currentUser?: any;
}

interface ComponentState {
  defaultIds?: string;
  input?: string;
}

class InvestPoolList extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    this.state = {}
  }

  columns: StandardTableColumnProps[] = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text, record) => {
        return <a href={`/pm/tracking/${record.dataType}/${record.type}/${record._id}`}>{record.name}</a>
      },
    },
    // {
    //   title: '跟踪对象',
    //   dataIndex: 'dataType',
    //   render: (value) => {
    //     return value === 'fund' ? '基金' : '基金经理'
    //   },
    // },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '筛选条件',
      dataIndex: 'type',
      render: (text, record) => {
        if (text === 'static') {
          return '自选'
        }
        return (
          <div>
            <span style={{ marginRight: 5 }}>动态</span>
            <Popover
              title="筛选条件"
              placement="right"
              content={
                <FilterDescTable
                  config={record.config}
                  filters={this.props.mutualFundFilters}
                />
              }
            >
              <QuestionCircleOutlined />
            </Popover>
          </div>
        )
      },
    },
    {
      title: '创建人',
      dataIndex: 'author',
      width: 120,
      render: (val, record) => {
        return (
          <Space size="small">
            <span>{val && val.nickname}</span>
            {this.props.currentUser._id !== record.authorId &&
            <Tooltip title={`来自${val.nickname}的分享`}>
              <ShareAltOutlined />
            </Tooltip>
            }
          </Space>
        )
      }
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      align: 'right',
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        if (record.authorId !== this.props.currentUser._id) {
          return false
        }
        return <>
          <InvestPoolModal
            typeName={record.name}
            investpool={record}
            type={record.type}
            config={record.config}
            dispatch={this.props.dispatch}
            filters={this.props.mutualFundFilters}
            groupList={this.props.groupList}
          >
            <Tooltip title="编辑">
              <EditOutlined />
            </Tooltip>
          </InvestPoolModal>
          <Divider type="vertical" />
          <Popconfirm
            title={`确认删除${record.name}吗？`}
            onConfirm={() => this.removeInvestPool(record._id)}
            onCancel={() => {}}
            okText="确认"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>
        </>;
      },
    },
  ];

  removeInvestPool = (id: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'investpool/remove',
      payload: {
        id,
      },
    })
  };

  handleStandardTableChange = (params: Partial<TableListParams>) => {
    this.props.loadData(params)
  };

  handleSeachInput = (value: string) => {
    this.setState({ input: value }, () => {
      this.props.loadData({
        page: 1,
        input: value,
      })
    })
  };

  renderCreate() {
    const { isMixedList } = this.props
    const newPortfolioMenu = (
      <Menu>
        <Menu.Item key="menu1">
          <InvestPoolModal typeName="基金静态列表" dataType="fund" type="static" dispatch={this.props.dispatch} groupList={this.props.groupList}>
            基金静态列表
          </InvestPoolModal>
        </Menu.Item>
        <Menu.Item key="menu2">
          <InvestPoolModal typeName="基金动态列表" dataType="fund" type="dynamic" dispatch={this.props.dispatch} groupList={this.props.groupList}>
            基金动态列表
          </InvestPoolModal>
        </Menu.Item>
        <Menu.Item key="menu3">
          <InvestPoolModal typeName="基金经理静态列表" dataType="manager" type="static" dispatch={this.props.dispatch} groupList={this.props.groupList}>
            基金经理静态列表
          </InvestPoolModal>
        </Menu.Item>
        <Menu.Item key="menu4">
          <InvestPoolModal typeName="基金经理动态列表" dataType="manager" type="dynamic" dispatch={this.props.dispatch} groupList={this.props.groupList}>
            基金经理动态列表
          </InvestPoolModal>
        </Menu.Item>
      </Menu>
    )
    if (isMixedList) {
      return (
        <Dropdown overlay={newPortfolioMenu}>
          <a>
            <PlusOutlined />
            <span style={{ margin: '0 5px' }}>新建</span>
            <DownOutlined />
          </a>
        </Dropdown>
      )
    }
    return (
      <InvestPoolModal typeName="跟踪列表" dataType={this.props.dataType} type={this.props.type} dispatch={this.props.dispatch}>
        <a>
          <PlusOutlined />
          创建跟踪列表
        </a>
      </InvestPoolModal>
    )
  }

  render() {
    const { investPoolListData, loading, rowSelectionType, onSelectRow, selectedRows } = this.props

    return (
      <Card
        bordered={false}
        title={
          <>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={this.handleSeachInput}
            />
          </>
        }
        extra={
          this.renderCreate()
        }
      >
        <div>
          <StandardTable
            disableRowSlection={!rowSelectionType}
            bordered
            selectedRows={selectedRows || []}
            loading={loading}
            data={investPoolListData}
            columns={this.columns}
            onSelectRow={onSelectRow}
            onChange={this.handleStandardTableChange}
            size="small"
            rowKey="_id"
            rowSelectionType={rowSelectionType}
          />
        </div>
      </Card>
    );
  }
}

export default InvestPoolList
