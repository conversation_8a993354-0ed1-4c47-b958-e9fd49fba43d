import React from 'react'
import { Tag, Table } from 'antd'
import { FilterItem } from '@/components/FilterPanel'
import { getFilterDisplayValues } from '@/components/FilterPanel/filterPanelHelper'

export default ({ config, filters }) => {
  if (!config || !filters || !filters.length) {
    return false
  }
  const columns = [
    {
      title: '参数名',
      dataIndex: 'name',
    },
    {
      title: '参数值',
      dataIndex: 'values',
      render: (text, record) => {
        return record.values.map(val => <Tag>{val}</Tag>)
      },
    },
  ]
  const filterValues = JSON.parse(config)
  const allFilters = filters.reduce((out, item) => {
    if (item.type === 'tabs') {
      return out.concat(item.tabs)
    }
    return out.concat([item])
  }, [])
  const data = allFilters
    .map((filter: FilterItem) => {
      const displayValues = getFilterDisplayValues(filter, filterValues)
      if (!displayValues || !displayValues.length) {
        return false
      }
      return {
        name: filter.name,
        values: displayValues,
      }
    })
    .filter(Boolean)
  return (
    <div>
      <Table columns={columns} dataSource={data} size="small" pagination={false} />
    </div>
  )
}
