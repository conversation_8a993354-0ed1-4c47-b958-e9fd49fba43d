import PropTypes from 'prop-types'
import React, { Component } from 'react'
import classnames from 'classnames'
import { Button, Popover } from 'antd'

export default  class CheckboxPopover extends Component {
  static propTypes = {
    id: PropTypes.string.isRequired,
    list: PropTypes.array.isRequired,
    selected: PropTypes.array.isRequired,
    onChange: PropTypes.func.isRequired,
    title: PropTypes.string.isRequired,
    className: PropTypes.string,
    children: PropTypes.object
  }

  handleSelectChange = item => () => {
    const { selected } = this.props
    const names = selected.map(select => select.name)
    const index = names.indexOf(item.name)
    if (index !== -1) {
      selected.splice(index, 1)
    } else {
      selected.push(item)
    }
    this.props.onChange(selected)
  }

  renderPopover() {
    const { list, selected, id, title, className, children } = this.props
    const names = selected.map(item => item.name)
    const popoverClick = (
      <Popover
        key={`popover-${id}`}
        placement="bottom"
        content={
          <div>
            {list.map((item, index) => (
              <div key={`${id}-${index}`}>
                <label >
                  <input
                    type="checkbox"
                    key={`select-${id}-${index}`}
                    checked={!!~names.indexOf(item.name)}
                    onChange={this.handleSelectChange(item)}
                  />{' '}
                  {item.name}
                </label>{' '}
                {id === 'scenariosPop' && (
                  <Popover content={<p style={{ whiteSpace: 'pre-wrap' }}>{item.description}</p>}>
                    <i className="fa fa-question-circle-o" />
                  </Popover>
                )}
              </div>
            ))}
          </div>
        }
      >
        {children === undefined ? (
          <Button>
            {title}({selected.length}
           个
          </Button>
        ) : (
          children
        )}
      </Popover>
    )
    return (
      <div
        className={classnames(className)}
      >
        {popoverClick}
      </div>
    )
  }

  render() {
    return this.renderPopover()
  }
}
