import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { Modal, Tabs, Button, Input } from 'antd'
import { connect } from 'dva'
import styles from './index.less'
import sortQuotaFn from '@/utils/sortQuotaFn'
import StandardTable, {
  StandardTableColumnProps,
  TableListItem,
  TableListParams,
} from '@/components/StandardTable'
import moment from 'moment'

const { TabPane } = Tabs
const { Search } = Input

interface ComponentProps {
  dispatch: Dispatch<any>;
  className?: string;
  fundList?: any;
  loading?: boolean;
  waitFunds?: any;
  rowSelectionType?: 'checkbox' | 'radio' | undefined;
  defaultTabs?: TabItem[];
  onChange: (rows: TableListItem[]) => void;
  style?: any,
  title?: string,
  portfolioType?: string,
}
interface ComponentState {
  selectedRows: TableListItem[];
  show: boolean;
  activeKey: string;
  input: string;
  tabs?: TabItem[];
}
interface TabItem {
  name: string;
  value: string;
}
@connect(({ fund, investpool, loading }: {
  fund: any;
  investpool: any;
  loading: { models: { [key: string]: boolean } }
}) => ({
  waitFunds: fund.waitFunds,
  fundList: fund.fundListData,
  investPoolListData: investpool.investPoolListData,
  allOrigFunds: investpool.allOrigFunds,
  loading: loading.models.fund || loading.models.investpool,
}))
export default class SelectFundModal extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    const tabs: TabItem[] = props.defaultTabs || [
      // {
      //   name: '待配基金',
      //   value: 'allocationPool',
      // },
      {
        name: '实盘组合',
        value: 'private',
      },
      {
        name: '公募基金',
        value: 'mutual',
      },
      {
        name: '市场基准',
        value: 'index',
      },
      {
        name: '跟踪列表',
        value: 'trackinglist',
      },
    ]
    this.state = {
      tabs,
      show: false,
      selectedRows: [],
      activeKey: tabs[0].value,
      input: '',
    }
  }

  state = {
    show: false,
    selectedRows: [],
    activeKey: 'allocationPool',
    input: '',
  };

  loadFundList = (params: Partial<TableListParams> = {}) => {
    const { activeKey } = this.state
    const { dispatch } = this.props

    if (activeKey === 'allocationPool') {
      dispatch({
        type: 'fund/getWaitFunds',
      })
    } else if (activeKey === 'trackinglist') {
      dispatch({
        type: 'investpool/fetch',
        payload: {
          ...params,
          dataType: 'fund',
          type: 'static',
        },
      })
    } else {
      if (activeKey === 'taaStrategy') {
        params.type = 'portfolio'
        params.portfolioType = 'taa'
      } else if (activeKey === 'saaStrategy') {
        params.type = 'portfolio'
        params.portfolioType = 'saa'
      } else {
        params.type = activeKey
        if (this.props.portfolioType) {
          params.portfolioType = this.props.portfolioType
        }
      }
      dispatch({
        type: 'fund/fetch',
        payload: params,
      })
    }
  };

  loadInvestPoolFunds = (investPool) => {
    const { dispatch } = this.props
    dispatch({
      type: 'investpool/fetchAllOrigFunds',
      payload: {
        id: investPool._id,
      },
    })
  }

  getSelectedRows = () => {
    const { activeKey, selectedRows } = this.state
    const { allOrigFunds } = this.props
    return activeKey === 'trackinglist' ? allOrigFunds : selectedRows
  }

  handleTableChange = (params: Partial<TableListParams>) => {
    if (this.state.activeKey !== 'allocationPool') {
      this.loadFundList({
        ...params,
        input: this.state.input,
      })
    }
  };

  handleSelectRows = (rows: TableListItem[]) => {
    const { activeKey } = this.state
    if (activeKey === 'trackinglist') {
      this.loadInvestPoolFunds(rows[0])
    }
    this.setState({
      selectedRows: rows,
    })
  };

  handleTabChange = (activeKey: string) => {
    let newSelectedRows = this.state.selectedRows
    if (activeKey === 'trackinglist' || this.state.activeKey === 'trackinglist') {
      newSelectedRows = []
      this.props.dispatch({
        type: 'investpool/save',
        payload: {
          allOrigFunds: [],
        },
      })
    }
    this.setState({ activeKey, selectedRows: newSelectedRows }, () => {
      this.loadFundList({
        page: 1,
      })
    })
  };

  handleSeachInput = (value: string) => {
    this.setState({ input: value })
    this.loadFundList({
      input: value,
      page: 1,
    })
  };

  handleSelectFunds = () => {
    const currentSelectedRows = this.getSelectedRows()
    this.setState({ show: false, selectedRows: [], input: '' })
    this.props.onChange(currentSelectedRows)
  };

  close = () => {
    this.setState({ show: false, selectedRows: [], input: '' })
  };

  open = () => {
    this.setState({ show: true }, () => {
      this.loadFundList()
    })
  };

  render() {
    const { selectedRows, activeKey, tabs } = this.state
    const columns: StandardTableColumnProps[] = [
      {
        title: '名称',
        dataIndex: 'name',
      },
      {
        title: '成立时间',
        dataIndex: 'startDate',
        format: 'date',
      },
      {
        title: '累计收益',
        dataIndex: 'accReturn',
        sorter:
          activeKey === 'allocationPool'
            ? sortQuotaFn({ value: 'accReturn', isNumeric: true }, 'asc')
            : true,
        align: 'right',
        format: 'percentage',
      },
      {
        title: '年化收益',
        dataIndex: 'yearReturn',
        sorter:
          activeKey === 'allocationPool'
            ? sortQuotaFn({ value: 'yearReturn', isNumeric: true }, 'asc')
            : true,
        align: 'right',
        format: 'percentage',
      },
      {
        title: '最大回撤',
        dataIndex: 'maxDrawdown',
        sorter:
          activeKey === 'allocationPool'
            ? sortQuotaFn({ value: 'maxDrawdown', isNumeric: true }, 'asc')
            : true,
        align: 'right',
        format: 'valPercentage',
      },
      {
        title: '波动率',
        dataIndex: 'vol',
        sorter:
          activeKey === 'allocationPool'
            ? sortQuotaFn({ value: 'vol', isNumeric: true }, 'asc')
            : true,
        align: 'right',
        format: 'valPercentage',
      },
    ]
    const investPoolColumns: StandardTableColumnProps[] = [
      {
        title: '名称',
        dataIndex: 'name',
      },
      {
        title: '描述',
        dataIndex: 'description',
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        format: 'date',
        align: 'right',
      },
      {
        title: '最后修改时间',
        dataIndex: 'updated_at',
        align: 'right',
        render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
      },
    ]
    const { fundList, loading, children, className, waitFunds, rowSelectionType, investPoolListData, style, title } = this.props
    let data = fundList
    if (activeKey === 'allocationPool') {
      data = {
        list: waitFunds,
        pagination: {
          pageSize: 10,
          total: Math.ceil(waitFunds / 10),
          showQuickJumper: false,
          showSizeChanger: false,
        },
      }
    } else if (activeKey === 'trackinglist') {
      data = investPoolListData
    }
    const currentSelectedRows = this.getSelectedRows()
    return (
      <div style={{ display: 'inline-block', ...style }} className={className}>
        <div onClick={this.open}>{children}</div>
        <Modal
          className={styles.modal}
          title={
            <>
              <span>
                {title || '请选择基金'}<span>({currentSelectedRows.length})</span>
                {selectedRows.length > 0 && (
                  <span style={{ marginLeft: '10px' }}>
                    <a onClick={() => this.setState({ selectedRows: [] })}>清空</a>
                  </span>
                )}
              </span>
              {activeKey !== 'allocationPool' && (
                <Search
                  style={{ float: 'right', width: '300px', marginRight: '25px' }}
                  size="small"
                  placeholder="按回车进行搜索"
                  onSearch={this.handleSeachInput}
                  enterButton
                />
              )}
            </>
          }
          visible={this.state.show}
          onCancel={this.close}
          width={1024}
          footer={[
            <Button disabled={currentSelectedRows.length === 0} onClick={this.handleSelectFunds} type="primary">
              确定
            </Button>,
          ]}
        >
          {tabs.length > 1 && (
            <Tabs
              activeKey={activeKey}
              className={styles.tabs}
              onChange={this.handleTabChange}
              tabBarExtraContent={
                activeKey === 'trackinglist' ?
                  <Button size="small" type="primary">
                    <a href="/reportbuilder/trackinglist" target="_blank">新建跟踪列表</a>
                  </Button> : null
              }
            >
              {tabs.map((tab: any) => (
                <TabPane tab={tab.name} key={tab.value} />
              ))}
            </Tabs>
          )}
          <StandardTable
            selectedRows={selectedRows}
            loading={loading}
            data={data}
            columns={activeKey === 'trackinglist' ? investPoolColumns : columns}
            onSelectRow={this.handleSelectRows}
            onChange={this.handleTableChange}
            size="small"
            rowKey="_id"
            rowSelectionType={activeKey === 'trackinglist' ? 'radio' : rowSelectionType}
            bordered
          />
        </Modal>
      </div>
    )
  }
}
