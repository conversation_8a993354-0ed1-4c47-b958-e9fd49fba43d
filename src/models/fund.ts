import { Effect } from 'dva'
import { Reducer } from 'redux'
import { notification } from 'antd'
import moment from 'moment'
import buildTreeData from '@/utils/buildTreeData'

import { TableListData } from '@/components/StandardTable'
import {
  queryFunds,
  queryFund,
  queryBenchmark,
  createNavPortfolio,
  updateNavPortfolio,
  getNavPortfolio,
  deleteFund,
  getFundListByMutualCodes,
  getWaitFundsMutualCodes,
  queryNavList,
  getCssPortfolioWeightPenetrate,
  getPositionData,
  queryKymQueryOptions,
  getDashboardData,
  queryChildFunds,
  queryChildFundsPosition,
  queryChildFundsStockData,
  computeCorrelationData,
  getDailyStockData,
  getScenarios,
  queryChildFundsIndustry,
  queryFactorScoreData,
  getAssetData,
  getGroupList,
} from '@/services/fund'

import { queryManager } from '@/services/manager'

import { calculateReturns } from '@/utils/calculator'

import {
  buildListPaylod,
  addNewItemToList,
  removeItemFromList,
  updateItemToList,
} from '@/utils/modelHelper'

export interface FundModelState {
  fundListData?: TableListData;
  fundFactorScoreData?: any;
  portfolioListData?: TableListData;
  currentFund?: any;
  currentManager?: any;
  currentPortfolio?: any;
  currentBenchmark?: any;
  currentBenchmarkDet?: any;
  editNetPortfolioSuccess?: boolean;
  addNetPortfolioSuccess?: boolean;
  defaultWaitFunds: any;
  waitFunds: any;
  navFundList?: any;
  portfolioFundWeights?: any;
  fundPositionData: any;
  fundAssetData: any;
  queryOptions?: any;
  mutualFundFilters?: any;
  managerFilters?: any;
  dashboardData: any;
  childFundList: any;
  childFundPosition: {
    assetData: any;
    stockIndustry: any;
    sharedStocks: any;
    fundNameMap: any;
  };
  childFundIndustry: any;
  childFundStockData: any;
  correlationData: any;
  dailyStockPosition: any;
  scenarios: any;
  userGroups: any;
}

export interface FundModelType {
  namespace: 'fund';
  state: FundModelState;
  effects: {
    fetch: Effect;
    fetchFactorScore: Effect;
    fetchOne: Effect;
    addNavPortfolio: Effect;
    editNavPortfolio: Effect;
    fetchNavPortfolio: Effect;
    fetchPortfolios: Effect;
    fetchManagers: Effect;
    fetchManager: Effect;
    fetchBenchmark: Effect;
    fetchBenchmarkDet: Effect;
    fetchPositionData: Effect;
    fetchAssetData: Effect;
    deleteNavPortfolio: Effect;
    getDefaultWaitFunds: Effect;
    getWaitFunds: Effect;
    getNavList: Effect;
    getPortfolioWeightPenetrate: Effect;
    fetchSaaStrategies: Effect;
    fetchTaaSchema: Effect;
    fetchTaaStrategies: Effect;
    fetchQueryOptions: Effect;
    fetchDashboardData: Effect;
    fetchChildFundList: Effect;
    fetchChildFundPosition: Effect;
    fetchChildFundStockData: Effect;
    fetchChindFundIndustry: Effect;
    getCorrelationData: Effect;
    fetchDailyStockPosition: Effect;
    fetchScenarios: Effect;
    fetchUserGroups: Effect;
  };
  reducers: {
    save: Reducer<FundModelState>;
    removePortfolioFromList: Reducer<FundModelState>;
    addNewPortfolioToList: Reducer<FundModelState>;
  };
}

const getDateList = (startDate, endDate, freq) => {
  let end = endDate
  let temp = moment(startDate).endOf(freq).format('YYYYMMDD')
  const results = []
  while (temp <= end) {
    results.push(temp)
    temp = moment(temp).add(1, freq).endOf(freq).format('YYYYMMDD')
  }
  return results.filter(item => item >= startDate && item <= endDate).reverse()
}

const getFactorSchemaOptions = (list, type) => {
  return list.filter(item => {
    return item.schemaType === type
  }).map(item => {
    return {
      title: item.name,
      dataIndex: item._id,
      key: item._id,
    }
  })
}

const getFactorSettingDefaultValues = ({
  factorDateOptions,
  stockTypeOptions,
  bondTypeOptions,
  allocTypeOptions,
  passiveTypeOptions,
}) => {
  return {
    factorDate: factorDateOptions[0] && factorDateOptions[0].dataIndex,
    stockTypeId:stockTypeOptions[0] && stockTypeOptions[0].dataIndex,
    bondTypeId: bondTypeOptions[0] && bondTypeOptions[0].dataIndex,
    allocTypeId: allocTypeOptions[0] && allocTypeOptions[0].dataIndex,
    passiveTypeId: passiveTypeOptions[0] && passiveTypeOptions[0].dataIndex,
  }
}

const getFactorFilterTableColumns = (factorColumn, isDimFactor, dateList) => {
  const startDate = '20100101'
  const endDate = moment().format('YYYYMMDD')
  const reportDatesMonthly = getDateList(startDate, endDate, 'month')
  const valueTypeOptions = [{
    title: '排名',
    dataIndex: 'value_rank',
    key: 'rank',
    format: 'number',
    tooltip: '取值范围1-N；降序，1表示最高',
  }, {
    title: isDimFactor ? '值(%)' : '分位数(%)',
    dataIndex: 'value_quantile',
    key: 'quantile',
    tooltip: isDimFactor ? '取值范围0-100；100表示最高' : '取值范围0-100；分位数越高因子值越小',
  }]
  if (!isDimFactor) {
    valueTypeOptions.push({
      title: '因子值(%)',
      dataIndex: 'factor_value',
      key: 'value',
    })
  }
  const dateColumn = {
    title: '日期',
    dataIndex: 'reportDate',
    width: '15%',
    key: 'reportDate',
    format: 'Select',
  }
  if (dateList) {
    dateColumn.options = dateList.map(name => {
      return {
        title: name,
        dataIndex: name,
        key: name,
        disableRef: true,
      }
    })
  } else {
    dateColumn.dateType = 'factor'
    dateColumn.options = reportDatesMonthly.map(name => {
      return {
        title: name,
        dataIndex: name,
        key: name,
        disableRef: true,
      }
    })
  }
  return [{
    title: '逻辑运算',
    dataIndex: 'logicalOperator',
    width: '10%',
    key: 'logicalOperator',
    format: 'LogicalOperatorSelect',
    options: [{
      title: 'AND',
      dataIndex: 'and',
      key: 'and',
    }, {
      title: 'OR',
      dataIndex: 'or',
      key: 'or',
    }],
  }, factorColumn, dateColumn, {
    title: '指标选项',
    dataIndex: 'valueType',
    width: '10%',
    key: 'valueType',
    format: 'Select',
    options: valueTypeOptions,
  }, {
    title: '指标范围',
    dataIndex: 'valueRange',
    width: '25%',
    key: 'valueRange',
    format: 'NumberRange',
    refColumn: 'valueType',
  }]
}

const getAdvancedFilter = (queryOptions: any, isManager?: boolean) => {
  const swIndustries = [
    '通信',
    '计算机',
    '传媒',
    '电子',
    '银行',
    '房地产',
    '非银金融',
    '医药生物',
    '商贸零售',
    '轻工制造',
    '农林牧渔',
    '社会服务',
    '食品饮料',
    '纺织服饰',
    '家用电器',
    '汽车',
    '美容护理',
    '建筑装饰',
    '公用事业',
    '交通运输',
    '环保',
    '综合',
    '建筑材料',
    '基础化工',
    '钢铁',
    '有色金属',
    '煤炭',
    '石油石化',
    '电力设备',
    '机械设备',
    '国防军工',
  ]
  const industryBoards = [
    'TMT',
    '金融',
    '医药生物',
    '消费',
    '公共产业',
    '周期中游',
    '周期上游',
    '周期下游',
  ]
  const datePrefixList = [[
    'ytd', 'YTD',
  ], [
    'last1M', '1个月以来',
  ], [
    'last3M', '3个月以来',
  ], [
    'last6M', '6个月以来',
  ], [
    'last1Y', '1年以来',
  ], [
    'last3Y', '3年以来',
  ]]

  const quotas = [{
    name: '收益',
    valueSufix: 'AccReturn',
    value: 'accReturn',
  }, {
    name: '波动率',
    valueSufix: 'Vol',
    value: 'vol',
  }, {
    name: '最大回撤',
    valueSufix: 'MaxDrawdown',
    value: 'maxDrawdown',
  }, {
    name: '夏普比率',
    valueSufix: 'SharpeRatio',
    value: 'sharpeRatio',
  }, {
    name: '索提诺比率',
    valueSufix: 'SortinoRatio',
    value: 'sortinoRatio',
  }, {
    name: '信息比率',
    valueSufix: 'InformationRatio',
    value: 'informationRatio',
  }, {
    name: '卡尔马',
    valueSufix: 'CalmarRatio',
    value: 'calmarRatio',
  }]

  const options = quotas.reduce((out, quota) => {
    const totalTitle = `成立以来${quota.name}`
    const totalIndex = quota.value
    const totalIndexRank = `${totalIndex}Rank`
    const results = [{
      title: totalTitle,
      dataIndex: totalIndex,
      key: totalIndex,
      format: 'percentage',
    }, {
      title: `${totalTitle}排名`,
      dataIndex: totalIndexRank,
      key: totalIndexRank,
      format: 'percentage',
    }]
    const dateQuotas = datePrefixList.reduce((out, item) => {
      const title = `${item[1]}${quota.name}`
      const index = `${item[0]}${quota.valueSufix}`
      return out.concat([{
        title,
        dataIndex: index,
        key: index,
        format: 'percentage',
      }, {
        title: `${title}排名`,
        dataIndex: `${index}Rank`,
        key: `${index}Rank`,
        format: 'percentage',
      }])
    }, [])
    return out.concat(results).concat(dateQuotas)
  }, [])
  options.push({
    title: '综合得分',
    dataIndex: 'totalFactorScore',
    key: 'totalFactorScore',
    format: 'number',
  })
  if (isManager) {
    options.unshift({
      title: '从业年限',
      dataIndex: 'positionDuration',
      key: 'positionDuration',
      format: 'number',
    })
  }
  const quotaTableFormColumns = [{
    title: '指标',
    dataIndex: 'quota',
    width: '30%',
    key: 'quota',
    format: 'Select',
    options,
  }, {
    title: '指标范围',
    dataIndex: 'valueRange',
    width: '40%',
    key: 'valueRange',
    format: 'NumberRange',
  }]
  const startDate = '20100101'
  const endDate = moment().format('YYYYMMDD')
  const reportDates = getDateList(startDate, endDate, 'quarter')
  const reportHalfDates = reportDates.filter(item => {
    const monthDay = item.slice(-4)
    return ['0630', '1231'].includes(monthDay)
  })
  const industryTableFormColumns = [{
    title: '行业',
    dataIndex: 'industry',
    width: '20%',
    key: 'industry',
    format: 'Select',
    options: swIndustries.map(name => {
      return {
        title: name,
        dataIndex: name,
        key: name,
      }
    }),
  }, {
    title: '报告日期',
    dataIndex: 'reportDate',
    width: '15%',
    key: 'reportDate',
    format: 'Select',
    options: reportHalfDates.map(name => {
      return {
        title: name,
        dataIndex: name,
        key: name,
        disableRef: true,
      }
    }),
  }, {
    title: '占比(%)',
    dataIndex: 'valueRange',
    width: '30%',
    key: 'valueRange',
    format: 'NumberRange',
  }]
  const industryBoardTableFormColumns = [{
    title: '行业板块',
    dataIndex: 'industryBoard',
    width: '20%',
    key: 'industryBoard',
    format: 'Select',
    options: industryBoards.map(name => {
      return {
        title: name,
        dataIndex: name,
        key: name,
      }
    }),
  }, {
    title: '报告日期',
    dataIndex: 'reportDate',
    width: '15%',
    key: 'reportDate',
    format: 'Select',
    options: reportHalfDates.map(name => {
      return {
        title: name,
        dataIndex: name,
        key: name,
        disableRef: true,
      }
    }),
  }, {
    title: '占比(%)',
    dataIndex: 'valueRange',
    width: '30%',
    key: 'valueRange',
    format: 'NumberRange',
  }]
  const stockTableFormColumns = [{
    title: '股票',
    dataIndex: 'stock',
    width: '20%',
    key: 'stock',
    format: 'Select',
    options: (queryOptions.stocks).map(item => {
      return {
        title: item.name,
        dataIndex: item.value,
        key: item.value,
      }
    }),
  }, {
    title: '报告日期',
    dataIndex: 'reportDate',
    width: '15%',
    key: 'reportDate',
    format: 'Select',
    options: reportDates.map(name => {
      return {
        title: name,
        dataIndex: name,
        key: name,
        disableRef: true,
      }
    }),
  }, {
    title: '占比(%)',
    dataIndex: 'valueRange',
    width: '30%',
    key: 'valueRange',
    format: 'NumberRange',
  }]
  const isValidValueRange = valueRange => {
    if (!valueRange || !valueRange.length) {
      return false
    }
    if (!valueRange.filter(item => item !== null && item !== undefined).length) {
      return false
    }
    return true
  }
  const factorTierNames = ['收益类', '风险类', '归因类', '策略类', '持仓类', '基金经理类', '基金公司类']
  const factorTierSortFn = ((pre, nex) => factorTierNames.indexOf(pre.title) - factorTierNames.indexOf(nex.title))
  const factorOptions = buildTreeData(queryOptions.factorList || [], ['class1', 'class2']).sort(factorTierSortFn)
  const fundTagOptions = buildTreeData(queryOptions.tagList || [], ['assetTypeName', 'class1', 'class2'])
  const dimFactorList = [{
    name: '收益类',
    value: 'incomeFactorScore',
    factorCode: 'FdIncomeDim_M',
  }, {
    name: '风险类',
    value: 'riskFactorScore',
    factorCode: 'FdRiskDim_M',
  }, {
    name: '归因类',
    value: 'attributionFactorScore',
    factorCode: 'FdAttrDim_M',
  }, {
    name: '策略类',
    value: 'strategyFactorScore',
    factorCode: 'FdStrategyDim_M',
  }, {
    name: '基金公司类',
    value: 'companyFactorScore',
    factorCode: 'FdCompanyDim_M',
  }, {
    name: '基金经理类',
    value: 'managerFactorScore',
    factorCode: 'FdManagerDim_M',
  }, {
    name: '持仓类',
    value: 'positionFactorScore',
    factorCode: 'FdPositionDim_M',
  }, {
    name: '总得分',
    value: 'totalFactorScore',
    factorCode: 'FdTotalDim_M',
  }]
  const dimClass1 = '维度得分'
  const refDimFactorList = dimFactorList.map(item => {
    return {
      class1: dimClass1,
      dataIndex: item.factorCode,
      title: item.name,
    }
  })
  const dimFactorOption = {
    id: dimClass1,
    key: dimClass1,
    label: dimClass1,
    title: dimClass1,
    value: dimClass1,
    children: dimFactorList.map(item => {
      return {
        id: `${dimClass1}__${item.factorCode}`,
        key: `${dimClass1}__${item.factorCode}`,
        label: item.name,
        title: item.name,
        value: item.factorCode,
      }
    })
  }
  // factorOptions.push(dimFactorOption)
  const factorColumn =  {
    title: '因子',
    dataIndex: 'factor',
    width: '25%',
    key: 'factor',
    format: 'Cascader',
    options: factorOptions,
    refOptions: (queryOptions.factorList || []).concat(refDimFactorList),
  }
  const fundTagColumn = {
    title: '标签',
    dataIndex: 'factor',
    width: '25%',
    key: 'factor',
    format: 'Cascader',
    options: fundTagOptions,
    refOptions: (queryOptions.tagList || []),
  }
  const quotaFilterTableColumns = getFactorFilterTableColumns(factorColumn, false)
  const tagFilterTableColumns = getFactorFilterTableColumns(fundTagColumn, false, queryOptions.tagDateList || []).slice(0, 3)
  const advancedFilter = {
    name: '高级筛选',
    type: 'tabs',
    filterId: 'advancedFilter',
    tabs: [
      // {
      //   name: '指标筛选',
      //   type: 'tableForm',
      //   formKey: 'quotas',
      //   componentProps: {
      //     tableColumns: quotaTableFormColumns,
      //     validateRow: item => {
      //       if (!item.quota) {
      //         return '请选择指标'
      //       }
      //       if (!isValidValueRange(item.valueRange)) {
      //         return '请输入指标范围'
      //       }
      //       return null
      //     },
      //   },
      // },
      {
        name: '指标筛选',
        type: 'tableForm',
        formKey: 'factors',
        disableOverlayLogic: true,
        componentProps: {
          tableColumns: [...quotaFilterTableColumns],
          validateRow: (item, index) => {
            if (index !== 0 && !item.logicalOperator) {
              return '请选择逻辑运算'
            }
            if (!item.factor) {
              return '请选择因子'
            }
            if (!item.valueType) {
              return '请选择指标选项'
            }
            if (!item.valueRange || !item.valueRange.filter(Boolean).length) {
              return '请设置指标范围'
            }
            return null
          },
        },
      },
      {
        name: '标签筛选',
        type: 'tableForm',
        formKey: 'fundTags',
        disableOverlayLogic: true,
        componentProps: {
          tableColumns: [...tagFilterTableColumns],
          validateRow: (item, index) => {
            if (index !== 0 && !item.logicalOperator) {
              return '请选择逻辑运算'
            }
            if (!item.factor) {
              return '请选择标签'
            }
            return null
          },
        },
      },
      {
        name: '行业占比',
        type: 'tableForm',
        formKey: 'industryRatio',
        componentProps: {
          tableColumns: industryTableFormColumns,
          validateRow: item => {
            if (!item.industry) {
              return '请选择行业'
            }
            if (!item.reportDate) {
              return '请选择报告日期'
            }
            if (!isValidValueRange(item.valueRange)) {
              return '请输入占比'
            }
            return null
          },
        },
      },
      {
        name: '行业板块占比',
        type: 'tableForm',
        formKey: 'industryBoardRatio',
        componentProps: {
          tableColumns: industryBoardTableFormColumns,
          validateRow: item => {
            if (!item.industryBoard) {
              return '请选择行业板块'
            }
            if (!item.reportDate) {
              return '请选择报告日期'
            }
            if (!isValidValueRange(item.valueRange)) {
              return '请输入占比'
            }
            return null
          },
        },
      },
      {
        name: '股票占比',
        type: 'tableForm',
        formKey: 'stockRatio',
        componentProps: {
          tableColumns: stockTableFormColumns,
          validateRow: item => {
            if (!item.stock) {
              return '请选择股票'
            }
            if (!item.reportDate) {
              return '请选择报告日期'
            }
            if (!isValidValueRange(item.valueRange)) {
              return '请输入占比'
            }
            return null
          },
        },
      },
    ].filter(item => {
      if (!isManager) {
        return true
      }
      return item.formKey !== 'fundTags'
    })
  }
  return advancedFilter
}

const FundModel: FundModelType = {
  namespace: 'fund',

  state: {
    fundListData: {
      list: [],
      pagination: {},
    },
    fundFactorScoreData: [],
    waitFunds: [],
    defaultWaitFunds: [],
    fundPositionData: {
      assetScales: [],
      bondPositions: [],
      stockIndustry: [],
      stockPositions: [],
      bondPosition: [],
      stockStyleData: [],
      stockSizeData: [],
    },
    fundAssetData: {
      assetScales: [],
    },
    mutualFundFilters: [],
    managerFilters: [],
    dashboardData: {
      stylesData: [],
      portfolios: [],
    },
    childFundList: [],
    childFundPosition: {
      assetData: [],
      stockIndustry: [],
      sharedStocks: [],
      fundNameMap: {},
    },
    childFundStockData: [],
    correlationData: {},
    dailyStockPosition: [],
    childFundIndustry: [],
    scenarios: [],
    userGroups: [],
  },

  effects: {
    *fetch({ payload = {} }, { call, put }) {
      const response = yield call(queryFunds, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          fundListData: data,
        },
      })
    },
    *fetchFactorScore({ payload = {} }, { call, put }) {
      const response = yield call(queryFactorScoreData, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          fundFactorScoreData: data,
        },
      })
    },
    *fetchPortfolios({ payload = {} }, { call, put }) {
      const response = yield call(queryFunds, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          portfolioListData: data,
        },
      })
    },
    *fetchBenchmark({ payload }, { call, put }) {
      const response = yield call(queryBenchmark, payload.id, payload.params)
      yield put({
        type: 'save',
        payload: { currentBenchmark: response },
      })
    },
    *fetchBenchmarkDet({ payload }, { call, put }) {
      const response = yield call(queryBenchmark, payload.id, payload.params)
      yield put({
        type: 'save',
        payload: { currentBenchmarkDet: response },
      })
    },
    *fetchManager({ payload }, { call, put }) {
      const response = yield call(queryManager, payload.id)
      const ref_fund_end_date = response.ref_fund_end_date && response.ref_fund_end_date.replace(/-/g, ''),
      const ref_fund_start_date = response.ref_fund_start_date && response.ref_fund_start_date.replace(/-/g, ''),
      const currentFund = {
        ...response.refFundData,
        isManager: true,
        dates: (response.refFundData.dates || []).filter(item => {
          if (!ref_fund_end_date || !ref_fund_start_date) {
            return true
          }
          return item >= ref_fund_start_date && item <= ref_fund_end_date
        }),
        ref_fund_end_date: response.ref_fund_end_date && response.ref_fund_end_date.replace(/-/g, ''),
        ref_fund_start_date: response.ref_fund_start_date && response.ref_fund_start_date.replace(/-/g, ''),
        ref_fund_end_date_str: response.ref_fund_end_date,
        ref_fund_start_date_str: response.ref_fund_start_date,
        ref_fund_end_date_ts: response.ref_fund_end_date && +moment(response.ref_fund_end_date).startOf('date'),
        ref_fund_start_date_ts: response.ref_fund_start_date && +moment(response.ref_fund_start_date).startOf('date'),
      }
      yield put({
        type: 'save',
        payload: { currentManager: response, currentFund },
      })
    },
    *fetchPositionData({ payload }, { call, put }) {
      const response = yield call(getPositionData, payload.id, payload.params)
      yield put({
        type: 'save',
        payload: { fundPositionData: response },
      })
    },
    *fetchAssetData({ payload }, { call, put }) {
      const response = yield call(getAssetData, payload.id, payload.params)
      yield put({
        type: 'save',
        payload: { fundAssetData: response },
      })
    },
    *fetchQueryOptions(_, { call, put }) {
      const queryOptions = yield call(queryKymQueryOptions)
      const styleTypes = [
        { value: '1', name: '价值型' },
        { value: '2', name: '成长型' },
        { value: '3', name: '均衡型' },
        { value: '4', name: '信用选择型' },
        { value: '5', name: '久期择时型' },
        { value: '6', name: '可转债型' },
      ]

      const compositTypes = [
        { value: '1', name: '金牌级' },
        { value: '2', name: '银牌级' },
        { value: '3', name: '铜牌级' },
      ]

      const dimFactorList = [{
        name: '收益类',
        value: 'incomeFactorScore',
        factorCode: 'FdIncomeDim_M',
      }, {
        name: '风险类',
        value: 'riskFactorScore',
        factorCode: 'FdRiskDim_M',
      }, {
        name: '归因类',
        value: 'attributionFactorScore',
        factorCode: 'FdAttrDim_M',
      }, {
        name: '策略类',
        value: 'strategyFactorScore',
        factorCode: 'FdStrategyDim_M',
      }, {
        name: '基金公司类',
        value: 'companyFactorScore',
        factorCode: 'FdCompanyDim_M',
      }, {
        name: '基金经理类',
        value: 'managerFactorScore',
        factorCode: 'FdManagerDim_M',
      }, {
        name: '持仓类',
        value: 'positionFactorScore',
        factorCode: 'FdPositionDim_M',
      }, {
        name: '总得分',
        value: 'totalFactorScore',
        factorCode: 'FdTotalDim_M',
      }]
      const dimClass1 = '维度得分'
      const refDimFactorList = dimFactorList.map(item => {
        return {
          class1: dimClass1,
          dataIndex: item.factorCode,
          title: item.name,
        }
      })
      const factorColumn =  {
        title: '因子',
        dataIndex: 'factor',
        width: '25%',
        key: 'factor',
        format: 'Select',
        options: refDimFactorList,
        refOptions: refDimFactorList,
      }

      const quotaFilterTableColumns = getFactorFilterTableColumns(factorColumn, true)
      const dateColumn = quotaFilterTableColumns.find(item => {
        return item.dataIndex === 'reportDate'
      })
      const factorFilterTableColumns = quotaFilterTableColumns.filter(item => {
        return item.dataIndex !== 'reportDate'
      })

      const factorSettingCompProps = {
        factorDateOptions: (queryOptions.factorScoreDateList || []).map(name => {
          return {
            title: name,
            dataIndex: name,
            key: name,
            disableRef: true,
          }
        }),
        stockTypeOptions: getFactorSchemaOptions(queryOptions.factorSchemaList || [], 'stock'),
        bondTypeOptions: getFactorSchemaOptions(queryOptions.factorSchemaList || [], 'bond'),
        allocTypeOptions: getFactorSchemaOptions(queryOptions.factorSchemaList || [], 'allocate'),
        passiveTypeOptions: getFactorSchemaOptions(queryOptions.factorSchemaList || [], 'passive'),
      }

      const factorSettingDefaultValues = getFactorSettingDefaultValues(factorSettingCompProps)

      const mutualFilters = [
        {
          name: '资产配置分类',
          type: 'tagSelect',
          formKey: 'asset_type',
          data: [
            { name: '纯股型', value: '1' },
            { name: '纯债型', value: '2' },
            { name: '配置型', value: '3' },
            { name: '被动型', value: '4' },
            { name: 'FOF', value: 'FOF' },
            { name: '其他', value: '0' },
          ],
        },
        {
          name: '基金风格分类',
          type: 'tagSelect',
          formKey: 'style_type',
          data: [
            { name: '价值型', value: '101' },
            { name: '成长型', value: '102' },
            { name: '均衡型', value: '103' },
            { name: '信用型', value: '201' },
            { name: '久期型', value: '202' },
            { name: '平衡型', value: '203' },
            { name: '积极配置型', value: '301' },
            { name: '消极配置型', value: '302' },

          ],
        },
        {
          name: '基金公司',
          type: 'groupByInitialsSelect',
          formKey: 'company',
          data: queryOptions.company || [],
        },
        {
          name: '因子方案',
          type: 'tabs',
          filterId: 'factorSchema',
          tabs: [{
            name: '选择方案',
            type: 'factorSetting',
            formKey: 'factorSetting',
            componentProps: factorSettingCompProps,
            defaultValues: factorSettingDefaultValues,
          }, {
            name: '维度得分',
            type: 'tableForm',
            formKey: 'factorDims',
            disableOverlayLogic: true,
            componentProps: {
              tableColumns: [...factorFilterTableColumns],
              validateRow: (item, index) => {
                if (index !== 0 && !item.logicalOperator) {
                  return '请选择逻辑运算'
                }
                if (!item.factor) {
                  return '请选择因子'
                }
                if (!item.valueType) {
                  return '请选择指标选项'
                }
                if (!item.valueRange || !item.valueRange.filter(Boolean).length) {
                  return '请设置指标范围'
                }
                return null
              },
            },
          }]
        }
      ]

      const mutualFundFilters = [
        ...mutualFilters,
        {...getAdvancedFilter(queryOptions)},
        {
          type: 'switchList',
          filterId: 'fundSwticher',
          switchList: [{
            name: 'C份额',
            value: 'isCShare',
          }, {
            name: 'ETF',
            value: 'isEtf',
          }, {
            name: 'QDII',
            value: 'isQdii',
          }, {
            name: '港股',
            value: 'isHkFund',
          }, {
            name: '已到期',
            value: 'isStopped',
          }],
        },
      ]
      const managerFilters = [
        ...mutualFilters,
        {...getAdvancedFilter(queryOptions, true)},
        // {
        //   name: '基金公司',
        //   type: 'groupByInitialsSelect',
        //   formKey: 'company',
        //   data: queryOptions.company || [],
        // }
      ]
      yield put({
        type: 'save',
        payload: { queryOptions, mutualFundFilters, managerFilters },
      })
    },
    *fetchDashboardData({ payload }, { call, put }) {
      const response = yield call(getDashboardData)
      const dashboardData = {
        ...response,
        stylesData: response.stylesData.map((item: any) => {
          item.returns = calculateReturns(item.nets)
          return item
        }),
      }
      yield put({
        type: 'save',
        payload: { dashboardData },
      })
    },
    *fetchOne({ payload }, { call, put }) {
      const response = yield call(queryFund, payload.id)
      yield put({
        type: 'save',
        payload: { currentFund: response },
      })
    },
    *addNavPortfolio({ payload: { data, params } }, { call, put }) {
      const response = yield call(createNavPortfolio, data, params)
      yield put({
        type: 'save',
        payload: {
          portfolio: response,
          addNetPortfolioSuccess: true,
        },
      })
      yield put({
        type: 'addNewPortfolioToList',
        payload: response,
      })
      notification.success({ message: '创建成功' })
    },
    *editNavPortfolio({ payload: { data, id } }, { call, put }) {
      const response = yield call(updateNavPortfolio, id, data)
      yield put({
        type: 'save',
        payload: { portfolio: response, editNetPortfolioSuccess: true },
      })
      yield put({
        type: 'updatePortfolioToList',
        payload: response,
      })
      notification.success({ message: '更新成功' })
    },
    *fetchNavPortfolio({ payload: { id } }, { call, put }) {
      const response = yield call(getNavPortfolio, id)
      yield put({
        type: 'save',
        payload: { portfolio: response },
      })
    },
    *deleteNavPortfolio({ payload: { id } }, { call, put }) {
      yield call(deleteFund, id)
      yield put({
        type: 'removePortfolioFromList',
        payload: { id },
      })
      notification.success({ message: '删除成功！' })
    },
    *getDefaultWaitFunds({ payload }, { call, put }) {
      const response = yield call(getFundListByMutualCodes, payload)
      yield put({
        type: 'save',
        payload: {
          defaultWaitFunds: response,
        },
      })
    },
    *getWaitFunds(_, { call, put }) {
      const result = yield call(getWaitFundsMutualCodes)
      if (!result || !result.data || !result.data.length) {
        return
      }
      const data = result.data
      const codes = data.map(item => item.windCode).join(',')
      const response = yield call(getFundListByMutualCodes, { codes })
      yield put({
        type: 'save',
        payload: {
          waitFunds: response,
        },
      })
    },
    *getNavList({ payload }, { call, put }) {
      const response = yield call(queryNavList, payload)
      yield put({
        type: 'save',
        payload: { navFundList: response },
      })
    },
    *getPortfolioWeightPenetrate({ payload: { id } }, { call, put }) {
      const response = yield call(getCssPortfolioWeightPenetrate, id)
      yield put({
        type: 'save',
        payload: { portfolioFundWeights: response },
      })
    },
    *fetchTaaSchema({ payload }, { call, put }) {
      const response = yield call(queryFunds, {
        ...payload,
        portfolioType: 'css',
        type: 'portfolio',
      })
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          portfolioListData: data,
        },
      })
    },
    *fetchTaaStrategies({ payload }, { call, put }) {
      const response = yield call(queryFunds, {
        ...payload,
        portfolioType: 'taa',
        type: 'portfolio',
      })
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          portfolioListData: data,
        },
      })
    },
    *fetchSaaStrategies({ payload }, { call, put }) {
      const response = yield call(queryFunds, {
        ...payload,
        portfolioType: 'saa',
        type: 'portfolio',
      })
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          portfolioListData: data,
        },
      })
    },
    *fetchChildFundList({ payload: { id } }, { call, put }) {
      const response = yield call(queryChildFunds, id)
      yield put({
        type: 'save',
        payload: {
          childFundList: response,
        },
      })
    },
    *fetchChildFundPosition({ payload: { id } }, { call, put }) {
      const response = yield call(queryChildFundsPosition, id)
      yield put({
        type: 'save',
        payload: {
          childFundPosition: response,
        },
      })
    },
    *fetchChildFundStockData({ payload: { fundId, params } }, { call, put }) {
      const response = yield call(queryChildFundsStockData, fundId, params)
      yield put({
        type: 'save',
        payload: {
          childFundStockData: response,
        },
      })
    },
    *fetchChindFundIndustry({ payload: { id, params } }, { call, put }) {
      const response = yield call(queryChildFundsIndustry, id, params)
      yield put({
        type: 'save',
        payload: {
          childFundIndustry: response,
        },
      })
    },
    *getCorrelationData({ payload: { params } }, { call, put }) {
      const response = yield call(computeCorrelationData, params)
      yield put({
        type: 'save',
        payload: {
          correlationData: response,
        },
      })
    },
    *fetchDailyStockPosition({ payload: { id, params } }, { call, put }) {
      const response = yield call(getDailyStockData, id, params)
      yield put({
        type: 'save',
        payload: {
          dailyStockPosition: response,
        },
      })
    },
    *fetchScenarios(_, { call, put }) {
      const response = yield call(getScenarios)
      yield put({
        type: 'save',
        payload: {
          scenarios: response.list,
        },
      })
    },
    *fetchUserGroups(_, { call, put }) {
      const response = yield call(getGroupList)
      yield put({
        type: 'save',
        payload: {
          userGroups: response.list,
        },
      })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    addNewPortfolioToList(state: FundModelState, action) {
      return addNewItemToList(state, action.payload, 'portfolioListData')
    },
    removePortfolioFromList(state: FundModelState, action) {
      return removeItemFromList(state, action.payload, 'portfolioListData')
    },
    updatePortfolioToList(state: FundModelState, action) {
      return updateItemToList(state, action.payload, 'portfolioListData')
    },
  },
}

export default FundModel
