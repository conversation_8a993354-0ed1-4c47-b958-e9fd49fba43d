import { Reducer } from 'redux'
import { routerRedux } from 'dva/router'
import { Effect } from 'dva'
import { stringify } from 'querystring'
import { notification } from 'antd'

import { fakeAccountLogin, getFakeCaptcha, logout } from '@/services/login'
import { setAuthority } from '@/utils/authority'
import { getPageQuery } from '@/utils/utils'

export interface StateType {
  status?: 'ok' | 'error';
  type?: string;
  currentAuthority?: 'user' | 'guest' | 'admin';
  captchaTs?: number,
}

export interface LoginModelType {
  namespace: string;
  state: StateType;
  effects: {
    login: Effect;
    getCaptcha: Effect;
    logout: Effect;
  };
  reducers: {
    changeLoginStatus: Reducer<StateType>;
  };
}

const Model: LoginModelType = {
  namespace: 'login',

  state: {
    status: undefined,
    captchaTs: Date.now(),
  },

  effects: {
    *login({ payload }, { call, put }) {
      const response = yield call(fakeAccountLogin, payload)
      console.log(response)
      if (response.name === 'Error') {
        if (!response.message.includes('signin.')) {
          notification.error({
            message: response.message,
          })
        }
        if (response.message === 'signin.emailPasswordNotMatch') {
          notification.error({
            message: '用户名密码不正确',
          })
        }
        if (response.message === 'signin.captchaError') {
          notification.error({
            message: '用户名密码不正确',
          })
        }
        if (response.message === 'signin.captchaInvalid') {
          notification.error({
            message: '验证码不正确',
          })
        }
        yield put({
          type: 'changeLoginStatus',
          payload: {
            requireCaptcha: response.message === 'signin.captchaError'
              || response.message === 'signin.captchaInvalid'
              || (response.extra && response.extra.requireCaptcha),
            captchaTs: Date.now(),
          },
        })
        return
      }
      yield put({
        type: 'changeLoginStatus',
        payload: response,
      })
      // Login successfully
      const token = `Bearer ${response.token}`
      window.__user_token = token
      localStorage.setItem('token', token)
      setAuthority(response.profile.role)
      const urlParams = new URL(window.location.href)
      const params = getPageQuery()
      let { redirect } = params as { redirect: string }
      if (redirect) {
        const redirectUrlParams = new URL(redirect)
        if (redirectUrlParams.origin === urlParams.origin) {
          redirect = redirect.substr(urlParams.origin.length)
          if (redirect.match(/^\/.*#/)) {
            redirect = redirect.substr(redirect.indexOf('#') + 1)
          }
        } else {
          window.location.href = redirect
          return
        }
      }
      window.location.href = '/'
      // yield put(routerRedux.replace(redirect || '/'))
    },

    *getCaptcha({ payload }, { call }) {
      yield call(getFakeCaptcha, payload)
    },
    *logout(_, { call }) {
      localStorage.clear()
      const { redirect } = getPageQuery()
      yield call(logout)
      window.location.href = '/user/login'
      // redirect
      // if (window.location.pathname !== '/user/login' && !redirect) {
      //   yield put(
      //     routerRedux.replace({
      //       pathname: '/user/login',
      //       search: stringify({
      //         redirect: window.location.href,
      //       }),
      //     }),
      //   )
      // }
    },
  },

  reducers: {
    changeLoginStatus(state, { payload }) {
      if (payload.profile) setAuthority(payload.profile.role)
      return {
        ...state,
        ...payload,
      }
    },
  },
}

export default Model
