import { Effect } from 'dva'
import { Reducer } from 'redux'

import { queryCurrent, query as queryUsers, updatePassword, querySearchOptions, querySystemInfo } from '@/services/user'
import { notification } from 'antd'
import { setAuthority } from '@/utils/authority'
export interface CurrentUser {
  avatar?: string;
  nickname?: string;
  _id?: string;
  admin_scopes?: string[];
  user_scopes?: string[];
  userid?: string;
  notifyCount?: number;
  unreadCount?: number;
}

export interface SystemInfo {
  userFrom?: string,
  networkSegment?: string,
}

export interface UserModelState {
  currentUser?: CurrentUser;
  searchOptions?: any;
  systemInfo? : SystemInfo;
}

export interface UserModelType {
  namespace: 'user';
  state: UserModelState;
  effects: {
    fetch: Effect;
    fetchCurrent: Effect;
    changePassword: Effect;
    fetchSearchOptions: Effect;
    fetchSystemInfo: Effect;
  };
  reducers: {
    saveCurrentUser: Reducer<UserModelState>;
    changeNotifyCount: Reducer<UserModelState>;
    save: Reducer<UserModelState>;
  };
}

const UserModel: UserModelType = {
  namespace: 'user',

  state: {
    currentUser: {},
    searchOptions: {
      mutualFunds: [],
      funds: [],
      managers: [],
    },
    systemInfo: {},
  },

  effects: {
    *fetch(_, { call, put }) {
      const response = yield call(queryUsers)
      yield put({
        type: 'save',
        payload: response,
      })
    },
    *fetchCurrent(_, { call, put }) {
      const response = yield call(queryCurrent)
      yield put({
        type: 'saveCurrentUser',
        payload: response,
      })
    },
    *changePassword({ payload }, { call, put }) {
      const response = yield call(updatePassword, payload)
      if (response.name === 'Error') {
        notification.warning({ message: response.message })
      } else {
        notification.success({ message: '修改成功' })
        setTimeout(() => {
          window.location.href = '/'
        }, 1000)
      }
    },
    *fetchSearchOptions(_, { call, put }) {
      const response = yield call(querySearchOptions)
      yield put({
        type: 'save',
        payload: {
          searchOptions: response,
        },
      })
    },
    *fetchSystemInfo(_, { call, put }) {
      const response = yield call(querySystemInfo)
      yield put({
        type: 'save',
        payload: {
          systemInfo: response,
        },
      })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    saveCurrentUser(state, action) {
      setAuthority(action.payload && action.payload.role)
      return {
        ...state,
        currentUser: action.payload || {},
      }
    },
    changeNotifyCount(
      state = {
        currentUser: {},
      },
      action,
    ) {
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          notifyCount: action.payload.totalCount,
          unreadCount: action.payload.unreadCount,
        },
      }
    },
  },
}

export default UserModel
