import _ from 'lodash'
import { Effect } from 'dva'
import { Reducer } from 'redux'
import { queryTaaScore, queryFundList, queryTaaScoreByDate } from '@/services/taa'

export interface ModelState {
  taaScore?: any;
  fundListData: FundItem[];
  stylesScoreData?: any;
  currentDate?: string;
  taaWeightStrategy: string;
  saaWeightData: StyleItem[];
  taaWeightData: StyleItem[];
  fundWeightData: FundItem[];
  totalWeightLimitValue: number;
  similarAssetsFundNumLimit: number;
  customFundList: FundItem[];
  fundSelectScopes: 'kymGold' | 'kymGoldProspect' | 'all' | 'custom';
  fundWeightStrategy: 'equalWeight' | 'useKymScore' | 'limitedEqualWeight';
  fundEqualWeightLimitValue: number;
}

export interface ModelType {
  namespace: 'taa';
  state: ModelState;
  effects: {
    fetchTaaScore: Effect;
    fetchTaaScoreByDate: Effect;
    fetchFundList: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
    updateTaaStyleWeights: Reducer<ModelState>;
    updateFundWeights: Reducer<ModelState>;
  };
}

interface StyleItem {
  key: string;
  styleName: string;
  weight?: number;
}

interface TaaScoreItem {
  the_date: string;
  class_name: string;
  taa_score: number;
}

interface FundItem {
  key: string;
  class_code: string;
  class_name: string;
  fund_abbr_name: string;
  fund_id: string;
  class_level: number;
  class_score: number;
  prospect: number;
  weight?: number;
}

const THRESHOLD = 2.5

const signFn = (value: number) => {
  if (value > 0) {
    return 1
  } else if (value < 0) {
    return -1
  } else {
    return 0
  }
}

const calculateWeightForCash = (taaWeightData: StyleItem[], weightData: any) => {
  const cashWeight = 1 - _.sum(_.values(weightData))
  return taaWeightData.map(item => {
    if (item.styleName === '现金') {
      item.weight = cashWeight
    } else {
      item.weight = weightData[item.styleName] || 0
    }
    if (item.weight) {
      item.weight = item.weight * 100
    }
    return item
  })
}

const calculateTaaStyleWeightsByScore = (taaWeightData: StyleItem[], scoreList: TaaScoreItem[]) => {
  const styleNames = taaWeightData.map(item => item.styleName)
  const scoreData = scoreList.filter(
    item => item.class_name !== '现金' && styleNames.includes(item.class_name),
  )
  const scoreSum = _.sumBy(scoreData, 'taa_score')
  const weightData = scoreData.reduce((out, item) => {
    out[item.class_name] =
      (item.taa_score / scoreSum) * Math.min(scoreSum / THRESHOLD / scoreData.length, 1)
    return out
  }, {})
  return calculateWeightForCash(taaWeightData, weightData)
}

const calculateTaaStyleWeightsByTotalLimit = (
  taaWeightData: StyleItem[],
  scoreList: TaaScoreItem[],
  saaWeightData: StyleItem[],
  totalWeightLimitValue: number,
) => {
  const styleNames = taaWeightData.map(item => item.styleName)
  const scoreData = scoreList.filter(
    item => item.class_name !== '现金' && styleNames.includes(item.class_name),
  )
  const sortedStyles = scoreData
    .map(item => {
      return {
        styleName: item.class_name,
        value: Math.abs(item.taa_score - THRESHOLD) / THRESHOLD,
        taa_score: item.taa_score,
      }
    })
    .sort((fst, snd) => snd.value - fst.value)
  const saaWeightMap = saaWeightData.reduce((out, item) => {
    out[item.styleName] = item.weight
    return out
  }, {})
  const styleValueSum = _.sumBy(sortedStyles, 'value')
  const weightData = sortedStyles.reduce((out, item) => {
    const tempWeightSum = _.sum(_.values(out))
    const saaWeight = saaWeightMap[item.styleName] || 0
    const middleWeight =
      saaWeight / 100 +
      ((item.value / styleValueSum) * signFn(item.taa_score - THRESHOLD) * totalWeightLimitValue) /
        100
    const weight = Math.min(Math.max(middleWeight, 0), 1 - tempWeightSum)
    out[item.styleName] = weight
    return out
  }, {})
  return calculateWeightForCash(taaWeightData, weightData)
}

const calculateTaaFundWeight = (state: ModelState) => {
  const {
    fundListData,
    fundSelectScopes,
    taaWeightData,
    fundWeightStrategy,
    fundEqualWeightLimitValue,
    similarAssetsFundNumLimit,
    customFundList,
  } = state
  let filteredFunds = fundSelectScopes === 'custom' ? customFundList : fundListData
    .filter(item => {
      if (fundSelectScopes === 'kymGold') {
        return item.class_level === 1
      } else if (fundSelectScopes === 'kymGoldProspect') {
        return item.class_level === 1 && item.prospect > 3
      } else if (fundSelectScopes === 'all') {
        return true
      } else {
        return false
      }
    })
  filteredFunds = filteredFunds.map(item => {
      return {
        ...item,
        key: item.fund_id,
      }
    })
  const taaStyleWeight = taaWeightData.reduce((out, item) => {
    out[item.styleName] = item.weight / 100
    return out
  }, {})
  const calcualteWeights = (funds: FundItem[], styleWeight: number) => {
    const scoreSum = _.sum(funds.map(fund => fund.class_score))
    if (fundWeightStrategy === 'useKymScore') {
      return funds.map(fund => {
        fund.weight = styleWeight * (fund.class_score / scoreSum)
        return fund
      })
    }
    const equalWeight = styleWeight / funds.length
    const weightLimitValue = fundEqualWeightLimitValue / 100
    if (fundWeightStrategy === 'limitedEqualWeight' && equalWeight < weightLimitValue) {
      const fundNum = Math.floor(styleWeight / weightLimitValue)
      const newFunds = funds.slice(0, fundNum)
      return newFunds.map(fund => {
        fund.weight = styleWeight / newFunds.length
        return fund
      })
    }
    return funds.map(fund => {
      fund.weight = equalWeight
      return fund
    })
  }

  return _.reduce(
    _.groupBy(filteredFunds, 'class_name'),
    (out, values, styleName) => {
      const sortedFunds = values
        .sort((fst, snd) => snd.class_score - fst.class_score)
        .slice(0, similarAssetsFundNumLimit)
      const funds = calcualteWeights(sortedFunds, taaStyleWeight[styleName])
      console.log(taaStyleWeight)
      return out.concat(funds)
    },
    [],
  ).map(fund => {
    fund.weight = fund.weight * 100
    return fund
  })
}

const classNameMap = {
  1: '股票价值型',
  2: '股票均衡型',
  3: '股票成长型',
  4: '债券久期择时型',
  5: '债券信用选择型',
}

const KymModel: ModelType = {
  namespace: 'taa',

  state: {
    taaScore: {
      scoreListData: [],
      corrListData: [],
      dateList: [],
    },
    fundListData: [],
    stylesScoreData: [],
    customFundList: [],
    taaWeightStrategy: 'useTaaScore',
    taaWeightData: [
      '股票价值型',
      '股票均衡型',
      '股票成长型',
      '债券久期择时型',
      '债券信用选择型',
      '现金',
    ].map((item, index) => {
      return {
        key: `${index}`,
        styleName: item,
      }
    }),
    saaWeightData: [
      '股票价值型',
      '股票均衡型',
      '股票成长型',
      '债券久期择时型',
      '债券信用选择型',
    ].map((item, index) => {
      return {
        key: `${index}`,
        styleName: item,
        weight: 100 / 5,
      }
    }),
    totalWeightLimitValue: 0,
    similarAssetsFundNumLimit: 20,
    fundEqualWeightLimitValue: 0,
    fundSelectScopes: 'kymGold',
    fundWeightStrategy: 'equalWeight',
    fundWeightData: [],
  },

  effects: {
    *fetchTaaScore({ payload: { params } }, { call, put }) {
      const response = yield call(queryTaaScore, params)
      const { dateList } = response
      yield put({
        type: 'save',
        payload: {
          taaScore: response,
          currentDate: dateList[0],
        },
      })
    },
    *fetchTaaScoreByDate({ payload: { params } }, { call, put }) {
      const response = yield call(queryTaaScoreByDate, params)
      yield put({
        type: 'save',
        payload: {
          stylesScoreData: response,
        },
      })
      yield put({
        type: 'updateTaaStyleWeights',
      })
    },
    *fetchFundList({ payload: { params } }, { call, put }) {
      const response = yield call(queryFundList, params)
      yield put({
        type: 'save',
        payload: {
          fundListData: response.map(item => {
            return {
              ...item,
              class_name: classNameMap[item.style_type],
            }
          }),
        },
      })
      yield put({
        type: 'updateFundWeights',
      })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    updateTaaStyleWeights(state: ModelState, action) {
      const {
        taaWeightData,
        taaWeightStrategy,
        totalWeightLimitValue,
        saaWeightData,
        stylesScoreData,
      } = state
      let result
      if (taaWeightStrategy === 'useTaaScore') {
        result = calculateTaaStyleWeightsByScore(taaWeightData, stylesScoreData)
      } else if (totalWeightLimitValue !== undefined) {
        result = calculateTaaStyleWeightsByTotalLimit(
          taaWeightData,
          stylesScoreData,
          saaWeightData,
          totalWeightLimitValue,
        )
      }
      if (!result) {
        return state
      }
      return {
        ...state,
        taaWeightData: result,
      }
    },
    updateFundWeights(state: ModelState) {
      const fundWeightData = calculateTaaFundWeight(state)
      return {
        ...state,
        fundWeightData,
      }
    },
  },
}

export default KymModel
