import { Effect } from 'dva'
import { Reducer } from 'redux'
import buildListPaylod from '@/utils/buildListPaylod'
import { TableListData } from '@/components/StandardTable'
import { queryManager, queryManagers, queryBenchmarks, queryFactorScoreData } from '@/services/manager'

export interface ModelState {
  managerListData?: TableListData;
  managerFactorScoreData?: any;
  currentManager?: any;
  benchmarkList?: any;
}

export interface ModelType {
  namespace: 'manager';
  state: ModelState;
  effects: {
    fetch: Effect;
    fetchFactorScore: Effect;
    fetchOne: Effect;
    fetchBenchmarks: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
  };
}

const FundModel: ModelType = {
  namespace: 'manager',

  state: {
    managerListData: {
      list: [],
      pagination: {},
    },
    managerFactorScoreData: {
      list: [],
      pagination: {},
    },
    benchmarkList: [],
  },

  effects: {
    *fetch({ payload = {} }, { call, put }) {
      const response = yield call(queryManagers, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          managerListData: data,
        },
      })
    },
    *fetchFactorScore({ payload = {} }, { call, put }) {
      const response = yield call(queryFactorScoreData, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          managerFactorScoreData: data,
        },
      })
    },
    *fetchOne({ payload }, { call, put }) {
      const response = yield call(queryManager, payload.id)
      yield put({
        type: 'save',
        payload: { currentManager: response },
      })
    },
    *fetchBenchmarks({ payload }, { call, put }) {
      const response = yield call(queryBenchmarks, payload.type)
      yield put({
        type: 'save',
        payload: { benchmarkList: response },
      })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
  },
}

export default FundModel
