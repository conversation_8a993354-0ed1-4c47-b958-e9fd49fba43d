import { Effect } from 'dva'
import { Reducer } from 'redux'
import { notification } from 'antd'
import { TableListData } from '@/components/StandardTable'
import {
  queryInvestPool,
  queryInvestPools,
  createInvestPool,
  updateInvestPool,
  deleteInvestPool,
  addFundsToInvestPool,
  deleteFundsFromInvestsPool,
  queryFunds,
  queryAllOrigFunds,
  queryGroupList,
} from '@/services/investpool'

import {
  buildListPaylod,
  addNewItemToList,
  removeItemFromList,
  updateItemToList,
  addItemsToList,
  removeItemsFromList,
} from '@/utils/modelHelper'

export interface ModelState {
  investPoolListData?: TableListData;
  currentInvestPool?: any;
  fundListData?: TableListData;
  allOrigFunds: any;
  groupList: any;
}

export interface ModelType {
  namespace: 'investpool';
  state: ModelState;
  effects: {
    fetch: Effect;
    fetchOne: Effect;
    add: Effect;
    edit: Effect;
    remove: Effect;
    addFunds: Effect;
    removeFunds: Effect;
    fetchFunds: Effect;
    fetchAllOrigFunds: Effect;
    fetchGroupList: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
  };
}

const InvestPoolModel: ModelType = {
  namespace: 'investpool',

  state: {
    investPoolListData: {
      list: [],
      pagination: {},
    },
    fundListData: {
      list: [],
      pagination: {},
    },
    allOrigFunds: [],
    groupList: [],
  },

  effects: {
    *fetch({ payload = {} }, { call, put }) {
      const response = yield call(queryInvestPools, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          investPoolListData: data,
        },
      })
    },
    *fetchOne({ payload }, { call, put }) {
      const response = yield call(queryInvestPool, payload.id)
      yield put({
        type: 'save',
        payload: { currentInvestPool: response },
      })
    },
    *add({ payload: { data } }, { call, put }) {
      const response = yield call(createInvestPool, data)
      yield put({
        type: 'save',
        payload: {
          currentInvestPool: response,
        },
      })
      yield put({
        type: 'addNewInvestPoolToList',
        payload: response,
      })
      notification.success({ message: '创建成功！' })
    },
    *edit({ payload: { data, id } }, { call, put }) {
      const response = yield call(updateInvestPool, id, data)
      yield put({
        type: 'save',
        payload: { portfolio: response },
      })
      yield put({
        type: 'updateInvestPoolToList',
        payload: response,
      })
      notification.success({ message: '更新成功！' })
    },
    *remove({ payload: { id } }, { call, put }) {
      yield call(deleteInvestPool, id)
      yield put({
        type: 'removeInvestPoolFromList',
        payload: { id },
      })
      notification.success({ message: '删除成功！' })
    },
    *addFunds({ payload: { id, data } }, { call, put }) {
      const newFunds = yield call(addFundsToInvestPool, id, data)
      yield put({
        type: 'addFundsToList',
        payload: newFunds,
      })
      notification.success({ message: '添加成功！' })
    },
    *removeFunds({ payload: { id, data } }, { call, put }) {
      yield call(deleteFundsFromInvestsPool, id, data)
      yield put({
        type: 'removeFundsFromList',
        payload: data,
      })
      notification.success({ message: '移除成功！' })
    },
    *fetchFunds({ payload: { id, params } }, { call, put }) {
      const response = yield call(queryFunds, id, params)
      const data = buildListPaylod(params, response)
      yield put({
        type: 'save',
        payload: {
          fundListData: data,
        },
      })
    },
    *fetchAllOrigFunds({ payload: { id, params } }, { call, put }) {
      const response = yield call(queryAllOrigFunds, id, params)
      if (response.length) {
        notification.success({ message: `成功加载${response.length}支基金` })
      } else {
        notification.warning({ message: '当前跟踪列表没有基金' })
      }
      yield put({
        type: 'save',
        payload: {
          allOrigFunds: response,
        },
      })
    },
    *fetchGroupList({}, { call, put }) {
      const response = yield call(queryGroupList)
      yield put({
        type: 'save',
        payload: {
          groupList: response,
        },
      })
    }
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    addNewInvestPoolToList(state: ModelState, action) {
      return addNewItemToList(state, action.payload, 'investPoolListData')
    },
    removeInvestPoolFromList(state: FundModelState, action) {
      return removeItemFromList(state, action.payload, 'investPoolListData')
    },
    updateInvestPoolToList(state: FundModelState, action) {
      return updateItemToList(state, action.payload, 'investPoolListData')
    },
    addFundsToList(state: ModelState, action) {
      return addItemsToList(state, action.payload, 'fundListData')
    },
    removeFundsFromList(state: ModelState, action) {
      return removeItemsFromList(state, action.payload, 'fundListData')
    },
  },
}

export default InvestPoolModel
