import moment from 'moment'

export default function changeFrequency(data, freq, isFilterDate) {
  if (!data || !data.length) {
    return data
  }
  const data0 = data[0]
  const isArray = Array.isArray(data0)
  const initialValue = isArray ? data0[0] : data0.value
  const dateValueMap = data.reduce((out, item) => {
    let date
    let months
    let quarter
    let month
    let timestamp
    let value
    if (isArray) {
      timestamp = item[0]
      value = item
    } else {
      timestamp = item.date
      value = item.value
    }
    const oringinalDate = moment(new Date(timestamp))
    switch (freq) {
      case 'week':
        // date = oringinalDate.format('YYYY-\\Www')
        const year_ = oringinalDate.isoWeekYear()
        const week_ = oringinalDate.isoWeek()
        const padWeek = week_ > 9 ? week_ : `0${week_}`
        date = `${year_}-W${padWeek}`
        break
      case 'month':
        date = oringinalDate.format('YYYY-MM')
        break
      case 'quarter':
        quarter = oringinalDate.quarter() - 1
        months = [2, 5, 8, 11]
        date = moment([oringinalDate.year(), 0, 31])
          .add(months[quarter], 'month')
          .format('YYYY-MM-DD')
        break
      case 'halfyear':
        month = oringinalDate.month()
        date = moment([oringinalDate.year(), 0, 31])
          .add(month >= 5 ? 11 : 5, 'month')
          .format('YYYY-MM-DD')
        break
      case 'year':
        date = oringinalDate.endOf('year').format('YYYY-MM-DD')
        break
      default:
        break
    }
    out[date] = value
    return out
  }, {})
  const results = Object.keys(dateValueMap)
    .map(key => {
      let date = moment(key)
      if (freq === 'week') {
        date = +date.endOf('isoWeek')
      } else if (freq === 'month') {
        date = +date.endOf('month')
      } else if (freq === 'year') {
        date = +date.startOf('year')
      } else {
        date = +date.endOf('month')
      }
      const value = dateValueMap[key]
      return [date, value]
    })
    .sort((fst, snd) => fst[0] - snd[0])
  const initialDate = +moment(new Date(results[0][0])).subtract(1, freq)
  results.unshift([initialDate, initialValue])
  const finalResults = results.map(item =>
    isArray ? [item[0], item[1][1], item[1][2]] : { date: item[0], value: item[1] },
  )
  if (!isFilterDate) {
    return finalResults
  }
  const dataLast = data[data.length - 1]
  const startDate = isArray ? data0[0] : data0.date
  const endDate = isArray ? dataLast[0] : dataLast.date
  return finalResults.filter(item => {
    const date = isArray ? item[0] : item.date
    return date >= startDate && date <= endDate
  })
}
