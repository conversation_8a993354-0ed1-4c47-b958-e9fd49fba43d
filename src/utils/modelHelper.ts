export function buildListPaylod(params: any, response: any) {
  const { list, totalNum } = response
  const { page, per_page } = params
  return {
    list,
    pagination: {
      total: totalNum,
      pageSize: per_page || 10,
      current: page || 1,
    },
  }
}

export function addNewItemToList(state: any, payload: any, listDataKey: string) {
  const listData = state[listDataKey]
  const newList = [payload, ...listData.list]
  return {
    ...state,
    [listDataKey]: {
      ...listData,
      list: newList,
    },
  }
}

export function addItemsToList(state: any, payload: any, listDataKey: string) {
  const listData = state[listDataKey]
  const newList = [...payload, ...listData.list]
  return {
    ...state,
    [listDataKey]: {
      ...listData,
      list: newList,
    },
  }
}

export function removeItemFromList(state: any, payload: any, listDataKey: string) {
  const listData = state[listDataKey]
  const newList = listData.list.filter(item => item._id !== payload.id)
  return {
    ...state,
    [listDataKey]: {
      ...listData,
      list: newList,
    },
  }
}

export function removeItemsFromList(state: any, payload: any, listDataKey: string) {
  const listData = state[listDataKey]
  const newList = listData.list.filter(item => !payload.ids.includes(item._id))
  return {
    ...state,
    [listDataKey]: {
      ...listData,
      list: newList,
    },
  }
}

export function updateItemToList(state: any, payload: any, listDataKey: string) {
  const listData = state[listDataKey]
  const newList = listData.list.map(item => {
    if (payload._id !== item._id) {
      return item
    }
    return {
      ...item,
      ...payload,
    }
  })
  return {
    ...state,
    [listDataKey]: {
      ...listData,
      list: newList,
    },
  }
}
