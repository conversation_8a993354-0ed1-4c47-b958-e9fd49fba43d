import countBy from 'lodash/countBy'

export default function countByRange(values, ranges) {
  const getRange = value => {
    let ret
    ranges.some((item, index) => {
      if (
        !isNaN(value) &&
        value > item &&
        !isNaN(ranges[index + 1]) &&
        value <= ranges[index + 1]
      ) {
        ret = ranges[index + 1]
        return true
      }
      return false
    })
    return ret
  }
  return countBy(values, getRange)
}
