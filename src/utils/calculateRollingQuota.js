
const _ = require('lodash')
const moment = require('moment')
const calculator = require('./calculator')

const getCalcStartDate = (navList, date) => {
  const index = _.findLastIndex(navList, item => item.date <= date)
  if (index === -1) {
    return date
  } else {
    return navList[index].date
  }
}

const getLastMonthEnd = (date, window) => {
  return moment(date).subtract(window, 'month').endOf('month').format('YYYYMMDD')
}

function calculateFundRollingQuotas(nets, window) {
  if (nets.length < 2) {
    return []
  }
  const results = []
  const startDate = nets[0].date
  let endDate = nets[nets.length - 1].date
  endDate = getLastMonthEnd(endDate, 1)
  while (startDate < endDate) {
    const lastMonthEnd = getLastMonthEnd(endDate, window)
    const last2MonthEnd = getLastMonthEnd(lastMonthEnd, window)
    const navStartDate = getCalcStartDate(nets.filter(item => item.date >= last2MonthEnd), lastMonthEnd)
    const navList = nets.filter(item => item.date >= navStartDate && item.date <= endDate)
    if (navList.length >= 2 && startDate <= navStartDate) {
      const ret = {
        date: endDate,
        value: calculator.calculateAccReturn(navList),
      }
      results.push(ret)
    }
    endDate = getLastMonthEnd(endDate, 1)
  }
  return results
}

export default calculateFundRollingQuotas
