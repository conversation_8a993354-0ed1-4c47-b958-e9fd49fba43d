import moment from 'moment'
import _ from 'lodash'
import * as calculator from '@/utils/calculator'

const calculateRollingDailyData = (data, valueFn, benchmarkData, { rollingPeriod, rollingGap }) => {
  const result = []
  if (!data || !data.length) {
    return []
  }
  let endDate = moment(data[data.length - 1].date).subtract(1, 'month').endOf('month')
  const startDate = moment(data[0].date).add(rollingPeriod, 'month').endOf('month')
  while (startDate <= endDate) {
    const endTs = +endDate
    const startTs = +endDate.clone().subtract(rollingPeriod, 'month').endOf('month')
    const values = data.filter(item => item.date > startTs && item.date <= endTs)
    const benchmarkValues = benchmarkData && benchmarkData.filter(item => item.date > startTs && item.date <= endTs)
    endDate = endDate.subtract(rollingGap, 'month').endOf('month')
    result.push({
      date: endTs,
      value: valueFn(values, benchmarkValues),
    })
  }
  return result.sort((fst, snd) => fst.date - snd.date)
}

const calculateRollingReturn = (data, rollingPeriod, rollingGap) => {
  return calculateRollingDailyData(data, values =>
    calculator._cucmulativeReturn(values.map(item => item.value))
  , null, { rollingPeriod, rollingGap })
}

const calculateRollingVol = (data, rollingPeriod, rollingGap) => {
  return calculateRollingDailyData(data, values =>
    calculator.vol(values.map(item => item.value), 'day')
  , null, { rollingPeriod, rollingGap })
}

export default {
  calculateRollingReturn,
  calculateRollingVol,
}
