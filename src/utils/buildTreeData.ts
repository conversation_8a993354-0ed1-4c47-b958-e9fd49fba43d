import _ from 'lodash'

function buildParent(filterKey) {
  const value = filterKey.split('___').pop()
  const ret = {
    id: filterKey,
    label: value,
    title: value,
    key: value,
    value,
  }
  return ret
}

function iter(list, keys, resultKey, useSortIndex) {
  const sorter = (fst, snd) => {
    if (!useSortIndex) {
      return snd.value[1] - fst.value[1]
    } else {
      return fst.sortIndex - snd.sortIndex
    }
  }
  let parent
  if (keys.length === 0) {
    const children = list.map((item) => {
      const result = {
        label: item.title,
        value: item.dataIndex,
        title: item.title,
        key: item.dataIndex,
        isDetail: true,
        sortIndex: item.sortIndex,
      }
      return result
    })
    parent = buildParent(resultKey)
    parent.children = children.sort(sorter)
    parent.sortIndex = _.min(parent.children.map(item => item.sortIndex))
    return parent
  }
  const children = _.map(_.groupBy(list, keys[0]), (values, dataKey) =>
    iter(
      values,
      keys.slice(1),
      resultKey ? `${resultKey}___${dataKey}` : dataKey,
      useSortIndex
    )
  )
  parent = buildParent(resultKey)
  parent.children = children.sort(sorter)
  if (children.length === 1 && children[0].value === 'undefined') {
    parent.children = children[0].children
  }
  parent.sortIndex = _.min(parent.children.map(item => item.sortIndex))
  return parent
}

function buildTreeData(data, keys, useSortIndex) {
  const parent = iter(data, keys, '', useSortIndex)
  return parent.children
}

export default buildTreeData
