import XLSX_NS from 'xlsx'
import { createActionLog } from '@/services/user'

export function getSheetData(dataSource, columns) {
  const data = dataSource.reduce((out, item) => {
    const row = columns.map(col => {
      const value = item[col.dataIndex]
      if (col.dataIndex === 'managers') {
        return value && value[0] ? value[0].name : ''
      }
      return value
    })
    out.push(row)
    return out
  }, [columns.map(col => col.titleText || col.title)])
  return data
}

export function getSheet(dataSource, columns) {
  const data = getSheetData(dataSource, columns)
  const sheet = XLSX_NS.utils.aoa_to_sheet(data)
  return sheet
}

export function exportTableAsExcel(
  dataSource: any[],
  columns: any[],
  filename: string,
) {
  const sheet = getSheet(dataSource, columns)
  XLSX_NS.writeFile(
    {
      SheetNames: ['Sheet1'],
      Sheets: { Sheet1: sheet },
    },
    `${filename}.xlsx`,
  )
  createActionLog({ remark: `下载数据:${filename}` })
}

export function exportMultiTableAsExcel(filename, tables) {
  const sheetNames = tables.map(item => item.name)
  const sheets = tables.reduce((out, item) => {
    const { dataSource, columns } = item
    const sheet = getSheet(dataSource, columns)
    out[item.name] = sheet
    return out
  }, {})
  XLSX_NS.writeFile(
    {
      SheetNames: sheetNames,
      Sheets: sheets,
    },
    `${filename}.xlsx`,
  )
  createActionLog({ remark: `下载数据:${filename}` })
}

export function exportNArrayDataAsExcel(filename, data) {
  const sheet = XLSX_NS.utils.aoa_to_sheet(data)
  XLSX_NS.writeFile(
    {
      SheetNames: ['Sheet1'],
      Sheets: {
        Sheet1: sheet,
      },
    },
    `${filename}.xlsx`,
  )
  createActionLog({ remark: `下载数据:${filename}` })
}
