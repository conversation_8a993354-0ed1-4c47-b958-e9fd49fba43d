import moment from 'moment'

export function validateIndexBasicInfo(answer, t) {
  // eslint-disable-line
  const ret = {}
  if (!answer.fundName) {
    ret.error = '请填写名称'
    return ret
  }
  const { startDate, endDate } = answer
  const realEndDate = endDate === 'now' ? new Date() : new Date(+endDate)
  const dayDiff = moment(realEndDate).diff(moment(new Date(+startDate)), 'day')
  if (dayDiff < 2) {
    ret.error = "基金周期至少为 2 天"
    return ret
  }
  return ret
}

export function validateFundBasicInfo(answer, t) {
  const isPAYL = window.__appConfig && window.__appConfig.isPAYL
  // eslint-disable-line
  const ret = validateIndexBasicInfo(answer, t)
  if (ret.error) {
    return ret
  }
  if (!isPAYL) {
    if (!answer.fundNature) {
      ret.error = '请选择基金类型'
      return ret
    }
    if (!answer.fundStrategy) {
      ret.error = '请选择投资策略'
      return ret
    }
  } else {
    if (!answer.fundInvestmentType) {
      ret.error = '请输入或选择产品投资类型'
      return ret
    }
    if (!answer.fundManagementType) {
      ret.error = '请输入或选择管理资金类型'
      return ret
    }
  }
  if (!answer.benchmark) {
    ret.error = '请选择投资基准'
    return ret
  }
  return ret
}

export function validateIndexAnswer(answer, t) {
  // eslint-disable-line
  const ret = validateIndexBasicInfo(answer, t)
  if (ret.error) {
    return ret
  }
  const answerArray = JSON.parse(answer.answer)
  if (answerArray.length < 2) {
    ret.error = "净值至少填 2 天"
    return ret
  }
  return ret
}

export function validateFixedIndex(answer, t) {
  // eslint-disable-line
  const ret = validateIndexBasicInfo(answer, t)
  if (ret.error) {
    return ret
  }
  if (!answer.fixedReturn) {
    ret.error = '请填写年化收益率'
    return ret
  }
  return ret
}

export function validateFloatingIndex(answer, t) {
  // eslint-disable-line
  const ret = validateIndexBasicInfo(answer, t)
  if (ret.error) {
    return ret
  }
  if (!answer.floatingReturn) {
    ret.error = '请填写浮动比例'
    return ret
  }
  if (!answer.benchmark) {
    ret.error = '请选择投资基准'
    return ret
  }
  return ret
}

export default function validateFundAnswer(answer, t) {
  // eslint-disable-line
  const ret = validateFundBasicInfo(answer, t)
  if (ret.error) {
    return ret
  }
  const answerArray = JSON.parse(answer.answer)
  if (answerArray.length < 2) {
    ret.error = "净值至少填 2 天"
    return ret
  }
  return ret
}
