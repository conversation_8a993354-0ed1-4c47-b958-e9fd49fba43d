import _ from 'lodash'

function getIndustryBoardData(industryData) {
  const getIndustryBoard = position => {
    const board = position.INDUSTRY
    const date = position.BIZ_DATE
    const fundId = position.F_CODE
    let name
    if (['采掘', '有色金属'].includes(board)) {
      name = '周期上游'
    } else if (['钢铁', '化工', '公用事业', '交通运输'].includes(board)) {
      name = '周期中游'
    } else if (['建筑材料', '建筑装饰', '汽车', '机械设备'].includes(board)) {
      name = '周期下游'
    } else if (['银行', '非银金融', '房地产'].includes(board)) {
      name = '大金融'
    } else if (
      [
        '轻工制造',
        '商业贸易',
        '休闲服务',
        '家用电器',
        '纺织服装',
        '医药生物',
        '食品饮料',
        '农林牧渔',
      ].includes(board)
    ) {
      name = '消费'
    } else if (['计算机', '传媒', '通信', '电气设备', '电子'].includes(board)) {
      name = 'TMT'
    } else if (board && board.includes('(HS)')) {
      name = '港股'
    } else {
      name = '其他'
    }
    return `${position.INDUSTRY_BOARD}::${date}::${fundId}`
  }
  const rows = _.map(_.groupBy(industryData, getIndustryBoard), (values, name) => {
    const names = name.split('::')
    const BIZ_DATE = names[1]
    const INDUSTRY = names[0]
    const F_CODE = names[2]
    return {
      F_CODE,
      BIZ_DATE,
      INDUSTRY,
      BALANCE: _.sum(values.map(item => item.BALANCE)),
      BALANCE_RATIO: _.sum(values.map(item => item.BALANCE_RATIO)),
      BALANCETONAV: _.sum(values.map(item => item.BALANCETONAV)),
    }
  })
  return rows
}

export default getIndustryBoardData
