export default function normalizeUnit(input) {
  const formatDecimalsRegExp = /(?:\.0*|(\.[^0]+)0+)$/
  const unitMap = {
    b: 1,
    kb: 1 << 10,
    mb: 1 << 20,
    gb: 1 << 30,
    tb: (1 << 30) * 1024
  }
  const prefix = input >= 0 ? '' : '-'
  const value = Math.abs(input)
  let unit = 'B'
  if (value >= unitMap.tb) {
    unit = 'TB'
  } else if (value >= unitMap.gb) {
    unit = 'GB'
  } else if (value >= unitMap.mb) {
    unit = 'MB'
  } else if (value >= unitMap.kb) {
    unit = 'kB'
  }

  return unit
    ? prefix +
        (value / unitMap[unit.toLowerCase()])
          .toFixed(2)
          .replace(formatDecimalsRegExp, '$1') +
        unit
    : prefix + value
}
