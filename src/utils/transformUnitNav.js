export default function transformUnitNav(data) {
  return data.reduce((out, item, index) => {
    let tmp
    if (index === 0) {
      tmp = {
        date: item.date,
        value: 1
      }
    } else {
      const last = out[index - 1]
      const lastData = data[index - 1]
      const ret =
        (item.value - (item.injection || 0) - lastData.value) / lastData.value
      const nav = (1 + ret) * last.value
      tmp = {
        date: item.date,
        value: nav
      }
    }
    out.push(tmp)
    return out
  }, [])
}
