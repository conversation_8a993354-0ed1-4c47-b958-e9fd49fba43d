import { isUrl } from './utils'

describe('isUrl tests', (): void => {
  it('should return false for invalid and corner case inputs', (): void => {
    expect(isUrl([] as any)).toBeFalsy()
    expect(isUrl({} as any)).toBeFalsy()
    expect(isUrl(false as any)).toBeFalsy()
    expect(isUrl(true as any)).toBeFalsy()
    expect(isUrl(NaN as any)).toBeFalsy()
    expect(isUrl(null as any)).toBeFalsy()
    expect(isUrl(undefined as any)).toBeFalsy()
    expect(isUrl('')).toBeFalsy()
  })

  it('should return false for invalid URLs', (): void => {
    expect(isUrl('foo')).toBeFalsy()
    expect(isUrl('bar')).toBeFalsy()
    expect(isUrl('bar/test')).toBeFalsy()
    expect(isUrl('http:/example.com/')).toBeFalsy()
    expect(isUrl('ttp://example.com/')).toBeFalsy()
  })

  it('should return true for valid URLs', (): void => {
    expect(isUrl('http://example.com/')).toBeTruthy()
    expect(isUrl('https://example.com/')).toBeTruthy()
    expect(isUrl('http://example.com/test/123')).toBeTruthy()
    expect(isUrl('https://example.com/test/123')).toBeTruthy()
    expect(isUrl('http://example.com/test/123?foo=bar')).toBeTruthy()
    expect(isUrl('https://example.com/test/123?foo=bar')).toBeTruthy()
    expect(isUrl('http://www.example.com/')).toBeTruthy()
    expect(isUrl('https://www.example.com/')).toBeTruthy()
    expect(isUrl('http://www.example.com/test/123')).toBeTruthy()
    expect(isUrl('https://www.example.com/test/123')).toBeTruthy()
    expect(isUrl('http://www.example.com/test/123?foo=bar')).toBeTruthy()
    expect(isUrl('https://www.example.com/test/123?foo=bar')).toBeTruthy()
  })
})
