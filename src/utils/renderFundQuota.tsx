import React from 'react'
import moment from 'moment'
import _ from 'lodash'
import thousandFormatter from './thousandFormatter'
import FundManagerListPopover from '@/components/FundManagerListPopover'

interface FundQuota {
  name?: string;
  value: string;
  format?: string;
  formatter?: any;
  isNumeric?: boolean;
  dataIndex?: string;
}

const renderDate = (date: any) => {
  if (!date) {
    return ''
  }
  return moment(new Date(date)).format('YYYYMMDD')
}

const renderColor = (value: number) => {
  return value > 0 ? 'colorUp' : 'colorDown'
}

export default function renderFundQuota(quota: FundQuota, fund: any, defaultText?: string) {
  const isEN = localStorage.getItem('umi_locale') === 'en-US'
  let value = fund[quota.dataIndex || quota.value]
  if (quota.formatter) {
    return <span>{quota.formatter(fund, quota.value)}</span>
  }
  if (quota.format === 'maxDrawdownRange') {
    const { maxDrawdownStartDate, maxDrawdownEndDate } = fund
    if (!maxDrawdownEndDate && !maxDrawdownStartDate) {
      return '-'
    }
    return `${renderDate(maxDrawdownStartDate)}~${renderDate(maxDrawdownEndDate)}`
  }
  if (value === undefined || value === null || Number.isNaN(value)) {
    return defaultText || '-'
  }
  if (quota.format === 'date') {
    return moment(new Date(value)).format('YYYY-MM-DD')
  }
  if (quota.format === 'text') {
    return value
  }
  if (quota.format === 'website') {
    return (
      <a href={`http://${value}`} target="_blank">
        {value}
      </a>
    )
  }
  const color = renderColor(value)
  value = Number(value)
  if (quota.format === 'hundredMillion') {
    return isEN
      ? `${(value / 1000000000).toFixed(2)} billion`
      : `${(value / 100000000).toFixed(2)}亿`
  }
  if (quota.format === 'tenThousandToBillion') {
    return thousandFormatter(Number((value / 10000).toFixed(2)))
  }
  if (quota.format === 'tenThousand') {
    const formatedText = thousandFormatter(Number((value / 10000).toFixed(2)))
    return `${formatedText}万`
  }
  if (quota.format === 'commaNumber') {
    return thousandFormatter(Number(value.toFixed(2)))
  }
  if (quota.format === 'integer') {
    return value.toFixed(0)
  }
  if (quota.format === 'percentage') {
    return <span className={color}>{(value * 100).toFixed(2) + '%'}</span>
  }
  if (quota.format === 'valPercentage') {
    return <span>{(value * 100).toFixed(2) + '%'}</span>
  }
  if (quota.format === 'absPercentage') {
    const fixedValue = (Math.abs(value) * 100).toFixed(2) + '%'
    return <span>{value > 0 ? fixedValue : `(${fixedValue})`}</span>
  }
  if (quota.format === 'percentageSuffix') {
    return <span>{(value).toFixed(2) + '%'}</span>
  }
  if (quota.format === 'managerList') {
    const managers = fund[quota.dataIndex || quota.value]
    return <FundManagerListPopover managers={managers} />
  }
  if (quota.format === 'curManagerList') {
    const managers = fund[quota.dataIndex || quota.value] || []
    return <FundManagerListPopover managers={managers.filter(item => item.endToNow)} />
  }
  if (quota.format === 'dateCount') {
    if (value === 0) {
      return '-'
    }
    if (value > 1) {
      return value.toFixed(1) + (isEN ? ' year' : ' 年')
    }
    return moment.duration(value, 'year').humanize()
  }
  if (quota.format === 'bool') {
    if (value) {
      return isEN ? 'True' : '是'
    }
    return isEN ? 'False' : '否'
  }
  if (quota.isNumeric) {
    return thousandFormatter(value || '')
  }
  if (Number.isNaN(value)) {
    return <p>{fund[quota.value]}</p>
  }
  if (quota.format === 'zeroNotNumber' && value === 0) {
    return '-'
  }
  return <span>{_.round(value, 2)}</span>
}
