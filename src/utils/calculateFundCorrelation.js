import math from './math'

export function calculateCorrelation(fst, snd) {
  const fstReturns = []
  const sndReturns = []
  const dateValueMapper = (out, item) => {
    out[item.date] = item.value
    return out
  }
  const fstReturnsMap = fst.returns.reduce(dateValueMapper, {})
  const sndReturnsMap = snd.returns.reduce(dateValueMapper, {})
  fst.returns.forEach(item => {
    if (fstReturnsMap[item.date] !== undefined && sndReturnsMap[item.date] !== undefined) {
      fstReturns.push(fstReturnsMap[item.date])
      sndReturns.push(sndReturnsMap[item.date])
    }
  })
  if (fstReturns.length < 2 || sndReturns.length < 2) {
    return null
  }
  return math.sampleCorrelation(fstReturns, sndReturns)
}

export default function calculateFundCorrelation(list) {
  const cor = {}
  list.forEach((fst, ii) => {
    list.forEach((snd, jj) => {
      if (ii < jj) {
        return
      }
      if (ii === jj) {
        cor[`${ii}-${jj}`] = 1
        return
      }
      if (cor[`${ii}-${jj}`]) {
        return
      }
      const correlation = calculateCorrelation(fst, snd)
      if (!correlation) return
      cor[`${ii}-${jj}`] = cor[`${jj}-${ii}`] = correlation
    })
  })
  return cor
}
