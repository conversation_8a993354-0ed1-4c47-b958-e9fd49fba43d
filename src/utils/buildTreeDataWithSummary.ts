import _ from 'lodash'

const buildTreeData = (rawData, columns, keys, nameKey, detailFilter) => {
  function buildParent(filterKey, children) {
    const arr = filterKey.split('_')
    const temp = children || []
    const ret = columns.reduce((out, col) => {
      if (col.getValue) {
        out[col.dataIndex] = col.getValue(out)
      } else {
        out[col.dataIndex] = _.sumBy(temp, col.dataIndex)
      }
      return out
    }, {
      id: filterKey,
      [nameKey]: filterKey.split('_').pop(),
    })
    if (arr.length !== 1) {
      ret.parent = arr.slice(0, -1).join('_')
    } else {
      ret.isRoot = true
    }
    return ret
  }

  function iter(list, keys, resultKey) {
    let parent
    if (keys.length === 0) {
      const children = list.map((item, index) => {
        const result = {
          id: `${resultKey}_${index}`,
          parent: resultKey,
          [nameKey]: item[nameKey],
          REF_CODE: item.REF_CODE,
          EXCHANGE_TYPE: item.EXCHANGE_TYPE,
          ASSET_TYPE: item.ASSET_TYPE,
          INDUSTRY: item.INDUSTRY,
          isDetail: true,
        }
        return columns.reduce((out, col) => {
          if (col.getValue) {
            out[col.dataIndex] = col.getValue(item)
          } else {
            out[col.dataIndex] = item[col.dataIndex]
          }
          return out
        }, result)
      })
      parent = buildParent(resultKey, children)
      parent.children = children
      if (detailFilter) {
        parent.children = parent.children.filter(detailFilter)
      }
      return parent
    }
    const result = _.map(_.groupBy(list, keys[0]), (values, dataKey) =>
      iter(
        values,
        keys.slice(1),
        resultKey ? `${resultKey}_${dataKey}` : dataKey
      )
    )
    parent = buildParent(resultKey, result)
    parent.children = result
    const hasOneNull = result.length === 1 && result[0][nameKey] === 'null'
    if (hasOneNull) {
      parent.children = result[0].children
    }
    return parent
  }
  return iter(rawData, keys, '').children
}

export default buildTreeData
