import moment from 'moment'
import { isSymbol } from 'lodash'

const sortQuotaFn = (quota: any) => (fst: any, snd: any, order: string) => {
  const sortField = quota.value || quota.dataIndex
  const isNumeric =
    quota.isNumeric ||
    ~['valPercentage', 'percentage', 'commaNumber', 'hundredMillion', 'integer', 'percentageSuffix', 'number', 'zeroNotNumber'].indexOf(
      quota.format,
    )
  let fstVal = fst[sortField]
  let sndVal = snd[sortField]
  const isDate =
    typeof fstVal === 'string' &&
    typeof fstVal === 'string' &&
    moment(new Date(fstVal)).isValid() &&
    moment(new Date(sndVal)).isValid()
  const getValue = () => {
    if (order === 'descend') {
      return -1
    } else {
      return 1
    }
  }
  if (fstVal === null || fstVal === undefined) { return getValue() }
  if (sndVal === null || sndVal === undefined) { return -getValue() }
  if (isNumeric) {
    fstVal = Number(fstVal)
    sndVal = Number(sndVal)
  } else if (isDate) {
    fstVal = +moment(new Date(fstVal))
    sndVal = +moment(new Date(sndVal))
  } else {
    fstVal = (fstVal || '') + ''
    sndVal = (sndVal || '') + ''
  }
  if (fstVal === sndVal) {
    return 0
  }
  return fstVal > sndVal ? 1 : -1
}

function compareAscending(value, other) {
  if (value !== other) {
    var valIsDefined = value !== undefined
    var valIsNull = value === null || value === undefined || value === '-'
    var valIsReflexive = value === value
    var valIsSymbol = isSymbol(value)

    var othIsDefined = other !== undefined
    var othIsNull = other === null || other === undefined || other === '-'
    var othIsReflexive = other === other
    var othIsSymbol = isSymbol(other)

    console.log(value, other, valIsNull, othIsNull)

    if (valIsNull) {
      return 1
    }

    if (othIsNull) {
      return -1
    }

    if ((!othIsNull && !othIsSymbol && !valIsSymbol && value > other) ||
      (valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol) ||
      (valIsNull && othIsDefined && othIsReflexive) ||
      (!valIsDefined && othIsReflexive) ||
      !valIsReflexive) {
      return 1
    }
    if ((!valIsNull && !valIsSymbol && !othIsSymbol && value < other) ||
      (othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol) ||
      (othIsNull && valIsDefined && valIsReflexive) ||
      (!othIsDefined && valIsReflexive) ||
      !othIsReflexive) {
      return -1
    }
  }
  return 0
}

function smartCompare(x, y, order) {
  const getValue = () => {
    if (order === 'desc') {
      return -1
    } else {
      return 1
    }
  }
  if (x == null || x === undefined) {
    return getValue()
  }
  if (y == null || y === undefined) {
    return -getValue()
  }

  if (typeof x === 'number' && typeof y === 'number') {
    return x - y
  }

  if (typeof x === 'string' && typeof y === 'string') {
    // 字符串使用 默认的字典序
    if (x < y) {
      return -1
    } else if (x > y) {
      return 1
    } else {
      return 0
    }
  }
  return 0
}

sortQuotaFn.compareAscending = smartCompare

export default sortQuotaFn
