import * as ss from 'simple-statistics'
/*
  normalize statisitic functions
  std, var
*/
ss.std = (values) => {
  if (values.length < 2) {
    return
  }
  return ss.sampleStandardDeviation(values)
}
ss.var = (values) => {
  if (values.length < 2) {
    return
  }
  return ss.sampleVariance(values)
}
ss.subtract = (fst, snd) => {
  if (typeof fst === 'number' && typeof snd === 'number') {
    return fst - snd
  }
  return fst.map((item, index) => item - snd[index])
}

export default ss
