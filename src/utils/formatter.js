import moment from 'moment';
import thousandFormatter from './thousandFormatter';

const renderColor = (value) => {
  return value < 0 ? 'green' : 'red';
};

export default function renderFundQuota (value, formatter, defaultText) {
  const local = localStorage.getItem('umi_locale');
  //没有数据的时候展示什么，默认-
  if (value === undefined || value === null || value === '--' || Number.isNaN(value)) {
    return defaultText || '--';
  }
  if (formatter === 'wy') {
    if (value === 0) return 0
    else if (!value) return '--'
    else return value + '万元'
  }
  if (formatter === 'tofixed1') {
    if (!value) return '--'
    else return value.toFixed(1) + '%'
  }
  //转时间 - 年月日
  if (formatter === 'date') {
    return moment(new Date(value)).format('YYYY-MM-DD');
  }
  //转时间 - 年月日时分秒
  if (formatter === 'datetime') {
    return moment(new Date(value)).format('YYYY-MM-DD hh:mm:SS');
  }
  if (formatter === 'year') {
    return `${value.toFixed(2) || 0}年`
  }
  if (formatter === 'text') {
    return value || '--';
  }
  //保留四位小数
  if (formatter === 'fourDecimal') {
    return parseFloat(value).toFixed(4)
  }
  if (formatter === 'number') {
    return Number(value).toFixed(2) || '--';
  }
  if (formatter == 'gender') {
    return value === 'm' ? '男' : '女'
  }
  const color = renderColor(value);
  value = Number(value);
  //普通数值/10000转万
  if (formatter === 'tenThousand') {
    return `${(value / 10000).toFixed(2)}万`;
  }
  if (formatter === 'tenThousandComma') {
    return `${thousandFormatter(Number((value / 10000).toFixed(2)))}`;
  }
  //普通数值转亿
  if (formatter === 'hundredMillion') {
    return `${value}亿`;
  }
  if (formatter === 'hundredMillions') {
    if (value === null) return '--'
    else if (value === 0) return value
    else if (value < 1) return `${(Number(value) * 1000).toFixed(0)}万`
    return `${Number(value).toFixed(2)}亿`;
  }
  if (formatter === 'valueMillion') {
    return `${(value / 100000000).toFixed(2)}亿`;
  }
  if (formatter === 'valueMillionOnly') {
    return `${(value / 100000000).toFixed(2)}`;
  }
  // 1000000 ====> 1,000,000
  if (formatter === 'commaNumber') {
    return thousandFormatter(Number(value.toFixed(2)));
  }
  if (formatter === 'integer') {
    return value.toFixed(0);
  }
  //值乘以100后面直接加%，带颜色
  if (formatter === 'percent') {
    return <span style={{ color: color }}>{(value * 100).toFixed(2) + '%'}</span>;
  }
  //值后面直接加%带颜色
  if (formatter === 'text_percent') {
    return (value).toFixed(2) + '%'
  }
  //值*100后加%
  if (formatter === 'valPercentage') {
    return (value * 100).toFixed(2) + '%'
  }
  //绝对值*100加%
  if (formatter === 'absPercentage') {
    const fixedValue = (Math.abs(value) * 100).toFixed(2) + '%';
    return value > 0 ? fixedValue : `(${fixedValue})`
  }
  //数字转年
  if (formatter === 'dateCount') {
    if (value === 0) {
      return '-';
    }
    if (value > 1) {
      return value.toFixed(1) + (local == 'en-US' ? ' year' : ' 年');
    }
    return moment.duration(value, 'year').humanize();
  }
  //bool值转是否
  if (formatter === 'bool') {
    if (value) {
      return local == 'en-US' ? 'True' : '是';
    }
    return local == 'en-US' ? 'False' : '否';
  }
  return <span>{value.toFixed(2)}</span>;
}
