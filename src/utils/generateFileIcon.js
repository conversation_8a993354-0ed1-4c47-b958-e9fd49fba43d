export default function generateFileIcon(filename) {
  const fileType = {
    code: /\.([rR])$/i,
    csv: /\.csv$/i,
    xls: /\.xl(s|s[xmb]|t[xm]|am)$/i,
    word: /\.do(c|c[xm]|t|t[xm]$)/i,
    img: /\.(gif|jpe?g|png)$/i,
    pdf: /\.pdf$/i,
    ppt: /\.pp(t|t[xm]|s[xm])$/i,
    zip: /\.(zip|rar|tar|tar\.gz|)$/i
  }
  const fileImage = {
    csv: 'fa fa-file-excel-o',
    xls: 'fa fa-file-excel-o',
    word: 'fa fa-file-word-o',
    img: 'fa fa-file-image-o',
    pdf: 'fa fa-file-pdf-o',
    ppt: 'fa fa-file-powerpoint-o',
    zip: 'fa fa-file-zip-o',
    code: 'fa fa-file-code-o',
    default: 'fa fa-file-text-o'
  }
  let fileIcon = 'fa fa-file-image-o'
  Object.keys(fileType).some(type => {
    if (fileType[type].test(filename)) {
      fileIcon = fileImage[type]
      return true
    }
    return false
  })
  return fileIcon
}
