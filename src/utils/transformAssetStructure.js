import math from './math'
import { formatMessage } from 'umi-plugin-react/locale'

const ASSET_MAP = {
  '1': 'quota.assettype.cash',
  '2': 'quota.assettype.stock',
  '3': 'quota.assettype.futures',
  '4': 'quota.assettype.bond',
  '5': 'quota.assettype.fund',
  '6': 'quota.assettype.warrant',
  '7': 'quota.assettype.financialProduct',
  '8': 'quota.assettype.buyBackResale',
  '9': 'quota.assettype.derivatives',
  '10': 'quota.assettype.option',
  '0': 'quota.assettype.others',
}

export default function transformAssetStructure(rows, summary) {
  // eslint-disable-line
  const results = rows
    .map(row => {
      return {
        ...row,
        BALANCE_NET: row.BALANCE_LONG + row.BALANCE_SHORT,
        ASSET_TYPE: formatMessage({ id: ASSET_MAP[row.ASSET_TYPE] || row.ASSET_TYPE }),
      }
    })
    .sort((fst, snd) => snd.BALANCE_NET - fst.BALANCE_NET)
  const totalBalanceWithoutFutures = math.sum(
    results
      .filter(item => !~item.ASSET_TYPE.indexOf(formatMessage({ id: 'quota.assettype.futures' })))
      .map(item => item.BALANCE_NET),
  )
  const total = ['BALANCE_LONG', 'BALANCE_SHORT', 'BALANCE_NET'].reduce((out, key) => {
    out[key] = math.sum(results.map(item => item[key]))
    return out
  }, {})
  const assetStructurRatioOption =
    window.__systemSettings && window.__systemSettings.assetStructurRatioOption
  const assetStructurRatioCalculMethod =
    window.__systemSettings && window.__systemSettings.assetStructurRatioCalculMethod
  return results.map(item => {
    if (assetStructurRatioOption === 'excludeFutures') {
      if (~item.ASSET_TYPE.indexOf(formatMessage({ id: 'quota.assettype.futures' }))) {
        return item
      }
      item.RATIO = item.BALANCE_NET / totalBalanceWithoutFutures
    } else {
      item.RATIO = item.BALANCE_NET / Math.abs(total.BALANCE_NET)
    }
    if (summary) {
      if (assetStructurRatioCalculMethod === 'longByAsset') {
        item.RATIO = item.BALANCE_LONG / summary.ASSET
      }
      if (assetStructurRatioCalculMethod === 'longByNetAsset') {
        item.RATIO = item.BALANCE_LONG / summary.NET_ASSET
      }
    }
    return item
  })
}
