const datePrefixList = [[
  'ytd', 'YTD',
], [
  'last1D', '1天以来',
], [
  'last1M', '1个月以来',
], [
  'last3M', '3个月以来',
], [
  'last6M', '6个月以来',
], [
  'last1Y', '1年以来',
], [
  'last2Y', '2年以来',
], [
  'last3Y', '3年以来',
]]

const quotas = [{
  name: '收益率',
  valueSufix: 'AccReturn',
  value: 'accReturn',
  class1: '收益指标',
}, {
  name: '波动率',
  valueSufix: 'Vol',
  value: 'vol',
  class1: '风险指标',
}, {
  name: '最大回撤',
  valueSufix: 'MaxDrawdown',
  value: 'maxDrawdown',
  class1: '风险指标',
}, {
  name: '超额收益',
  valueSufix: 'ExcessReturn',
  value: 'excessReturn',
  class1: '收益指标',
}, {
  name: '规模下降幅度',
  valueSufix: 'ScaleDeclineRatio',
  value: 'scaleDeclineRatio',
  class1: '基金规模',
}, {
  name: '相对基准回撤',
  valueSufix: 'ExMdd',
  value: 'exMdd',
  class1: '风险指标',
}, {
  name: '相对基准波动率倍数',
  valueSufix: 'VolToBenchmarkRatio',
  value: 'volToBenchmarkRatio',
  class1: '风险指标',
}]

export const navQuotas = quotas.reduce((out, quota) => {
  const totalTitle = `成立以来${quota.name}`
  const totalIndex = quota.value
  const totalIndexRank = `${totalIndex}Rank`
  const results = [{
    title: totalTitle,
    dataIndex: totalIndex,
    key: totalIndex,
    format: 'percentage',
    class1: quota.class1,
    class2: quota.name,
  }, {
    title: `${totalTitle}排名`,
    dataIndex: totalIndexRank,
    key: totalIndexRank,
    format: 'percentage',
  }]
  const dateQuotas = datePrefixList.reduce((out, item) => {
    const title = `${item[1]}${quota.name}`
    const index = `${item[0]}${quota.valueSufix}`
    return out.concat([{
      title,
      dataIndex: index,
      key: index,
      format: 'percentage',
      class1: quota.class1,
      class2: quota.name,
    }, {
      title: `${title}排名`,
      dataIndex: `${index}Rank`,
      key: `${index}Rank`,
      format: 'percentage',
    }])
  }, [])
  return out.concat(results).concat(dateQuotas)
}, [])

const industryList = [
  '通信',
  '计算机',
  '传媒',
  '电子',
  '银行',
  '房地产',
  '非银金融',
  '医药生物',
  '商贸零售',
  '轻工制造',
  '农林牧渔',
  '社会服务',
  '食品饮料',
  '纺织服饰',
  '家用电器',
  '汽车',
  '美容护理',
  '建筑装饰',
  '公用事业',
  '交通运输',
  '环保',
  '综合',
  '建筑材料',
  '基础化工',
  '钢铁',
  '有色金属',
  '煤炭',
  '石油石化',
  '电力设备',
  '机械设备',
  '国防军工',
]

const positionMonitoringQuotas = [{
  title: '持股数量',
  dataIndex: 'stockNum',
  key: 'stockNum',
  format: 'integer',
  class1: '风险敞口指标',
  class2: '组合层面',
}, {
  title: '组合中股票停牌数',
  dataIndex: 'suspendedStockNum',
  key: 'suspendedStockNum',
  format: 'integer',
  class1: '风险敞口指标',
  class2: '组合层面',
}, {
  title: '投资股票占资产总值比例',
  dataIndex: 'stockRatioToAsset',
  key: 'stockRatioToAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '组合层面',
}, {
  title: '投资债券占资产总值比例',
  dataIndex: 'bondRatioToAsset',
  key: 'bondRatioToAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '组合层面',
}, {
  title: '投资国债占资产总值比例',
  dataIndex: 'nationalNondRatioToAsset',
  key: 'nationalNondRatioToAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '组合层面',
},  {
  title: '投资股票占资产净值比例',
  dataIndex: 'stockRatioToNetAsset',
  key: 'stockRatioToNetAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '组合层面',
}, {
  title: '投资债券占资产净值比例',
  dataIndex: 'bondRatioToNetAsset',
  key: 'bondRatioToNetAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '组合层面',
}, {
  title: '投资国债占资产净值比例',
  dataIndex: 'nationalNondRatioToNetAsset',
  key: 'nationalNondRatioToNetAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '组合层面',
}, {
  title: '总资产超过基金净资产比例',
  dataIndex: 'assetToNetAsset',
  key: 'assetToNetAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '组合层面',
}, {
  title: '第一大股票占资产净值比例',
  dataIndex: 'maxStockRatioToNetAsset',
  key: 'maxStockRatioToNetAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '组合层面',
}, {
  title: '前十大股票占资产净值比例',
  dataIndex: 'top10StockRatioToNetAsset',
  key: 'top10StockRatioToNetAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '组合层面',
},
...industryList.map(ind => {
  return {
    title: `${ind}占净资产比`,
    dataIndex: `singleIndustryRatioToNetAsset_${ind}`,
    key: `singleIndustryRatioToNetAsset_${ind}`,
    format: 'percentage',
    class1: '风险敞口指标',
    class2: '行业占净资产比例',
  }
}),
...industryList.map(ind => {
  return {
    title: `${ind}超越可比基准`,
    dataIndex: `singleIndustryRatioToBenchmark_${ind}`,
    key: `singleIndustryRatioToBenchmark_${ind}`,
    format: 'percentage',
    class1: '风险敞口指标',
    class2: '行业超越可比基准',
  }
}),
{
  title: '单一股票占资产净值比例',
  dataIndex: 'singleStockRatioToNetAsset',
  key: 'singleStockRatioToNetAsset',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '个股层面',
}, {
  title: '单一股票占上市公司可流通股票比例',
  dataIndex: 'singleStockRatioToTradableShares',
  key: 'singleStockRatioToTradableShares',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '个股层面',
}, {
  title: '单一股票当日跌幅',
  dataIndex: 'singleStockLast1DDeclineRatio',
  key: 'singleStock1DDeclineRatio',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '个股层面',
}, {
  title: '单一股票近2个交易日跌幅',
  dataIndex: 'singleStockLast2DDeclineRatio',
  key: 'singleStock2DDeclineRatio',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '个股层面',
}, {
  title: '单一股票近3个交易日跌幅',
  dataIndex: 'singleStockLast3DDeclineRatio',
  key: 'singleStockLast3DDeclineRatio',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '个股层面',
}, {
  title: '单一股票近一个月跌幅',
  dataIndex: 'singleStockLast1MDeclineRatio',
  key: 'singleStockLast1MDeclineRatio',
  format: 'percentage',
  class1: '风险敞口指标',
  class2: '个股层面',
}]

export const navQuotasIgnoreRank = navQuotas.filter(item => !/.+Rank$/.test(item.dataIndex))

export const monitoringQuotas = [{
  title: '管理费计提占比',
  dataIndex: 'mgtFeeRatio',
  key: 'mgtFeeRatio',
  format: 'percentage',
  class1: '管理费',
}].concat(navQuotasIgnoreRank).concat(positionMonitoringQuotas)
