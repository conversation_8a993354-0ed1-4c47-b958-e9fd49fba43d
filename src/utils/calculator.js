import moment from 'moment'
import math from './math'
import groupBy from 'lodash/groupBy'
import guessFrequency from './guessFrequency'
const ppfMap = {
  90: -1.2815515655446004,
  95: -1.6448536269514729,
  99: -2.3263478740408408,
}

const freqMap = {
  day: 245,
  week: 50,
  month: 12,
}
const yearStamp = 1000 * 60 * 60 * 24 * 365

export function _cucmulativeReturn(returns) {
  return returns.map(num => 1 + num).reduce((out, num) => out * num, 1) - 1
}

export function _yearReturn(startDate, endDate, returns) {
  const yearDiff = moment(endDate).diff(startDate, 'year', true)
  if (yearDiff <= 1) {
    return _cucmulativeReturn(returns)
  }
  return Math.pow(1 + _cucmulativeReturn(returns), 1 / yearDiff) - 1
}

export function cucmulativeReturn(nets) {
  if (nets.length < 2) {
    return 0
  }
  return nets[nets.length - 1] / nets[0] - 1
}

export function estimateYearReturn(dayDiff, nets) {
  const ret = cucmulativeReturn(nets)
  const avgRet = Math.pow(1 + ret, 1 / dayDiff) - 1
  return Math.pow(1 + avgRet, 365) - 1
}

export function _yearReturnByStamp(time, nets, fallback) {
  const yearDiff = time / yearStamp
  if (yearDiff < 1) {
    return !fallback || fallback === 'estimateReturn'
      ? estimateYearReturn(yearDiff * 365, nets)
      : cucmulativeReturn(nets)
  }
  return Math.pow(1 + cucmulativeReturn(nets), 1 / yearDiff) - 1
}

export function _estimateYearReturnByAccReturn(dayDiff, accReturn) {
  const avgRet = Math.pow(1 + accReturn, 1 / dayDiff) - 1
  return Math.pow(1 + avgRet, 365) - 1
}

export function _yearReturnByAccReturn(time, accReturn, fallback) {
  const yearDiff = time / yearStamp
  if (yearDiff < 1) {
    return !fallback || fallback === 'estimateReturn'
      ? _estimateYearReturnByAccReturn(yearDiff * 365, accReturn)
      : accReturn
  }
  return Math.pow(1 + accReturn, 1 / yearDiff) - 1
}

export function yearReturn(startDate, endDate, nets, fallback) {
  const yearDiff = moment(endDate).diff(startDate, 'year', true)
  if (yearDiff < 1) {
    return !fallback || fallback === 'estimateReturn'
      ? estimateYearReturn(moment(endDate).diff(startDate, 'day'), nets)
      : cucmulativeReturn(nets)
  }
  return Math.pow(1 + cucmulativeReturn(nets), 1 / yearDiff) - 1
}

// volatility
export function vol(returns, freq = 'month') {
  if (returns.length < 2) {
    return null
  }
  return math.std(returns) * Math.sqrt(freqMap[freq])
}

// value at risk
export function VAR(returns, alpha) {
  return math.mean(returns) - ppfMap[alpha] * math.std(returns)
}

export function VaRHistory(returns, alpha) {
  // use history model
  const index = Math.floor((1 - alpha) * returns.length)
  const VaR = [...returns].sort()[index]
  return VaR < 0 ? VaR : 0
}

// conditional value at risk
export function cvar(returns, alpha) {
  return (
    math.mean(returns) -
    (Math.pow(Math.E, -Math.pow(ppfMap[alpha], 2) / 2) / (ppfMap[alpha] * Math.sqrt(2 * math.PI))) *
      math.std(returns)
  )
}

export function deviationTracking(returns, compared, freq = 'month') {
  if (returns.length <= 2) {
    return undefined
  }
  return math.std(math.subtract(returns, compared)) * Math.sqrt(freqMap[freq])
}

export function downsideDev(returns, freq = 'month') {
  const downValues = returns.filter(item => item < 0)
  if (downValues.length <= 2) {
    return undefined
  }
  return math.std(downValues) * Math.sqrt(freqMap[freq])
}

export function sharpeRatio(returns, er, freq = 'month') {
  return er / vol(returns, freq)
}

export function sortinoRatio(returns, er, freq = 'month') {
  return er / downsideDev(returns, freq)
}

export function informationRatio(er, returns, compared, freq = 'month') {
  return er / deviationTracking(returns, compared, freq)
}

export function maxDrawdown(values) {
  let peak = values[0]
  let maxDD = 0

  for (let ii = 0; ii < values.length; ii++) {
    const value = values[ii]

    if (value > peak) {
      peak = value
    } else {
      const drawdown = (peak - value) / peak
      if (drawdown > maxDD) {
        maxDD = drawdown
      }
    }
  }

  return -maxDD
}

export function calculateMaxDD(nav) {
  const getInterval = (start, end) => moment(end).diff(moment(start), 'day')
  const values = nav.map(item => item.value)
  const dates = nav.map(item => new Date(item.date))
  let peak = values[0]
  let bottom = values[0]
  let lastPeakDate = dates[0]
  let lastBottomDate = dates[0]
  let startDate = dates[0]
  let endDate = dates[0]

  let maxDD = 0
  let maxDDInterval = getInterval(lastPeakDate, lastPeakDate)
  let maxDDDuration = getInterval(lastPeakDate, lastPeakDate)
  let recoveryTime = getInterval(lastBottomDate, lastPeakDate)
  let currentDrawdownDuration = null
  let currentRecoveryTime = null

  for (let ii = 0; ii < values.length; ii++) {
    const value = values[ii]
    currentDrawdownDuration = getInterval(lastPeakDate, dates[ii])
    currentRecoveryTime = getInterval(lastBottomDate, dates[ii])

    if (value > peak) {
      peak = value
      lastPeakDate = dates[ii]

      if (currentDrawdownDuration > maxDDDuration) {
        maxDDDuration = currentDrawdownDuration
      }

      if (currentRecoveryTime > recoveryTime) {
        recoveryTime = currentRecoveryTime
      }
      // Reset the recovery time calculation, as the recovery is
      // now complete
      lastBottomDate = dates[ii]
      bottom = value
    } else {
      const drawdown = (peak - value) / peak
      if (drawdown > maxDD) {
        maxDD = drawdown
        maxDDInterval = getInterval(lastPeakDate, dates[ii])
        startDate = lastPeakDate
        endDate = dates[ii]
      }
    }
    if (value < bottom) {
      bottom = value
      lastBottomDate = dates[ii]
    }
  }

  // check if current drawdown duration is longer than the max
  // drawdown duration currently calculated --> use it because it is
  // the longest duration even if we do not know how much longer it
  // will get

  if (currentDrawdownDuration !== null && currentDrawdownDuration > maxDDDuration) {
    maxDDDuration = currentDrawdownDuration
  }

  if (currentRecoveryTime !== null && currentRecoveryTime > recoveryTime) {
    recoveryTime = currentRecoveryTime
  }

  return {
    maxDrawdown: -maxDD,
    maxDrawdownDuration: maxDDDuration,
    maxDrawdownInterval: maxDDInterval,
    maxDrawdownRecoveryTime: recoveryTime,
    maxDrawdownStartDate: +startDate,
    maxDrawdownEndDate: +endDate,
  }
}

export function omega(returns) {
  const up = math.sum(returns.filter(item => item >= 0))
  const down = -math.sum(returns.filter(item => item < 0))
  if (down === 0) {
    return undefined
  }
  return up / down
}

function calculateCapture(returns, benchmarkReturns, filterFn) {
  const fundReturns = []
  const targetReturns = []
  benchmarkReturns.forEach((value, index) => {
    if (filterFn(value)) {
      targetReturns.push(value)
      fundReturns.push(returns[index])
    }
  })
  if (!targetReturns.length) {
    return undefined
  }
  return _cucmulativeReturn(fundReturns) / _cucmulativeReturn(targetReturns)
}

export function downsideCapture(returns, benchmarkReturns, hw=0) {
  return calculateCapture(returns, benchmarkReturns, value => value < hw)
}

export function upsideCapture(returns, benchmarkReturns, hw=0) {
  return calculateCapture(returns, benchmarkReturns, value => value > hw)
}

const getAvgReturn = ret => Math.pow(1 + ret, 1 / 365) - 1

export function calculateAccReturn(nets, floatingReturn) {
  if (nets.length < 2) {
    return 0
  }
  const start = nets[0]
  const end = nets[nets.length - 1]
  let ret = end.value / start.value - 1
  if (floatingReturn) {
    const avgReturn = getAvgReturn(floatingReturn / 100)
    const dayTs = 24 * 60 * 60 * 1000
    const dayGap = (end.date - start.date) / dayTs
    ret = ret + Math.pow(1 + avgReturn, dayGap) - 1
  }
  return ret
}

export function calculateReturns(nets, floatingReturn) {
  const returns = []
  for (let idx = 1; idx < nets.length; idx++) {
    const item = nets[idx]
    let ret = item.value / nets[idx - 1].value - 1
    if (floatingReturn) {
      const avgReturn = getAvgReturn(floatingReturn / 100)
      const dayTs = 24 * 60 * 60 * 1000
      const dayGap = (item.date - nets[idx - 1].date) / dayTs
      ret = ret + Math.pow(1 + avgReturn, dayGap) - 1
    }
    returns.push({
      date: item.date,
      value: ret,
    })
  }
  return returns
}

export function calculateYearReturns(nets, floatingReturn) {
  if (!nets.length) {
    return []
  }
  const returns = []
  for (let idx = 1; idx < nets.length; idx++) {
    const item = nets[idx]
    let ret = (item.value / nets[idx - 1].value) - 1
    if (floatingReturn) {
      const avgReturn = getAvgReturn(floatingReturn / 100)
      const dayTs = 24 * 60 * 60 * 1000
      const dayGap = (item.date - nets[idx - 1].date) / dayTs
      ret = ret + Math.pow(1 + avgReturn, dayGap) - 1
    }
    returns.push({
      date: item.date,
      value: ret,
    })
  }
  return returns
}

exports.calculateYearReturn = function calculateYearReturn(nets, fallback, floatingReturn) {
  if (nets.length < 2) {
    return 0
  }
  const startDate = new Date(nets[0].date)
  const endDate = new Date(nets[nets.length - 1].date)
  const yearDiff = moment(endDate).diff(startDate, 'year', true)
  if (yearDiff < 1) {
    return (!fallback || fallback === 'estimateReturn')
      ? exports.estimateYearReturn(yearDiff * 365, nets)
      : exports.calculateAccReturn(nets, floatingReturn)
  }
  return Math.pow(1 + exports.calculateAccReturn(nets, floatingReturn), 1 / yearDiff) - 1
}

export function calculateCapitalReturn(scale) {
  if (scale.length < 2) {
    return undefined
  }
  scale[0].injection = scale[0].injection || scale[0].value
  const startDate = scale[0].date
  const endDate = scale[scale.length - 1].date
  const dateDiff = endDate - startDate
  const injections = scale.filter(item => item.injection)
  const injectionSum = math.sum(
    injections.map(item => (item.injection * (endDate - item.date)) / dateDiff),
  )
  const capitalDiff =
    scale[scale.length - 1].value - math.sum(injections.map(item => item.injection))
  return capitalDiff / injectionSum
}

export function calculateAnnualCapitalReturn(scale) {
  if (scale.length < 2) {
    return undefined
  }
  scale[0].injection = scale[0].injection || scale[0].value
  const endDate = scale[scale.length - 1].date
  const yearTimestamp = 365 * 24 * 60 * 60 * 1000
  const injections = scale.filter(item => item.injection)
  const injectionSum = math.sum(
    injections.map(item => (item.injection * (endDate - item.date)) / yearTimestamp),
  )
  const capitalDiff =
    scale[scale.length - 1].value - math.sum(injections.map(item => item.injection))
  return capitalDiff / injectionSum
}

export function calculateNav(returns) {
  if (returns.length === 0) {
    return []
  }
  const frequency = guessFrequency(returns)
  const startDate = returns[0].date
  const navStartDate = +moment(new Date(startDate)).subtract(1, frequency)
  let initialNav = 1
  const nav = [
    {
      date: navStartDate,
      value: initialNav,
    },
  ]
  returns.forEach(item => {
    initialNav = initialNav * (1 + item.value)
    nav.push({
      date: item.date,
      value: initialNav,
    })
  })
  return nav
}

function stateOfPeriodTime(before, distribution, pointer) {
  let period = pointer
  // find all distribution between curr and before
  while (before.date < distribution[period][1]) {
    period--
  }
  if (period === pointer) {
    // same state
    return distribution[period][0]
  }
  const alldist = groupBy(distribution.slice(period, pointer + 1), dist => dist[0])
  const distsTimes = Object.values(alldist).map(dist => ({
    total: dist.reduce((pre, curr) => pre + curr[2] - curr[1], 0),
    state: dist[0][0],
  }))
  let result
  distsTimes.forEach(dist => {
    if (!result) {
      result = dist
    }
    if (result.total < dist.total) {
      result = dist
    }
  })
  return result.state
}

export function distributeNets(nets, distribution) {
  const dist = {}
  let pointer = 0
  // deprecate nets before all distribution
  let start = 0
  for (; start < nets.length; start++) {
    if (nets[start].date >= distribution[0][1]) {
      break
    }
  }
  // init
  for (let idx = start; idx < nets.length; idx++) {
    while (nets[idx].date > distribution[pointer][2]) {
      pointer++
    }
    const currState = distribution[pointer][0]
    if (dist[currState]) {
      dist[currState].push(nets[idx])
    } else {
      dist[currState] = [nets[idx]]
    }
  }

  return dist
}

/**
 * calculate returns with state
 * @param  {Array} nets
 * @param  {Array} distribution
 * @return {Array}
 */
export function calculateReturnsWithState(nets, distribution, annualReturnFallback) {
  const dist = {}
  let pointer = 0
  let lastState
  const freq = guessFrequency(nets)
  // deprecate nets before all distribution
  let start = 0
  for (; start < nets.length; start++) {
    if (nets[start].date >= distribution[0][1]) {
      break
    }
  }
  // init
  for (let idx = start + 1; idx < nets.length; idx++) {
    while (nets[idx].date > distribution[pointer][2]) {
      pointer++
    }
    const currState = stateOfPeriodTime(nets[idx - 1], distribution, pointer)
    // calculate return only if in same states
    if (dist[currState]) {
      dist[currState].push({
        time: nets[idx].date - nets[idx - 1].date,
        returns: nets[idx].value / nets[idx - 1].value - 1,
        net: nets[idx].value,
      })
    } else {
      dist[currState] = [
        {
          time: nets[idx].date - nets[idx - 1].date,
          returns: nets[idx].value / nets[idx - 1].value - 1,
          net: nets[idx].value,
        },
      ]
    }
    lastState = currState
  }
  // calculate mean returns of each state
  Object.keys(dist).forEach(state => {
    const years = dist[state].reduce((pre, curr) => pre + curr.time, 0)
    const ret =
      dist[state].map(item => item.returns).reduce((out, item) => out * (1 + item), 1) - 1
    dist[state] = {
      returns: _yearReturnByAccReturn(years, ret, annualReturnFallback),
      volatility: vol(dist[state].map(item => item.returns), freq),
    }
  })
  return dist
}
