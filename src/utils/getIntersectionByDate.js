const getIntersectionByDate = (fst, snd) => {
  const results = []
  const dateValueMapper = (out, item) => {
    out[item.date] = item.value
    return out
  }
  const fstReturnsMap = fst.reduce(dateValueMapper, {})
  const sndReturnsMap = snd.reduce(dateValueMapper, {})
  fst.forEach(item => {
    const date = item.date
    if (fstReturnsMap[date] !== undefined && sndReturnsMap[date] !== undefined) {
      results.push([date, fstReturnsMap[date], sndReturnsMap[date]])
    }
  })
  return results
}

export default getIntersectionByDate
