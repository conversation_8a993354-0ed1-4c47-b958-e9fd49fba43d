import _ from 'lodash'
import moment from 'moment'

const getSeriesChartData = (rows: any[], nameKey: string, valueKey: string, nameMap = {}, pctValueKey?: string) => {
  const getLastValue = data => data[data.length - 1][1]
  const data = _.reduce(_.groupBy(rows, 'BIZ_DATE'), (out, values) => {
    const sumValue = _.sumBy(values, valueKey)
    const ret = values.map(val => {
      if (pctValueKey) {
        val.chartPctValue = val[pctValueKey]
      } else {
        val.chartPctValue = val[valueKey] / sumValue
      }
      return val
    })
    return out.concat(ret)
  }, [])
  return _.map(_.groupBy(data, nameKey), (values, name) => {
    const data = values
      .sort((fst, snd) => fst.BIZ_DATE - snd.BIZ_DATE)
      .map(item => [+moment(item.BIZ_DATE).startOf('date'), item[valueKey], item.chartPctValue])
      .map(item => ({ x: item[0], y: item[2] * 100, yValue: item[1] / 10000 }))
    return {
      name: nameMap[name] || name,
      data,
    }
  }).sort((fst, snd) => {
    return getLastValue(snd.data) - getLastValue(fst.data)
  })
}

export default getSeriesChartData
