
const DAY_IN_SECOND = 24 * 60 * 60 * 1000

const fillEmptyValue = (data, endDate, validDates, fillValue) => {
  if (!data || !data.length) {
    return []
  }
  const dateValueMapper = (out, item) => {
    out[item.date] = item.value
    return out
  }
  const startDate = data[0].date
  const dvMap = data.reduce(dateValueMapper, {})
  const fullData = []
  let cur = startDate
  let lastData
  while (cur <= endDate) {
    const curData = dvMap[cur]
    if (!validDates || ~validDates.indexOf(cur)) {
      if (curData) {
        lastData = curData
        fullData.push({
          date: cur,
          value: curData,
        })
      } else {
        fullData.push({
          date: cur,
          value: fillValue === undefined ? lastData : fillValue,
        })
      }
    } else if (curData) {
      lastData = curData
    }
    cur += DAY_IN_SECOND
  }
  return fullData
}

export default fillEmptyValue
