import _ from 'lodash'
import moment from 'moment'
import math from './math'
import * as calculator from './calculator'
import guessFrequency from './guessFrequency'
import changeFrequency from './changeFrequency'

const valueMapper = item => item.value
const dateFilterFn = (startDate, endDate) => item => item.date >= startDate && item.date <= endDate
const dateGtFilter = date => item => item.date >= date

const calculateMonthlyReturn = (navList) => {
  const monthlyNavList = changeFrequency(navList, 'month')
  return calculator.calculateReturns(monthlyNavList)
}
const calculateAvgMonthlyReturn = (monthlyRetList, backMonths, endMonth) => {
  let values = monthlyRetList.map(item => item.value)
  if (backMonths && endMonth) {
    const endMonthDate = endMonth.date
    const startMonth = +moment(new Date(endMonthDate)).subtract(backMonths, 'month')
    values = monthlyRetList
    .filter(item => item.date >= startMonth && item.date <= endMonthDate)
    .map(item => item.value)
  }
  if (!values.length) {
    return undefined
  }
  return math.mean(values)
}

const calculateAccReturnByYear = (fund, navList) => {
  const floatingReturn = fund.floatingReturn
  const yearlyNavList = changeFrequency(navList, 'year')
  const yearlyRetList = calculator.calculateYearReturns(yearlyNavList, floatingReturn)
  const { isFixedReturn, fixedReturn } = fund
  yearlyRetList.forEach(item => {
    const year = moment(new Date(item.date)).year()
    if (isFixedReturn && fixedReturn) {
      fund[`accReturn${year}`] = fixedReturn / 100
    } else {
      fund[`accReturn${year}`] = item.value
    }
  })
}
const getFieldByTime = (time, unit) => field => {
  if (!time) {
    return field
  }
  if (time === 'ytd' || time === 'mtd') {
    return `${time}${_.upperFirst(field)}`
  }
  return `last${time}${unit}${_.upperFirst(field)}`
}

const getCalcStartDate = (navList, date) => {
  const index = _.findLastIndex(navList, item => item.date <= date)
  if (index === -1) {
    return date
  } else {
    return navList[index].date
  }
}

const calculateQuotasByTime = (fund, navListOriginal, returnListOriginal, fofSettings, frequency, time, unit, latestDate, scaleListOriginal) => {
  const { isFixedReturn, fixedReturn } = fund
  const floatingReturn = fund.floatingReturn
  let navList = navListOriginal
  let returnList = returnListOriginal
  let scaleList = scaleListOriginal
  if (time) {
    let realStartDate
    if (time === 'mtd') {
      realStartDate =  +moment(latestDate).startOf('month').subtract(1, 'day')
    } else if (time === 'ytd') {
      realStartDate =  +moment(latestDate).startOf('year').subtract(1, 'day')
    } else {
      realStartDate = +moment(latestDate).subtract(time, unit === 'M' ? 'month' : 'year')
    }
    const navStartDate = getCalcStartDate(navListOriginal, realStartDate)
    navList = navListOriginal.filter(dateGtFilter(navStartDate))
    returnList = returnList.filter(dateGtFilter(navStartDate))
    scaleList = scaleList.filter(dateGtFilter(navStartDate))
  }
  if (navList.length < 2) {
    return fund
  }
  let annualReturnFallback
  let sharpeRatioConstant = 0
  if (fofSettings) {
    annualReturnFallback = fofSettings.annualReturnFallback
    sharpeRatioConstant = fofSettings.sharpeRatioConstant || 0
  }
  const navValues = navList.map(valueMapper)
  const returnValues = returnList.map(valueMapper)
  const gfbt = getFieldByTime(time, unit)
  if (unit === 'Y' && time === 1 && isFixedReturn && fixedReturn) {
    fund[gfbt('accReturn')] = fixedReturn / 100
  } else {
    fund[gfbt('accReturn')] = calculator.calculateAccReturn(navList, floatingReturn)
  }
  const scaleDeclineRatio = calculator.calculateAccReturn(scaleList)
  fund[gfbt('scaleDeclineRatio')] = scaleDeclineRatio < 0 ? Math.abs(scaleDeclineRatio) : 0
  if (unit === 'Y' && time >= 1 && isFixedReturn && fixedReturn) {
    fund[gfbt('yearReturn')] = fixedReturn / 100
  } else {
    fund[gfbt('yearReturn')] = calculator.calculateYearReturn(navList, annualReturnFallback, floatingReturn)
  }
  fund[gfbt('maxDrawdown')] = calculator.maxDrawdown(navValues)
  fund[gfbt('downsideDev')] = calculator.downsideDev(returnValues, frequency)
  fund[gfbt('sortinoRatio')] = calculator.sortinoRatio(returnValues, frequency) || null
  fund[gfbt('vol')] = calculator.vol(returnValues, frequency)
  fund[gfbt('sharpeRatio')] = calculator.sharpeRatio(returnValues, fund[gfbt('yearReturn')] - sharpeRatioConstant, frequency)
  fund[gfbt('omega')] = calculator.omega(returnValues)
}

const calculateQuotas = (fund, latestDate, fofSettings) => {
  const navList = fund.nets || []
  if (navList.length < 2) {
    return fund
  }
  const scaleList = fund.scale || []
  const frequency = guessFrequency(navList)
  const retList = calculator.calculateReturns(navList)
  const scaleValues = scaleList.map(valueMapper)
  const navStartData = navList[0]
  const navEndData = navList[navList.length - 1]

  fund.nets = navList
  fund.returns = retList
  fund.scale = scaleList
  fund.navStartDate = navList[0].date
  fund.navEndDate = navList[navList.length - 1].date
  fund.startDate = new Date(fund.navStartDate)
  fund.endDate = new Date(fund.navEndDate)
  if (~['mutual', 'pension'].indexOf(fund._syncType)) {
    fund.unitNavEndOfTerm = navEndData.unitNav
    fund.unitNavStartOfTerm = navStartData.unitNav
    fund.accNavEndOfTerm = navEndData.accNav
    fund.accNavStartOfTerm = navStartData.accNav
    fund.resNavEndOfTerm = navEndData.value
    fund.resNavStartOfTerm = navStartData.value
  } else {
    fund.unitNavEndOfTerm = navEndData.value
    fund.unitNavStartOfTerm = navStartData.value
  }
  const fundDuration = moment(fund.navEndDate).diff(moment(fund.navStartDate), 'year', true)
  fund.fundDuration = fundDuration
  if (scaleList.length) {
    fund.navEndOfTerm = scaleValues[scaleValues.length - 1]
    fund.navStartOfTerm = scaleValues[0]
    fund.injectionEndOfTerm = math.sum(scaleList.map(item => item.injection || 0))
    fund.capitalReturn = calculator.calculateCapitalReturn(scaleList)
    fund.annualCapitalReturn = calculator.calculateAnnualCapitalReturn(scaleList)
    const injectionSum = math.sum(scaleList.map((item, index) => (index === 0 ? 0 : item.injection) || 0))
    fund.accumulatedIncome = fund.navEndOfTerm - fund.navStartOfTerm - injectionSum
  }
  fund.last1DAccReturn = retList[retList.length - 1].value
  const mddObject = calculator.calculateMaxDD(navList)
  fund.maxDrawdown = mddObject.maxDrawdown
  fund.maxDrawdownDuration = mddObject.maxDrawdownDuration
  fund.maxDrawdownInterval = mddObject.maxDrawdownInterval
  fund.maxDrawdownRecoveryTime = mddObject.maxDrawdownRecoveryTime
  fund.maxDrawdownStartDate = mddObject.maxDrawdownStartDate
  fund.maxDrawdownEndDate = mddObject.maxDrawdownEndDate

  const monthlyRetList = calculateMonthlyReturn(navList)
  fund.avgMonthlyReturn = calculateAvgMonthlyReturn(monthlyRetList)
  fund.perPositive = monthlyRetList.filter(item => item.value > 0).length / monthlyRetList.length
  const endMonth = monthlyRetList[monthlyRetList.length - 1]
  if (fundDuration >= 1) fund.avg1YMonthlyReturn = calculateAvgMonthlyReturn(monthlyRetList, 11, endMonth)
  if (fundDuration >= 2) fund.avg2YMonthlyReturn = calculateAvgMonthlyReturn(monthlyRetList, 23, endMonth)
  if (fundDuration >= 3) fund.avg3YMonthlyReturn = calculateAvgMonthlyReturn(monthlyRetList, 35, endMonth)
  if (fundDuration >= 5) fund.avg5YMonthlyReturn = calculateAvgMonthlyReturn(monthlyRetList, 59, endMonth)

  const monthlyDownRetList = monthlyRetList.filter(item => item.value < 0)
  fund.avgDownMonthlyReturn = calculateAvgMonthlyReturn(monthlyDownRetList)
  if (fundDuration >= 1) fund.avg1YDownMonthlyReturn = calculateAvgMonthlyReturn(monthlyDownRetList, 11, endMonth)
  if (fundDuration >= 2) fund.avg2YDownMonthlyReturn = calculateAvgMonthlyReturn(monthlyDownRetList, 23, endMonth)
  if (fundDuration >= 3) fund.avg3YDownMonthlyReturn = calculateAvgMonthlyReturn(monthlyDownRetList, 35, endMonth)
  if (fundDuration >= 5) fund.avg5YDownMonthlyReturn = calculateAvgMonthlyReturn(monthlyDownRetList, 59, endMonth)

  calculateAccReturnByYear(fund, navList)

  calculateQuotasByTime(fund, navList, retList, fofSettings, frequency, null, null, null, scaleList)

  if (navList[0].date <= +moment().startOf('year').add(3, 'day')) {
    calculateQuotasByTime(fund, navList, retList, fofSettings, frequency, 'ytd', null, latestDate, scaleList)
  }
  if (fundDuration >= 0.083) {
    calculateQuotasByTime(fund, navList, retList, fofSettings, frequency, 1, 'M', latestDate, scaleList)
  }
  if (fundDuration >= 0.25) {
    calculateQuotasByTime(fund, navList, retList, fofSettings, frequency, 3, 'M', latestDate, scaleList)
  }
  if (fundDuration >= 0.5) {
    calculateQuotasByTime(fund, navList, retList, fofSettings, frequency, 6, 'M', latestDate, scaleList)
  }
  if (fundDuration >= 1) {
    calculateQuotasByTime(fund, navList, retList, fofSettings, frequency, 1, 'Y', latestDate, scaleList)
  }
  if (fundDuration >= 2) {
    calculateQuotasByTime(fund, navList, retList, fofSettings, frequency, 2, 'Y', latestDate, scaleList)
  }
  if (fundDuration >= 3) {
    calculateQuotasByTime(fund, navList, retList, fofSettings, frequency, 3, 'Y', latestDate, scaleList)
  }
  if (fundDuration >= 5) {
    calculateQuotasByTime(fund, navList, retList, fofSettings, frequency, 5, 'Y', latestDate, scaleList)
  }

  return fund
}

export const calculateFundQuotasByTimeRange = (fund, startDate, endDate, fofSettings) => {
  let result = fund
  const realStartDate = getCalcStartDate(fund.nets || [], startDate)
  const dateFilter = dateFilterFn(realStartDate, endDate)
  result.nets = (fund.nets || []).filter(dateFilter)
  result.scale = (fund.scale || []).filter(dateFilter)
  result = calculateQuotas(result, endDate, fofSettings)
  return result
}

export default function calculateFundQuotas(fund, fofSettings) {
  const navList = fund.nets || []
  if (navList.length < 2) {
    return fund
  }
  const latestDate = navList[navList.length - 1].date

  fund = calculateQuotas(fund, latestDate, fofSettings)
  return fund
}
