export const styleTypeMap = {
  101: '价值型',
  102: '成长型',
  103: '均衡型',
  201: '信用型',
  202: '久期型',
  203: '平衡型',
  301: '积极配置型',
  302: '消极配置型',
}

export const styleTypeManagerMap = {
  1: '价值型',
  2: '成长型',
  3: '均衡型',
  4: '信用选择型',
  5: '久期择时型',
  6: '可转债型',
}

export const compositTypeMap = {
  1: '金牌级',
  2: '银牌级',
  3: '铜牌级',
}

export const assetClassMap = {
  0: '其他',
  1: '纯股型',
  2: '纯债型',
  3: '配置型',
  4: '被动型',
  FOF: 'FOF',
}

export const companyTypeMap = {
  1: '公募',
  2: '私募',
}

export const mktCapMap = {
  1: '大盘',
  2: '中盘',
  3: '小盘',
}

export const assetTypeMap = {
  '1': '现金',
  '2': '股票',
  '3': '期货',
  '4': '债券',
  '5': '基金',
  '6': '权证',
  '7': '理财产品',
  '8': '买入返售',
  '9': '衍生工具',
  '10': '期权',
  '11': '养老金',
  '0': '其他',
}

export const industryListSW = [
  { code: '801770.SI', name: '通信' },
  { code: '801750.SI', name: '计算机' },
  { code: '801760.SI', name: '传媒' },
  { code: '801080.SI', name: '电子' },
  { code: '801780.SI', name: '银行' },
  { code: '801180.SI', name: '房地产' },
  { code: '801790.SI', name: '非银金融' },
  { code: '801150.SI', name: '医药生物' },
  { code: '801200.SI', name: '商贸零售' },
  { code: '801140.SI', name: '轻工制造' },
  { code: '801010.SI', name: '农林牧渔' },
  { code: '801210.SI', name: '社会服务' },
  { code: '801120.SI', name: '食品饮料' },
  { code: '801130.SI', name: '纺织服饰' },
  { code: '801110.SI', name: '家用电器' },
  { code: '801880.SI', name: '汽车' },
  { code: '801980.SI', name: '美容护理' },
  { code: '801720.SI', name: '建筑装饰' },
  { code: '801160.SI', name: '公用事业' },
  { code: '801170.SI', name: '交通运输' },
  { code: '801970.SI', name: '环保' },
  { code: '801230.SI', name: '综合' },
  { code: '801710.SI', name: '建筑材料' },
  { code: '801030.SI', name: '基础化工' },
  { code: '801040.SI', name: '钢铁' },
  { code: '801050.SI', name: '有色金属' },
  { code: '801950.SI', name: '煤炭' },
  { code: '801960.SI', name: '石油石化' },
  { code: '801730.SI', name: '电力设备' },
  { code: '801890.SI', name: '机械设备' },
  { code: '801740.SI', name: '国防军工' },
]

export const industryBoardListSW = [{
  name: 'TMT',
}, {
  name: '金融',
}, {
  name: '医药生物',
}, {
  name: '消费',
}, {
  name: '公共产业',
}, {
  name: '周期中游',
}, {
  name: '周期上游',
}, {
  name: '周期下游',
}]
