import base64js from 'base64-js'
import crypto from 'crypto-browserify'

const KEY = 'cb633cf8d89144d84daf81ed221e6ffbce2db08d57d61bbafb25382001570e4f'
const CIPHER_ALGORITHM = 'aes-256-ctr'

function textEncode(str) {
  if (window.TextEncoder) {
    return new TextEncoder().encode(str)
  }
  const utf8Str = unescape(encodeURIComponent(str))
  const result = new Uint8Array(utf8Str.length)
  for (let i = 0; i < utf8Str.length; i++) {
    result[i] = utf8Str.charCodeAt(i)
  }
  return result
}

function encrypt(plaintext) {
  const sha256 = crypto.createHash('sha256')
  sha256.update(KEY)
  // Initialization Vector
  const iv = crypto.randomBytes(16)
  const cipher = crypto.createCipheriv(CIPHER_ALGORITHM, sha256.digest(), iv)
  const ciphertext = cipher.update(textEncode(plaintext))
  const encrypted = base64js.fromByteArray(new Uint8Array([
    ...iv,
    ...ciphertext,
    ...cipher.final(),
  ]))
  return encrypted
}

function decrypt(encrypted) {
  const sha256 = crypto.createHash('sha256')
  sha256.update(KEY)
  const input = base64js.toByteArray(encrypted)
  // Initialization Vector
  const iv = input.slice(0, 16)
  const decipher = crypto.createDecipheriv(CIPHER_ALGORITHM, sha256.digest(), iv)
  const ciphertext = input.slice(16)
  const plaintext = decipher.update(ciphertext) + decipher.final()

  return plaintext
}

export default {
  encrypt, decrypt,
}
