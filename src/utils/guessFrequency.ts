import moment from 'moment'

export default function guessFrequency(nets: any) {
  if (!nets.length) {
    return 'day'
  }
  const startDate = nets[0].date
  const endDate = nets[nets.length - 1].date
  const days = moment(new Date(endDate)).diff(moment(new Date(startDate)), 'year', true) * 245
  const ratio = nets.length / days
  if (ratio >= 0.8) {
    return 'day'
  } else if (ratio >= 0.16) {
    return 'week'
  }
  return 'month'
}
