export function updateIframeHeight(id: string) {
  if (!window.parent) {
    return
  }
  const iframe = window.parent.document.querySelector('iframe')
  if (!iframe) {
    return
  }
  const element = document.querySelector(id)
  if (!element) {
    return
  }
  let height = element.offsetHeight + 400
  height = height < 500 ? 500 : height
  iframe.scrolling = 'no'
  iframe.height = height
  iframe.style.height = `${height}px`
}

export function resetIframeScrolling() {
  if (!window.parent) {
    return
  }
  const iframe = window.parent.document.querySelector('iframe')
  if (!iframe) {
    return
  }
  iframe.scrolling = 'yes'
  iframe.height = 600
}
