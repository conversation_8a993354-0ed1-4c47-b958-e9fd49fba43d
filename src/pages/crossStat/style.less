.crossStatePage {
  margin-top: 50px;
  .selectArea {
    margin: 20px;
    :global(.ant-form-item) {
      margin-bottom: 8px;
    }
  }
  .error {
    margin: 150px auto;
  }
  .wapper {
    margin-top: 20px;
    .leftButton {
      float: left;
      margin-right: 10px;
    }
    .rightButton {
      float: right;
      :global(.btn.btn-default){
        color:#fffefe !important;
        background-color:#424242 !important;
        outline: none !important;
      }
      :global(.btn.btn-default:focus){
        background-color:rgb(189, 126, 9) !important;
      }
      :global(.btn.btn-default:visited){
        background-color:rgb(189, 126, 9) !important;
      }
      :global(.btn.btn-default:target){
        background-color:rgb(189, 126, 9) !important;
      }
      :global(.btn.btn-default.active){
        background-color:rgb(189, 126, 9) !important;
      }
    }
    .sampleSelect {
      min-width: 100px;
    }
  }
  .wapper:not(.buttonWapper) {
    background-color: #2a2a2a;
  }
  .chartContainer {
    margin-top: 50px;
    :global(.highcharts-container) {
      max-height: 650px;
    }
  }
  .chartSwitcher {
    margin-bottom: 10px;
  }
}

@media (max-width: 768px) {
  .crossStatePage {
    .main {
      .sampleSelect {
        float: none;
        display: inline;
        :global(.selectize-control) {
          margin-top: 30px;
        }
      }
    }
    .chartContainer {
      margin-top: 30px;
    }
    .chartContainer.hasCategory {
      margin-top: 0;
    }
  }
}
