import { Effect } from 'dva'
import { Reducer } from 'redux'
import { loadCrossSurveys, postCross } from './service'

export interface CrossStatModelState {
  surveyLists?: any,
  surveys?: any
}

export interface ModelType {
  namespace: 'crossStat';
  state: CrossStatModelState;
  effects: {
    fetchSurveyList: Effect;
    postCrossStat: Effect;
  };
  reducers: {
    save: Reducer<CrossStatModelState>;
  };
}

const StatModel: ModelType = {
  namespace: 'crossStat',
  state: {
    surveyLists: [],
    surveys: []
  },
  effects: {
    *fetchSurveyList({ payload: { params } }, { call, put }) {
      const response = yield call(loadCrossSurveys, params)
      yield put({
        type: 'save',
        payload: {
          surveyLists: response,
        },
      })
    },
    *postCrossStat({ payload: { data } }, { call, put }) {
      const response = yield call(postCross, data)
      yield put({
        type: 'save',
        payload: {
          surveys: response,
        },
      })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
  },
}

export default StatModel
