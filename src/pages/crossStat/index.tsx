import React, { Component } from 'react'
import classnames from 'classnames'
import CrossStatItem from '@/components/CrossStatItem'
import deduplication from '../../utils/deduplication'
import { Menu, Select, Button, Form, Row, Col, Empty } from 'antd'
import { parse } from 'qs'
import router from 'umi/router'
import { connect } from 'dva'
import styles from './style.less'
import { CrossStatModelState } from './model'
const { Option } = Select
interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  match: any;
  currentUser?: CurrentUser,
  surveyLists: any,
  surveys?: any
}

interface ComponentState {
  viewMode: string,
  activeTab: string,
  ids: any
}

@connect(
  ({
    crossStat,
    loading,
  }: {
    crossStat: CrossStatModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }
  ) => {
    return ({
      surveyLists: crossStat.surveyLists,
      surveys: crossStat.surveys,
      loading: loading.effects['crossStat/fetchSurveyList'],
    })
  }
)

export default class CrossStat extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    this.state = {
      viewMode: 'splited',
      activeTab: 'cross',
      ids: []
    }

  }

  componentDidMount() {
    const { location } = this.props
    const queryObject = location.query || parse(location.search.substr(1))
    const category = queryObject && queryObject.category
    if (category) {
      this.loadCrossSurveys({ category })
    }
  }

  loadCrossSurveys = (params: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'crossStat/fetchSurveyList',
      payload: { params },
    })
  }

  postCrossStat = (data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'crossStat/postCrossStat',
      payload: { data },
    })
  }

  getQueryObject = (location: any) => {
    return location.query || parse(location.search.substr(1))
  }

  handleMenuChange = category => event => {
    const activeTab = event.key
    this.setState({ activeTab }, () => {
      if (activeTab === 'cross') {
        router.push(`/duediligence/stat/cross?category=${category}`)
      } else {
        router.push(`/duediligence/stat/crossUser?category=${category}`)
      }
    })
  }

  handleSurveysChange = ids => {
    this.setState({ ids })
  }

  onSubmit = () => {
    const { ids } = this.state
    this.postCrossStat({ ids })
  }

  isQuestionMatrix = question =>
    question && question.type === 'matrix' &&
    question.questions.find(item => item.isNumeric) // question numeric 矩阵

  isOptionMatrix = question =>
    question && ['matrix', 'table'].includes(question.type) &&
    question.options.find(option => option.isNumeric) // option numeric 矩阵

  isNumericMatrix = question =>
    this.isQuestionMatrix(question) || this.isOptionMatrix(question)

  isNumericSurveyQuestion = question => question && ['matrix', 'table'].includes(question.type) && (question.questions.find(item => item.isNumeric) || question.options.find(option => option.isNumeric))

  isAllNotNumeric = () => {
    const { surveys } = this.props
    return (surveys || []).every(survey => (survey.questions || []).every(question => {
      console.log('question', question)
      if (this.isNumericSurveyQuestion(question)) {
        return false
      }
      return !question.isNumeric
    }))
  }

  questionFilter = question => {
    return (
      question.answers &&
      ((~['radio', 'select', 'matrix_radio', 'input'].indexOf(question.type) &&
        question.isNumeric) ||
        question.type === 'slider')
    )
  }

  processMatrixData(data, questionName) {
    const { surveys } = this.props
    surveys.map((survey, index) => {
      return survey.questions
        .filter(question =>
          questionName ? question.title === questionName : question
        )
        .map(question => {
          const result = []
          let [vals, options, answers] = [[], [], []]
          if (this.isOptionMatrix(question)) {
            vals = question.options
              .filter(opt => opt.isNumeric)
              .map(opt => opt.value)
            options = Object.keys(question.answers)
            if (question.type === 'matrix') {
              options.map(opt => {
                vals.map(val => {
                  answers = []
                    ; (question.answers[opt] || []).map(auth => {
                      answers.push({
                        answer: auth.answer[val],
                        author: auth.author
                      })
                    })
                  result.push({ title: `${opt}-${val}`, answers })
                })
              })
            } else {
              const questions = [...new Set(question.answers.map(answer => answer.question))]
              options.forEach(opt => {
                const optAnswer = question.answers[opt]
                vals.map(val => {
                  answers = []
                  answers.push({
                    answer: optAnswer.answer[val],
                    author: optAnswer.author
                  })
                  const linePostfix = question.rowSize === 1 ? '' : `-行${questions.indexOf(optAnswer.question) + 1}`
                  result.push({ title: `${optAnswer.author.nickname}-${val}${linePostfix}`, answers })
                })
              })
            }
            data[index] = questionName ? result : data[index].concat(result)
          } else if (this.isQuestionMatrix(question)) {
            vals = question.questions
              .filter(item => item.isNumeric)
              .map(item => item.value)
            options = question.options.map(opt => opt.value)
            vals.map(val => {
              options.map(opt => {
                answers = []
                  ; (question.answers[val] || []).map(auth => {
                    answers.push({
                      answer: auth.answer[opt],
                      author: auth.author
                    })
                  })
                result.push({ title: `${opt}-${val}`, answers })
              })
            })
            data[index] = questionName ? result : data[index].concat(result)
          }
        })
    })
  }

  generateAllData() {
    const { surveys } = this.props
    const data = surveys.map(survey => {
      return survey.questions
        .filter(this.questionFilter)
        .reduce((out, question) => {
          if (~['radio', 'select', 'input', 'slider'].indexOf(question.type)) {
            out.push(question)
          } else if (question.type === 'matrix_number') {
            out.push(question)
          } else {
            const answerMap = question.answers.reduce((map, item) => {
              const key = `${question.title}:${item.question}`
              map[key] = map[key] || []
              map[key].push({
                answer: item.answer,
                author: item.author
              })
              return map
            }, {})
            question.questions.forEach(item => {
              const key = `${question.title}:${item.value}`
              if (answerMap[key]) {
                out.push({
                  title: key,
                  answers: answerMap[key]
                })
              }
            })
          }
          return out
        }, [])
    })
    this.processMatrixData(data)
    const categories = surveys.map(survey => survey.title)
    return { data, categories }
  }

  generateSingleData(questionName) {
    const { surveys } = this.props
    const data = surveys.map(survey => {
      return survey.questions
        .filter(this.questionFilter)
        .filter(question => question.title === questionName)
        .slice(0, 1)
        .reduce((out, question) => {
          if (~['radio', 'select', 'input', 'slider'].indexOf(question.type)) {
            out.push(question)
          } else if (question.type === 'matrix_number') {
            out.push(question)
          } else {
            const answerMap = question.answers.reduce((map, item) => {
              const key = item.question
              map[key] = map[key] || []
              map[key].push({
                answer: item.answer,
                author: item.author
              })
              return map
            }, {})
            question.questions.forEach(item => {
              const key = item.value
              if (answerMap[key]) {
                out.push({
                  title: key,
                  answers: answerMap[key]
                })
              }
            })
          }
          return out
        }, [])
    })
    this.processMatrixData(data, questionName)
    const categories = surveys.map(survey => survey.title)
    return { data, categories }
  }

  generateQuestionNames() {
    const { surveys } = this.props
    const matrixNames = surveys
      .map(survey =>
        (survey.questions || []).filter(question => this.isNumericMatrix(question))
      )
      .reduce((out, keys) => out.concat(keys), [])
      .map(question => question.title)
    const names = surveys
      .map(survey =>
        (survey.questions || [])
          .filter(this.questionFilter)
          .map(question => question.title)
      )
      .reduce((out, keys) => out.concat(keys), [])
    return deduplication(names.concat(matrixNames))
  }

  switchChartType = type => {
    this.setState({ type })
  }

  switchViewMode = viewMode => () => {
    this.setState({ viewMode })
  }

  render() {
    const { location, surveys, surveyLists } = this.props
    const { ids } = this.state
    const query = this.getQueryObject(location)
    const category = query && query.category
    const { viewMode, activeTab } = this.state
    const names = this.generateQuestionNames()
    const isAllNotNumeric = this.isAllNotNumeric()
    const hasSurveys = surveys && surveys.length
    return (
      <div className={classnames(styles.crossStatePage)}>
        <Menu
          onClick={this.handleMenuChange(category)}
          selectedKeys={[activeTab]}
          mode="horizontal"
        >
          <Menu.Item key="cross">
            问卷交叉分析
          </Menu.Item>
          <Menu.Item key="user">
            用户交叉分析
          </Menu.Item>
        </Menu>
        <div className={styles.selectArea}>
          <Form layout="vertical" hideRequiredMark>
            <Row gutter={0}>
              <Col span={24}>
                <Form.Item label='问卷列表'>
                  <Select mode="tags" style={{ width: '100%' }} value={ids} placeholder="Tags Mode" onChange={this.handleSurveysChange}>
                    {surveyLists.map((item: any) => {
                      return <Option value={item._id}>{item.title}</Option>
                    })}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={0}>
              <Col span={2}>
                <Button onClick={this.onSubmit}>确认</Button>
              </Col>
            </Row>
          </Form>
        </div>
        {hasSurveys && isAllNotNumeric && <div className={styles.error}>
          <Empty description=' 没有数值类型的问题，无法进行交叉分析' />
        </div>}
        {hasSurveys && !isAllNotNumeric && <div>
          <div
            className={classnames(
              'container',
              styles.wapper,
              styles.buttonWapper
            )}
          >
            <div className={classnames('btn-group', styles.rightButton)}>
              <button
                className={classnames('btn btn-default', {
                  active: viewMode === 'splited'
                })}
                onClick={this.switchViewMode('splited')}
              >
                <i className="fa fa-align-justify" />
              </button>
              <button
                className={classnames('btn btn-default', {
                  active: viewMode === 'mixed'
                })}
                onClick={this.switchViewMode('mixed')}
              >
                <i className="fa fa-th-large" />
              </button>
            </div>
          </div>
          {viewMode === 'mixed' && (
            <div className={classnames('container', styles.wapper)}>
              <CrossStatItem
                title=""
                id="all"
                {...this.generateAllData()}
              />
            </div>
          )}
          {viewMode === 'splited' &&
            names.map((name, index) => (
              <div
                className={classnames('container', styles.wapper)}
                key={`${name}-${index}`}
              >
                <CrossStatItem
                  title={name}
                  id={`cross-stat-item-${index}`}
                  {...this.generateSingleData(name)}
                />
              </div>
            ))}
        </div>}
      </div>
    )
  }
}
