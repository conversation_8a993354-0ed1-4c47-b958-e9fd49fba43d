import request from '@/utils/request'

export async function querySellSideReports(params?: any) {
  return request('/api/sellsidereports', {
    params,
  })
}

export async function querySellSideReport(id: string) {
  return request(`/api/sellsidereports/${id}`)
}

export async function queryRefSellSideReport(params?: any) {
  return request(`/api/sellsidereports/ref/list`, {
    params,
  })
}

export async function deleteSellSideReport(id: string) {
  return request(`/api/sellsidereports/${id}`, {
    method: 'delete',
  })
}

export async function updateSellSideReport(id: string, data: any) {
  return request(`/api/sellsidereports/${id}`, {
    method: 'put',
    data,
  })
}

export async function createSellSideReport(data: any) {
  return request(`/api/sellsidereports`, {
    method: 'post',
    data,
  })
}

export async function queryGroupList() {
  return request(`/api/duedocs/usergroup/list`)
}

export async function queryStockList() {
  return request(`/api/funds/kym/queryOptions/stocks`)
}
