import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import { connect } from 'dva'
import _ from 'lodash'
import moment from 'moment'
import { DeleteOutlined, EditOutlined, PlusOutlined, InboxOutlined } from '@ant-design/icons'
import {
  Table,
  Button,
  Tooltip,
  Popconfirm,
  Modal,
  Input,
  Card,
  Form,
  notification,
  Spin,
  Breadcrumb,
  Upload,
  Tag,
  message,
  Space,
  Row,
  Col,
  DatePicker,
  Select,
  InputNumber,
} from 'antd'
import ExportData from '@/components/ExportData'
import { querySellSideReports, updateSellSideReport, createSellSideReport, deleteSellSideReport, queryGroupList, queryStockList } from './service'
import t from '@/utils/t'
import { getToken } from '@/utils/utils'

const Search = Input.Search

const List = ({
  currentUser,
}: {
  currentUser: any,
}) => {
  const [form] = Form.useForm()
  const { setFieldsValue, getFieldValue } = form
  const initialFormValue = {
    name: '', description: '', fileInfo: null, refStocks: [],
    userGroupIds: [], userGroups: [], stockIds: [],
    reportSource: '', // 报告来源
    reportType: '', // 所属类别
    reportTag: [], // 报告标签
    industry: '', // 所属行业
    researcher: '', // 研究员
    reportPoint: '', // 报告观点
    reporter: '', // 上传人
    reportingTime: moment(), // 报告时间
    priorityRating: '', // 优先级评级
    ratingTime: moment(), // 评级时间
  }
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState({})
  const [refreshCount, setRefreshCount] = useState(0)
  const [uploadCount, setUploadCount] = useState(0)
  const [input, setInput] = useState('')
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    return querySellSideReports(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const onSaveSellSideReportSuccess = () => {
    handleOpenClose()
    setRefreshCount(refreshCount + 1)
    notification.success({
      message: '保存成功',
    })
  }
  const { loading: updatingSellSideReport, run: doUpdateSellSideReport} = useRequest((id, data) => {
    return updateSellSideReport(id, data)
  }, {
    manual: true,
    onSuccess: onSaveSellSideReportSuccess,
  })
  const { loading: creatingSellSideReport, run: doCreateSellSideReport} = useRequest((data) => {
    return createSellSideReport(data)
  }, {
    manual: true,
    onSuccess: onSaveSellSideReportSuccess,
  })
  const { run: doDeleteSellSideReport } = useRequest((id) => {
    return deleteSellSideReport(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const { data: groups = [] } = useRequest(() => {
    return queryGroupList()
  }, {
    cacheKey: 'queryGroupList',
  })

  const { data: stockList = [] } = useRequest(() => {
    return queryStockList()
  }, {
    cacheKey: 'queryStockList',
  })
  const [uploadStatus, setUploadStatus] = useState({
    uploading: false,
    hasError: false,
  })
  const handleOpenModal = (record) => () => {
    setCurrentItem(record)
    setVisibleTrue()
    setFieldsValue({
      ...record,
      reportingTime: moment(record.reportingTime),
      ratingTime: moment(record.ratingTime),
      stockIds: record.refStocks.map(item => item.id),
    })
  }
  const handleOpenClose = () => {
    setVisibleFalse()
    setFieldsValue(initialFormValue)
  }
  const handleClickSave = (values: any) => {
    const userGroupIds = values.userGroupIds
    values.userGroups = groups
      .filter(item => userGroupIds.includes(item._id))
      .map(item => {
        return {
          name: item.name,
          id: item._id,
        }
      })
    const stockIds = values.stockIds
    values.refStocks = stockList.filter(item => {
      return stockIds.includes(item.value)
    }).map(item => {
      return {
        name: item.name,
        id: item.value,
      }
    })
    if (currentItem._id) {
      doUpdateSellSideReport(currentItem._id, values)
    } else {
      doCreateSellSideReport(values)
    }
  }
  const handleClickDownload = (record) => {
    const href = `/api/sellsidereports/${record._id}/download?token=${getToken().slice(7)}`
    window.open(href)
  }

  const reportTypeList = ['金工','宏观','策略','行业','公司','产品','其他']
  const reportPointList = ['看多', '中性', '看空', '无观点']
  const columns = [
    {
      title: '报告来源',
      dataIndex: 'reportSource',
      fixed: 'left',
      width: 120,
    },
    {
      title: '所属类别',
      dataIndex: 'reportType',
      width: 120,
      filters: reportTypeList.map(item => {
        return {
          text: item,
          value: item,
        }
      })
    },
    {
      title: '报告标签',
      dataIndex: 'reportTag',
      width: 120,
    },
    {
      title: '所属行业',
      dataIndex: 'industry',
      width: 200,
    },
    {
      title: '研究员',
      dataIndex: 'researcher',
      width: 120,
    },
    {
      title: '报告观点',
      dataIndex: 'reportPoint',
      width: 120,
      filters: reportPointList.map(item => {
        return {
          text: item,
          value: item,
        }
      })
    },
    {
      title: '上传人',
      dataIndex: 'reporter',
      width: 120,
    },
    {
      title: '报告时间',
      dataIndex: 'reportingTime',
      width: 120,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD')
      }
    },
    {
      title: '优先级评级',
      dataIndex: 'priorityRating',
      width: 120,
    },
    {
      title: '评级时间',
      dataIndex: 'ratingTime',
      width: 120,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD')
      }
    },
    // {
    //   title: '可见用户组',
    //   dataIndex: 'userGroups',
    //   width: 150,
    //   render: (userGroups, record) => {
    //     const list = (userGroups || [])
    //     if (!list.length) {
    //       return  <Tag>仅自己</Tag>
    //     }
    //     return list.map(item => {
    //       return <Tag>{item.name}</Tag>
    //     })
    //   }
    // },
    {
      title: '关联证券',
      dataIndex: 'refStocks',
      width: 250,
      render: (text, record) => {
        return record.refStocks.map(item => item.name).join(',')
      }
    },
    {
      title: '创建人',
      dataIndex: 'author',
      width: 120,
      render: (val) => {
        return val && val.nickname
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      width: 150,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD hh:mm')
      }
    },
    {
      title: '可见用户组',
      dataIndex: 'userGroups',
      width: 150,
      render: (userGroups, record) => {
        if (currentUser._id !== record.authorId) {
          return false
        }
        const list = (userGroups || [])
        if (!list.length) {
          return  <Tag>仅自己</Tag>
        }
        return list.map(item => {
          return <Tag>{item.name}</Tag>
        })
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (text, record) => {
        if (currentUser._id !== record.authorId) {
          return (
            <ExportData isIcon title={record.fileInfo ? record.fileInfo.name : '下载'} onClick={() => handleClickDownload(record)}/>
          )
        }
        return (
          <>
            <Space>
              <Tooltip title="编辑">
                <EditOutlined onClick={handleOpenModal(record)} />
              </Tooltip>
              <ExportData isIcon title={record.fileInfo ? record.fileInfo.name : '下载'} onClick={() => handleClickDownload(record)}/>
              <Popconfirm
                title={`${t('portfolio.delTip')}${t('portfolio.questionEnd')}？`}
                onConfirm={() => { doDeleteSellSideReport(record._id) }}
                onCancel={() => {}}
                okText={t('portfolio.confirm')}
                cancelText={t('portfolio.cancel')}
              >
                <Tooltip title="删除">
                  <DeleteOutlined />
                </Tooltip>
              </Popconfirm>
            </Space>
          </>
        )
      },
    },
  ]

  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e
    }
    const list = e.fileList.map(item => item.response).filter(Boolean)
    return list[list.length - 1]
  }

  const normFundManager = (funds: any) => {
    return funds.map(item => {
      return {
        name: item.name,
        id: item.id,
      }
    })
  }

  const beforeFileUpload = (file) => {
    const fileType = file.type
    const typeList = ['doc', 'docx', 'xls', 'xlsx', 'png', 'jpg', 'jpeg', 'zip', 'pdf', 'tar.gz']
    const valid = typeList.some(type => fileType.includes(type))
    if (!valid) {
      message.error('只能上传后缀为doc,docx,xls,xlsx,png,jpg,jpeg,zip,pdf的文件')
    }
    return valid || Upload.LIST_IGNORE
  }
  const fileInfoValue = getFieldValue('fileInfo')
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>智能尽调</Breadcrumb.Item>
        <Breadcrumb.Item>卖方报告</Breadcrumb.Item>
      </Breadcrumb>
      <Card
        title={
          <>
            <Search
              size="small"
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
        extra={
          <>
            <a onClick={
              handleOpenModal(initialFormValue)
            }>
              <PlusOutlined />
              新建卖方报告
            </a>
          </>
        }
      >
        <Table
          size="small" columns={columns} rowKey="_id"
          scroll={{ x: 700 }}
          {...tableProps}
        />
      </Card>
      <Modal
        title={
          <>
            <span>{currentItem && currentItem._id ? '编辑卖方报告' : '新建卖方报告'}</span>
          </>
        }
        visible={visible}
        onCancel={handleOpenClose}
        width={900}
        footer={[
          <Button
            type="primary"
            loading={updatingSellSideReport || creatingSellSideReport}
            onClick={() => form.submit()}
          >
            保存
          </Button>,
        ]}
      >
        <Spin spinning={updatingSellSideReport || creatingSellSideReport}>
          <Form
            hideRequiredMark
            form={form}
            layout="vertical"
            initialValues={initialFormValue}
            onFinish={handleClickSave}
          >
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  label="报告来源"
                  name="reportSource"
                  rules={[
                    {
                      required: true,
                      message: '请输入报告来源',
                    },
                  ]}
                >
                  <Input placeholder="报告来源"/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="所属类别"
                  name="reportType"
                  rules={[
                    {
                      required: true,
                      message: '请输入所属类别',
                    },
                  ]}
                >
                  <Select
                  >
                    {reportTypeList.map(item => <Select.Option key={item}>{`${item}`}</Select.Option>)}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  label="报告标签"
                  name="reportTag"
                >
                  <Select mode="tags" placeholder="量化对冲/多因子/机器学习"/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="所属行业"
                  name="industry"
                >
                  <Input placeholder="所属行业"/>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  label="研究员"
                  name="researcher"
                  rules={[
                    {
                      required: true,
                      message: '请输入研究员',
                    },
                  ]}
                >
                  <Input placeholder="研究员"/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="报告观点"
                  name="reportPoint"
                  rules={[
                    {
                      required: true,
                      message: '请输入报告观点',
                    },
                  ]}
                >
                  <Select
                  >
                    {reportPointList.map(item => <Select.Option key={item}>{`${item}`}</Select.Option>)}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  label="上传人"
                  name="reporter"
                  rules={[
                    {
                      required: true,
                      message: '请输入上传人',
                    },
                  ]}
                >
                  <Input placeholder="上传人"/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="报告时间"
                  name="reportingTime"
                  rules={[
                    {
                      required: true,
                      message: '请输入报告时间',
                    },
                  ]}
                >
                  <DatePicker placeholder="报告时间"/>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  label="优先级评级(0-1)"
                  name="priorityRating"
                >
                  <InputNumber style={{ width: '150px' }} min={0} max={1} step={0.1} placeholder="请输入0-1的数"/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="评级时间"
                  name="ratingTime"
                >
                  <DatePicker placeholder="评级时间"/>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              label="上传文档"
              name="fileInfo"
              getValueFromEvent={normFile}
              rules={[
                {
                  validator: (rule, values) => {
                    if (!values && !uploadStatus.uploading && !uploadStatus.hasError) {
                      return Promise.reject('请上传报告文档')
                    }
                    setUploadCount(uploadCount + 1)
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <Upload.Dragger
                action="/api/duedocs/upload"
                headers={{
                  authorization: getToken(),
                }}
                name="attachment"
                showUploadList={false}
                beforeUpload={beforeFileUpload}
                // defaultFileList={fileInfoValue && [fileInfoValue]}
                onChange = {({ file }) => {
                  const { status, error } = file
                  if (status === 'uploading') {
                    setUploadStatus({
                      uploading: true,
                      hasError: false,
                    })
                  } else if (status === 'done') {
                    setUploadStatus({
                      uploading: false,
                      hasError: false,
                    })
                  } else if (status === 'error') {
                    if (error && error.status === 413) {
                      notification.error({ message: `文档太大，上传失败，请确保上传文件不要超过100MB.` })
                    } else {
                      notification.error({ message: `${file.name} 上传失败.` })
                    }
                    setUploadStatus({
                      uploading: false,
                      hasError: true,
                    })
                  }
                }}
              >
                <p className="ant-upload-drag-icon">
                  { uploadStatus.uploading ? <Spin/> : <InboxOutlined />}
                </p>
                <p className="ant-upload-text">
                  {!uploadStatus.hasError && fileInfoValue ? <a>{`已上传: ${fileInfoValue.name}`}</a> : '点击或者拖放文件到此处进行上传'}
                </p>
                <p className="ant-upload-hint">支持上传的文件类型为: doc,docx,xls,xlsx,png,jpg,jpeg,zip,pdf</p>
                <p className="ant-upload-hint">上传附件请不要超过100MB</p>
              </Upload.Dragger>
            </Form.Item>
            <Form.Item
              label="可见用户组"
              name="userGroupIds"
            >
              <Select
                showSearch
                mode="multiple"
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {groups.map(item => <Select.Option key={item._id}>{`${item.name}`}</Select.Option>)}
              </Select>
            </Form.Item>
            <Form.Item
              label="关联证券"
              name="stockIds"
            >
              <Select
                showSearch
                mode="multiple"
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {stockList.map(item => <Select.Option key={item.value}>{`${item.name}`}</Select.Option>)}
              </Select>
            </Form.Item>
          </Form>
        </Spin>
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
