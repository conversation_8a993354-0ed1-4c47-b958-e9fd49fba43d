import React from 'react'
import _ from 'lodash'
import moment from 'moment'
import { useRequest } from '@umijs/hooks'
import { Card, Breadcrumb, Spin, Row, Col, Select } from 'antd'
import Link from 'umi/link'
import CorrelationTable from '@/components/CorrelationTable'
import { queryReportData, queryReports } from './service'
import CustomQuotaTable from './components/CustomQuotaTable'
import MonthlyReturnTable from './components/MonthlyReturnTable'
import QuotaSeriesChart from './components/QuotaSeriesChart'
import AssetStructureChart from './components/AssetStructureChart'
import styles from './style.less'

const { Option } = Select

const renderReportContent = (content, report) => {
  const type = content.type
  if (!type || ['positionChange', 'assetStructur'].includes(type)) {
    return  <CustomQuotaTable content={content} report={report} />
  }
  if (type === 'monthlyReturn') {
    return <MonthlyReturnTable funds={report.funds} />
  } else if (type === 'correlation') {
    return <CorrelationTable useIdAsKey funds={report.funds} cor={report.correlationData}/>
  }
  return <QuotaSeriesChart content={content} report={report} />
}

export default ({
  match,
}: {
  match: any,
}) => {
  const {
    params: { id },
  } = match
  const { loading, data: report } = useRequest(() => {
    return queryReportData(id)
  })
  const getReportDateRange = () => {
    const { startDate, endToNow, endDate } = report
    return `${moment(new Date(startDate)).format('YYYY/MM/DD')} - ${endToNow ? '至今' : moment(new Date(endDate)).format('YYYY/MM/DD')}`
  }
  const { data: allReportList = { list: [] } } = useRequest(() => {
    return queryReports({ per_page: 100 })
  }, {
    cacheKey: 'allReportList',
  })
  const contents = report ? report.contents : []
  return (
    <Spin spinning={loading}>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link to={'/report'}>报表管理</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{report && report.name}</Breadcrumb.Item>
      </Breadcrumb>
      <Select
        style={{ width: 200, float: 'right', marginTop: -40 }}
        placeholder="切换报表"
        onChange={(value) => {
          window.location.href = `/report/${value}`
        }}
      >
        {allReportList.list.map(item => <Option value={item._id}>{item.name}</Option>)}
      </Select>
      {report &&
      <Card title={getReportDateRange()}>
        <div>{report.description}</div>
      </Card>}
      <div style={{ marginTop: 10 }} />
      <Row gutter={12} style={{ minHeight: 400 }}>
        {contents.map((item, index) => (
          <Col lg={(contents.length === 1 || index === contents.length - 1 && contents.length % 2 === 1 )? 24 : 12} md={24} xs={24} style={{ marginBottom: 15 }}>
            {item.type === 'assetStructureSeries' ?
            <AssetStructureChart report={report} /> :
            <Card title={item.name} className={styles.reportCard}>
               {renderReportContent(item, report)}
            </Card>}
          </Col>
        ))}
      </Row>
    </Spin>
  )
}
