import React from 'react'
import Chart from '@/components/Chart/Chart'

const buildNavSeriesConfig = (funds) => {
  const config = {
    chart: {
      type: 'line',
    },
    tooltip: {
      pointFormat:
        '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.4f}({point.change:.2f}%)</b><br/>',
    },
    scrollbar: {
      enabled: false,
    },
    yAxis: {
      labels: {
        format: '{value}%',
      },
    },
    series: funds.map(fund => {
      return {
        name: fund.name,
        compare: 'percent',
        data: fund.nets.map(item => [item.date, item.value]),
      }
    }),
  }
  return config
}

const buildRetSeriesConfig = (funds) => {
  const config = {
    chart: {
      type: 'line',
    },
    tooltip: {
      pointFormat:
        '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.2f}%</b><br/>',
    },
    scrollbar: {
      enabled: false,
    },
    yAxis: {
      labels: {
        format: '{value}%',
      },
    },
    series: funds.map(fund => {
      return {
        name: fund.name,
        data: fund.returns.map(item => [item.date, item.value * 100]),
      }
    }),
  }
  return config
}

const buildCurveSeriesConfig = (report, type) => {
  const series = report[`${type}Data`] || []
  const config = {
    chart: {
      type: 'line',
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.y:,.2f}</b><br/>',
    },
    series,
    scrollbar: {
      enabled: false,
    },
    yAxis: {
      labels: {
        format: '{value}',
      },
    },
  }
  return config
}

const QuotaSeriesChart = ({
  content,
  report,
}: {
  content: any;
  report: any;
}) => {
  let config
  const type = content.type
  const curveTypes = ['netAssets', 'assetSeries', 'varSeries', 'peSeries', 'pbSeries', 'couponRateSeries', 'ytmSeries', 'durationSeries', 'residualDurationSeries']
  if (type === 'retSeries') {
    config = buildRetSeriesConfig(report.funds || [])
  } else if (type === 'navSeries') {
    config = buildNavSeriesConfig(report.funds || [])
  } else if (curveTypes.includes(type)) {
    config = buildCurveSeriesConfig(report, type)
  }
  if (!config) {
    return false
  }
  return <Chart options={config} constructorType="stockChart" />
}

export default QuotaSeriesChart
