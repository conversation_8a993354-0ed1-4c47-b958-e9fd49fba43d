import React from 'react'
import { Table } from 'antd'
import _ from 'lodash'
import moment from 'moment'
import renderFundQuota from '@/utils/renderFundQuota'

const calculateReturnByYear = (fund, year, returns) => {
  const defaultValueReducer = (out, item) => {
    out[item] = undefined
    return out
  }
  const returnSumReducer = (out, item) => out * (1 + item.value)
  const quarterResult = ['q1', 'q2', 'q3', 'q4'].reduce(
    defaultValueReducer,
    {}
  )
  const monthResult = [
    'm1',
    'm2',
    'm3',
    'm4',
    'm5',
    'm6',
    'm7',
    'm8',
    'm9',
    'm10',
    'm11',
    'm12',
  ].reduce(defaultValueReducer, {})
  const ytd = returns.reduce(returnSumReducer, 1) - 1
  const groupByQuarter = _.groupBy(returns, 'quarter')

  Object.keys(groupByQuarter).forEach(quarter => {
    quarterResult[`q${quarter}`] =
      groupByQuarter[quarter].reduce(returnSumReducer, 1) - 1
  })
  returns.forEach(item => {
    monthResult[`m${item.month}`] = item.value
  })

  return {
    year,
    ytd,
    name: fund.name,
    _id: fund._id,
    ...quarterResult,
    ...monthResult,
  }
}

const processMonthlyReturnData = (fundList) => {
  const data = fundList.reduce((out, fund) => {
    const returns = fund.monthlyReturns.map(item => {
      const date = moment(new Date(item.date))
      return {
        value: item.value,
        month: date.month() + 1,
        year: date.year(),
        quarter: date.quarter(),
      }
    })
    const groupByYear = _.groupBy(returns, 'year')
    const signleFundData = Object.keys(groupByYear).map(year =>
      calculateReturnByYear(fund, year, groupByYear[year])
    )
    return out.concat(signleFundData)
  }, [])
  return data
}

const generateMonthlyReturnData = (fundList) => {
  const quotas = [
    {
      title: '年份',
      dataIndex: 'year',
      format: 'text',
      fixed: 'left',
      width: 50,
    },
  ]
  if (fundList && fundList.length > 1) {
    quotas.push({
      title: '基金',
      dataIndex: 'name',
      format: 'text',
      fixed: 'left',
      width: 150,
      filters: fundList.map(item => {
        return {
          text: item.name,
          value: item._id,
        }
      }),
      onFilter: (value, record) => record._id === value,
    })
  }
  Array(12).fill(0).map((_, key) => key + 1).map(
    item => {
      quotas.push({
        title: `${item}月`,
        dataIndex: `m${item}`,
        format: 'percentage',
      })
    }
  )
  ;['1', '2', '3', '4'].forEach(item => {
    quotas.push({
      title: `Q${item}`,
      dataIndex: `q${item}`,
      format: 'percentage',
    })
  })
  quotas.push({
    title: '累计收益',
    dataIndex: 'ytd',
    format: 'percentage',
    width: 100,
  })
  const data = processMonthlyReturnData(fundList)
    .sort((fst, snd) => {
      if (fst.year === snd.year) {
        return snd.name > fst.name ? 1 : -1
      }
      return snd.year - fst.year
    })
    .filter(item => item.name.slice(-4) !== '超额收益')
  return {
    quotas: quotas.map(item => {
      const { format, dataIndex } = item
      item.render = (text, row) => {
        return renderFundQuota({ format, value: dataIndex || '', dataIndex }, row)
      }
      return item
    }),
    data,
  }
}

const MonthlyReturnTable = ({
  funds,
}: {
  funds: any,
}) => {
  const { quotas, data } = generateMonthlyReturnData(funds)
  return (
    <Table
      columns={quotas}
      dataSource={data}
      pagination={false}
      size="small"
      scroll={{ x: 1300, y: 260 }}
    />
  )
}

export default MonthlyReturnTable
