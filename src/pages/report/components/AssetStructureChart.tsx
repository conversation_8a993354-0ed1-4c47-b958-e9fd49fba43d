import React, { useState } from 'react'
import { Card, Select } from 'antd'
import Area<PERSON>ieChart from '@/components/AreaPieChart'
import { assetTypeMap } from '@/utils/kymDefMapping'

const getFundId = fund => fund._syncType === 'mutual' ? fund._qutkeId : fund._id

const AssetStructureChart = ({
  report,
}: {
  report: any
}) => {
  const { assetStructureSeriesData, funds } = report
  const [fundId, setFundId] = useState(getFundId(funds[0]))
  const data = assetStructureSeriesData
    .map(item => {
      return {
        F_CODE: item[0],
        BIZ_DATE: item[1],
        BALANCE: item[2],
        ASSET_TYPE: item[4],
      }
    })
    .filter(item => item.F_CODE === fundId)
  return (
    <Card
      title="资产类型结构"
      extra={
        <Select
          showSearch
          size="small"
          placeholder="选择基金"
          value={fundId}
          style={{
            width: '150px',
          }}
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
          onChange={(fundId) => setFundId(fundId)}
        >
          {funds.map(item => {
            return (
              <Select.Option value={getFundId(item)}>
                {item.name}
              </Select.Option>
            )
          })}
        </Select>
      }
    >
      <AreaPieChart
        disablePie
        height={300}
        rows={data}
        nameKey="ASSET_TYPE"
        valueKey="BALANCE"
        nameMap={assetTypeMap}
      />
    </Card>
  )
}

export default AssetStructureChart
