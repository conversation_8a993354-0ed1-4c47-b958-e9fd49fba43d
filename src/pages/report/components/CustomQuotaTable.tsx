import React from 'react'
import _ from 'lodash'
import { Table } from 'antd'
import { navQuotasIgnoreRank } from '@/utils/quotas'
import renderFundQuota from '@/utils/renderFundQuota'
import sortQuotaFn from '@/utils/sortQuotaFn'

const getPositionChangeColumns = (data) => {
  const assetTypes = _.uniq(data.map(item => item.assetType))
  return [
    {
      title: '资产类型',
      dataIndex: 'assetType',
      fixed: 'left',
      filters: assetTypes.map(item => {
        return {
          text: item,
          value: item,
        }
      }),
      onFilter: (value, record) => record.assetType === value,
    },
    {
      title: '资产名称',
      dataIndex: 'stockName',
      format: 'text',
    },
    {
      title: '资产代码',
      dataIndex: 'stockCode',
      format: 'text',
    },
    {
      title: '期初日期',
      value: 'startDate',
      format: 'text',
    },
    {
      title: '期末日期',
      dataIndex: 'endDate',
      format: 'text',
    },
    {
      title: '期初数量',
      dataIndex: 'startAmount',
      isNumeric: true,
    },
    {
      title: '期末数量',
      dataIndex: 'endAmount',
      isNumeric: true,
    },
    {
      title: '数量增长率',
      dataIndex: 'amountGrowthRatio',
      format: 'percentage',
    },
    {
      title: '期初金额',
      dataIndex: 'startBalance',
      isNumeric: true,
    },
    {
      title: '期末金额',
      dataIndex: 'endBalance',
      isNumeric: true,
    },
    {
      title: '金额增长率',
      dataIndex: 'balanceGrowthRatio',
      format: 'percentage',
    },
  ]
}

const getAssetStructureColumns = (data) => {
  const assetTypes = _.uniq(data.map(item => item.assetType))
  return [
    {
      title: '资产类型',
      dataIndex: 'assetType',
      fixed: 'left',
      filters: assetTypes.map(item => {
        return {
          text: item,
          value: item,
        }
      }),
      onFilter: (value, record) => record.assetType === value,
    },
    { title: '期初日期', dataIndex: 'startDate' },
    {
      title: '期初净资产',
      dataIndex: 'startBalanceNet',
      format: 'hundredMillion',
    },
    {
      title: '期初占比',
      dataIndex: 'startRatio',
      format: 'percentage',
    },
    { title: '期末日期', dataIndex: 'endDate' },
    {
      title: '期末净资产',
      dataIndex: 'endBalanceNet',
      format: 'hundredMillion',
    },
    { title: '期末占比', dataIndex: 'endRatio', format: 'percentage' },
    { title: '增长率', dataIndex: 'rise', format: 'percentage' },
  ]
}

const getTableColumns = quotas => {
  return quotas.map(item => {
    const ret = {
      ...item,
      key: item.dataIndedx,
    }
    if (item.format) {
      const { format, dataIndex } = item
      ret.render = (text, row) => {
        return renderFundQuota({ format, value: dataIndex || '', dataIndex }, row)
      }
      ret.sorter = sortQuotaFn(item, 'asc')
    }
    return ret
  })
}

const CustomQuotaTable = ({
  content,
  report,
}: {
  content: any;
  report: any;
}) => {
  let columns = []
  let data = []
  if (content.type === 'positionChange') {
    data = report.positionChangeData || []
    columns = getPositionChangeColumns(data)
  } else if (content.type === 'assetStructur') {
    data = report.assetsStructure || []
    columns = getAssetStructureColumns(data)
  } else {
    const quotasList = navQuotasIgnoreRank.filter(item => content.quotas.includes(item.dataIndex))
    columns = getTableColumns(quotasList)
    data = report.funds
  }
  columns = columns.map(item => {
    const ret = {
      ...item,
      key: item.dataIndedx,
    }
    if (item.format) {
      const { format, dataIndex } = item
      const formatData = { format, dataIndex, value: dataIndex }
      ret.render = (text, row) => {
        return renderFundQuota(formatData, row)
      }
      ret.sorter = sortQuotaFn(formatData, 'asc')
    }
    return ret
  })
  columns.unshift({
    title: '基金',
    dataIndex: 'name',
    width: 150,
    fixed: 'left',
    filters: report.funds.map(item => {
      return {
        text: item.name,
        value: item._id,
      }
    }),
    onFilter: (value, record) => record._id === value,
  })
  return (
    <Table
      columns={columns}
      dataSource={data}
      pagination={false}
      size="small"
      scroll={{ x: 1300, y: 260 }}
    />
  )
}

export default CustomQuotaTable
