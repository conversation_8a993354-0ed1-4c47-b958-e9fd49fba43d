import { Card, Select, Tabs, Divider, Tooltip, Tag, Button, Form, Modal } from 'antd'
import { DeleteOutlined, PlusOutlined, SaveOutlined, DownOutlined, UpOutlined } from '@ant-design/icons'
import React, { useState } from 'react'
import { useBoolean } from '@umijs/hooks'
import { FormProps } from 'antd/es/form'
import StandardFormRow from '@/components/FilterPanel/StandardFormRow'
import TagSelect from '@/components/FilterPanel/TagSelect'
const FormItem = Form.Item
const { TabPane } = Tabs

interface ComponentProps extends FormProps {
  filters?: any;
  filterValues?: any;
  onChange?: any;
  children: any;
}

export interface FilterItem {
  name: string;
  type?: string;
  formKey: string;
  data?: any;
  tabs?: any;
  componentProps?: any;
}

const templateFilters: FilterItem[] = [{
  name: '常用图表',
  formKey: 'tableTemplate',
  data: [
    {
      name: '相关系数',
      value: 'correlation',
    },
    {
      name: '资产类型结构',
      value: 'assetStructur',
    },
    {
      name: '月度收益',
      value: 'monthlyReturn',
    },
    {
      name: '持仓变动监测',
      value: 'positionChange',
    },
  ],
}, {
  name: '指标曲线',
  formKey: 'seriesTemplate',
  data: [
    {
      name: '收益率曲线',
      value: 'retSeries',
    },
    {
      name: '净值曲线',
      value: 'navSeries',
    },
    {
      name: '总资产',
      value: 'assetSeries',
    },
    {
      name: '净资产',
      value: 'netAssets',
    },
    {
      name: 'PE',
      value: 'peSeries',
    },
    {
      name: 'PB',
      value: 'pbSeries',
    },
    {
      name: '票面利率',
      value: 'couponRateSeries',
    },
    {
      name: '到期收益率',
      value: 'ytmSeries',
    },
    {
      name: '久期',
      value: 'durationSeries',
    },
    {
      name: '剩余久期',
      value: 'residualDurationSeries',
    },
    {
      name: '资产类型结构',
      value: 'assetStructureSeries',
    },
  ],
}]

const SelectReportItemTemplate: React.FC<ComponentProps> = props => {
  const {
    filterValues,
    onChange,
    children,
  } = props
  const [form] = Form.useForm()
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const handleOpenModal = () => {
    form.resetFields()
    setVisibleTrue()
  }
  const handleSubmit = (allValues) => {
    setVisibleFalse()
    const values = Object.keys(allValues).reduce((out, key) => {
      return out.concat(allValues[key])
    }, [])
    const allData = templateFilters.reduce((out, item) => {
      return out.concat(item.data)
    }, [])
    const results = allData
      .filter(item => values.includes(item.value))
      .map(item => {
        return {
          type: item.value,
          name: item.name,
        }
      })
    onChange(results)
  }

  const renderFilterItem = (filterItem: FilterItem) => {
    return (
      <FormItem name={filterItem.formKey}>
        <TagSelect expandable={filterItem.data.length > 10}>
          {filterItem.data.map(item => (
            <TagSelect.Option key={item.value} value={item.value}>
              {item.name}
            </TagSelect.Option>
          ))}
        </TagSelect>
      </FormItem>
    )
  }

  return (
    <>
      <div style={{ display: 'inline-block', width: '100%' }} onClick={handleOpenModal}>
        {children}
      </div>
      <Modal
        title={
          <div>
            选择模板
          </div>
        }
        width={700}
        visible={visible}
        onCancel={setVisibleFalse}
        footer={[
          <Button
            type="primary"
            onClick={() => form.submit()}
          >
            确定
          </Button>,
        ]}
      >
        <Form
          layout="inline"
          form={form}
          initialValues={filterValues}
          onFinish={handleSubmit}
        >
          {templateFilters.map((filterItem: FilterItem) => {
            if (filterItem.type === 'tabs') {
              return (
                <StandardFormRow title={filterItem.name} block style={{ paddingBottom: 11 }}>
                  <Tabs animated={false} size="small" defaultActiveKey="1" onChange={() => {}}>
                    {filterItem.tabs.map((tab: FilterItem, index: number) => {
                      return (
                        <TabPane tab={tab.name} key={`${index + 1}`}>
                          {renderFilterItem(tab)}
                        </TabPane>
                      )
                    })}
                  </Tabs>
                </StandardFormRow>
              )
            }
            return (
              <StandardFormRow
                key={filterItem.name}
                title={filterItem.name}
                block
                style={{ paddingBottom: 11 }}
              >
                {renderFilterItem(filterItem)}
              </StandardFormRow>
            )
          })}
        </Form>
      </Modal>
    </>
  )
}

export default SelectReportItemTemplate
