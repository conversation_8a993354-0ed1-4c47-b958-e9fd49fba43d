import React from 'react'
import {
  Button,
  Input,
  Select,
  Form,
  Modal,
} from 'antd'
import { useBoolean } from '@umijs/hooks'
import { navQuotasIgnoreRank } from '@/utils/quotas'

const ReportTableModal = ({
  data,
  children,
  onChange,
}: {
  data: any,
  children: any,
  onChange: any,
}) => {
  const [form] = Form.useForm()
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const handleSubmit = values => {
    setVisibleFalse()
    onChange(values)
    form.resetFields()
  }
  return (
    <>
      <div style={{ display: 'inline-block', width: '100%' }} onClick={setVisibleTrue}>
        {children}
      </div>
      <Modal
        title={data._id ? '编辑图表' : '创建图表'}
        visible={visible}
        onCancel={setVisibleFalse}
        footer={[
          <Button
            type="primary"
            onClick={() => form.submit()}
          >
            保存
          </Button>,
        ]}
      >
        <Form
          hideRequiredMark
          form={form}
          layout="vertical"
          initialValues={data}
          onFinish={handleSubmit}
        >
          <Form.Item
            label="名称"
            name="name"
            rules={[
              {
                required: true,
                message: '请输入名称',
              },
            ]}
          >
            <Input placeholder="名称"/>
          </Form.Item>
          <Form.Item
            label="指标"
            name="quotas"
            rules={[
              {
                required: true,
                message: '请选择指标',
              },
            ]}
          >
            <Select
              showSearch
              mode="multiple"
              placeholder="选择指标"
              style={{
                width: '100%',
              }}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {navQuotasIgnoreRank.map(item => {
                return (
                  <Select.Option value={item.dataIndex}>
                    {item.title}
                  </Select.Option>
                )
              })}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default ReportTableModal
