import React from 'react'
import _ from 'lodash'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Popconfirm,
  Button,
  Tooltip,
  List,
  Row,
  Col,
} from 'antd'
import t from '@/utils/t'
import SelectReportItemTemplate from './SelectReportItemTemplate'
import ReportTableModal from './ReportTableModal'
import styles from './ReportFormItem.less'

const ReportFormItem = ({
  onChange,
  value,
} : {
  onChange?: any,
  value?: any,
}) => {
  const data = value || []
  const handleRemoveReportItem = index => () => {
    const newData = data.filter((item, idx) => index !== idx)
    onChange(newData)
  }
  const handleAddReportTemplates = templates => {
    const types = data.map(item => item.type).filter(Boolean)
    const newTemplates = templates
      .filter(item => !types.includes(item.type))
    if (newTemplates.length) {
      onChange([
        ...data,
        ...templates,
      ])
    }
  }
  const handleAddReportItem = reportItem => {
    const newData = [...data, reportItem]
    onChange(newData)
  }
  const handleEditReportItem = index => (reportItem) => {
    const newData = data.map((item, idx) => {
      if (index !== idx) {
        return item
      }
      return {
        ...item,
        ...reportItem,
      }
    })
    onChange(newData)
  }
  return (
    <List
      className={styles.reportItem}
      size="small"
      header={<div>图表({data.length})</div>}
      footer={
        <Row>
          <Col span={12}>
            <ReportTableModal onChange={handleAddReportItem} data={{ name: '', quotas: [] }}>
              <Button style={{ width: '100%', marginTop: 10 }} type="dashed" icon={<PlusOutlined />}>
                添加图表
              </Button>
            </ReportTableModal>
          </Col>
          <Col span={12}>
            <SelectReportItemTemplate onChange={handleAddReportTemplates}>
              <Button style={{ width: '100%', marginTop: 10 }} type="dashed" icon={<PlusOutlined />}>
                图表模板
              </Button>
            </SelectReportItemTemplate>
          </Col>
        </Row>
      }
      bordered
      dataSource={data || []}
      renderItem={(item, index) => {
        const actions = [
          <Popconfirm
            title={`${t('portfolio.delTip')}${item.name}${t('portfolio.questionEnd')}？`}
            onConfirm={handleRemoveReportItem(index)}
            onCancel={() => {}}
            okText={t('portfolio.confirm')}
            cancelText={t('portfolio.cancel')}
          >
            <Tooltip title="删除">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>,
        ]
        if (!item.type) {
          const quotas = item.quotas.map(quota => quota.value || quota)
          actions.unshift((
            <Tooltip title="编辑">
              <ReportTableModal onChange={handleEditReportItem(index)} data={{ ...item, quotas }}><EditOutlined/></ReportTableModal>
            </Tooltip>
          ))
        }
        return (
          <List.Item
            actions={actions}
          >
            <span>{item.name}</span>
          </List.Item>
        )
      }}
    />
  )
}

export default ReportFormItem
