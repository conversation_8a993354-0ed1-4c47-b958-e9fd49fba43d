import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryReports(params: TableListParams) {
  return request('/api/reports', {
    params,
  })
}

export async function queryReport(id: string) {
  return request(`/api/reports/${id}`)
}

export async function deleteReport(id: string) {
  return request(`/api/reports/${id}`, {
    method: 'delete',
  })
}

export async function updateReport(id: string, data: any) {
  return request(`/api/reports/${id}`, {
    method: 'put',
    data,
  })
}

export async function copyReport(id: string) {
  return request(`/api/reports/${id}/copy`, {
    method: 'post',
  })
}

export async function createReport(data: any) {
  return request(`/api/reports`, {
    method: 'post',
    data,
  })
}

export async function queryReportData(id: string) {
  return request(`/api/reports/${id}/data`)
}
