import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import Link from 'umi/link'
import { connect } from 'dva'
import moment from 'moment'
import { DeleteOutlined, EditOutlined, PlusOutlined, CopyOutlined } from '@ant-design/icons'
import {
  Table,
  Button,
  Tooltip,
  Divider,
  Popconfirm,
  Modal,
  Input,
  Card,
  Form,
  notification,
  Row,
  Col,
  DatePicker,
  Spin,
} from 'antd'
import { queryReports, updateReport, createReport, deleteReport, copyReport } from './service'
import t from '@/utils/t'
import FundListFormItem from '@/components/FundListFormItem'
import ReportFormItem from './components/ReportFormItem'
import styles from './style.less'

const Search = Input.Search
const InputGroup = Input.Group

interface ReportItem {
  _id?: string,
  name: string,
  description: string,
  funds: {
    name: string,
    _id: string,
  }[],
  content: any,
  startDate?: string,
  endDate?: string,
  endToNow?: boolean,
  updated_at?: string,
}

const List = ({
  currentUser,
}: {
  currentUser: any,
}) => {
  const [form] = Form.useForm()
  const { setFieldsValue } = form
  const initialFormValue = {
    name: '', description: '', contents: [],
    startDate: undefined, endDate: undefined, endToNow: false, funds: [],
  }
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState({})
  const [refreshCount, setRefreshCount] = useState(0)
  const [input, setInput] = useState('')
  const [endToNow, setEndToNow] = useState(false)
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    return queryReports(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const onSaveReportSuccess = () => {
    setVisibleFalse()
    setRefreshCount(refreshCount + 1)
    notification.success({
      message: '保存成功',
    })
  }
  const { loading: updatingReport, run: doUpdateReport} = useRequest((id, data) => {
    return updateReport(id, data)
  }, {
    manual: true,
    onSuccess: onSaveReportSuccess,
  })
  const { loading: creatingReport, run: doCreateReport} = useRequest((data) => {
    return createReport(data)
  }, {
    manual: true,
    onSuccess: onSaveReportSuccess,
  })
  const { loading: copyingReport, run: doCopyReport} = useRequest((id) => {
    return copyReport(id)
  }, {
    manual: true,
    onSuccess: () => {
      setVisibleFalse()
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '复制成功',
      })
    },
  })
  const { run: doDeleteReport } = useRequest((id) => {
    return deleteReport(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const handleEndToNowClick = () => {
    setFieldsValue({
      endDate: moment(),
      endToNow: true,
    })
    setEndToNow(true)
  }
  const handleEndDateChange = () => {
    setFieldsValue({
      endToNow: false,
    })
    setEndToNow(false)
  }
  const handleOpenModal = (record: ReportItem) => () => {
    setCurrentItem(record)
    setVisibleTrue()
    const newFormValues = {
      ...record,
      dateRange: record._id ? [
        moment(new Date(record.startDate)),
        moment(new Date(record.endDate)),
      ] : [],
    }
    if (record.startDate) {
      newFormValues.startDate = moment(new Date(record.startDate))
    }
    if (record.endDate) {
      newFormValues.endDate = moment(new Date(record.endDate))
    }
    if (record.endToNow) {
      newFormValues.endDate = moment()
    }
    setFieldsValue(newFormValues)
    setEndToNow(record.endToNow)
  }
  const handleClickSave = (values: any) => {
    const newValues = {
      ...values,
      startDate: values.startDate.format('YYYY-MM-DD'),
      endDate: values.endDate.format('YYYY-MM-DD'),
    }
    if (currentItem._id) {
      doUpdateReport(currentItem._id, newValues)
    } else {
      doCreateReport(newValues)
    }
  }

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text, record) => {
        return <Link to={`/report/${record._id}`}>{text}</Link>
      },
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      render: text => moment(new Date(text)).format('YYYY-MM-DD'),
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      render: (text, record) => {
        if (record.endToNow) {
          return '至今'
        }
        return moment(new Date(text)).format('YYYY-MM-DD')
      },
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      align: 'right',
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        if (currentUser._id !== record.authorId) {
          return false
        }
        return (
          <>
            <Tooltip title="编辑">
              <EditOutlined onClick={handleOpenModal(record)} />
            </Tooltip>
            <Divider type="vertical" />
            <Tooltip title="复制">
              <CopyOutlined onClick={() => doCopyReport(record._id)} />
            </Tooltip>
            <Divider type="vertical" />
            <Popconfirm
              title={`${t('portfolio.delTip')}${record.name}${t('portfolio.questionEnd')}？`}
              onConfirm={() => { doDeleteReport(record._id) }}
              onCancel={() => {}}
              okText={t('portfolio.confirm')}
              cancelText={t('portfolio.cancel')}
            >
              <Tooltip title="删除">
                <DeleteOutlined />
              </Tooltip>
            </Popconfirm>
          </>
        )
      },
    },
  ]

  return (
    <div>
      <Card
        title={
          <>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
        extra={
          <>
            <a onClick={
              handleOpenModal(initialFormValue)
            }>
              <PlusOutlined />
              新建报表
            </a>
          </>
        }
      >
        <Table size="small" columns={columns} rowKey="_id" {...tableProps} />
      </Card>
      <Modal
        title={
          <>
            <span>{currentItem && currentItem._id ? '编辑报表' : '新建报表'}</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={700}
        footer={[
          <Button
            type="primary"
            loading={updatingReport || creatingReport || copyingReport}
            onClick={() => form.submit()}
          >
            保存
          </Button>,
        ]}
      >
        <Spin spinning={updatingReport || creatingReport || copyingReport}>
          <Form
            hideRequiredMark
            form={form}
            layout="vertical"
            initialValues={initialFormValue}
            onFinish={handleClickSave}
          >
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  label="名称"
                  name="name"
                  rules={[
                    {
                      required: true,
                      message: '请输入名称',
                    },
                  ]}
                >
                  <Input placeholder="名称"/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="描述"
                  name="description"
                >
                  <Input placeholder="描述"/>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} className={styles.dateRange}>
              <Col lg={12} md={12}>
                <Form.Item
                  label="开始时间"
                  name="startDate"
                  rules={[
                    {
                      required: true,
                      message: '请选择开始时间',
                    },
                  ]}
                >
                  <DatePicker style={{ width: '100%' }}/>
                </Form.Item>
              </Col>
              <Col lg={12} md={12}>
                <div className="ant-col ant-form-item-label">
                  <label htmlFor="endDate">结束时间</label>
                </div>
                <InputGroup compact>
                  <Form.Item
                    name="endDate"
                    rules={[
                      {
                        required: true,
                        message: '请选择结束时间',
                      },
                    ]}
                  >
                    <DatePicker style={{ width: 255 }} onChange={handleEndDateChange}/>
                  </Form.Item>
                  <Button
                    onClick={handleEndToNowClick}
                    className={{ active: endToNow }}
                  >
                    至今
                  </Button>
                </InputGroup>
              </Col>
            </Row>
            <Form.Item
              name="funds"
              rules={[
                {
                  validator: (rule, values) => {
                    if (!values || values.length === 0) {
                      return Promise.reject('请添加组合')
                    } else {
                      return Promise.resolve()
                    }
                  },
                },
              ]}
            >
              <FundListFormItem />
            </Form.Item>
            <Form.Item
              name="contents"
              rules={[
                {
                  validator: (rule, values) => {
                    if (!values || values.length === 0) {
                      return Promise.reject('请添加图表')
                    } else {
                      return Promise.resolve()
                    }
                  },
                },
              ]}
            >
              <ReportFormItem />
            </Form.Item>
          </Form>
        </Spin>
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
