import _ from 'lodash'
import { PlusOutlined } from '@ant-design/icons'
import { Card, Input, Tooltip, Button, Space, Switch, Table } from 'antd'
import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { ModelState } from '@/models/manager'
import FilterPanel from '@/components/FilterPanel'
import { styleTypeManagerMap, compositTypeMap } from '@/utils/kymDefMapping'
import StandardTable, {
  StandardTableColumnProps,
  TableListItem,
  TableListParams,
} from '@/components/StandardTable'
import SelectInvestPoolModal from '@/components/investpool/SelectInvestPoolModal'
import ExportData from '@/components/ExportData'
import SelectedFundButton from '@/components/SelectedFundButton'
import { getToken } from '@/utils/utils'

import styles from './style.less'

const { Search } = Input

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  managerListData: any;
  location: any;
  managerFilters?: any;
  filterValues?: any;
  currentInvestPool?: any;
}

interface ComponentState {
  selectedRows: TableListItem[];
  isPage?: boolean;
  defaultIds?: string;
  input?: string;
  filterValues?: any;
  isAll: boolean;
}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    manager,
    loading,
    fund,
  }: {
    manager: ModelState;
    fund: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    managerListData: manager.managerListData,
    managerFactorScoreData: manager.managerFactorScoreData,
    managerFilters: fund.managerFilters,
    loading: loading.effects['manager/fetchFactorScore'],
  }),
)
class Managers extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    const {
      location: {
        pathname,
      },
    } = props
    const filterData = localStorage.getItem('qutke:filters:manager')
    const state: ComponentState = {
      selectedRows: [],
      isPage: /\/page\//.test(pathname),
      filterValues: {},
      isAll: false,
    }

    let filterValues = {}
    if (props.filterValues) {
      filterValues = props.filterValues
    } else {
      if (filterData) {
        filterValues = JSON.parse(filterData)
      }
      const factorSchemaFilter = (props.managerFilters || []).find(item => item.filterId === 'factorSchema')
      if (factorSchemaFilter) {
        if (!filterValues.factorSetting) {
          filterValues.factorSetting = factorSchemaFilter.tabs[0].defaultValues
        }
      }
    }
    state.filterValues = filterValues
    this.state = state
  }

  componentDidMount() {
    this.loadData({})
  }

  loadData(params: Partial<TableListParams>) {
    const { dispatch } = this.props
    const { filterValues, isAll } = this.state
    params.type = 'mutual'
    params.isAll = isAll ? 'Y' : 'N'
    const tableFormKeys = ['quotas', 'industryRatio', 'industryBoardRatio', 'stockRatio', 'factorDims']
    const filterParams = Object.keys(filterValues).reduce((out, key) => {
      const values = filterValues[key]
      if (values !== undefined) {
        if (Array.isArray(values) && values.length) {
          if (tableFormKeys.includes(key)) {
            const vals = values.filter(item => item.valueRange).map(item => {
              const ret = _.omit(item, ['key'])
              const refOption = ret._refOption
              ret.valueRange = ret.valueRange.map(item => {
                if (!item) {
                  return item
                }
                if (refOption.format === 'number') {
                  return item
                }
                return item / 100
              })
              return ret
            })
            if (vals.length) {
              out[key] = JSON.stringify(vals)
            }
          } else {
            out[key] = values.join(',')
          }
        } else {
          if (key === 'factorSetting') {
            out[key] = JSON.stringify(values)
          } else {
            out[key] = values
          }
        }
      }
      return out
    }, {})
    dispatch({
      type: 'manager/fetchFactorScore',
      payload: {
        ...params,
        ...filterParams,
        input: this.state.input,
      },
    })
  }

  handleStandardTableChange = (params: Partial<TableListParams>) => {
    this.loadData(params)
  };

  handleSeachInput = (value: string) => {
    this.setState({ input: value }, () => {
      this.loadData({
        page: 1,
      })
    })
  };

  handleIsAllChange = () => {
    this.setState({ isAll: !this.state.isAll }, () => {
      this.loadData({
        page: 1,
      })
    })
  }

  handleSelectRows = (rows: TableListItem[]) => {
    this.setState({
      selectedRows: rows,
    })
  };

  handleFilterChange = (filterValues: any) => {
    this.setState({ filterValues }, () => {
      this.loadData({ page: 1 })
    })
    localStorage.setItem('qutke:filters:manager', JSON.stringify(filterValues))
  };

  addFundsToInvestPool = (rows: TableListItem[]) => {
    const { selectedRows } = this.state
    const ids = selectedRows.map(item => item._id)
    const investPoolId = rows[0]._id
    this._addFundsToInvestPool(investPoolId, ids)
  };

  _addFundsToInvestPool = (investPoolId: string, ids: string[]) => {
    const { dispatch } = this.props
    dispatch({
      type: 'investpool/addFunds',
      payload: {
        id: investPoolId,
        data: {
          ids,
        },
      },
    })
  };

  render() {
    const { managerFactorScoreData, loading, managerFilters, dispatch, currentInvestPool } = this.props

    const { selectedRows, filterValues } = this.state

    const renderSingleScore = (score: number) => {
      if (score === undefined || score === null) {
        return '-'
      }
      return score.toFixed(2)
    }
    const factorTierData = [{
      title: '收益类',
      dataIndex: 'incomeFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '风险类',
      dataIndex: 'riskFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '归因类',
      dataIndex: 'attributionFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '策略类',
      dataIndex: 'strategyFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '基金公司类',
      dataIndex: 'companyFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '基金经理类',
      dataIndex: 'managerFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '持仓类',
      dataIndex: 'positionFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '综合得分',
      dataIndex: 'totalFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '综合得分排名',
      dataIndex: 'totalFactorScoreRank',
      format: 'zeroNotNumber',
      width: 120,
    }].map(item => {
      return {
        width: 100,
        ...item,
        sorter: true,
      }
    })
    const columns: StandardTableColumnProps[] = [
      {
        title: '姓名',
        dataIndex: 'name',
        fixed: 'left',
        width: 80,
        render: (text, record) => (
          <a
            href={`/manager/persona/${record._id}/factor_evaluation`}
            rel="noopener noreferrer"
            target="_blank"
          >
            {record.name}
          </a>
        ),
      },
      {
        title: '基金公司',
        dataIndex: 'company_abbr_name',
        format: 'text',
        sorter: true,
        width: 100,
      },
      {
        title: '资产分类',
        dataIndex: 'asset_type',
        width: 100,
        sorter: true,
      }, {
        title: '风格分类',
        dataIndex: 'style_type',
        width: 100,
        sorter: true,
      },
      ...factorTierData,
    ]
    const handleDownload = () => {
      const token = getToken()
      const factorSetting = filterValues.factorSetting
      let href = `/api/factorschemas/scoredata/manager?token=${token.slice(7)}`
      if (factorSetting) {
        href = `${href}&factorSetting=${JSON.stringify(factorSetting)}`
      }
      window.open(href)
    }
    return (
      <>
        {managerFilters && !!managerFilters.length && (
          <FilterPanel
            filters={managerFilters}
            filterValues={filterValues || {}}
            onChange={this.handleFilterChange}
            dispatch={dispatch}
            currentInvestPool={currentInvestPool}
            investPoolDataType="manager"
          />
        )}
        <Card
          bordered={false}
          title={
            <>
              <Search
                style={{ width: '300px' }}
                placeholder="按回车进行搜索"
                onSearch={this.handleSeachInput}
              />
            </>
          }
          extra={
            <Space>
              <span>显示全部经理</span>
              <Switch size="small" onChange={this.handleIsAllChange} />
              {!!selectedRows.length &&
              <SelectedFundButton
                isManager
                selectedRows={selectedRows}
                onUnselectAll={() => {
                  this.handleSelectRows([])
                }}
              />}
              <SelectInvestPoolModal disabled={!selectedRows.length} dataType="manager" onSelectRow={this.addFundsToInvestPool}>
                <Tooltip title="添加到静态跟踪列表">
                  <Button
                    ghost
                    disabled={!selectedRows.length}
                    type="primary"
                    icon={<PlusOutlined />}
                    size="small"
                  >
                    跟踪列表
                  </Button>
                </Tooltip>
              </SelectInvestPoolModal>
              <ExportData onClick={handleDownload} title="导出因子得分"/>
            </Space>
          }
        >
          <div className={styles.tableList}>
            <StandardTable
              bordered
              selectedRows={selectedRows}
              loading={loading}
              data={managerFactorScoreData}
              columns={columns}
              onSelectRow={this.handleSelectRows}
              onChange={this.handleStandardTableChange}
              size="small"
              rowKey="_id"
              scroll={{ x: 1200 }}
            />
          </div>
        </Card>
      </>
    )
  }
}

@connect(
  ({
    loading,
  }: {
    loadingOptions: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    loadingOptions: loading.effects['fund/fetchQueryOptions'],
  }),
)
class LoadingWrapper extends Component<ComponentProps, ComponentState> {
  render() {
    const columns = [{
      title: '姓名',
      dataIndex: 'name',
    }, {
      title: '收益类',
      dataIndex: 'incomeFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '风险类',
      dataIndex: 'riskFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '归因类',
      dataIndex: 'attributionFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '策略类',
      dataIndex: 'strategyFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '基金公司类',
      dataIndex: 'companyFactorScore',
      format: 'zeroNotNumber',
      width: 100,
    }, {
      title: '基金经理类',
      dataIndex: 'managerFactorScore',
      format: 'zeroNotNumber',
      width: 100,
    }, {
      title: '持仓类',
      dataIndex: 'positionFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '综合得分',
      dataIndex: 'totalFactorScore',
      format: 'zeroNotNumber',
      width: 90,
    }]
    if (this.props.loadingOptions) {
      return <Table columns={columns} loading/>
    }
    return <Managers {...this.props}/>
  }
}
export default LoadingWrapper
