import React, { useEffect } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import router from 'umi/router'
import Link from 'umi/link'
import { UserOutlined } from '@ant-design/icons'
import { Empty, Tabs, Card, Row, Col, Avatar, Table, Breadcrumb, Select, Affix } from 'antd'
import { GridContent } from '@ant-design/pro-layout'
import PageLoading from '@/components/PageLoading'
import Chart from '@/components/Chart/Chart'
import IndividualRating from '@/pages/persona/components/IndividualRating'
import { assetClassMap, styleTypeMap, compositTypeMap } from '@/utils/kymDefMapping'
import styles from './style.less'

const { TabPane } = Tabs
const { Option } = Select

interface WrapperProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentManager: any;
  match: any;
  location: any;
  searchOptions: any;
}

const Wrapper: React.FC<WrapperProps> = props => {
  const {
    dispatch,
    children,
    location: { pathname },
    match: {
      params: { id },
    },
    currentManager,
    loading,
    searchOptions,
    currentUser,
  } = props
  useEffect(() => {
    dispatch({
      type: 'fund/fetchManager',
      payload: {
        id,
      },
    })
    dispatch({
      type: 'manager/fetchBenchmarks',
      payload: {
        type: 'public'
      }
    })
  }, [])
  if (!currentManager && loading) {
    return <PageLoading />
  }
  if (!currentManager) {
    return (
      <Empty
        style={{ padding: '100px' }}
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="你查看的基金经理不存在"
      />
    )
  }
  const activeKey = pathname.split('/').pop()
  const handleTabChange = (activeKey: string) =>
    router.push(`/manager/persona/${currentManager._id}/${activeKey}`)
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      align: 'right',
      render: (val: string, record: any, index: number) => {
        const avatar = (
          <div style={{ textAlign: 'center' }}>
            <Avatar
              shape="square"
              size={90}
              icon={<UserOutlined />}
              src={currentManager.avatar}
              style={{
                background: 'none',
                verticalAlign: 'middle',
              }}
            />
            <div style={{ marginTop: '15px' }}>{currentManager.name}</div>
          </div>
        )
        if (index !== 0) {
          return record.value
        }
        return {
          children: avatar,
          props: {
            rowSpan: 6,
          },
        }
      },
    },
  ]
  const data = [
    {
      name: '个人信息',
    },
    {
      name: '资产类别',
      value: assetClassMap[currentManager.asset_class],
    },
    {
      name: '风格类别',
      value: styleTypeMap[currentManager.style_type],
    },
    {
      name: '综合类别',
      value: compositTypeMap[currentManager.composite_rating],
    },
    {
      name: '服务公司',
      value: currentManager.company_abbr_name || currentManager.company_name,
    },
    {
      name: '代表产品',
      value: currentManager.ref_fund_name,
    },
  ]
  const chartQuotas = [
    {
      name: '基金经理个人属性',
      value: 'manager_property',
    },
    {
      name: '基金公司业务发展',
      value: 'development',
    },
    {
      name: '核心团队稳定性',
      value: 'team_stability',
    },
    {
      name: '历史业绩',
      value: 'invest_performance',
    },
    {
      name: '投资流程抗压性',
      value: 'invest_process_resistence',
    },
    {
      name: '投资业绩可解释性',
      value: 'invest_performance_explicable',
    },
    {
      name: '投资理念一惯性',
      value: 'invest_philosophy_consistency',
    },
  ].map(item => {
    return {
      ...item,
      tab: item.value.toLowerCase(),
    }
  })
  const refFundTabs = [
    {
      name: '基金经理',
      tab: 'persona',
    },
    {
      name: '基金公司',
      tab: 'company',
    },
    {
      name: '业绩概况',
      tab: 'invest_performance',
    }, {
      name: '资产配置',
      tab: 'asset_structure',
    }, {
      name: '权益持仓',
      tab: 'stock_analyze',
    }, {
      name: '债券持仓',
      tab: 'bond_analyze',
    }, {
      name: '持仓明细',
      tab: 'position_series_query',
    }, {
      name: '归因分析',
      tab: 'stock_attribution',
    }, {
      name: '相关文档',
      tab: 'ref_due_doc',
    }]

  let pageMenusIds = currentUser && currentUser.menus.map(item => item.menuId)
  //menuid e基金经理详情因子评价的id
  if (pageMenusIds && pageMenusIds && pageMenusIds.includes('e')) {
    refFundTabs.unshift({
      name: '因子评价',
      tab: 'factor_evaluation',
    })
  }
  const getRatingData = item => {
    return chartQuotas.reduce((out, quota) => {
      const key = quota.value
      out[key] = item && item[key] || 0
      return out
    }, {})
  }
  const chartData = [
    {
      name: '基金经理',
      ...getRatingData(currentManager),
    },
    {
      name: '同业平均',
      ...getRatingData(currentManager.portraitInfoAvg),
    },
  ]
  const series = chartData.map(item => {
    return {
      pointPlacement: 'on',
      name: item.name,
      data: chartQuotas.map(quota => item[quota.value]),
    }
  })
  const categories = chartQuotas.map(item => item.name)
  const chartConfig = {
    chart: {
      polar: true,
      type: 'line',
      height: 280,
    },
    pane: {
      size: '80%',
    },
    tooltip: {
      shared: true,
      pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.2f}</b><br/>',
    },
    xAxis: {
      categories,
      tickmarkPlacement: 'on',
      lineWidth: 0,
      labels: {
        useHTML: true,
        formatter: function formatter() {
          const labelValue = this.value + ''
          const start = labelValue.slice(0, 4)
          const end = labelValue.slice(4)
          if (!end) {
            return `<span>${start}</span>`
          }
          return `<span>${start}</span><br/><span>${end}</span>`
        },
      },
    },
    yAxis: {
      gridLineInterpolation: 'polygon',
      min: 0,
      max: 5,
      labels: {
        format: '{value}',
      },
    },
    series,
  }
  return (
    <GridContent>
      <Row gutter={16}>
        {false && <Col lg={6} md={24}>
          <Card title="简介">
            <Table
              showHeader={false}
              pagination={false}
              columns={columns}
              dataSource={data}
              size="small"
            />
          </Card>
          <Card title="基金经理描述">
            <div>{currentManager.integrated_evaluation}</div>
          </Card>
          <Card
            title={<IndividualRating title="综合评分：" rating={currentManager.integrated_grade} />}
          >
            <Chart options={chartConfig} />
          </Card>
        </Col>}
        <Col lg={24} md={24}>
          <Breadcrumb className="breadcrumb">
            <Breadcrumb.Item>
              <a href={`/manager/persona`}>
                基金经理
              </a>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{currentManager.name}</Breadcrumb.Item>
          </Breadcrumb>
          <Affix offsetTop={0}>
            <Card className={styles.navTabs}>
              <Tabs
                animated={false}
                activeKey={activeKey}
                className={styles.tabs}
                onTabClick={handleTabChange}
                tabBarExtraContent={
                  <Select
                    showSearch
                    placeholder="输入名称搜索"
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }}
                    onChange={value => {
                      const url = `/manager/persona/${value}/factor_evaluation`
                      window.open(url)
                    }}
                    style={{ width: '200px' }}
                  >
                    {searchOptions.managers.map(item => {
                      return (
                        <Option value={`${item._id}`}>
                          {item.name}
                        </Option>
                      )
                    })}
                  </Select>
                }
              >
                {refFundTabs.map(item => (
                  <TabPane tab={item.name} key={item.tab} />
                ))}
              </Tabs>
            </Card>
          </Affix>
          <Card className={styles.navTabs}>
            <div className={styles.container}>
              {children}
            </div>
          </Card>
        </Col>
      </Row>
    </GridContent>
  )
}

export default connect(
  ({
    fund,
    loading,
    user,
  }: {
    fund: any;
    user: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentManager: fund.currentManager,
    searchOptions: user.searchOptions,
    loading: loading.models.fund,
    currentUser: user.currentUser,
  }),
)(Wrapper)
