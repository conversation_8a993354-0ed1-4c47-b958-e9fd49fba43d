import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import { connect } from 'dva'
import {
  Table,
  Space,
  Spin,
  Tabs,
  Input,
} from 'antd'
import _ from 'lodash'
import { queryRoleMatrixOverview } from './service'
import ExportData from '@/components/ExportData'
import { exportMultiTableAsExcel } from '@/utils/exportAsExcel'
import buildTableColumn from '@/utils/buildTableColumn'

const { TabPane } = Tabs
const { Search } = Input

const getTableProps = (data, input, searchCols) => {
  const titles = data[0]
  let dataSource = data.slice(1)
  if (input && searchCols && searchCols.length) {
    dataSource = dataSource.filter(item => {
      return searchCols.some(col => {
        const value = item[col]
        const reg = new RegExp(input, 'i')
        return reg.test(value)
      })
    })
  }
  const filterNames = ['用户类别', '网段', '公司', '角色', '用户状态']
  const columns = titles.map((item, index) => {
    const ret = {
      title: item,
      dataIndex: index,
      hasSorter: true,
    }
    if (index === 0) {
      ret.fixed = 'left'
    }
    let width = item.length * 20
    if (width < 80) {
      width = 80
    }
    ret.width = width
    if (filterNames.includes(item)) {
      ret.width = 110
      ret.onFilter = (value: string, record) => value === record[index]
      ret.filters = _.uniq(dataSource.map(record => record[index])).map(it => {
        return {
          text: it,
          value: it,
        }
      })
    }
    return ret
  }).map(buildTableColumn)
  return {
    columns,
    dataSource,
    scroll: { x: 700 },
  }
}

const Overview = ({
  currentUser,
  location,
}: {
  currentUser: any,
  location: any,
}) => {
  const [input, setInput] = useState('')
  const { data = [], loading } = useRequest(() => {
    return queryRoleMatrixOverview()
  })
  const handleDownload = () => {
    const tables = data.map(item => {
      return {
        name: item.name,
        ...getTableProps(item.data),
      }
    })
    exportMultiTableAsExcel('权限矩阵', tables)
  }
  const searchColMap = {
    用户信息: [2, 3, 4],
    组合权限信息: [0, 1, 2, 3],
    角色功能一览: [0, 1],
  }
  return (
    <div>
      <Spin spinning={loading}>
        <Tabs
          animated={false}
          onChange={() => setInput('')}
          tabBarExtraContent={
            <Space>
              <Search
                style={{ width: '300px' }}
                placeholder="按回车进行搜索"
                onSearch={setInput}
              />
              <ExportData title="导出" onClick={handleDownload}/>
            </Space>
          }
        >
          {data.map(item => {
            return (
              <TabPane tab={item.name} key={item.name}>
                <Table size="small" {...getTableProps(item.data, input, searchColMap[item.name])}/>
              </TabPane>
            )
          })}
        </Tabs>
      </Spin>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(Overview)
