import React, { useRef, useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>confirm, But<PERSON>, message, Switch } from 'antd'
import { EditOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import ProTable from '@ant-design/pro-table';
import formatter from '../../../utils/formatter'
import RoleForm from '@/components/RoleForm'
import { del, query, edit } from '@/services/system/role'
import { connect } from 'dva'
import { router } from 'umi'

interface ActionType {
  reload: (resetPageIndex?: boolean) => void;
  reloadAndRest: () => void;
  reset: () => void;
  clearSelected?: () => void;
  startEditable: (rowKey: Key) => boolean;
  cancelEditable: (rowKey: Key) => boolean;
}

const Role: React.FC = (props) => {
  const { currentUser } = props
  const [visiable, setvisiable] = useState(false)
  const columns = [
    {
      title: '角色名称',
      dataIndex: 'roleName'
    },
    {
      title: '角色id',
      dataIndex: 'roleKey',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      filters: true,
      onFilter: true,
      hideInSearch: true,
      valueType: "select",
      render: (text, record) => {
        return <Switch onChange={(value) => {
          console.log(value, record)
          edit(record._id, {
            ...record,
            status: value ? '0' : '1'
          }).then((res) => {
            if (res && !res.message) {
              reloadList()
            } else message.error(res.message)
          })
        }} checked={record.status == 0 ? true : false}></Switch>
      },
      valueEnum: {
        '0': {
          text: '正常',
          status: "0",
        },
        '1': {
          text: '停用',
          status: "1",
        },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 150,
      hideInSearch: true,
      render: (text) => {
        return <span>{formatter(text, 'date')}</span>
      }
    },
    {
      title: '操作',
      dataIndex: 'operate',
      width: 120,
      hideInSearch: true,
      render: (text, record) => {
        return <div style={{ display: 'flex', justifyContent: 'space-around' }}>
          <span>
            <a onClick={() => {
              setroleId(record._id)
              setvisiable(true)
            }}>编辑</a>
          </span>
          <span>
            <Popconfirm title={`是否确实删除`}
              onConfirm={() => {
                del(record._id, {})
                reloadList()
              }}>
              <a>删除</a>
            </Popconfirm>
          </span>
        </div>
      }
    }
  ]

  const ref = useRef<ActionType>();
  const [roleId, setroleId] = useState(0)
  const [dataColumns, setdataColumns] = useState(columns)
  useEffect(() => {
    if (currentUser?._id == 1 && (dataColumns.length - columns.length != 1)) {
      const newColumns = [...dataColumns].concat([{
        title: '操作',
        valueType: 'option',
        render: (text, record) => [
          <Tooltip title={'编辑'}>
            <Button size='small' type="link" onClick={() => {
              setroleId(record.roleId)
              setvisiable(true)
            }}><EditOutlined /></Button>
          </Tooltip>
          ,
          <Popconfirm
            title='删除'
            onConfirm={async () => {
              const { code } = await del({ roleIdList: [record.roleId] })
              if (code == 200) {
                message.success('删除成功！')
                ref.current.reload()
              } else {
                message.error('删除失败！')
              }
            }}
            onCancel={() => { }}
            okText={'确认'}
            cancelText={'取消'}
          >
            <DeleteOutlined style={{ color: '#ff4d4f' }} />
          </Popconfirm>,
        ]
      }])
      setdataColumns(newColumns)
    }
  }, [currentUser])
  const reloadList = () => {
    ref.current.reload()
  }

  const close = () => {
    setvisiable(false)

  }

  return (
    <div>
      <ProTable
        columns={dataColumns}
        actionRef={ref}
        size='small'
        bordered={true}
        request={async (params, sort, filter) => {
          let status = ''
          if (params.status == 'open') {
            status = '0'
          } else if (params.status == 'closed') {
            status = '1'
          }
          let obj = {
            ...params,
            status,
            pageNum: params.current,
            pageSize: params.pageSize
          }
          const res = await query(obj)
          return {
            data: res.list,
            success: res && !res.message,
            total: res.total
          }
        }}
        tableAlertRender={() => false}
        cardBordered={true}
        rowKey='roleId'
        pagination={{
          position: ['bottomCenter'],
          pageSize: 10
        }}
        search={{
          labelWidth: 'auto',
          size: 'small',
        }}
        dateFormatter="string"
        headerTitle={'角色列表'}
        toolbar={{
          title: '角色列表',
          settings: [],
          actions: [
            <Button
              size='small'
              type="primary"
              onClick={() => {
                setroleId(0)
                setvisiable(true)
              }}>
              <PlusOutlined />
              新建角色
            </Button>,
            <Button
            size='small'
            type="primary"
            onClick={() => {
              router.push('/rolematrix/overview')
            }}>
            权限矩阵
          </Button>
          ]
        }}
      />
      <RoleForm roleId={roleId} close={close} visiable={visiable} reload={reloadList} />
    </div>
  );
};

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(Role)
