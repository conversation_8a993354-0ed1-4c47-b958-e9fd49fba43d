import React from 'react'
import { useRequest } from '@umijs/hooks'
import _ from 'lodash'
import {
  Table,
  Card,
  Space,
  Button,
} from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import { queryRoleMatrix, queryUserRoleMatrix } from './service'
import buildTableColumn from '@/utils/buildTableColumn'
import ExportData from '@/components/ExportData'

const List = ({
  type,
}: {
  type: any,
}) => {
  const { data = [], loading } = useRequest(() => {
    return type === 'user' ? queryUserRoleMatrix() : queryRoleMatrix()
  })
  const getColumnFilter = dataIndex => {
    const items = _.uniq(data.map(item => item[dataIndex]))
    return items.map(item => {
      return {
        text: item,
        value: item,
      }
    })
  }
  const getColumnFilterFn = dataIndex => (value, record) => record[dataIndex] === value
  const columns = [
    {
      title: '角色名称',
      dataIndex: 'roleName',
      hasSorter: true,
      filters: getColumnFilter('roleName'),
      onFilter: getColumnFilterFn('roleName'),
    },
    {
      title: '一级菜单',
      dataIndex: 'menu1',
      hasSorter: true,
      filters: getColumnFilter('menu1'),
      onFilter: getColumnFilterFn('menu1'),
    },
    {
      title: '二级菜单',
      dataIndex: 'menu2',
      hasSorter: true,
      filters: getColumnFilter('menu2'),
      onFilter: getColumnFilterFn('menu2'),
    },
    {
      title: '操作权限',
      dataIndex: 'options',
      hasSorter: true,
    },
  ].map(buildTableColumn)
  if (type === 'user') {
    const userCol = buildTableColumn({
      title: '用户',
      dataIndex: 'nickname',
      hasSorter: true,
      filters: getColumnFilter('nickname'),
      onFilter: getColumnFilterFn('nickname'),
    })
    columns.unshift(userCol)
  }
  const title = type === 'user' ? '用户权限' : '角色矩阵'
  return (
    <div>
      <Card
        title={
          <Space>
            <span>{title}</span>
          </Space>
        }
        extra={
          <Space>
            <ExportData columns={columns} dataSource={data} filename={title}>
              <Button size="small" icon={<DownloadOutlined />}>
                导出
              </Button>
            </ExportData>
          </Space>
        }
        bordered={false}
      >
        <Table
          pagination={type === 'user' ? undefined : false}
          columns={columns}
          dataSource={data}
          size="small"
          loading={loading}
        />
      </Card>
    </div>
  )
}

export default List
