import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryUserRoleMatrix(params?: TableListParams) {
  return request('/api/admin/users/userrolematrix', {
    params,
  })
}

export async function queryRoleMatrix(params?: TableListParams) {
  return request('/api/admin/users/rolematrix', {
    params,
  })
}

export async function queryRoleMatrixOverview() {
  return request('/api/admin/users/rolematrix/overview')
}
