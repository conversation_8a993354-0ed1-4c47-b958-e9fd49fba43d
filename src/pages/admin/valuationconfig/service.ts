import request from '@/utils/request'

export function queryValuationConfigs(params: any) {
  return request(`/api/valuationconfigs`, {
    method: 'GET',
    params,
  })
}

export async function queryValuationConfig(id: string): Promise<any> {
  return request(`/api/valuationconfigs/${id}`, {
    method: 'GET',
  })
}

export async function createValuationConfig(data: any): Promise<any> {
  return request(`/api/valuationconfigs`, {
    method: 'POST',
    data,
  })
}

export async function updateValuationConfig(id: string, data: any): Promise<any> {
  return request(`/api/valuationconfigs/${id}`, {
    method: 'PUT',
    data,
  })
}

export async function deleteValuationConfig(id: string): Promise<any> {
  return request(`/api/valuationconfigs/${id}`, {
    method: 'DELETE',
  })
}
