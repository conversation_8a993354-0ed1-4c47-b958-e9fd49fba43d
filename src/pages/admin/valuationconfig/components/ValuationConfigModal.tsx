import React from 'react'
import { InboxOutlined } from '@ant-design/icons'
import { Button, Modal, Input, message, Upload, notification, Form } from 'antd'
import { getToken } from '@/utils/utils'

const { TextArea } = Input
const { Dragger } = Upload

interface ComponentProps {
  valuationConfig?: any;
  dispatch: any;
}

interface ComponentState {
  show?: boolean;
  name?: string;
  description?: string;
  config?: string;
  currentFile?: string;
}

class ValuationConfigModal extends React.Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    this.state = {
      show: false,
    }
  }

  handleNameChange = event => {
    this.setState({ name: event.target.value })
  };

  handleDescriptionChange = event => {
    this.setState({ description: event.target.value })
  };

  close = () => {
    this.setState({ show: false })
  };

  open = () => {
    const { valuationConfig } = this.props
    const newState: ComponentState = { show: true }
    if (valuationConfig) {
      newState.name = valuationConfig.name
      newState.description = valuationConfig.description
      newState.config = valuationConfig.config
      newState.currentFile = ''
    } else {
      newState.name = ''
      newState.description = ''
      newState.config = ''
      newState.currentFile = ''
    }
    this.setState(newState)
  };

  save = () => {
    const { name, description, config } = this.state
    if (!name) {
      message.error('请填写名字')
      return
    }
    if (!config) {
      message.error('请上传配置')
      return
    }
    const { dispatch, valuationConfig } = this.props
    const data = {
      name,
      description,
      config,
    }
    let action = 'valuationConfig/add'
    const payload = { data }
    if (valuationConfig) {
      payload.id = valuationConfig._id
      action = 'valuationConfig/edit'
    } else {
      action = 'valuationConfig/add'
    }
    dispatch({
      type: action,
      payload,
    })
    this.close()
  };

  render() {
    const { children, valuationConfig } = this.props
    const { name, description, currentFile } = this.state
    const props = {
      name: 'attachment',
      action: '/api/valuationconfigs/extractConfig',
      headers: {
        authorization: getToken(),
      },
      showUploadList: false,
      onChange: info => {
        if (info.file.status === 'done') {
          const result = info.file.response
          if (Object.keys(result).length === 0) {
            notification.error({
              message: '文件格式不正确',
            })
            return
          }
          notification.success({ message: '上传成功' })
          this.setState({
            currentFile: info.file.name,
            config: JSON.stringify(result),
          })
        } else if (info.file.status === 'error') {
          notification.error({
            message: info.file.response.message,
          })
        }
      },
    }
    return (
      <div style={{ display: 'inline-block' }}>
        <div onClick={this.open}>{children}</div>
        <Modal
          title={
            <>
              <span>{valuationConfig ? '更新配置文件' : '创建配置文件'}</span>
            </>
          }
          visible={this.state.show}
          onCancel={this.close}
          width={700}
          footer={
            <>
              <Button type="primary" onClick={this.save}>
                保存
              </Button>
            </>
          }
        >
          <Form layout="vertical">
            <Form.Item label="名称">
              <Input placeholder="名称" value={name} onChange={this.handleNameChange} />
            </Form.Item>
            <Form.Item label="描述">
              <TextArea
                placeholder="描述"
                value={description}
                onChange={this.handleDescriptionChange}
              />
            </Form.Item>
            <Form.Item label="上传配置文件">
              <Dragger {...props}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">{currentFile || '点击此处或拖放配置文档进行上传'}</p>
              </Dragger>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    )
  }
}

export default ValuationConfigModal
