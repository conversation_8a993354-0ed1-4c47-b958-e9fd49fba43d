import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Card, Input, Tooltip, Divider, Popconfirm } from 'antd';
import moment from 'moment'
import { ModelState } from './model'
import StandardTable, { TableListParams } from '@/components/StandardTable'
import ValuationConfigModal from './components/ValuationConfigModal'

const { Search } = Input

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  valuationConfigData: any;
  location: any;
}

interface ComponentState {
  input?: string;
}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    valuationConfig,
    loading,
  }: {
    valuationConfig: ModelState;
    fund: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    valuationConfigData: valuationConfig.valuationConfigData,
    loading: loading.models.valuationConfig,
  }),
)
class ValuationConfig extends Component<ComponentProps, ComponentState> {
  state = {};

  componentDidMount() {
    this.loadData({})
  }

  loadData = (params: Partial<TableListParams>) => {
    const { dispatch } = this.props
    dispatch({
      type: 'valuationConfig/fetch',
      payload: {
        ...params,
        input: this.state.input,
      },
    })
  };

  removeItem = (id: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'valuationConfig/remove',
      payload: {
        id,
      },
    })
  };

  handleStandardTableChange = (params: Partial<TableListParams>) => {
    this.loadData(params)
  };

  handleSeachInput = (value: string) => {
    this.setState({ input: value }, () => {
      this.loadData({
        page: 1,
      })
    })
  };

  render() {
    const { valuationConfigData, loading, dispatch } = this.props

    const columns = [
      {
        title: '名称',
        dataIndex: 'name',
      },
      {
        title: '描述信息',
        dataIndex: 'description',
      },
      {
        title: '最后修改时间',
        dataIndex: 'updated_at',
        align: 'right',
        render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
      },
      {
        title: '操作',
        align: 'center',
        render: (text, record) => {
          return <>
            <ValuationConfigModal valuationConfig={record} dispatch={this.props.dispatch}>
              <Tooltip title="编辑">
                <EditOutlined />
              </Tooltip>
            </ValuationConfigModal>
            <Divider type="vertical" />
            <Popconfirm
              title={`确认删除${record.name}吗？`}
              onConfirm={() => this.removeItem(record._id)}
              onCancel={() => {}}
              okText="确认"
              cancelText="取消"
            >
              <Tooltip title="删除">
                <DeleteOutlined />
              </Tooltip>
            </Popconfirm>
          </>;
        },
      },
    ]

    return (
      <Card
        title={
          <>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={this.handleSeachInput}
            />
          </>
        }
        extra={
          <>
            <ValuationConfigModal dispatch={dispatch}>
              <a>
                <PlusOutlined />
                新建配置
              </a>
            </ValuationConfigModal>
          </>
        }
      >
        <div>
          <StandardTable
            disableRowSlection
            loading={loading}
            data={valuationConfigData}
            columns={columns}
            onChange={this.handleStandardTableChange}
            size="small"
            rowKey="_id"
          />
        </div>
      </Card>
    );
  }
}

export default ValuationConfig
