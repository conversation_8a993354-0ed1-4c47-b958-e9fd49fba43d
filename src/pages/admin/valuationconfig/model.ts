import { Effect } from 'dva'
import { Reducer } from 'redux'
import { notification } from 'antd'

import {
  queryValuationConfigs,
  queryValuationConfig,
  deleteValuationConfig,
  updateValuationConfig,
  createValuationConfig,
} from './service'

import {
  buildListPaylod,
  addNewItemToList,
  removeItemFromList,
  updateItemToList,
} from '@/utils/modelHelper'

export interface ModelState {
  valuationConfigData: any;
  currentValuationConfig?: any;
}

export interface ModelType {
  namespace: 'valuationConfig';
  state: ModelState;
  effects: {
    fetch: Effect;
    fetchOne: Effect;
    add: Effect;
    edit: Effect;
    remove: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
    addNewToList: Reducer<ModelState>;
    removeFromList: Reducer<ModelState>;
    updateToList: Reducer<ModelState>;
  };
}

const ValuationConfigModel: ModelType = {
  namespace: 'valuationConfig',

  state: {
    valuationConfigData: {
      list: [],
      pagination: {},
    },
  },

  effects: {
    *fetch({ payload }, { call, put }) {
      const response = yield call(queryValuationConfigs, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          valuationConfigData: data,
        },
      })
    },
    *fetchOne({ payload }, { call, put }) {
      const response = yield call(queryValuationConfig, payload.id)
      yield put({
        type: 'save',
        payload: { currentValuationConfig: response },
      })
    },
    *add({ payload: { data } }, { call, put }) {
      const response = yield call(createValuationConfig, data)
      yield put({
        type: 'save',
        payload: {
          currentValuationConfig: response,
        },
      })
      yield put({
        type: 'addNewToList',
        payload: response,
      })
      notification.success({ message: '创建成功！' })
    },
    *edit({ payload: { data, id } }, { call, put }) {
      const response = yield call(updateValuationConfig, id, data)
      yield put({
        type: 'save',
        payload: { portfolio: response },
      })
      yield put({
        type: 'updateToList',
        payload: response,
      })
      notification.success({ message: '更新成功！' })
    },
    *remove({ payload: { id } }, { call, put }) {
      yield call(deleteValuationConfig, id)
      yield put({
        type: 'removeFromList',
        payload: { id },
      })
      notification.success({ message: '删除成功！' })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    addNewToList(state: ModelState, action) {
      return addNewItemToList(state, action.payload, 'valuationConfigData')
    },
    removeFromList(state: ModelState, action) {
      return removeItemFromList(state, action.payload, 'valuationConfigData')
    },
    updateToList(state: ModelState, action) {
      return updateItemToList(state, action.payload, 'valuationConfigData')
    },
  },
}

export default ValuationConfigModel
