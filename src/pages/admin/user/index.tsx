import React, { useEffect, useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import moment from 'moment'
import { connect } from 'dva'
import _ from 'lodash'
import validator from 'validator'
import { DeleteOutlined, EditOutlined, PlusOutlined, LockOutlined, QuestionCircleOutlined, CopyOutlined, ExclamationCircleOutlined, UserOutlined } from '@ant-design/icons'
import {
  Table,
  Button,
  Tooltip,
  Divider,
  Popconfirm,
  Modal,
  Input,
  Card,
  Form,
  notification,
  Space,
  Tree,
  Select,
  Tag,
  Row,
  Col,
  Dropdown,
  Tabs,
  Breadcrumb,
} from 'antd'
import { queryUsers, updateUser, createUser, deleteUser, queryActiveFunds, authorizeFunds, queryAuthorizationList } from './service'
import t from '@/utils/t'
import { getToken } from '@/utils/utils'
import { query } from '@/services/system/role'

const { Search } = Input
const { Option } = Select
const { TabPane } = Tabs

const { confirm } = Modal

const ROLE_LIST = [{
  title: '内部用户',
  dataIndex: 'Internal_User',
  color: '#1890ff',
}, {
  title: '内部报表用户',
  dataIndex: 'Internal_User_Report',
  color: '#ff85c0',
}, {
  title: 'A类用户',
  dataIndex: 'ClassA_User',
  color: '#d48806',
}, {
  title: 'B类用户',
  dataIndex: 'ClassB_User',
  color: '#ad6800',
}, {
  title: '运维',
  dataIndex: 'opr',
  color: '#52c41a',
}]

interface UserListItem {
  _id?: string,
  nickname: string,
  email: string,
  company: string,
  updated_at?: string,
}

const List = ({
  currentUser,
  location,
}: {
  currentUser: any,
  location: any,
}) => {
  const userFrom = location.pathname.includes('exusers') ? 'iolab_ex' : 'iolab'
  const [form] = Form.useForm()
  const { setFieldsValue } = form
  const [initialFormValue, setInitialFormValue] = useState({ nickname: '', username: '', email: '', company: '', password: '', roleIds: [], status: '1' })
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const { state: authVisible, setTrue: setAuthVisibleTrue, setFalse: setAuthVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState({})
  const [currentAuthItem, setCurrentAuthItem] = useState({})
  const [refreshCount, setRefreshCount] = useState(0)
  const [input, setInput] = useState('')
  const [checkedFunds, setCheckedFunds] = useState([])
  const [options, setOptions] = useState([])
  const [activeTab, setActiveTab] = useState(userFrom === 'iolab' ? 'internal' : 'external')
  const [usernameCopy, setUsernameCopy] = useState('')
  const getCopyUserInfo = (userInfo) => {
    return _.omit(userInfo, ['_id', 'password', 'username', 'nickname', 'email', 'phone'])
  }

  useEffect(() => {
    query({ status: 0, pageSize: 1000 }).then(res => {
      setOptions(res.list)
      const roleIds = res.list.filter(item => item.roleKey === 'Internal_User').map(item => item._id)
      setInitialFormValue({
        ...initialFormValue,
        roleIds,
      })
    })
  }, [])

  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    p.from = userFrom
    p.userType = activeTab
    return queryUsers(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input, activeTab],
  })
  const { data: activeFunds = [], mutate: mutateActiveFunds } = useRequest(() => {
    return queryActiveFunds()
  })
  const topLevelFundFilter = item => {
    return !item._qutkeId || !item._qutkeId.includes('_') || ['demo_eq_1', 'demo_fi_1'].includes(item._qutkeId)
  }
  let activeTreeData = activeFunds.filter(topLevelFundFilter).map(item => {
    const childFundCodes = item.childFundCodes || []
    const childFunds = activeFunds
      .filter(fund => childFundCodes.includes(fund._qutkeId))
      .map(fund => {
        return {
          title: fund.name,
          key: fund._id,
        }
      })
    if (childFunds.length) {
      childFunds.unshift({
        title: item.name,
        key: item._id,
      })
    }
    return {
      title: item.name,
      key: `p_${item._id}`,
      children: childFunds,
    }
  })
  activeTreeData = _.orderBy(activeTreeData, 'title')
  const onAuthorizeSuccess = ({ userId, fundIds }) => {
    setAuthVisibleFalse()
    notification.success({
      message: '产品授权成功',
    })
    const funds = activeFunds.map(item => {
      const authorizationList = (item.authorizationList || []).filter(item => item !== userId)
      if (fundIds.includes(item._id)) {
        authorizationList.push(userId)
      }
      item.authorizationList = authorizationList
      return item
    })
    mutateActiveFunds(funds)
  }
  const { loading: authorizing, run: doAuthorizeFunds } = useRequest((id, data) => {
    return authorizeFunds(id, data)
  }, {
    manual: true,
    onSuccess: onAuthorizeSuccess,
  })
  const onSaveUserSuccess = () => {
    setVisibleFalse()
    setRefreshCount(refreshCount + 1)
    notification.success({
      message: '保存成功',
    })
    form.resetFields()
  }
  const { loading: updatingUser, run: doUpdateUser } = useRequest((id, data) => {
    return updateUser(id, data)
  }, {
    manual: true,
    onSuccess: onSaveUserSuccess,
  })
  const { loading: creatingUser, run: doCreateUser } = useRequest((data) => {
    return createUser(data)
  }, {
    manual: true,
    onSuccess: onSaveUserSuccess,
  })
  const { run: doDeleteUser } = useRequest((id) => {
    return deleteUser(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const { loading: loadingAuthList, run: doQueryAuthorizationList } = useRequest(() => {
    return queryAuthorizationList(usernameCopy)
  }, {
    manual: true,
    onSuccess: (data) => {
      if (data.status === 'userNotFound') {
        notification.warn({
          message: '用户不存在，请重新输入'
        })
      } else if (!data.authorizationList.length) {
        notification.warn({
          message: '该用户没有实盘组合权限'
        })
      } else {
        notification.success({
          message: `组合权限加载成功，请确认保存`
        })
        const ids = getCheckedFundIdsFromAuthList(data.authorizationList)
        setCheckedFunds(ids)
      }
    }
  })
  const handleOpenModal = (record: UserListItem) => () => {
    record.roleIds = record.roleIds || []
    setCurrentItem(record)
    setVisibleTrue()
    setFieldsValue(record)
  }
  const menuItems = [
    {
      label: '基金内部用户',
      key: 'internal',
    },
    {
      label: '基金外部用户',
      key: 'external',
    },
  ]

  const handleOpenInternalModal = handleOpenModal({ userType: 'internal', ...initialFormValue })
  const handleOpenExternalModal = handleOpenModal({ userType: 'external', ...initialFormValue })
  const handleMenuClick = (e) => {
    if (e.key === 'external') {
      handleOpenExternalModal()
    } else {
      handleOpenInternalModal()
    }
  }

  const menuProps = {
    items: menuItems,
    onClick: handleMenuClick,
  }
  const getCheckedFundId = item => {
    if (topLevelFundFilter(item) && (!item.childFundCodes || !item.childFundCodes.length)) {
      return `p_${item._id}`
    }
    return item._id
  }
  const getCheckedFundIds = (userId) => {
    return activeFunds.filter(item => {
      return item.authorizationList && item.authorizationList.includes(userId)
    }).map(getCheckedFundId)
  }
  const getCheckedFundIdsFromAuthList = (authList) => {
    return activeFunds.map(item => {
      if (authList.includes(item._id)) {
        return {
          ...item,
        }
      }
      return null
    }).filter(Boolean)
    .map(getCheckedFundId)
  }
  const handleOpenAuthModal = (record: UserListItem) => () => {
    setCurrentAuthItem(record)
    setAuthVisibleTrue()
    const userId = record._id
    const fundIds = getCheckedFundIds(userId)
    setCheckedFunds(fundIds)
  }
  const handleClickSave = (values: any) => {
    if (currentItem._id) {
      doUpdateUser(currentItem._id, values)
    } else {
      values.from = userFrom
      values.userType = activeTab
      doCreateUser(values)
    }
  }
  const handleSelectAllFunds = () => {
    const fundIds = activeFunds.map(getCheckedFundId)
    setCheckedFunds(fundIds)
  }
  const handleReverseSelectFunds = () => {
    const fundIds = activeFunds.map(getCheckedFundId)
    const ids = fundIds.filter(item => !checkedFunds.includes(item))
    setCheckedFunds(ids)
  }
  const handleUnselectAllFunds = () => {
    setCheckedFunds([])
  }
  const handleDownload = () => {
    const token = getToken()
    const href = `/api/admin/users/rolematrix/download?token=${token.slice(7)}`
    window.open(href)
  }
  const validatePassword = (rule, value) => {
    if (value && value.length < 8) {
      return Promise.reject('密码长度不能少于8位')
    } else if (value && !validator.isStrongPassword(value)) {
      return Promise.reject('密码必须包含数字、大小写字母和特殊字符')
    } else {
      return Promise.resolve()
    }
  }
  // const isIolabEx = userFrom === 'iolab_ex'
  const isIolabEx = activeTab === 'external'
  const emailColSpan = isIolabEx ? 8 : 12
  const internalCols = [{
    title: '部门',
    dataIndex: 'department',
  }, {
    title: '科室',
    dataIndex: 'office',
  }, {
    title: '岗位',
    dataIndex: 'post',
  }]
  const externalCols = [{
    title: '业务关系对接人',
    dataIndex: 'cpName',
  }, {
    title: '对接人UM账号',
    dataIndex: 'cpUMId',
  }, {
    title: '对接人部门',
    dataIndex: 'cpDepartment',
  }, {
    title: '对接人岗位',
    dataIndex: 'cpPost',
  }]
  const columns = [
    // {
    //   title: '用户类型',
    //   dataIndex: 'userType',
    //   render: (userType) => {
    //     if (userType === 'external') {
    //       return '基金外部用户'
    //     }
    //     return '基金内部用户'
    //   }
    // },
    {
      title: '姓名',
      dataIndex: 'nickname',
      sorter: true,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      sorter: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
    },
    {
      title: '公司',
      dataIndex: 'company',
    },
    ...(isIolabEx ? externalCols : internalCols),
    // {
    //   title: '角色',
    //   dataIndex: 'roleName',
    //   sorter: true,
    //   render: text => {
    //     const roleItem = options.find(item => item.roleName === text) || {}
    //     return <Tag color={roleItem.color}>{roleItem.title}</Tag>
    //   },
    // },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      align: 'right',
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '用户状态',
      dataIndex: 'status',
      align: 'right',
      render: (val) => {
        if (val === '0') {
          return <Tag color="#f50">已注销</Tag>
        } else if (val === '2') {
          return <Tag color="#f50">已锁定</Tag>
        }
        return <Tag color="#87d068">正常</Tag>
      }
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        return (
          <>
            <Tooltip title="编辑">
              <EditOutlined onClick={handleOpenModal(record)} />
            </Tooltip>
            <Divider type="vertical" />
            <Tooltip title="复制此用户权限新建用户">
              <CopyOutlined onClick={handleOpenModal(getCopyUserInfo(record))} />
            </Tooltip>
            <Divider type="vertical" />
            <Tooltip title="授权产品">
              <LockOutlined onClick={handleOpenAuthModal(record)} />
            </Tooltip>
            <Divider type="vertical" />
            <Popconfirm
              title={`${t('portfolio.delTip')}${record.nickname}${t('portfolio.questionEnd')}？`}
              onConfirm={() => { doDeleteUser(record._id) }}
              onCancel={() => { }}
              okText={t('portfolio.confirm')}
              cancelText={t('portfolio.cancel')}
            >
              <Tooltip title="删除">
                <DeleteOutlined />
              </Tooltip>
            </Popconfirm>
          </>
        )
      },
    },
  ]
  const tabs = [{
    name: '基金内部用户',
    tab: 'internal'
  }, {
    name: '基金外部用户',
    tab: 'external'
  }]
  const showPromiseConfirm = () => {
    confirm({
      title: '请输入要复制的用户名',
      icon: <ExclamationCircleOutlined />,
      content: <Input size="large" placeholder="请输入用户名" prefix={<UserOutlined />} onChange={(e) => setUsernameCopy(e.target.value)} />,
      onOk() {
        return doQueryAuthorizationList()
      },
      onCancel() {},
    })
  }  
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>{userFrom === 'iolab' ? '内网用户管理' : '外网用户管理'}</Breadcrumb.Item>
      </Breadcrumb>
      <Tabs
        animated={false}
        activeKey={activeTab}
        onChange={setActiveTab}
        tabBarExtraContent={null}
      >
        {tabs.map(item => <TabPane tab={item.name} key={item.tab}/>)}
      </Tabs>
      <Card
        title={
          <Space>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </Space>
        }
        bordered={false}
        extra={
          <Space>
            <Button onClick={handleOpenModal(initialFormValue)}>
              <Space>
                <PlusOutlined />
                新建用户
              </Space>
            </Button>
            {/* <Dropdown menu={menuProps}>
              <Button>
                <Space>
                  新建用户
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown> */}
          </Space>
        }
      >
        <Table size="small" columns={columns} rowKey="_id" {...tableProps} />
      </Card>
      <Modal
        title={
          <>
            <span>{currentItem && currentItem._id ? '编辑用户' : '新建用户'}</span>
          </>
        }
        visible={visible}
        onCancel={() => {
          setVisibleFalse();
          form.resetFields();
        }}
        width={900}
        footer={[
          <Button
            type="primary"
            loading={updatingUser || creatingUser}
            onClick={() => {
              form.submit()
            }}
          >
            保存
          </Button>,
        ]}
      >
        <Form
          hideRequiredMark
          form={form}
          layout="vertical"
          initialValues={initialFormValue}
          onFinish={handleClickSave}
        >
          <Row gutter={8}>
            <Col span={12} sm={12} xs={24}>
              <Form.Item
                label="姓名"
                name="nickname"
                rules={[
                  {
                    required: true,
                    message: '请输入姓名',
                  },
                ]}
              >
                <Input placeholder="姓名" />
              </Form.Item>
            </Col>
            <Col span={12} sm={12} xs={24}>
              <Form.Item
                label="用户名"
                name="username"
                rules={[
                  {
                    required: true,
                    message: '请输入用户名',
                  },
                ]}
              >
                <Input placeholder="用户名，用于登录系统" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={8}>
            <Col span={emailColSpan} sm={emailColSpan} xs={24}>
              <Form.Item
                label="邮箱"
                name="email"
                rules={[
                  {
                    required: true,
                    message: '请输入邮箱',
                  },
                  {
                    type: 'email',
                    message: '请输入正确的邮箱',
                  },
                ]}
              >
                <Input placeholder="邮箱" />
              </Form.Item>
            </Col>
            {isIolabEx &&
            <Col span={8} sm={8} xs={24}>
              <Form.Item
                label="手机号码"
                name="phone"
                rules={[
                  {
                    required: true,
                    message: '请输入手机号码',
                  },
                ]}
              >
                <Input placeholder="手机号码" />
              </Form.Item>
            </Col>}
            <Col span={emailColSpan} sm={emailColSpan} xs={24}>
              <Form.Item
                label="公司"
                name="company"
                rules={[
                  {
                    required: true,
                    message: '请输入公司',
                  },
                ]}
              >
                <Input placeholder="公司" />
              </Form.Item>
            </Col>
          </Row>
          {isIolabEx &&
          <Row gutter={8}>
            <Col span={6} sm={6} xs={24}>
              <Form.Item
                label={<Space>
                  <span>业务关系对接人</span>
                  <Tooltip title="需为平安基金内部员工，一般为销售岗">
                    <QuestionCircleOutlined/>
                  </Tooltip>
                </Space>}
                name="cpName"
                rules={[
                  {
                    required: true,
                    message: '请输入业务关系对接人',
                  },
                ]}
              >
                <Input placeholder="业务关系对接人" />
              </Form.Item>
            </Col>
            <Col span={6} sm={6} xs={24}>
              <Form.Item
                label="对接人UM账号"
                name="cpUMId"
                rules={[
                  {
                    required: true,
                    message: '请输入对接人UM账号',
                  },
                ]}
              >
                <Input placeholder="对接人UM账号" />
              </Form.Item>
            </Col>
            <Col span={6} sm={6} xs={24}>
              <Form.Item
                label="对接人部门"
                name="cpDepartment"
                rules={[
                  {
                    required: true,
                    message: '请输入对接人部门',
                  },
                ]}
              >
                <Input placeholder="对接人部门" />
              </Form.Item>
            </Col>
            <Col span={6} sm={6} xs={24}>
              <Form.Item
                label="对接人岗位"
                name="cpPost"
                rules={[
                  {
                    required: true,
                    message: '请输入对接人岗位',
                  },
                ]}
              >
                <Input placeholder="对接人岗位" />
              </Form.Item>
            </Col>
          </Row>}
          {!isIolabEx &&
          <Row gutter={8}>
            <Col span={8} sm={8} xs={24}>
              <Form.Item
                label="部门"
                name="department"
                rules={[
                  {
                    required: true,
                    message: '请输入部门',
                  },
                ]}
              >
                <Input placeholder="部门" />
              </Form.Item>
            </Col>
            <Col span={8} sm={8} xs={24}>
              <Form.Item
                label="科室"
                name="office"
              >
                <Input placeholder="科室" />
              </Form.Item>
            </Col>
            <Col span={8} sm={8} xs={24}>
              <Form.Item
                label="岗位"
                name="post"
                rules={[
                  {
                    required: true,
                    message: '请输入岗位',
                  },
                ]}
              >
                <Input placeholder="岗位" />
              </Form.Item>
            </Col>
          </Row>}
          <Row gutter={8}>
            <Col span={12} sm={12} xs={24}>
              <Form.Item
                label="角色"
                name="roleIds"
                rules={[
                  {
                    required: true,
                    message: '请选择用户角色',
                  },
                ]}
              >
                <Select placeholder="角色" mode="multiple" showArrow optionFilterProp="children">
                  {options.map(item => <Option value={item._id}>{item.roleName}</Option>)}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12} sm={12} xs={24}>
              <Form.Item
                label="用户状态"
                name="status"
                rules={[
                  {
                    required: true,
                    message: '请选择用户状态',
                  },
                ]}
              >
                <Select placeholder="用户状态">
                  <Option value="1">正常</Option>
                  <Option value="0">注销</Option>
                  <Option value="2">锁定</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            hasFeedback
            label="密码"
            name="password"
            rules={[
              {
                required: !currentItem._id,
                message: '请输入密码',
              },
              {
                validator: validatePassword,
              },
            ]}
          >
            <Input.Password placeholder="密码" />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title={
          <div>
            <span>授权{currentAuthItem.nickname}产品权限</span>
            <Button
              type="primary"
              size="small"
              icon={<CopyOutlined />}
              loading={loadingAuthList}
              style={{
                float: "right",
                marginRight: 20
              }}
              onClick={showPromiseConfirm}
            >
              复制权限
            </Button>
          </div>
        }
        visible={authVisible}
        onCancel={setAuthVisibleFalse}
        width={700}
        footer={
          <>
            <Button
              onClick={handleSelectAllFunds}
            >
              全选
            </Button>
            <Button
              onClick={handleReverseSelectFunds}
            >
              反选
            </Button>
            <Button
              onClick={handleUnselectAllFunds}
            >
              全取消
            </Button>
            <Button
              type="primary"
              loading={authorizing}
              onClick={() => {
                // if (!checkedFunds.length) {
                //   notification.warn({ message: '请选择产品' })
                //   return
                // }
                const fundsIds = _.uniq(checkedFunds.map(id => id.replace('p_', '')))
                doAuthorizeFunds(currentAuthItem._id, { ids: fundsIds })
              }}
            >
              保存
            </Button>
          </>
        }
      >
        <Tree
          checkable
          checkedKeys={checkedFunds}
          selectable={false}
          onCheck={(keys) => { setCheckedFunds(keys) }}
          treeData={activeTreeData}
        />
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
