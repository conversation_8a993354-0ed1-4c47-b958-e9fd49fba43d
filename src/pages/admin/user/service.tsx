import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryUsers(params: TableListParams) {
  return request('/api/admin/users', {
    params,
  })
}

export async function queryActiveFunds(params?: TableListParams) {
  return request('/api/admin/users/activefunds', {
    params,
  })
}


export async function authorizeFunds(id: string, data: any) {
  return request(`/api/admin/users/${id}/funds`, {
    method: 'POST',
    data,
  })
}

export async function deleteUser(id: string) {
  return request(`/api/admin/users/${id}`, {
    method: 'delete',
  })
}

export async function updateUser(id: string, data: any) {
  return request(`/api/admin/users/${id}`, {
    method: 'put',
    data,
  })
}

export async function createUser(data: any) {
  return request(`/api/admin/users`, {
    method: 'post',
    data,
  })
}

export async function queryAuthorizationList(username: string) {
  return request(`/api/admin/users/${username}/authorizationList`)
}