import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import moment from 'moment'
import { connect } from 'dva'
import {
  Table,
  Input,
  Card,
  Space,
  DatePicker,
  Button,
  Radio,
} from 'antd'
import ExportData from '@/components/ExportData'
import { getToken } from '@/utils/utils'
import { queryUsers } from './service'

const { Search } = Input
const { RangePicker } = DatePicker

const List = ({
  currentUser,
  location,
}: {
  currentUser: any,
  location: any,
}) => {
  const [time, setTime] = useState('0.5')
  const handleTimeChange = event => {
    setTime(event.target.value)
  }
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    p.time = time
    return queryUsers(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [time],
  })
  const handleDownload = () => {
    const token = getToken()
    const href = `/api/admin/users/online/download?token=${token.slice(7)}&time=${time}`
    window.open(href)
  }

  const columns = [
    {
      title: '姓名',
      dataIndex: 'nickname',
    },
    {
      title: '用户名',
      dataIndex: 'username',
    },
    {
      title: '最后活跃时间',
      dataIndex: 'updated_at',
      align: 'right',
      sorter: true,
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
  ]
  return (
    <div>
      <Card
        title={
          <Space>
            <span>在线用户查询</span>
          </Space>
        }
        extra={
          <Space>
            <Radio.Group
              value={time}
              size="small"
              onChange={handleTimeChange}
            >
              <Radio.Button value="0.5">30分钟</Radio.Button>
              <Radio.Button value="1">1小时</Radio.Button>
              <Radio.Button value="5">5小时</Radio.Button>
              <Radio.Button value="8">8小时</Radio.Button>
              <Radio.Button value="24">24小时</Radio.Button>
            </Radio.Group>
            <ExportData title="导出" onClick={handleDownload}/>
          </Space>
        }
        bordered={false}
      >
        <Table size="small" columns={columns} rowKey="_id" {...tableProps} />
      </Card>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
