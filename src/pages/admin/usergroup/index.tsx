import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import { connect } from 'dva'
import _ from 'lodash'
import moment from 'moment'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Table,
  Button,
  Tooltip,
  Popconfirm,
  Modal,
  Input,
  Card,
  Form,
  notification,
  Spin,
  Breadcrumb,
  Space,
  Select,
} from 'antd'
import { queryUserGroups, updateUserGroup, createUserGroup, deleteUserGroup, queryUserList } from './service'
import t from '@/utils/t'

const Search = Input.Search

const List = ({
  currentUser,
}: {
  currentUser: any,
}) => {
  const [form] = Form.useForm()
  const { setFieldsValue } = form
  const initialFormValue = {
    name: '', description: '', userIds: []
  }
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState({})
  const [refreshCount, setRefreshCount] = useState(0)
  const [input, setInput] = useState('')
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    return queryUserGroups(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const onSaveUserGroupSuccess = () => {
    handleOpenClose()
    setRefreshCount(refreshCount + 1)
    notification.success({
      message: '保存成功',
    })
  }
  const { loading: updatingUserGroup, run: doUpdateUserGroup} = useRequest((id, data) => {
    return updateUserGroup(id, data)
  }, {
    manual: true,
    onSuccess: onSaveUserGroupSuccess,
  })
  const { loading: creatingUserGroup, run: doCreateUserGroup} = useRequest((data) => {
    return createUserGroup(data)
  }, {
    manual: true,
    onSuccess: onSaveUserGroupSuccess,
  })
  const { run: doDeleteUserGroup } = useRequest((id) => {
    return deleteUserGroup(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const { data: users = [] } = useRequest(() => {
    return queryUserList()
  }, {
    cacheKey: 'queryUserList',
  })
  const handleOpenModal = (record) => () => {
    setCurrentItem(record)
    setVisibleTrue()
    setFieldsValue({
      ...record,
      reportingTime: moment(record.reportingTime),
      ratingTime: moment(record.ratingTime),
    })
  }
  const handleOpenClose = () => {
    setVisibleFalse()
    setFieldsValue(initialFormValue)
  }
  const handleClickSave = (values: any) => {
    if (currentItem._id) {
      doUpdateUserGroup(currentItem._id, values)
    } else {
      doCreateUserGroup(values)
    }
  }
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 120,
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '用户数',
      dataIndex: 'userIds',
      width: 100,
      render: (text) => {
        return text.length
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      width: 150,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD hh:mm')
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (text, record) => {
        // if (currentUser._id !== record.authorId) {
        //   return false
        // }
        return (
          <>
            <Space>
              <Tooltip title="编辑">
                <EditOutlined onClick={handleOpenModal(record)} />
              </Tooltip>
              <Popconfirm
                title={`${t('portfolio.delTip')}${record.name}${t('portfolio.questionEnd')}？`}
                onConfirm={() => { doDeleteUserGroup(record._id) }}
                onCancel={() => {}}
                okText={t('portfolio.confirm')}
                cancelText={t('portfolio.cancel')}
              >
                <Tooltip title="删除">
                  <DeleteOutlined />
                </Tooltip>
              </Popconfirm>
            </Space>
          </>
        )
      },
    },
  ]

  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>用户组管理</Breadcrumb.Item>
      </Breadcrumb>
      <Card
        title={
          <>
            <Search
              size="small"
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
        extra={
          <>
            <a onClick={
              handleOpenModal(initialFormValue)
            }>
              <PlusOutlined />
              新建用户组
            </a>
          </>
        }
      >
        <Table
          size="small" columns={columns} rowKey="_id"
          scroll={{ x: 700 }}
          {...tableProps}
        />
      </Card>
      <Modal
        title={
          <>
            <span>{currentItem && currentItem._id ? '编辑用户组' : '新建用户组'}</span>
          </>
        }
        visible={visible}
        onCancel={handleOpenClose}
        width={700}
        footer={[
          <Button
            type="primary"
            loading={updatingUserGroup || creatingUserGroup}
            onClick={() => form.submit()}
          >
            保存
          </Button>,
        ]}
      >
        <Spin spinning={updatingUserGroup || creatingUserGroup}>
          <Form
            hideRequiredMark
            form={form}
            layout="vertical"
            initialValues={initialFormValue}
            onFinish={handleClickSave}
          >
            <Form.Item
              label="名称"
              name="name"
              rules={[
                {
                  required: true,
                  message: '请输入名称',
                },
              ]}
            >
              <Input placeholder="名称"/>
            </Form.Item>
            <Form.Item
              label="描述"
              name="description"
              rules={[
                {
                  required: true,
                  message: '请输入描述',
                },
              ]}
            >
              <Input.TextArea placeholder="描述"/>
            </Form.Item>
            <Form.Item
              label="用户"
              name="userIds"
              rules={[
                {
                  required: true,
                  message: '请选择用户',
                },
              ]}
            >
              <Select
                showSearch
                mode="multiple"
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {users.map(item => <Select.Option key={item._id}>{`${item.nickname} <${item.email}>`}</Select.Option>)}
              </Select>
            </Form.Item>
          </Form>
        </Spin>
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
