import request from '@/utils/request'

export async function queryUserGroups(params?: any) {
  return request('/api/usergroups', {
    params,
  })
}

export async function queryUserGroup(id: string) {
  return request(`/api/usergroups/${id}`)
}

export async function deleteUserGroup(id: string) {
  return request(`/api/usergroups/${id}`, {
    method: 'delete',
  })
}

export async function updateUserGroup(id: string, data: any) {
  return request(`/api/usergroups/${id}`, {
    method: 'put',
    data,
  })
}

export async function createUserGroup(data: any) {
  return request(`/api/usergroups`, {
    method: 'post',
    data,
  })
}

export async function queryUserList() {
  return request(`/api/usergroups/list/users`)
}
