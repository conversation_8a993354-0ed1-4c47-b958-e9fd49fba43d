import React, { useState } from 'react'
import {
  Spin,
  Card,
  Table,
} from 'antd'
import moment from 'moment'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import { getFundMonitorData } from './service'
import NavTabs from '@/components/NavTabs'
import tabs from './tabs'

export default ({ location }) => {
  const { loading, data } = useRequest(() => {
    return getFundMonitorData()
  })
  const columns = [{
    title: '日期',
    dataIndex: 'TRADE_DAYS',
  }, {
    title: '组合名称',
    dataIndex: 'F_NAME',
  }, {
    title: '组合代码',
    dataIndex: 'REF_CODE',
  }, {
    title: '异常信息',
    dataIndex: 'NOTE',
  }, {
    title: '更新时间',
    dataIndex: 'UPDATETIME',
    align: 'right',
    render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
  }]
  return (
    <Card className="nav-tab-wrapper">
      <NavTabs tabs={tabs} offsetTop={0} location={location}/>
      <Spin spinning={loading}>
        <Table size="small" columns={columns} dataSource={data}></Table>
      </Spin>
    </Card>
  )
}