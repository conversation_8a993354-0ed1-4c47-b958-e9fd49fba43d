import React, { useState } from 'react'
import {
  Spin,
  Card,
  DatePicker,
  Table,
  Tag,
} from 'antd'
import _ from 'lodash'
import ms from 'ms'
import moment from 'moment'
import { useRequest } from '@umijs/hooks'
import { getCronJobExecResult } from './service'
import NavTabs from '@/components/NavTabs'
import tabs from './tabs'

export default ({ location }) => {
  const [execDate, setExecDate] = useState(moment())
  const { loading, data } = useRequest(() => {
    return getCronJobExecResult({ execDate: execDate.format('YYYY-MM-DD') })
  }, {
    refreshDeps: [execDate]
  })
  const columns = [{
    title: '任务ID',
    fixed: 'left',
    width: 120,
    dataIndex: 'JOB_ID',
  }, {
    title: '状态',
    fixed: 'left',
    width: 80,
    dataIndex: 'STATUS',
    render: status => status === 1 ? <Tag color="green">成功</Tag> : <Tag color="volcano">失败</Tag>
  }, {
    title: '任务名称',
    dataIndex: 'JOB_NAME',
  }, {
    title: '执行时长',
    dataIndex: 'JOB_EXEC_MS',
    width: 80,
    render: execMs => ms(execMs),
  }, {
    title: '执行结果',
    dataIndex: 'RESULT',
  }, {
    title: '开始时间',
    dataIndex: 'START_DATE',
    render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
  }, {
    title: '结束时间',
    dataIndex: 'END_DATE',
    render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
  }, {
    title: '结果表',
    dataIndex: 'JOB_TABLE',
  }, {
    title: '调度策略',
    dataIndex: 'JOB_TIMING_PATTERN',
  }, {
    title: '更新机制',
    dataIndex: 'JOB_UPDATE_METHOD',
  }, {
    title: '脚本路径',
    dataIndex: 'JOB_SCRIPT_PATH',
  }]
  return (
    <Card className="nav-tab-wrapper">
      <NavTabs tabs={tabs} offsetTop={0} location={location} tabBarExtraContent={
        <DatePicker onChange={setExecDate} value={execDate} />
      }/>
      <Spin spinning={loading}>
        <Table
          size="small"
          columns={columns}
          dataSource={data}
          scroll={{ x: 700 }}
        />
      </Spin>
    </Card>
  )
}