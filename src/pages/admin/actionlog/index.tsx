import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import moment from 'moment'
import { connect } from 'dva'
import {
  Table,
  Input,
  Card,
  Space,
  DatePicker,
  Button,
  Select,
  Tooltip,
} from 'antd'
import { getToken } from '@/utils/utils'
import { queryLogs, queryUsers } from './service'
import ExportData from '@/components/ExportData'

const { Search } = Input
const { RangePicker } = DatePicker
const { Option } = Select

const List = ({
  currentUser,
  location,
}: {
  currentUser: any,
  location: any,
}) => {
  const [input, setInput] = useState('')
  const [userId, setUserId] = useState('')
  const [dateRange, setDateRange] = useState([
    moment().subtract(6, 'month'),
    moment(),
  ])
  const { data: users = [] } = useRequest(() => {
    return queryUsers()
  })
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    if (userId) {
      p.userId = userId
    }
    p.startDate = +dateRange[0]
    p.endDate = +dateRange[1]
    return queryLogs(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [input, dateRange, userId],
  })
  const handleDownload = () => {
    const token = getToken()
    const href = `/api/admin/users/actionlogs/download?token=${token.slice(7)}&startDate=${+dateRange[0]}&endDate=${+dateRange[1]}&userId=${userId}`
    window.open(href)
  }

  const columns = [
    {
      title: '功能模块',
      dataIndex: 'functionModule',
      sorter: true,
    },
    {
      title: '菜单名称',
      dataIndex: 'menu',
      sorter: true,
    },
    {
      title: '操作类型',
      dataIndex: 'opType',
      sorter: true,
    },
    {
      title: '操作员',
      dataIndex: 'name',
    },
    {
      title: '操作内容',
      dataIndex: 'opContent',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: 'IP',
      dataIndex: 'ip',
    },
    {
      title: 'User-Agent',
      dataIndex: 'userAgent',
      render: (text, record) => {
        if (!text) {
          return ''
        }
        return (
          <Tooltip title={text} placement="top">
            {record.browser} @ {record.os}
          </Tooltip>
        )
      },
    },
    {
      title: '时间',
      dataIndex: 'created_at',
      align: 'right',
      sorter: true,
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
  ]
  return (
    <div>
      <Card
        title={
          <Space>
            <span>操作日志查询</span>
            {false &&
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />}
          </Space>
        }
        extra={
          <Space>
            <RangePicker
              showTime={{ format: 'HH:mm' }}
              format="YYYY-MM-DD HH:mm"
              defaultValue={dateRange}
              onChange={setDateRange}
            />
            <Select
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              placeholder="选择用户"
              style={{ width: 130 }}
              onChange={setUserId}
              allowClear showSearch
            >
              {users.map(user => <Option value={user._id}>{user.nickname}</Option>)}
            </Select>
            <ExportData title="导出" onClick={handleDownload}/>
          </Space>
        }
        bordered={false}
      >
        <Table size="small" columns={columns} rowKey="_id" {...tableProps} />
      </Card>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
