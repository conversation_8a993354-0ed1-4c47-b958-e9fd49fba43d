import request from '@/utils/request'

export async function querySurvey(id: string): Promise<any> {
  return request(`/api/admin/survey/${id}`, {
    method: 'GET',
  })
}

export function loadTemplate(): Promise<any> {
  return request(`/api/admin/templates`, {
    method: 'GET',
  })
}

export function loadFavorite(): Promise<any> {
  return request(`/api/favorite_questions`, {
    method: 'GET',
  })
}

export function createFavorite(data: any) {
  return request('/api/favorite_questions', {
    method: 'POST',
    data,
  })
}


export function removeFavorite(id: string) {
  return request(`/api/favorite_questions/${id}`, {
    method: 'DELETE',
  })
}


export function createSurvey(data: any): Promise<any> {
  return request(`/api/admin/survey`, {
    method: 'POST',
    data,
  })
}

export function editSurvey(id: string, data: any): Promise<any> {
  return request(`/api/admin/survey/${id}`, {
    method: 'PUT',
    data,
  })
}

export function addCategory(data: any) {
  return request('/api/favorite_questions/template', {
    method: 'POST',
    data
  })
}


