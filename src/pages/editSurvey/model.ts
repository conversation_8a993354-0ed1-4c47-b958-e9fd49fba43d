import { Effect } from 'dva'
import { Reducer } from 'redux'
import { querySurvey, loadTemplate, loadFavorite, createSurvey, editSurvey, createFavorite, removeFavorite, addCategory } from './service'
import { notification } from 'antd'

export interface ModelState {
  survey?: any;
  templates?: any,
  favorites?: any,
}

export interface ModelType {
  namespace: 'editSurvey';
  state: ModelState;
  effects: {
    fetchSurvey: Effect;
    fetchTemplate: Effect;
    fetchFavorite: Effect;
    fetchCreat: Effect;
    fetchEdit: Effect;
    fetchCreatFavorites: Effect;
    fetchRemoveFavorite: Effect;
    fetchAddCategory: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
    saveFavorites: Reducer<ModelState>;
    removeFavorites: Reducer<ModelState>;
    resetSurveyState: Reducer<ModelState>;
  };
}

const EditSurveysModel: ModelType = {
  namespace: 'editSurvey',
  state: {
    survey: null,
    templates: [],
    favorites: [],
  },
  effects: {
    *fetchSurvey({ payload: { id } }, { call, put }) {
      const response = yield call(querySurvey, id)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
    },
    *fetchTemplate({ payload: { params } }, { call, put }) {
      const response = yield call(loadTemplate, params)
      yield put({
        type: 'save',
        payload: {
          templates: response,
        },
      })
    },
    *fetchFavorite({ payload: { params } }, { call, put }) {
      const response = yield call(loadFavorite, params)
      yield put({
        type: 'save',
        payload: {
          favorites: response,
        },
      })
    },
    *fetchCreat({ payload: { data } }, { call, put }) {
      const response = yield call(createSurvey, data)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
      notification.success({ message: '创建成功！' })
    },
    *fetchEdit({ payload: { data, id } }, { call, put }) {
      const response = yield call(editSurvey, id, data)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
      notification.success({ message: '修改成功！' })
    },
    *fetchCreatFavorites({ payload: { data } }, { call, put }) {
      const response = yield call(createFavorite, data)
      yield put({
        type: 'saveFavorites',
        payload: {
          favorites: response,
        },
      })
    },
    *fetchRemoveFavorite({ payload: { id } }, { call, put }) {
      yield call(removeFavorite, id)
      yield put({
        type: 'removeFavorites',
        payload: {
          id: id,
        },
      })
    },
    *fetchAddCategory({ payload: { data } }, { call, put }) {
      const response = yield call(addCategory, data)
      yield put({
        type: 'saveFavorites',
        payload: {
          favorites: response,
        },
      })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    saveFavorites(state, action) {
      const list = [...state.favorites, action.payload.favorites]
      return {
        ...state,
        favorites: list,
      }
    },
    removeFavorites(state, action) {
      const list = [...state.favorites].filter(item => item._id != action.payload.id)
      return {
        ...state,
        favorites: list
      }
    },
    resetSurveyState(state, action) {
      return {
        ...state,
        ...action.payload.data,
      }
    },
  },
}

export default EditSurveysModel
