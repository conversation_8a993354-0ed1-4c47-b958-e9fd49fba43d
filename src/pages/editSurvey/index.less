.navbar {
  z-index: 900;
  ul > li > a {
    padding: 10px;
  }
  ul > li > a >button {
    padding: 5px 15px;
    font-size: 16px;
  }
  .tip {
    display: none;
    position: absolute;
    text-align: center;
    font-size: 18px;
    top: 15px;
    width: 50%;
    left: 25%;
    height: 100%;
    transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;
  }
  .tip.show {
    display: block;
  }
}
.editContainer {
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 4px;
  position: relative;
  border-bottom: 1px solid #807c7c;
  .sidebar {
    position: fixed;
    top: 115px;
    bottom: 0;
    left: 60px;
    width: 371px;
    border-right: 1px solid #807c7c;
    background-color: #0d131b;
    // z-index: 10;
    .menu {
      padding: 10px 20px;
      ul {
        position: absolute;
        right: 20px;
        bottom: 0px;
        left: 20px;
        top: 60px;
        overflow: auto;
      }
    }
    .sidebarTab {
      margin-bottom: 15px;
    }
    .widgetList {
      li {
        cursor: pointer;
        border-radius: 0 !important;
        margin-bottom: 5px;
      }
      :global(.list-group-item) {
        background-color: #181f29;
        border:none
      }
    }
    .templateList {
      .title {
        padding-right: 25px;
      }
      .button {
        position: absolute;
        right: 15px;
        top: 10px;
      }
      :global(.list-group-item) {
        background-color: rgb(51, 50, 50 , 0.6);
        border:none
      }
    }
  }

  .sidebar.tabOpen {
    top: 120px;
    left: 60px;
  }

  .library {
    position: fixed;
    top: 105px;
    right: -330px;
    bottom: 0;
    width: 326px;
    background: #2d2d2d;
    border-left:1px solid #7b7b7b;
    color: #fff;
    -webkit-transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    .title {
      position: relative;
      padding: 10px;
      .closeBtn {
        position: absolute;
        right: 10px;
        top: 20px;
        cursor: pointer;
        font-size: 16px;
      }
    }
    .toggleBtn {
      position: absolute;
      padding-left: 10px;
      width: 110px;
      height: 32px;
      line-height: 32px;
      left: -110px;
      top: 15px;
      border: 1px solid #969595;
      border-right: none;
      background-color:#2d2d2d;
      border-top-left-radius: 16px;
      border-bottom-left-radius: 16px;
      cursor: pointer;
    }
    .favoriteTitle {
      border-bottom: 1px solid #ddd;
      padding-bottom: 10px;
      cursor: pointer;
    }
    .noFavorite {
      color: #646464;
      margin-top: 15px;
      font-size: 13px;
      text-align: center;
    }
    .outerList {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      top: 50px;
      overflow: auto;
      li {
        border-radius: 0;
        border: 0;
        background-color: #323232;
      }
    }
    .innerList {
      margin-bottom: 0;
      padding: 5px 10px;
      li {
        border: 0;
        padding: 10px 10px;
        cursor: pointer;
        .questionTitle {
          width: 82%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        :global(.fa) {
          cursor: pointer;
          margin-left: 5px;
          position: absolute;
          top: 13px;
        }
        :global(.fa-close) {
          right: 25px;
        }
        :global(.fa-info-circle) {
          right: 5px;
        }
        &:hover {
          background-color: #ddd;
        }
      }
    }
  }

  .library.open {
    -webkit-transform: translateX(-330px);
    transform: translateX(-330px);
  }

  .mobileSelect {
    display: none;
  }

  .main {
    position: fixed;
    top: 60px;
    bottom: 0;
    left: 410px;
    right: 0;
    overflow: auto;
    overflow-x: hidden;
    margin:20px;
    -webkit-transition: right 0.3s;
    transition: right 0.3s;
    white-space: nowrap;
    text-align: center;
    :global(.inner) {
      margin: 15px auto;
      max-width: 710px;
      color:white
    }
  }

  .main.libraryOpen {
    right: 325px;
  }

  .surveyWapper {
    display: inline-block;
    zoom: 1;
    vertical-align: middle;
    white-space: normal;
    margin-top: 40px;
    position: relative;
    padding: 0;
    font-size: 14px;
    text-align: left;
    width: 100%;
  }

  .surveyMain {
    padding-top: 30px;
    padding-bottom: 30px;
    border: 1px solid #807c7c;
    border-top: 0;
    background-color: #181f29;
    .surveyTitle, .surveyDescription {
      margin: 10px 0;
      padding: 13px 0;
      input {
        height: 50px;
        font-size: 20px;
        text-align: center;
        border: none;
        box-shadow: none;
        border-radius: 0;
      }
      input:focus,
      input:hover,
      input:active {
        border: 1px solid #ccc;
      }
      :global(.form-controle) {
        background-color: #181f29;
        color:white
      }
      :global(.description-wrapper-relative) {
        display: block !important;
        position: relative !important;
        margin-bottom: 25px !important;
        height: 200px !important;
      }
      :global(.description-toolbar-absolute) {
        position: absolute !important;
        top: 155px !important;
        width: 550px !important;
        border-radius: 3px !important;
        background: #313030 !important;
        border: 1px solid #313030 !important;
      }
    }
    .surveyTitle:hover, .surveyDescription:hover {
      background-color: #313030;
      border-top: 1px solid #313030;
      border-bottom: 1px solid #313030;
      :global(.fr-wrapper) {
        border: 1px solid #736d6d;
        background-color: #353434;
        color: white;
        :global(.fr-view:focus-visible){
          outline:none !important;
          color:white;
          background-color: #353434;
          :global(p){
            color:white !important;
            // border: 1px solid rgb(139, 139, 139);
          }
        }
        :global(.fr-element table td.fr-selected-cell){
          border-color: orange;
        }
        :global(.fr-element table tr:focus-visible){
          border-color: orange;
        }
        :gloabl(.fr-placeholder){
          color:white !important
        }
      }

    }
  }
}

@media (max-width: 768px) {
  .editContainer {
    .sidebar {
      display: none;
      position: relative;
    }
    .mobileSelect {
      display: block;
    }
    .main {
      position: relative;
      padding: 0;
      left: 0;
      top: 0;
    }
    .surveyWapper {
      padding: 0;
      padding-top: 0;
      margin-top: 0;
      border: 0;
      background-color: inherit;
    }
  }
}
