import { Popover, message, But<PERSON>, <PERSON>lt<PERSON>, Select, Tabs, <PERSON><PERSON><PERSON>rumb, Spin } from 'antd';
import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { ModelState } from './model'
import { CurrentUser, UserModelState } from '@/models/user'
import randomId from '@/utils/randomId'
import Link from 'umi/link'
import groupBy from 'lodash/groupBy'
import deduplication from '@/utils/deduplication'
import classnames from 'classnames'
import { parse } from 'qs'
import styles from './index.less'
import Question from '@/components/Question'
import QuestionEditor from '@/components/QuestionEditor'
import SurveyTab from '@/components/SurveyTab'
import WysiwygEditor from '@/components/WysiwygEditor'

const { TabPane } = Tabs
const { Option } = Select
interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  currentUser?: CurrentUser;
  survey?: any,
  templates?: any,
  favorites?: any,
}

interface ComponentState {
  title?: string,
  currentTab?: string,
  tabs?: any,
  currentSurveyTab?: any,
  libraryOpen?: any,
  description?: string
  id?: string,
  questionMap?: any,
  editting?: any,
  newQuestion?: any,
  isAddNew?: any,
  activeIndex?: any,
  templateId?: any,
  mounted: boolean
}

@connect(
  ({
    user,
    editSurvey,
    loading,
  }: {
    user: UserModelState,
    editSurvey: ModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentUser: user.currentUser,
    survey: editSurvey.survey,
    templates: editSurvey.templates,
    favorites: editSurvey.favorites,
    loading: loading.effects['editSurvey/fetchSurvey'],
  }),
)
class editSurvey extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    const { survey, location } = props
    let state = {}
    const templateId = parse(this.props.location.search.substr(1)).templateId
    if (survey) {
      state = {
        title: survey.title,
        tabs: survey.tabs,
        questionMap: this.groupQuestionByTab(survey.tabs, survey.questions)
      }
    } else {
      const firstId = randomId()
      state = {
        tabs: [
          {
            name: '第一页',
            id: firstId
          }
        ],
        questionMap: {
          [firstId]: []
        }
      }
    }
    state.editting = {}
    state.isAddNew = false
    state.currentTab = templateId ? 'template' : 'widget'
    state.templateId = templateId
    state.currentSurveyTab = state.tabs[0].id
    state.libraryOpen = false
    state.mounted = false
    this.state = state
    // this.state = {
    //   tabs: [
    //     {
    //       name: '第一页',
    //       id: firstId
    //     }
    //   ],
    //   questionMap: {
    //     [firstId]: []
    //   },
    //   editting: {},
    //   isAddNew: false,
    //   currentTab: templateId ? 'template' : 'widget',
    //   templateId: templateId,
    //   currentSurveyTab: firstId,
    //   libraryOpen: false,
    //   mounted: false,
    // }
  }
  componentDidMount() {
    const { location } = this.props
    const queryObject = this.getQueryObject(location)
    const id = queryObject && queryObject.id
    if (id) {
      this.loadSurvey(id)
    }
    this.loadTemplate()
    this.loadFavorite()
    this.setState({ mounted: true })
  }

  componentWillReceiveProps(nextProps: any) {
    const { templateId } = this.state
    const { templates, survey } = nextProps
    if (survey != this.props.survey) {
      const { title, tabs, questions } = nextProps.survey
      console.log(' nextProps.survey', nextProps.survey)
      this.setState({
        title: title,
        tabs: tabs,
        currentSurveyTab: tabs[0] && tabs[0].id,
        questionMap: this.groupQuestionByTab(tabs, questions),
        editting: {}
      })
    }
    if (!this.props.templates.length !== templates.length) {
      let template
      templates.some(item => {
        if (item._id === templateId) {
          template = item
          return true
        }
        return false
      })
      if (template) {
        this.useTemplate(template)()
      }
    }
  }

  componentWillUnmount() {
    this.resetSurveyState({ survey: null })
  }

  getQueryObject = location => parse(location.search.substr(1)) || location.query


  loadSurvey = (id: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'editSurvey/fetchSurvey',
      payload: { id },
    })
  }
  loadTemplate = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'editSurvey/fetchTemplate',
      payload: {},
    })
  }
  loadFavorite = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'editSurvey/fetchFavorite',
      payload: {},
    })
  }
  createFavorite = (data) => {
    const { dispatch } = this.props
    dispatch({
      type: 'editSurvey/fetchCreatFavorites',
      payload: { data },
    })
  }

  postSuevey(data: any) {
    const { dispatch } = this.props
    dispatch({
      type: 'editSurvey/fetchCreat',
      payload: { data },
    })
  }

  editSuevey(id: string, data: any) {
    console.log('data====>', data)
    const { dispatch } = this.props
    dispatch({
      type: 'editSurvey/fetchEdit',
      payload: {
        id,
        data
      },
    })
  }

  delFavorite(id: string) {
    const { dispatch } = this.props
    dispatch({
      type: 'editSurvey/fetchRemoveFavorite',
      payload: {
        id
      },
    })
  }

  addCategory = (data) => {
    const { dispatch } = this.props
    dispatch({
      type: 'editSurvey/fetchAddCategory',
      payload: { data },
    })
  }

  resetSurveyState = (data) => {
    const { dispatch } = this.props
    dispatch({
      type: 'editSurvey/resetSurveyState',
      payload: { data },
    })
  }



  groupQuestionByTab(tabs, questions) {
    // normolize old data
    questions.forEach(question => {
      if (!question.tab) {
        question.tab = tabs[0].id
      }
    })
    return tabs.reduce((out, tab) => {
      out[tab.id] = questions.filter(question => question.tab === tab.id)
      return out
    }, {})
  }


  updateQuestion = question => {
    const { currentSurveyTab, editting } = this.state
    const questions = this.getCurrentQuestions().map(item => {
      if (question._id === item._id) {
        return question
      }
      return item
    })
    this.setState(
      {
        questionMap: {
          ...this.state.questionMap,
          [currentSurveyTab]: questions
        },
        editting: {
          ...editting,
          [question._id]: false
        }
      },
      this.autoSave
    )
  }

  autoSave = () => {
    const { title, description } = this.state
    if (title && description) {
      this.save()
    }
  }

  save = () => {
    const { title, description, tabs, questionMap } = this.state
    const { survey } = this.props
    const questions = tabs.reduce(
      (out, tab) => out.concat(questionMap[tab.id]),
      []
    )
    const symbol = {
      icon: 'id-card',
      color: '#54C7FC'
    }
    if (!title) {
      return message.error('请输入问卷标题')
    }
    if (!questions.length) {
      return message.error('请添加问题')
    }
    if (!survey) {
      this.postSuevey({ title, description, questions, tabs, symbol })
      this.setState({ currentTab: 'widget' })
    } else {
      this.editSuevey(survey._id, { title, description, questions, tabs })
    }
  }

  onTitleChange = event => {
    this.setState({
      title: event.target.value
    })
  }

  stopEdit = id => () => {
    this.setState({
      editting: {
        ...this.state.editting,
        [id]: false
      }
    })
  }

  createEmptyOption() {
    return {
      id: ((Math.random() * 0x100000000) | 0).toString(16),
      value: ''
    }
  }
  useTemplate = template => () => {
    const { title, description, tabs } = template
    const questions = template.questions.map(question => ({
      ...question,
      _id: Math.random().toString(16)
    }))
    this.setState({
      title,
      description,
      tabs,
      currentSurveyTab: tabs[0] && tabs[0].id,
      questionMap: this.groupQuestionByTab(tabs, questions)
    })
  }

  toggleSurvey = () => {
    const { survey } = this.props
    const status = survey.status === 'off' ? 'on' : 'off'
    this.editSuevey(survey._id, { status })
  }

  toggleLibrary = () => {
    this.setState({ libraryOpen: !this.state.libraryOpen })
  }

  updateTab = (id, newProps) => {
    const { tabs } = this.state
    this.setState({
      tabs: tabs.map(tab => {
        if (tab.id === id) {
          return { ...tab, ...newProps }
        }
        return tab
      })
    })
  }

  removeTab = id => {
    const { currentSurveyTab, tabs } = this.state
    const nextTab = currentSurveyTab === id ? tabs[0].id : currentSurveyTab
    this.setState(
      {
        tabs: tabs.filter(tab => tab.id !== id)
      },
      () => {
        this.switchSurveyTab(nextTab)
      }
    )
  }

  removeFavorite = question => () => {
    this.delFavorite(question._id)
  }

  addTab = tab => {
    tab.id = randomId()
    this.setState({
      tabs: [...this.state.tabs, tab],
      currentSurveyTab: tab.id,
      questionMap: {
        ...this.state.questionMap,
        [tab.id]: []
      }
    })
  }

  changeFavorOpen = open => {
    const { favorites } = this.props
    const categories = (favorites || []).filter(favor => favor.category)
      .map(favor => favor.category)
      .concat('myFavorOpen')
    if (!this.state[open]) {
      categories.forEach(category => {
        if (category === open) {
          this.setState({ [`${open}`]: true })
        } else {
          this.setState({ [`${category}`]: false })
        }
      })
    } else {
      this.setState({ [`${open}`]: false })
    }
  }


  cancelNewQuestion = () => {
    this.setState({ newQuestion: null, isAddNew: false })
  }

  onSort = event => {
    const { currentSurveyTab, questionMap } = this.state
    const { oldIndex, newIndex } = event
    const questions = this.getCurrentQuestions()
    const newList = this.reindexElement([...questions], oldIndex, newIndex)
    this.setState({
      questionMap: {
        ...questionMap,
        [currentSurveyTab]: newList
      }
    })
  }

  sortableGroupDecorator = componentBackingInstance => {
    const Sortable = require('../../libs/Sortable.js')
    // check if backing instance not null
    if (componentBackingInstance) {
      const options = {
        animation: 200,
        group: 'survey',
        draggable: '.question',
        // onMove: this.onMove,
        onSort: this.onSort
      }
      Sortable.create(componentBackingInstance, options)
    }
  }

  startEdit = id => {
    this.setState({
      editting: {
        ...this.state.editting,
        [id]: true
      }
    })
  }

  removeQuestion = id => {
    const { currentSurveyTab } = this.state
    const questions = this.getCurrentQuestions()
    this.setState({
      activeIndex: undefined,
      questionMap: {
        ...this.state.questionMap,
        [currentSurveyTab]: questions.filter(question => question._id !== id)
      }
    })
  }

  addNewQuestion = (type) => () => {
    const { currentSurveyTab } = this.state
    const newQuestion = {
      type,
      tab: currentSurveyTab,
      required: true,
      scale: 1,
      options: [this.createEmptyOption(), this.createEmptyOption()]
    }
    newQuestion._id = Math.random().toString(16)
    if (~type.indexOf('matrix')) {
      newQuestion.questions = [
        this.createEmptyOption(),
        this.createEmptyOption()
      ]
    }
    this.setState({ newQuestion, isAddNew: true }, () => {
    })
  }

  saveNewQuestion = question => {
    const { activeIndex, currentSurveyTab } = this.state
    const questions = this.getCurrentQuestions()
    const index = activeIndex === undefined ? questions.length : activeIndex + 1
    questions.splice(index, 0, question)
    this.setState(
      {
        newQuestion: null,
        isAddNew: false,
        questionMap: {
          ...this.state.questionMap,
          [currentSurveyTab]: questions
        }
      },
      this.autoSave
    )
  }

  copyQuestion = question => {
    const id = Math.random().toString(16)
    const options = question.options
      ? question.options.map(option => {
        return {
          ...option,
          id: ((Math.random() * 0x100000000) | 0).toString(16)
        }
      })
      : []
    const questionOptions = question.questions
      ? question.questions.map(option => {
        return {
          ...option,
          id: ((Math.random() * 0x100000000) | 0).toString(16)
        }
      })
      : []
    const { activeIndex, currentSurveyTab } = this.state
    const questions = this.getCurrentQuestions()
    questions.splice(activeIndex + 1, 0, {
      ...question,
      _id: id,
      options,
      questions: questionOptions
    })
    this.setState(
      {
        questionMap: {
          ...this.state.questionMap,
          [currentSurveyTab]: questions
        }
      },
      this.autoSave
    )
  }

  addFromLibrary = question => () => {
    question.tab = this.state.currentSurveyTab
    this.copyQuestion(question)
  }

  onAddNewQuestion = (values) => {
    this.addNewQuestion(values)
  }

  onClickQuestion = index => () => {
    if (this.state.activeIndex === index) {
      return
    }
    this.setState({ activeIndex: index })
  }

  onEditorStateChange = description => {
    this.setState({
      description
    })
  }

  getCurrentQuestions() {
    const { currentSurveyTab, questionMap } = this.state
    return questionMap[currentSurveyTab]
  }

  reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list)
    const [removed] = result.splice(startIndex, 1)
    result.splice(endIndex, 0, removed)
    return result
  }

  reindexElement(arr, oldIndex, newIndex) {
    if (arr.length <= 1) {
      return arr
    }
    arr.splice(newIndex, 0, arr.splice(oldIndex, 1)[0])
    return arr
  }

  switchTab = tab => () => {
    this.setState({ currentTab: tab })
  }

  switchSurveyTab = tab => {
    this.setState({
      currentSurveyTab: tab,
      newQuestion: null,
      activeIndex: undefined
    })
  }

  onTabSort = result => {
    // dropped outside the list
    if (!result.destination) {
      return
    }

    const items = this.reorder(
      this.state.tabs,
      result.source.index,
      result.destination.index
    )

    this.setState({
      tabs: items
    })
  }

  renderQuestions(sortable) {
    const {
      // features: { fundEnabled },
      currentUser,
      favorites,
      survey,
      loading
    } = this.props
    const { uploadResult, uploadAttachment, resetSurveyState } = this.props
    const { newQuestion, editting, isAddNew, activeIndex } = this.state
    const isEditting = Object.keys(editting).some(key => editting[key])
    const questions = this.getCurrentQuestions()
    const indexMap = (questions || [])
      .filter(question => question.type !== 'paragraph')
      .reduce((out, question, index) => {
        out[question._id] = index + 1
        return out
      }, {})
    const categories = deduplication(
      (favorites || []).filter(favor => favor.category).map(favor => favor.category)
    )
    if (loading && sortable) {
      return <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Spin />
      </div>
    }



    console.log('survey=====>', survey)

    return (
      <div
        className={classnames(styles.surveyContainer, {
          hidden: sortable ? isEditting || isAddNew : !isEditting && !isAddNew
        })}
        ref={sortable ? this.sortableGroupDecorator : null}
      >
        {questions.map((question, index) => {
          const renderItem = () => {
            return editting[question._id] ? (
              <QuestionEditor
                {...{
                  question,
                  uploadResult,
                  uploadAttachment,
                  resetSurveyState
                }}
                cancel={this.stopEdit(question._id)}
                save={this.updateQuestion}
                createEmptyOption={this.createEmptyOption}
              />
            ) : (
              <Question
                categories={categories}
                currentUser={currentUser}
                question={question}
                index={indexMap[question._id]}
                edit={this.startEdit}
                remove={this.removeQuestion}
                copy={this.copyQuestion}
                favorite={this.createFavorite}
                addCategory={this.addCategory}
                onClick={this.onClickQuestion(index)}
                isEdit
              />
            )
          }
          return (
            <div
              onClick={this.onClickQuestion(index)}
              key={'show-edit' + question._id + index}
              className="question"
            >
              {renderItem()}
              {((activeIndex === undefined && index === questions.length - 1) ||
                activeIndex === index) && <div id="newQuestionDivider" />}
              {newQuestion &&
                ((activeIndex === undefined &&
                  index === questions.length - 1) ||
                  activeIndex === index) && (
                  <QuestionEditor
                    {...{
                      uploadResult,
                      uploadAttachment,
                      resetSurveyState
                    }}
                    question={newQuestion}
                    cancel={this.cancelNewQuestion}
                    save={this.saveNewQuestion}
                    createEmptyOption={this.createEmptyOption}
                  />
                )}
            </div>
          )
        })}
        {questions.length === 0 && <div id="newQuestionDivider" />}
        {newQuestion && questions.length === 0 && (
          <QuestionEditor
            {...{
              uploadResult,
              uploadAttachment,
              resetSurveyState
            }}
            question={newQuestion}
            cancel={this.cancelNewQuestion}
            save={this.saveNewQuestion}
            createEmptyOption={this.createEmptyOption}
          />
        )}
      </div>
    )
  }
  render() {
    const {
      currentUser
    } = this.props

    const { survey, templates, favorites } = this.props
    const {
      title,
      tabs,
      currentSurveyTab,
      libraryOpen,
      description
    } = this.state
    const { updateTab, removeTab, addTab, switchSurveyTab, onTabSort } = this
    const showSidebarTab = !survey
    const dataKeys = groupBy(
      (favorites || []).filter(favor => favor.visibility === 'public'),
      'category'
    )
    const dataKeyMap = Object.keys(dataKeys).map(dataKey => {
      return {
        icon: 'star',
        name: dataKey,
        open: dataKey,
        data: dataKeys[dataKey]
      }
    })
    const FavoritesMap = [
      {
        icon: 'heart',
        name: '我的收藏',
        open: 'myFavorOpen',
        data: (favorites || []).filter(favor => favor.visibility !== 'public')
      }
    ].concat(dataKeyMap)
    return (
      <div>
        <div className={styles.navbar} style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ marginLeft: 10 }}>
            <Breadcrumb>
              <Breadcrumb.Item>
                <Link to="/duediligence/history">列表</Link>
              </Breadcrumb.Item>
              {survey && survey.title ?
                <Breadcrumb.Item>{survey.title}</Breadcrumb.Item>
                :
                <Breadcrumb.Item>创建问卷</Breadcrumb.Item>
              }
            </Breadcrumb>
          </div>
          <div style={{ marginRight: 30, zIndex: 10 }}>
            {survey && (
              <Link style={{ marginRight: 10 }} to={`/duediligence/preview/${survey._id}`}>
                <Button type='primary' >预览</Button>
              </Link>
            )}
            <Button style={{ marginRight: 10 }} type='primary' onClick={this.save}>保存</Button>
            {survey && (
              <span>
                {survey.status === 'off' ?
                  <Button type='primary' onClick={this.toggleSurvey}>开始回收</Button> :
                  <Button type='primary' onClick={this.toggleSurvey}>暂停回收</Button>
                }
              </span>
            )}
          </div>
        </div>
        <div className={styles.editContainer}>
          <div
            className={classnames(styles.sidebar, {
              [styles.tabOpen]: showSidebarTab
            })}
          >
            <div className={styles.menu}>
              <Tabs defaultActiveKey="widget" tabBarStyle={{ textAlign: 'center' }}>
                <TabPane tab="题目控件" key="widget" >
                  <ul className={classnames('list-group', styles.widgetList)}>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('radio')}
                    >
                      <i className="fa fa-dot-circle-o" />{' '}
                      <span>单选题</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('checkbox')}
                    >
                      <i className="fa fa-check-square-o" />{' '}
                      <span>多选题</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('select')}
                    >
                      <i className="fa fa-caret-square-o-down" />{' '}
                      <span>下拉题</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('matrix_radio')}
                    >
                      <i className="fa fa-object-group" />{' '}
                      <span>矩阵单选题</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('matrix_checkbox')}
                    >
                      <i className="fa fa-object-group" />{' '}
                      <span>矩阵多选题</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('matrix')}
                    >
                      <i className="fa fa-object-group" />{' '}
                      <span>矩阵题</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('sort')}
                    >
                      <i className="fa fa-sort-numeric-asc" />{' '}
                      <span>排序题</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('input')}
                    >
                      <i className="fa fa-file-text-o" />{' '}
                      <span>单行文本</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('textarea')}
                    >
                      <i className="fa fa-file-text-o" />{' '}
                      <span>多行文本</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('paragraph')}
                    >
                      <i className="fa fa-paragraph" />{' '}
                      <span>段落说明</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('table')}
                    >
                      <i className="fa fa-table" />
                      <span>表格题</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('attachment')}
                    >
                      <i className="fa fa-file" />
                      <span>附件题</span>
                    </li>
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('slider')}
                    >
                      <i className="fa fa-sliders" />
                      <span>滑块题</span>
                    </li>
                    {/* {fundEnabled && (
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('fund')}
                    >
                      <i className="fa fa-rmb" /> {t('questionType.fund')}
                    </li>
                  )}
                  {fundEnabled && (
                    <li
                      className="list-group-item"
                      onClick={this.addNewQuestion('valuation')}
                    >
                      <i className="fa fa-file" /> {t('questionType.valuation')}
                    </li>
                  )} */}
                  </ul>
                </TabPane>
                {
                  showSidebarTab &&
                  <TabPane tab="问卷模版" key="template">
                    <ul className={classnames('list-group', styles.templateList)}>
                      {(templates || []).map((template, index) => (
                        <li className="list-group-item" key={`template-${index}`}>
                          <div className={styles.title}>{`${template.title}`}</div>
                          <Tooltip
                            placement="top"
                            title='使用此模版'
                          >
                            <button
                              className={classnames(
                                'btn btn-default btn-xs',
                                styles.button
                              )}
                              onClick={this.useTemplate(template)}
                            >
                              <i className="fa fa-check" />
                            </button>
                          </Tooltip>
                        </li>
                      ))}
                    </ul>
                  </TabPane>
                }
              </Tabs>
            </div>
          </div>
          <Select
            onChange={this.onAddNewQuestion}
            className={styles.mobileSelect}
          >
            <Option value="radio">单选题</Option>
            <Option value="checkbox">多选题</Option>
            <Option value="select">下拉题</Option>
            <Option value="matrix_radio">
              矩阵单选题
            </Option>
            <Option value="matrix_checkbox">
              矩阵多选题
            </Option>
            <Option value="matrix">矩阵题</Option>
            <Option value="sort">排序题</Option>
            <Option value="input">单行文本</Option>
            <Option value="textarea">多行文本</Option>
            <Option value="paragraph">段落说明</Option>
            <Option value="table">表格题</Option>
            <Option value="attachment">附件题</Option>
            <Option value="slider">滑块题</Option>
          </Select>
          <div
            className={classnames(styles.main, {
              [styles.libraryOpen]: libraryOpen
            })}
            id="mainScroll"
          >
            <div className={styles.surveyWapper}>
              <SurveyTab
                isEdit
                currentTab={currentSurveyTab}
                {...{
                  tabs,
                  updateTab,
                  removeTab,
                  addTab,
                  switchSurveyTab,
                  onTabSort
                }}
              />
              <div className={classnames(styles.surveyMain, styles.editting)}>
                {tabs[0] && currentSurveyTab === tabs[0].id && (
                  <div className={styles.surveyTitle}>
                    <div className="inner">
                      <input
                        style={{ backgroundColor: '#353434', color: 'white', borderColor: '#736d6d' }}
                        className="form-control"
                        placeholder='请输入问卷标题'
                        value={title}
                        onChange={this.onTitleChange}
                      />
                    </div>
                  </div>
                )}
                {tabs[0] && currentSurveyTab === tabs[0].id && (
                  <div className={styles.surveyDescription}>
                    <div className="inner">
                      {this.state.mounted && (
                        <WysiwygEditor
                          config={{
                            toolbarInline: true,
                            charCounterCount: false,
                            toolbarVisibleWithoutSelection: false,
                            toolbarSticky: false,
                            placeholderText: '请输入...',
                            heightMin: 50,
                            language: 'zh_cn',
                            toolbarButtons: [
                              'bold',
                              'italic',
                              'underline',
                              'strikeThrough',
                              'subscript',
                              'superscript',
                              '|',
                              'fontFamily',
                              'fontSize',
                              'color',
                              '|',
                              'paragraphFormat',
                              'align'
                            ]
                          }}
                          model={description}
                          onModelChange={this.onEditorStateChange}
                        />
                      )}
                    </div>
                  </div>
                )}
                {this.state.mounted && (
                  <div>
                    {this.renderQuestions(true)}
                    {this.renderQuestions(false)}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div
            className={classnames(styles.library, {
              [styles.open]: libraryOpen
            })}
          >
            <span className={styles.toggleBtn} onClick={this.toggleLibrary}>
              <i className="fa fa-book" />{' '}
              {libraryOpen
                ? '关闭题库'
                : '展开题库'
              }
            </span>
            <div className={styles.title}>
              <h4>题库</h4>
              <span className={styles.closeBtn} onClick={this.toggleLibrary}>
                <i className="fa fa-close" />
              </span>
            </div>
            <ul
              className={classnames(
                'list-group',
                styles.list,
                styles.outerList
              )}
            >
              {FavoritesMap.map(favors => (
                <li
                  className="list-group-item"
                  key={`${favors.name}-${favors.icon}`}
                >
                  <div
                    className={styles.favoriteTitle}
                    onClick={() => {
                      this.changeFavorOpen(favors.open)
                    }}
                  >
                    <i className={`fa fa-${favors.icon}`} /> &nbsp;{favors.name}
                  </div>
                  <ul
                    className={classnames('list-group', styles.innerList)}
                    style={{
                      display:
                        favors.data.length
                          && this.state[favors.open]
                          ? 'block'
                          : 'none'
                    }}
                  >
                    {favors.data.map((favorite, index) => {
                      const popover = (
                        <Question
                          currentUser={currentUser}
                          question={favorite}
                          index={index + 1}
                          answer=""
                        />
                      )
                      const tooltip = '取消收藏'
                      const infoTooltip = '查看详情'
                      return (
                        <li className="list-group-item" key={favorite._id}>
                          <div
                            className={styles.questionTitle}
                            onClick={this.addFromLibrary(favorite)}
                          >
                            {favorite.title}
                          </div>
                          <Popover
                            trigger="click"
                            placement="bottom"
                            content={popover}
                          >
                            <Tooltip
                              placement="top"
                              title={infoTooltip}
                            >
                              <span className="fa fa-info-circle" />
                            </Tooltip>
                          </Popover>
                          {(favors.icon === 'heart' ||
                            !!~currentUser.admin_scopes.indexOf(
                              'fof_super_admin'
                            )) && (
                              <Tooltip placement="top" title={tooltip}>
                                <span
                                  className="fa fa-close"
                                  onClick={this.removeFavorite(favorite)}
                                />
                              </Tooltip>
                            )}
                        </li>
                      )
                    })}
                  </ul>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    )
  }

}
export default editSurvey
