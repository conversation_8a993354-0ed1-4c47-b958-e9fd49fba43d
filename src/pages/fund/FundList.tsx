import { EyeOutlined, MenuOutlined, PlusOutlined, StarOutlined, TableOutlined } from '@ant-design/icons'
import { Button, Card, Input, Tooltip, Divider, Row, Col, Pagination, Spin, Empty, Space, Switch, Dropdown, Menu, Table } from 'antd'
import React, { Component, Fragment } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import classnames from 'classnames'
import _ from 'lodash'
import { FundModelState } from '@/models/fund'
import FilterPanel from '@/components/FilterPanel'
import { checkFilterValue } from '@/components/FilterPanel/filterPanelHelper'
import PortfolioCard from '@/components/PortfolioCard'
import StandardTable, {
  StandardTableColumnProps,
  TableListItem,
  TableListParams,
} from '@/components/StandardTable'
import { formatMessage } from 'umi-plugin-react/locale'
import SelectInvestPoolModal from '@/components/investpool/SelectInvestPoolModal'
import NavPortfolioModal from '@/components/NavPortfolioModal'
import styles from './style.less'
import ExportData from '@/components/ExportData'
import SelectedFundButton from '@/components/SelectedFundButton'
import { getToken } from '@/utils/utils'

const { Search } = Input
const { Group: ButtonGroup } = Button
const t = (id: string, params?: any) => formatMessage({ id }, params)

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  loadingAll: boolean;
  fundListData: any;
  fundListDataAll: any;
  location: any;
  filters: any;
  filterValues?: any;
  currentInvestPool?: any;
  type: string;
  viewMode?: string;
}

interface ComponentState {
  selectedRows: TableListItem[];
  input?: string;
  filterValues?: any;
  viewMode?: string;
  isShowCEtfQdii?: string;
  perPage?: number,
}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    fund,
    loading,
  }: {
    fund: FundModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    fundListData: fund.fundListData,
    fundFactorScoreData: fund.fundFactorScoreData,
    loading: loading.effects['fund/fetch'],
    loadingFactorScore: loading.effects['fund/fetchFactorScore'],
  }),
)
class Funds extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    const { type } = props
    const filterData = localStorage.getItem(`qutke:filters:${type}`)
    const state = {
      selectedRows: [],
      filterValues: {},
    }
    if (props.viewMode) {
      state.viewMode = props.viewMode
    }
    let filterValues = {}
    if (props.filterValues) {
      filterValues = props.filterValues
    } else {
      if (filterData) {
        filterValues = JSON.parse(filterData)
      }
      const factorSchemaFilter = (props.filters || []).find(item => item.filterId === 'factorSchema')
      if (factorSchemaFilter) {
        if (!filterValues.factorSetting) {
          filterValues.factorSetting = factorSchemaFilter.tabs[0].defaultValues
        }
      }
    }
    state.filterValues = filterValues
    state.perPage = state.viewMode === 'grid' ? 12 : 10
    this.state = state
  }

  componentDidMount() {
    this.loadData({})
    // this.loadUserGroups()
  }

  loadUserGroups() {
    const { dispatch } = this.props
    dispatch({
      type: 'fund/fetchUserGroups',
    })
  }

  loadData(params: Partial<TableListParams>) {
    const { type } = this.props
    const { filterValues, input, viewMode, isShowCEtfQdii, perPage } = this.state
    const { dispatch } = this.props
    const tableFormKeys = ['quotas', 'industryRatio', 'industryBoardRatio', 'stockRatio', 'factors', 'factorDims', 'fundTags']
    const filterParams = Object.keys(filterValues).reduce((out, key) => {
      const values = filterValues[key]
      if (values !== undefined) {
        if (Array.isArray(values)) {
          if (values.length) {
            if (tableFormKeys.includes(key)) {
              const vals = values.filter(item => item.valueRange || key === 'fundTags').map(item => {
                const ret = _.omit(item, ['key'])
                const refOption = ret._refOption
                if (ret.valueRange) {
                  ret.valueRange = ret.valueRange.map(item => {
                    if (!item) {
                      return item
                    }
                    if (refOption.format === 'number') {
                      return item
                    }
                    return item / 100
                  })
                }
                return ret
              })
              if (vals.length) {
                out[key] = JSON.stringify(vals)
              }
            } else {
              out[key] = values.join(',')
            }
          }
        } else {
          if (key === 'factorSetting') {
            out[key] = JSON.stringify(values)
          } else {
            out[key] = values
          }
        }
      }
      return out
    }, {})
    const assetTypes = (filterParams.asset_type || '').split(',').filter(Boolean)
    if (assetTypes.length) {
      const styleTypes = (filterParams.style_type || '')
        .split(',')
        .filter(item => {
          return assetTypes.includes(item[0])
        })
      if (styleTypes.length) {
        filterParams.style_type = styleTypes.join(',')
      }
    } else {
      delete filterParams.style_type
    }
    params.type = type
    dispatch({
      type: type === 'mutual' ? 'fund/fetchFactorScore' : 'fund/fetch',
      payload: {
        per_page: perPage,
        ...params,
        ...filterParams,
        input,
        isShowCEtfQdii,
        // isAll: type === 'mutual' ? 'Y' : 'N',
      },
    })
  }

  handleStandardTableChange = (params: Partial<TableListParams>) => {
    this.setState({ perPage: params.per_page || this.state.perPage }, () => {
      this.loadData(params)
    })
  };

  handleGridPaginationChange = (page: number) => {
    this.loadData({ page })
  };

  handleGridPageSizeChange = (page: number, perPage: number) => {
    this.setState({ perPage: perPage }, () => {
      this.loadData({})
    })
  }

  handleSeachInput = (value: string) => {
    this.setState({ input: value }, () => {
      this.loadData({
        page: 1,
      })
    })
  };

  handleSwitchChange = (checked) => {
    const isShowCEtfQdii = checked ? 'Y' : 'N'
    this.setState({ isShowCEtfQdii }, () => {
      this.loadData({
        page: 1,
      })
    })
  }

  handleSelectRows = (rows: TableListItem[]) => {
    this.setState({
      selectedRows: rows,
    })
  };

  handleFilterChange = (filterValues: any) => {
    this.setState({ filterValues }, () => {
      this.loadData({ page: 1 })
    })
    localStorage.setItem('qutke:filters:mutual', JSON.stringify(filterValues))
  };

  handleViewModeClick = (viewMode: string) => () => {
    this.setState({ viewMode }, () => {
      this.loadData({ page: 1 })
    })
  };

  addFundToInvestPool = (fund: any) => (rows: TableListItem[]) => {
    const investPoolId = rows[0]._id
    const ids = [fund._id]
    this._addFundsToInvestPool(investPoolId, ids)
  };

  addFundsToInvestPool = (rows: TableListItem[]) => {
    const { selectedRows } = this.state
    const ids = selectedRows.map(item => item._id)
    const investPoolId = rows[0]._id
    this._addFundsToInvestPool(investPoolId, ids)
  };

  addFundQueryToInvestPool = (rows: TableListItem[]) => {
    const investPoolId = rows[0]._id
    this._addFundsToInvestPool(investPoolId, [], JSON.stringify(this.state.filterValues))
  }

  _addFundsToInvestPool = (investPoolId: string, ids: string[], filterValues?: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'investpool/addFunds',
      payload: {
        id: investPoolId,
        data: {
          ids,
          filterValues,
        },
      },
    })
  };

  render() {
    const { type, fundListData, fundFactorScoreData, loading, loadingFactorScore, filters, dispatch, currentInvestPool } = this.props
    const fundRouteType = type === 'mutual' ? 'fund' : 'activefund'
    const getDefaultTab = record => {
      const defaultTab = (type === 'mutual' && !['0', 'FOF'].includes(record.asset_type)) ? 'factor_evaluation' : 'invest_performance'
      return defaultTab
    }
    let fundColumns = [
      {
        title: '成立时间',
        dataIndex: 'startDate',
        format: 'date',
        align: 'right',
        sorter: true,
      },
      {
        title: '净值结束日期',
        dataIndex: 'navEndDate',
        align: 'right',
        format: 'date',
        sorter: true,
      },
      {
        title: '累计收益',
        dataIndex: 'accReturn',
        sorter: true,
        align: 'right',
        format: 'percentage',
      },
      {
        title: '年化收益',
        dataIndex: 'yearReturn',
        sorter: true,
        align: 'right',
        format: 'percentage',
      },
      {
        title: '最大回撤',
        dataIndex: 'maxDrawdown',
        align: 'right',
        format: 'valPercentage',
        sorter: true,
      },
      {
        title: '波动率',
        dataIndex: 'vol',
        align: 'right',
        format: 'valPercentage',
        sorter: true,
      },
    ]
    if (type === 'mutual') {
      fundColumns = [{
        title: '成立时间',
        dataIndex: 'startDate',
        format: 'date',
        align: 'right',
        sorter: true,
        width: 100,
      }, {
        title: '净值日期',
        dataIndex: 'navEndDate',
        align: 'right',
        format: 'date',
        sorter: true,
        width: 100,
      },{
        title: '基金经理',
        dataIndex: 'managers',
        format: 'curManagerList',
      }, {
        title: '资产分类',
        dataIndex: 'asset_type',
        width: 100,
      }, {
        title: '风格分类',
        dataIndex: 'style_type',
        width: 100,
      }, {
        title: '收益类',
        dataIndex: 'incomeFactorScore',
        format: 'zeroNotNumber',
      }, {
        title: '风险类',
        dataIndex: 'riskFactorScore',
        format: 'zeroNotNumber',
      }, {
        title: '归因类',
        dataIndex: 'attributionFactorScore',
        format: 'zeroNotNumber',
      }, {
        title: '策略类',
        dataIndex: 'strategyFactorScore',
        format: 'zeroNotNumber',
      }, {
        title: '基金公司类',
        dataIndex: 'companyFactorScore',
        format: 'zeroNotNumber',
        width: 100,
      }, {
        title: '基金经理类',
        dataIndex: 'managerFactorScore',
        format: 'zeroNotNumber',
        width: 100,
      }, {
        title: '持仓类',
        dataIndex: 'positionFactorScore',
        format: 'zeroNotNumber',
      }, {
        title: '综合得分',
        dataIndex: 'totalFactorScore',
        format: 'zeroNotNumber',
        width: 90,
      }, {
        title: '综合得分排名',
        dataIndex: 'totalFactorScoreRank',
        format: 'zeroNotNumber',
        width: 120,
      }].map(item => {
        return {
          ...item,
          sorter: item.dataIndex !== 'managers',
          width: item.width || 80,
        }
      })
    }
    const columns: StandardTableColumnProps[] = [
      {
        title: '名称',
        dataIndex: 'name',
        fixed: 'left',
        width: 120,
        render: (text, record) => {
          return (
            <a
              href={`/${fundRouteType}/${record._id}/${getDefaultTab(record)}`}
              rel="noopener noreferrer"
              target="_blank"
            >
              {record.name}
            </a>
          )
        },
      },
      ...fundColumns,
      {
        title: '操作',
        width: 60,
        render: (text, record) => (
          <Fragment>
            <Tooltip title="查看详情">
              <a
                href={`/${fundRouteType}/${record._id}/${getDefaultTab(record)}`}
                rel="noopener noreferrer"
                target="_blank"
              >
                <EyeOutlined />
              </a>
            </Tooltip>
            <Divider type="vertical" />
            <SelectInvestPoolModal dataType="fund" onSelectRow={this.addFundToInvestPool(record)}>
              <Tooltip title="添加到静态跟踪列表">
                <a>
                  <StarOutlined />
                </a>
              </Tooltip>
            </SelectInvestPoolModal>
          </Fragment>
        ),
      },
    ]
    const { selectedRows, filterValues, viewMode } = this.state
    const hasFilterValue = checkFilterValue(filters, filterValues)
    const filteredFundNum = fundListData && fundListData.pagination && fundListData.pagination.total
    const addPoolMenus = (
      <Menu>
        {!!selectedRows.length &&
        <Menu.Item>
          <SelectInvestPoolModal disabled={!selectedRows.length} dataType="fund" onSelectRow={this.addFundsToInvestPool}>
            当前选中的 {selectedRows.length} 支基金
          </SelectInvestPoolModal>
        </Menu.Item>}
        {hasFilterValue && !!filteredFundNum &&
        <Menu.Item>
          <SelectInvestPoolModal disabled={!filteredFundNum} dataType="fund" onSelectRow={this.addFundQueryToInvestPool}>
            满足查询条件的 {filteredFundNum} 支基金
          </SelectInvestPoolModal>
        </Menu.Item>}
      </Menu>
    )
    const handleDownload = () => {
      const token = getToken()
      const factorSetting = filterValues.factorSetting
      let href = `/api/factorschemas/scoredata/fund?token=${token.slice(7)}`
      if (factorSetting) {
        href = `${href}&factorSetting=${JSON.stringify(factorSetting)}`
      }
      window.open(href)
    }
    return (<>
      {filters && !!filters.length && (
        <FilterPanel
          filters={filters}
          filterValues={filterValues || {}}
          onChange={this.handleFilterChange}
          dispatch={dispatch}
          currentInvestPool={currentInvestPool}
          investPoolDataType="fund"
        />
      )}
      {viewMode !== 'grid' && (
        <Card
          bordered={false}
          title={
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={this.handleSeachInput}
            />
          }
          extra={
            <Space>
              {!!selectedRows.length &&
              <SelectedFundButton
                isManager={false}
                selectedRows={selectedRows}
                onUnselectAll={() => {
                  this.handleSelectRows([])
                }}
              />}
              <NavPortfolioModal t={t} funds={selectedRows}>
                <Button
                  ghost
                  disabled={!selectedRows.length}
                  type="primary"
                  icon={<PlusOutlined />}
                  size="small"
                >
                  创建组合
                </Button>
              </NavPortfolioModal>
              <Dropdown overlay={addPoolMenus} placement="bottomLeft">
                <Tooltip title="添加到静态跟踪列表">
                  <Button
                    ghost
                    disabled={!selectedRows.length && !hasFilterValue}
                    type="primary"
                    icon={<PlusOutlined />}
                    size="small"
                  >
                    静态跟踪列表
                  </Button>
                </Tooltip>
              </Dropdown>
              {viewMode && <Divider type="vertical" />}
              {viewMode && (
                <ButtonGroup size="small" className={styles.viewMode}>
                  <Button
                    className={classnames({ active: viewMode === 'grid' })}
                    icon={<TableOutlined />}
                    onClick={this.handleViewModeClick('grid')}
                  />
                  <Button
                    className={classnames({ active: viewMode === 'list' })}
                    icon={<MenuOutlined />}
                    onClick={this.handleViewModeClick('list')}
                  />
                </ButtonGroup>
              )}
              {type === 'mutual' &&
              <ExportData onClick={handleDownload} title="导出因子得分"/>}
            </Space>
          }
        >
          <div className={styles.tableList}>
            <StandardTable
              bordered
              selectedRows={selectedRows}
              loading={type === 'mutual' ? loadingFactorScore : loading}
              data={type === 'mutual' ?  fundFactorScoreData : fundListData}
              columns={columns}
              onSelectRow={this.handleSelectRows}
              onChange={this.handleStandardTableChange}
              rowKey="_id"
              size="small"
              scroll={{ x: 1200 }}
            />
            {false && type === 'mutual' &&
            <StandardTable
              selectedRows={selectedRows}
              loading={loadingAll}
              data={{
                list: fundListDataAll,
                pagination: true,
              }}
              columns={columns}
              onSelectRow={this.handleSelectRows}
              rowKey="_id"
              size="small"
              scroll={{ x: 1200 }}
            />}
          </div>
        </Card>
      )}
      {viewMode === 'grid' && (
        <Spin spinning={loading}>
          <div style={{ marginBottom: 15 }}>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={this.handleSeachInput}
            />
            {viewMode && (
              <ButtonGroup size="small" className={styles.viewMode} style={{ float: 'right' }}>
                <Button
                  className={classnames({ active: viewMode === 'grid' })}
                  icon={<TableOutlined />}
                  onClick={this.handleViewModeClick('grid')}
                />
                <Button
                  className={classnames({ active: viewMode === 'list' })}
                  icon={<MenuOutlined />}
                  onClick={this.handleViewModeClick('list')}
                />
              </ButtonGroup>
            )}
          </div>
          <Row gutter={10}>
            {fundListData &&
              fundListData.list.map(item => (
                <Col key={item._id} xs={24} sm={12} md={8} lg={6} xl={6} xxl={4}>
                  <div style={{ marginBottom: 15 }}>
                    <PortfolioCard portfolio={item} />
                  </div>
                </Col>
              ))}
          </Row>
          {(!fundListData || fundListData.list.length === 0) && (
            <div style={{ height: 350 }}>
              <Empty />
            </div>
          )}
          {fundListData && fundListData.list.length !== 0 && (
            <Pagination
              showQuickJumper
              hideOnSinglePage
              style={{ float: 'right' }}
              size="small"
              onChange={this.handleGridPaginationChange}
              onShowSizeChange={this.handleGridPageSizeChange}
              {...fundListData.pagination}
            />
          )}
        </Spin>
      )}
    </>)
  }
}

@connect(
  ({
    loading,
  }: {
    loadingOptions: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    loadingOptions: loading.effects['fund/fetchQueryOptions'],
  }),
)
class LoadingWrapper extends Component<ComponentProps, ComponentState> {
  render() {
    const columns = [{
      title: '名称',
      dataIndex: 'name',
    }, {
      title: '成立时间',
      dataIndex: 'startDate',
      format: 'date',
      align: 'right',
      sorter: true,
      width: 100,
    }, {
      title: '净值日期',
      dataIndex: 'navEndDate',
      align: 'right',
      format: 'date',
      sorter: true,
      width: 100,
    },{
      title: '基金经理',
      dataIndex: 'managers',
      format: 'curManagerList',
    }, {
      title: '收益类',
      dataIndex: 'incomeFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '风险类',
      dataIndex: 'riskFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '归因类',
      dataIndex: 'attributionFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '策略类',
      dataIndex: 'strategyFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '基金公司类',
      dataIndex: 'companyFactorScore',
      format: 'zeroNotNumber',
      width: 100,
    }, {
      title: '基金经理类',
      dataIndex: 'managerFactorScore',
      format: 'zeroNotNumber',
      width: 100,
    }, {
      title: '持仓类',
      dataIndex: 'positionFactorScore',
      format: 'zeroNotNumber',
    }, {
      title: '综合得分',
      dataIndex: 'totalFactorScore',
      format: 'zeroNotNumber',
      width: 90,
    }]
    if (this.props.loadingOptions) {
      return <Table columns={columns} loading/>
    }
    return <Funds {...this.props}/>
  }
}
export default LoadingWrapper
