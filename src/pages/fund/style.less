@import '~antd/es/style/themes/default.less';

.progress {
  :global(.ant-progress-inner) {
    background-color: #3a404c;
  }
}

.progressWrapper {
  position: relative;
  .progressTarget {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 9;
    width: 20px;
  }
  .up {
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 6px;
    background-color: rgb(19, 194, 194);
  }
  .down {
    position: absolute;
    top: auto;
    bottom: -2px;
    left: 0;
    width: 3px;
    height: 6px;
    background-color: rgb(19, 194, 194);
  }
}

.navTabs {
  :global(.ant-card-body) {
    padding-bottom: 0;
    padding-top: 0;
  }
  :global(.ant-tabs-nav) {
    margin-bottom: 0;
    padding-top: 0;
  }
}

.container {
  min-height: 80vh;
  background: @component-background;
}
