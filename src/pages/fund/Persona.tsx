import React, { useEffect } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import router from 'umi/router'
import Link from 'umi/link'
import { useRequest } from '@umijs/hooks'
import {
  Menu, Dropdown, Breadcrumb, Empty, Tabs, Card, Row,
  Col, Tooltip, Progress, Table, Cascader, Affix, Select, Space, Button,
} from 'antd'
import { FileExcelOutlined } from '@ant-design/icons'
import { GridContent } from '@ant-design/pro-layout'
import PageLoading from '@/components/PageLoading'
import Chart from '@/components/Chart/Chart'
import IndividualRating from '@/pages/persona/components/IndividualRating'
import ExportDataReportModal from '@/components/ExportDataReportModal'
import { assetClassMap, styleTypeMap, compositTypeMap } from '@/utils/kymDefMapping'
import { getActiveFunds } from '@/services/fund'
import styles from './style.less'
import { getToken } from '@/utils/utils'
import isPlustHost from '@/utils/isPlusHost'

const { TabPane } = Tabs
const { Option } = Select
interface WrapperProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentFund: any;
  match: any;
  location: any;
  searchOptions: any;
}

const MainContent = ({ currentFund, location, activeTreeData, searchOptions, currentUser }: { currentFund: any; location: any; activeTreeData?: any; searchOptions: any; }) => {
  const { pathname } = location
  const attrMenuData = [
    {
      name: '多资产归因',
      value: 'multiasset_attribution',
    }, {
      name: '权益归因',
      value: 'stock_attribution',
    }, {
      name: '债券归因',
      value: 'bond_attribution',
    }, {
      name: '可转债归因',
      value: 'convtbond_attribution',
    }, {
      name: '绝对贡献',
      value: 'absolute_attribution',
    }, {
      name: '风格归因 ',
      value: 'style_attribution',
    }]
  if (currentFund.isPortfolio) {
    attrMenuData.shift()
  }
  const splits = pathname.split('/')
  let activeKey = splits.pop()
  if (attrMenuData.some(item => item.value === activeKey)) {
    activeKey = 'attr'
  }
  if (activeKey.includes('detection_')) {
    activeKey = 'detection'
  }
  const pathPrefix = splits.join('/')
  const isBond = currentFund.name && currentFund.name.includes('固收')
  const handleTabChange = (activeKey: string) => {
    let tab = activeKey
    if (activeKey === 'attr') {
      tab = isBond ? 'bond_attribution' : 'stock_attribution'
    }
    if (activeKey === 'detection') {
      tab = 'detection_performance'
    }
    router.push(`${pathPrefix}/${tab}`)
  }
  const handleDownload = (event) => {
    const token = getToken()
    const href = `/api/products/${currentFund._id}/download/mutualreport?token=${token.slice(7)}&type=${event.key}`
    window.open(href)
  }
  const handleAttrMenuChange = (event) => {
    router.push(`${pathPrefix}/${event.key}`)
  }
  const attrMenu = (
    <Menu onClick={handleAttrMenuChange}>
      {attrMenuData.map(item => <Menu.Item key={item.value}>{item.name}</Menu.Item>)}
    </Menu>
  )
  const reportDownloadMenu = (
    <Menu onClick={handleDownload}>
      {[
        { name: '权益', value: 'stock' },
        { name: '转债', value: 'convtBond' },
        { name: '固收', value: 'bond' },
      ].map(item => <Menu.Item key={item.value}>{item.name}</Menu.Item>)}
    </Menu>
  )
  let tabs = [
    {
      name: '业绩概况',
      tab: 'invest_performance',
    },
  ]
  // if (currentFund.tableCount > 0) {
    tabs = tabs.concat([{
      name: '资产配置',
      tab: 'asset_structure',
    }, {
      name: '权益持仓',
      tab: 'stock_analyze',
    }, {
      name: '债券持仓',
      tab: 'bond_analyze',
    },
    ])
  // }
  if (currentFund._syncType === 'mutual') {
    tabs.push({
      name: '持仓明细',
      tab: 'position_series_query',
    })
    tabs.push({
      name: '归因分析',
      tab: 'attr',
    })
    tabs.push({
      name: '基金经理',
      tab: 'manager_list',
    })
    const isPlust = isPlustHost()
    if (isPlust) {
      tabs.push({
        name: '持仓探测',
        tab: 'detection',
      })
    }
  } else if (window.__isTradingNetwork) {
    tabs.push({
      name: '持仓明细',
      tab: 'position_series_query',
    })
    tabs.push({
      name: '归因分析',
      tab: 'attr',
    })
  }
  if (currentFund.isPortfolio) {
    tabs.unshift({
      name: '组合概览',
      tab: 'overview',
    })
  }
  let pageMenusIds = currentUser && currentUser.menus.map(item => item.menuId)
  //menuid d基金详情因子评价的id
  if (['mutual', 'activeFund'].includes(currentFund._syncType) && pageMenusIds && pageMenusIds.includes('d')) {
    if (currentFund._syncType === 'mutual') {
      tabs.unshift({
        name: '基金标签',
        tab: 'fundtags',
      })
    }
    tabs.unshift({
      name: '因子评价',
      tab: 'factor_evaluation',
    })
  }
  if (currentFund.childFundCodes && currentFund.childFundCodes.length) {
    tabs.push({
      name: '子基金分析',
      tab: 'mom_analyze',
    })
  }
  if (['mutual', 'activeFund'].includes(currentFund._syncType)) {
    tabs.push({
      name: '相关文档',
      tab: 'ref_due_doc',
    })
  }
  return (
    <Affix offsetTop={0}>
      <Card className={styles.navTabs}>
        <Tabs
          animated={false}
          activeKey={activeKey}
          className={styles.tabs}
          onTabClick={handleTabChange}
          tabBarExtraContent={
            currentFund._syncType === 'mutual' &&
            <Space>
              <Select
                showSearch
                placeholder="输入名称或代码搜索"
                filterOption={(input, option) => {
                  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }}
                onChange={value => {
                  const code = value.split('__')[0]
                  const fund = searchOptions.mutualFunds.find(item => item._qutkeId === code)
                  const url = `/fund/${fund._id}/factor_evaluation`
                  window.open(url)
                }}
                style={{ width: '250px' }}
              >
                {searchOptions.mutualFunds.map(item => {
                  return (
                    <Option value={`${item._qutkeId}__${item.name}`}>
                      <Space>
                        <Button type="primary" size="small" ghost style={{
                          height: 20,
                          padding: '0.8px 5px',
                          fontSize: 12,
                          borderColor: '#faad14',
                          color: '#faad14',
                        }}>
                          {item._qutkeId}
                        </Button>
                        <span>{item.name}</span>
                      </Space>
                    </Option>
                  )
                })}
              </Select>
              {false &&
                <Dropdown
                  overlay={reportDownloadMenu}
                // onVisibleChange={this.handleVisibleChange}
                // visible={this.state.visible}
                >
                  <Button icon={<FileExcelOutlined />}>
                    数据分析报告
                  </Button>
                </Dropdown>}
              <ExportDataReportModal fund={currentFund} />
            </Space>
          }
        >
          {tabs.map(item => {
            if (item.tab === 'attr_bak') {
              return (
                <TabPane tab={
                  <Dropdown
                    overlay={attrMenu}
                  // onVisibleChange={this.handleVisibleChange}
                  // visible={this.state.visible}
                  >
                    <div>归因分析</div>
                  </Dropdown>
                } key="attr" />
              )
            }
            return (
              <TabPane tab={item.name} key={item.tab} />
            )
          })}
        </Tabs>
        {currentFund._syncType === 'activeFund' &&
          <Cascader
            showSearch
            style={{ float: 'right', marginTop: -40 }}
            placeholder="切换组合"
            options={activeTreeData}
            expandTrigger="hover"
            displayRender={(label) => label[label.length - 1]}
            onChange={(labels) => {
              const { pathname } = location
              const splits = pathname.split('/')
              const fundId = labels.pop()
              splits[2] = fundId
              window.location.href = splits.join('/')
            }}
          />}
      </Card>
    </Affix>
  )
}

const LeftSider = ({ currentFund }: { currentFund: any }) => {
  const data = [
    {
      value: currentFund.name,
      name: '',
    },
    {
      name: '资产类别',
      value: assetClassMap[currentFund.asset_type],
    },
    {
      name: '风格类别',
      value: styleTypeMap[currentFund.style_type],
    },
    {
      name: '综合类别',
      value: compositTypeMap[currentFund.class_level],
    },
    {
      name: '基金公司',
      value: currentFund.company,
    },
    {
      name: '展望',
      format: 'predictText',
      value: currentFund.prospect,
    },
  ]
  const chartQuotas = [
    {
      name: '基金经理个人属性',
      value: 'manager_property',
    },
    {
      name: '基金公司业务发展',
      value: 'development',
    },
    {
      name: '核心团队稳定性',
      value: 'team_stability',
    },
    {
      name: '历史业绩',
      value: 'invest_performance',
    },
    {
      name: '投资流程抗压性',
      value: 'invest_process_resistence',
    },
    {
      name: '投资业绩可解释性',
      value: 'invest_performance_explicable',
    },
    {
      name: '投资理念一惯性',
      value: 'invest_philosophy_consistency',
    },
    {
      name: '费率',
      value: 'fee',
    },
  ].map(item => {
    return {
      ...item,
      tab: item.value.toLowerCase(),
    }
  })
  const getRatingData = item => {
    return chartQuotas.reduce((out, quota) => {
      const key = quota.value
      out[key] = item[key] || 0
      return out
    }, {})
  }
  const chartData = [
    {
      name: currentFund.name,
      ...getRatingData(currentFund),
    },
  ]
  const series = chartData.map(item => {
    return {
      pointPlacement: 'on',
      name: item.name,
      data: chartQuotas.map(quota => item[quota.value]),
    }
  })
  const categories = chartQuotas.map(item => item.name)
  const chartConfig = {
    chart: {
      polar: true,
      type: 'line',
      height: 280,
    },
    pane: {
      size: '80%',
    },
    tooltip: {
      shared: true,
      pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.2f}</b><br/>',
    },
    xAxis: {
      categories,
      tickmarkPlacement: 'on',
      lineWidth: 0,
      labels: {
        useHTML: true,
        formatter: function formatter() {
          const labelValue = this.value + ''
          const start = labelValue.slice(0, 4)
          const end = labelValue.slice(4)
          if (!end) {
            return `<span>${start}</span>`
          }
          return `<span>${start}</span><br/><span>${end}</span>`
        },
      },
    },
    yAxis: {
      gridLineInterpolation: 'polygon',
      min: 0,
      max: 5,
      labels: {
        format: '{value}',
      },
    },
    series,
  }

  const columns = [
    {
      dataIndex: 'name',
    },
    {
      dataIndex: 'value',
      align: 'right',
      render: (text: number, record: any) => {
        if (!record.format) {
          return text
        }

        if (record.format === 'predictText') {
          if (!currentFund.composite_rating || !currentFund.prospect) {
            return '维持'
          }
          const diff =
            ((currentFund.prospect - currentFund.composite_rating) / currentFund.composite_rating) *
            100
          if (diff >= 7) {
            return '看多'
          } else if (diff <= -7) {
            return '看空'
          }
          return '维持'
        }

        return (
          <Tooltip title={text && text.toFixed(2)}>
            <div className={styles.progressWrapper}>
              <Progress
                className={styles.progress}
                showInfo={false}
                strokeColor={{
                  '0%': '#87d068',
                  '50%': '#108ee9',
                  '100%': '#f44336',
                }}
                strokeLinecap="square"
                percent={100}
              />
              <div className={styles.progressTarget} style={{ left: `${(text / 5) * 100}%` }}>
                <span className={styles.up}></span>
                <span className={styles.down}></span>
              </div>
            </div>
          </Tooltip>
        )
      },
    },
  ]
  return (
    <>
      <Card title="">
        <Table
          showHeader={false}
          pagination={false}
          columns={columns}
          dataSource={data}
          size="small"
        />
      </Card>
      <IndividualRating title="综合评分：" rating={currentFund.composite_rating} />
      <IndividualRating title="展望评分：" rating={currentFund.prospect} />
      <Card>
        <Chart options={chartConfig} />
      </Card>
    </>
  )
}

const Wrapper: React.FC<WrapperProps> = props => {
  const {
    dispatch,
    children,
    location,
    match: {
      params: { id },
    },
    currentFund,
    loading,
    searchOptions,
    currentUser,
  } = props
  useEffect(() => {
    dispatch({
      type: 'fund/fetchOne',
      payload: {
        id,
      },
    })
    dispatch({
      type: 'manager/fetchBenchmarks',
      payload: {
        type: location.pathname.includes('/activefund') ? 'private' : 'public'
      }
    })
  }, [])
  const { data: activeFunds = [] } = useRequest(() => {
    return getActiveFunds()
  })
  const activeTreeData = activeFunds.filter(item => {
    return !item._qutkeId || !item._qutkeId.includes('_')
  }).map(item => {
    const childFundCodes = item.childFundCodes || []
    const childFunds = activeFunds
      .filter(fund => childFundCodes.includes(fund._qutkeId))
      .map(fund => {
        return {
          label: fund.name,
          value: fund._id,
        }
      })
    if (childFunds.length) {
      childFunds.unshift({
        label: item.name,
        value: item._id,
      })
    }
    return {
      label: item.name,
      value: item._id,
      children: childFunds,
    }
  })
  if (!currentFund && loading) {
    return <PageLoading />
  }
  if (!currentFund) {
    return (
      <Empty
        style={{ padding: '100px' }}
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="你查看的基金不存在"
      />
    )
  }
  if (currentFund._syncType !== '_mutual') {
    let activeKey = location.pathname.split('/')[1]
    if (currentFund.portfolioType === 'saa') {
      activeKey = 'saaschema'
    } else if (
      currentFund.portfolioType === 'factorbased'
      || currentFund.portfolioType === 'topdown'
      || currentFund.portfolioType === 'nav'
    ) {
      activeKey = currentFund.portfolioType
    }
    const nameMap = {
      fund: '基金产品',
      activefund: '实盘组合',
      fof: '模拟组合',
      saaschema: '配置方案池',
      fundPortfolio: '基金组合池',
      factorbased: '基于因子的组合构建',
      topdown: '自上而下组合构建',
      nav: '基金组合池',
    }
    const pathMap = {
      fof: 'fof/portfolios',
      saaschema: 'assetallocation/saaschema',
      fundPortfolio: 'fof/fundpool',
      factorbased: 'fof/factorbased',
      topdown: 'fof/topdown',
      nav: 'fof/fundpool',
    }
    return (
      <GridContent>
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>
            <Link to={`/${pathMap[activeKey] || activeKey}`}>
              {nameMap[activeKey]}
            </Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <span>{currentFund.name}</span>
            {currentFund._qutkeId &&
              <Button type="primary" size="small" ghost style={{
                marginLeft: 10,
              }}>
                {currentFund._qutkeId}
              </Button>}
          </Breadcrumb.Item>
        </Breadcrumb>
        <MainContent currentFund={currentFund} location={location} activeTreeData={activeTreeData} searchOptions={searchOptions} currentUser={currentUser} />
        <Card className={styles.navTabs}>
          <div className={styles.container}>
            {children}
          </div>
        </Card>
      </GridContent>
    )
  }

  return (
    <GridContent>
      <Row gutter={16}>
        <Col lg={6} md={24}>
          <LeftSider currentFund={currentFund} />
        </Col>
        <Col lg={18} md={24}>
          <MainContent currentFund={currentFund} location={location} />
          <Card className={styles.navTabs}>
            {children}
          </Card>
        </Col>
      </Row>
    </GridContent>
  )
}

export default connect(
  ({
    fund,
    loading,
    user,
  }: {
    fund: any;
    user: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    searchOptions: user.searchOptions,
    loading: loading.models.fund,
    currentUser: user.currentUser,
  }),
)(Wrapper)
