import React from 'react'
import { Tooltip } from 'antd'
import { EditOutlined, PlusOutlined } from '@ant-design/icons'
import NavPortfolioModal from '@/components/NavPortfolioModal'
import List from '../list'
import { formatMessage } from 'umi-plugin-react/locale'

const t = (id: string, params?: any) => formatMessage({ id }, params)

export default () => {
  const defaultTabs = [{
    name: '公募基金',
    value: 'mutual',
  },
  {
    name: '跟踪列表',
    value: 'trackinglist',
  }]
  const renderEdit = (record) => {
    return (
      <NavPortfolioModal
        isEdit
        defaultTabs={defaultTabs}
        fundData={record}
        t={t}
        fundType={record.portfolioType}
        navPortfolioType={
          record.portfolioType === 'nav' ? record.navPortfolioType || 'nav' : undefined
        }
      >
        <Tooltip title="编辑">
          <EditOutlined />
        </Tooltip>
      </NavPortfolioModal>
    )
  }
  const renderCreate = () => {
    return (
      <NavPortfolioModal isReloadAfterCreated t={t} defaultTabs={defaultTabs}>
        <a><PlusOutlined />新建基金组合池</a>
      </NavPortfolioModal>
    )
  }
  return (
    <List
      defaultParams={{ portfolioType: 'nav', type: 'portfolio' }}
      renderEdit={renderEdit}
      renderCreate={renderCreate}
    />
  )
}
