import { AnyAction, Reducer } from 'redux'
import { EffectsCommandMap } from 'dva'
import { queryFund } from './service'

import { TableListData } from '@/components/StandardTable'

export interface StateType {
  fundListData: TableListData;
}

export type Effect = (
  action: AnyAction,
  effects: EffectsCommandMap & { select: <T>(func: (state: StateType) => T) => T },
) => void;

export interface ModelType {
  namespace: string;
  state: StateType;
  effects: {
    fetch: Effect;
  };
  reducers: {
    updatePagination: Reducer<StateType>;
    updateDataList: Reducer<StateType>;
  };
}

const Model: ModelType = {
  namespace: 'portfolioList',

  state: {
    data: {
      list: [],
      pagination: {},
    },
  },

  effects: {
    *fetch({ payload = {} }, { call, put }) {
      const response = yield call(queryFund, payload)
      const { page, per_page } = payload || {}
      yield put({
        type: 'updatePagination',
        payload: {
          pageSize: per_page,
          current: page,
        },
      })
      yield put({
        type: 'updateDataList',
        payload: response,
      })
    },
  },

  reducers: {
    updatePagination(state, action) {
      const { data = { pagination: { pageSize: 10, current: 1 } } } = state || {}
      return {
        ...state,
        data: {
          ...data,
          pagination: {
            ...data.pagination,
            ...action.payload,
          },
        },
      }
    },
    updateDataList(state, action) {
      const { list, total } = action.payload
      const { data = { pagination: { pageSize: 10, current: 1 } } } = state || {}
      return {
        ...state,
        data: {
          list,
          pagination: {
            ...data.pagination,
            total,
          },
        },
      }
    },
  },
}

export default Model
