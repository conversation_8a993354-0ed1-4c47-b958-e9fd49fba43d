import React from 'react'
import { Tooltip, Dropdown, Menu } from 'antd'
import Link from 'umi/link'
import { EditOutlined, PlusOutlined, DownOutlined } from '@ant-design/icons'
import NavPortfolioModal from '@/components/NavPortfolioModal'
import List from '../list'
import { formatMessage } from 'umi-plugin-react/locale'

const t = (id: string, params?: any) => formatMessage({ id }, params)

export default () => {
  const renderEdit = (record) => {
    return (
      <NavPortfolioModal
        isEdit
        fundData={record}
        t={t}
        fundType={record.portfolioType}
        navPortfolioType={
          record.portfolioType === 'nav' ? record.navPortfolioType || 'nav' : undefined
        }
      >
        <Tooltip title="编辑">
          <EditOutlined />
        </Tooltip>
      </NavPortfolioModal>
    )
  }
  const newPortfolioMenu = (
    <Menu>
      <Menu.Item key="nav">
        <NavPortfolioModal t={t}>从净值创建</NavPortfolioModal>
      </Menu.Item>
      <Menu.Item key="cash">
        <NavPortfolioModal navPortfolioType="cash" fundType="nav" t={t}>
          从交易清单创建
        </NavPortfolioModal>
      </Menu.Item>
      <Menu.Item key="factor">
        <Link to="/fof/factorbased/new">从因子构建组合创建</Link>
      </Menu.Item>
    </Menu>
  )
  const renderCreate = () => {
    return (
      <Dropdown overlay={newPortfolioMenu}>
        <a>
          <PlusOutlined />
          <span style={{ margin: '0 5px' }}>新建</span>
          <DownOutlined />
        </a>
      </Dropdown>
    )
  }
  return (
    <List
      defaultParams={{ portfolioType: 'nav', type: 'portfolio' }}
      renderEdit={renderEdit}
      renderCreate={renderCreate}
    />
  )
}

