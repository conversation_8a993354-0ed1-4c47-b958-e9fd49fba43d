import React from 'react'
import { Tooltip } from 'antd'
import { EditOutlined, PlusOutlined } from '@ant-design/icons'
import NavPortfolioModal from '@/components/NavPortfolioModal'
import List from '../list'
import { formatMessage } from 'umi-plugin-react/locale'

const t = (id: string, params?: any) => formatMessage({ id }, params)

export default () => {
  const renderEdit = (record) => {
    return (
      <NavPortfolioModal
        isEdit
        defaultTabs={[{ name: '风格指数', value: 'styleBenchmark' }]}
        fundData={record}
        t={t}
        fundType={record.portfolioType}
        navPortfolioType={
          record.portfolioType === 'nav' ? record.navPortfolioType || 'nav' : undefined
        }
      >
        <Tooltip title="编辑">
          <EditOutlined />
        </Tooltip>
      </NavPortfolioModal>
    )
  }
  const renderCreate = () => {
    return (
      <NavPortfolioModal isReloadAfterCreated t={t} fundType="saa" defaultTabs={[{ name: '风格指数', value: 'styleBenchmark' }]}>
        <a><PlusOutlined />新建配置方案</a>
      </NavPortfolioModal>
    )
  }
  return (
    <List
      defaultParams={{ portfolioType: 'saa', type: 'portfolio' }}
      renderEdit={renderEdit}
      renderCreate={renderCreate}
    />
  )
}
