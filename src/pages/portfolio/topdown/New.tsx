import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import Link from 'umi/link'
import _ from 'lodash'
import moment from 'moment'
import {
  Button,
  Input,
  Card,
  Form,
  notification,
  Steps,
  Result,
  Spin,
  Breadcrumb,
  Space,
  DatePicker,
} from 'antd'
import useStepsForm from '@/hooks/useStepsForm'
import EditableTable from '@/components/EditableTable'
import { createPortfolio, updatePortfolio, queryPortfolio } from './service'
import SelectSaaSchema from './components/SelectSaaSchema'

const { Step } = Steps

export default ({
  match: {
    params: { id }
  },
}) => {
  const [currentId, setCurrentId] = useState(id)
  const [currrentSchema, setCurrentSchema] = useState({})
  const onSaveSuccess = () => {
    notification.success({
      message: '保存成功',
    })
  }
  const { loading } = useRequest(() => {
    if (currentId !== 'new') {
      return queryPortfolio(currentId)
    }
    return Promise.resolve({})
  }, {
    onSuccess: (data) => {
      if (currentId === 'new') {
        return data
      }
      const formValues = _.pick(data, ['name', 'description', 'startDate', 'saaSchema', 'fundPools'])
      formValues.startDate = moment(data.startDate)
      form.setFieldsValue(formValues)
      setCurrentSchema(data)
      return data
    },
  })
  const { loading: updating, run: doUpdate} = useRequest((id, data) => {
    return updatePortfolio(id, data)
  }, {
    manual: true,
    onSuccess: onSaveSuccess,
  })
  const { loading: creating, run: doCreate} = useRequest((data) => {
    return createPortfolio(data)
  }, {
    manual: true,
    onSuccess: onSaveSuccess,
  })
  const {
    form,
    current,
    gotoStep,
    stepsProps,
    formProps,
    submit,
    formLoading,
  } = useStepsForm({
    async submit(values) {
      let result = {}
      if (currentId === 'new') {
        result = await doCreate(values)
      } else {
        result = await doUpdate(currentId, values)
      }
      setCurrentId(result._id)
      return 'ok'
    },
    total: 4,
    isBackValidate: false,
  })

  const assetColumns = [{
    title: '大类资产',
    width: 200,
    dataIndex: 'name',
  },
  {
    title: '基金组合',
    dataIndex: 'fundPool',
    format: 'selectFundPool',
    editable: true,
  }]
  const formList = [
    <>
      <Form.Item
        label="组合名称"
        name="name"
        rules={[
          {
            required: true,
            message: '请输入组合名称',
          },
        ]}
      >
        <Input placeholder="请输入组合名称" />
      </Form.Item>
      <Form.Item
        label="起始时间"
        name="startDate"
        rules={[
          {
            required: true,
            message: '请选择起始时间',
          },
        ]}
      >
        <DatePicker placeholder="请选择起始时间" />
      </Form.Item>
      <Form.Item
        label="描述信息"
        name="description"
        rules={[
          {
            required: true,
            message: '请输入描述信息',
          },
        ]}
      >
        <Input.TextArea placeholder="请输入描述信息" />
      </Form.Item>
      <Form.Item style={{ marginTop: 30, textAlign: 'center' }}>
        <Button type="primary" onClick={() => {
          gotoStep(current + 1)
        }}>下一步</Button>
      </Form.Item>
    </>,
    <>
      <Form.Item
        name="saaSchema"
        rules={[
          {
            required: true,
            message: '请选择配置方案',
          },
        ]}
      >
        <SelectSaaSchema
          onChange={((schemaData) => {
            const fundPools = schemaData.fundPools.map(ast => {
              ast.key = ast.assetId
              return ast
            })
            form.setFieldsValue({ fundPools })
          })}
        />
      </Form.Item>
      <Form.Item style={{ marginTop: 30, textAlign: 'center' }}>
        <Space>
          <Button type="primary" onClick={() => gotoStep(current + 1)}>下一步</Button>
          <Button onClick={() => gotoStep(current - 1)}>上一步</Button>
        </Space>
      </Form.Item>
      <Form.Item
        hidden
        name="fundPools"
      />
    </>,
    <>
      <Form.Item
        name="fundPools"
        validateStatus="success"
        rules={[
          {
            required: true,
            type: 'array',
            message: '选择基金组合',
          },
          {
            validator: (rule, value) => {
              const invalid = value.find(item => !item.fundPool)
              if (invalid) {
                return Promise.reject(`请选择${invalid.name}对应基金池`)
              } else {
                return Promise.resolve()
              }
            },
          },
        ]}
      >
        <EditableTable
          size="small"
          columns={assetColumns}
        />
      </Form.Item>
      <Form.Item style={{ marginTop: 30, textAlign: 'center' }}>
        <Space>
          <Button
            type="primary"
            loading={formLoading}
            onClick={() => {
              submit().then(result => {
                if (result === 'ok') {
                  gotoStep(current + 1)
                }
              })
            }}
          >
            保存
          </Button>
          <Button onClick={() => gotoStep(current - 1)}>上一步</Button>
        </Space>
      </Form.Item>
    </>,
  ]

  return (
    <div style={{ marginBottom: 30 }}>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link to="/fof/topdown">自上而下组合构建</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          {currrentSchema.name || '新建组合'}
        </Breadcrumb.Item>
      </Breadcrumb>
      <Spin spinning={loading || updating || creating}>
        <div>
          <Card>
            <Steps {...stepsProps} style={{ marginBottom: 30 }} size="small">
              <Step title="基本信息" />
              <Step title="选择配置方案" />
              <Step title="选择基金组合" />
              <Step title="完成" />
            </Steps>
            <div>
              <Form {...formProps}>
                {formList[current]}
              </Form>
              {current === 3 && (
                <Result
                  status="success"
                  title="组合保存成功！"
                  extra={
                    <>
                      {/* <Button
                        type="primary"
                        onClick={() => {
                          form.resetFields()
                          gotoStep(0)
                        }}
                      >
                        再次创建
                      </Button> */}
                      <Button type="primary"><Link to="/fof/topdown">返回组合列表</Link></Button>
                    </>
                  }
                />
              )}
            </div>
          </Card>
        </div>
      </Spin>
    </div>
  )
}
