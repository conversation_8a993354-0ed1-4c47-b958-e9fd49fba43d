import React from 'react'
import Link from 'umi/link'
import { Tooltip } from 'antd'
import { EditOutlined, PlusOutlined } from '@ant-design/icons'
import List from '../list'

export default () => {
  const renderEdit = (record) => {
    return (
      <Link to={`/fof/topdown/${record._id}`}>
        <Tooltip title="编辑">
          <EditOutlined />
        </Tooltip>
      </Link>
    )
  }
  const renderCreate = () => {
    return (
      <Link to="/fof/topdown/new"><PlusOutlined />新建</Link>
    )
  }
  return (
    <List
      defaultParams={{ portfolioType: 'topdown', type: 'portfolio' }}
      renderEdit={renderEdit}
      renderCreate={renderCreate}
    />
  )
}
