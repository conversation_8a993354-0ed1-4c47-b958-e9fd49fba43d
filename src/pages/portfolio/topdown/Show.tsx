import React, { useState } from 'react'
import { FormStep, FormItem, Input, FormButtonGroup } from '@formily/antd'
import { createForm } from '@formily/core'
import { FormProvider, FormConsumer, createSchemaField } from '@formily/react'
import { But<PERSON>, Breadcrumb, Card, Upload } from 'antd'
import Link from 'umi/link'
import { UploadOutlined } from '@ant-design/icons'
import FormResult from './components/FormResult'
import SelectSaaSchema from './components/SelectSaaSchema'

const IDUpload = (props) => {
  console.log(props)
  return (
    <Upload
      {...props}
      action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
      headers={{
        authorization: 'authorization-text',
      }}
    >
      <Button icon={<UploadOutlined />}>Upload a copy</Button>
    </Upload>
  )
}

const SchemaField = createSchemaField({
  components: {
    FormItem,
    FormStep,
    Input,
    FormResult,
    IDUpload,
    SelectSaaSchema,
  },
})

const form = createForm()
const formStep = FormStep.createFormStep()

export default () => {
  const [portfolioName, setPortfolioName] = useState('新建组合')
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link to="/fof/topdown">至上而下组合构建</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          {portfolioName}
        </Breadcrumb.Item>
      </Breadcrumb>
      <Card>
        <FormProvider form={form}>
        <SchemaField>
          <SchemaField.Void
            x-component="FormStep"
            x-component-props={{ formStep }}
          >
            <SchemaField.Void
              x-component="FormStep.StepPane"
              x-component-props={{ title: 'First Step' }}
            >
              <SchemaField.String
                name="aaa"
                x-decorator="FormItem"
                required
                x-component="Input"
              />
            </SchemaField.Void>
            <SchemaField.Void
              x-component="FormStep.StepPane"
              x-component-props={{ title: 'First Step' }}
            >
              <SchemaField.String
                name="aaa"
                x-decorator="FormItem"
                required
                x-component="SelectSaaSchema"
              />
            </SchemaField.Void>
            <SchemaField.Void
              x-component="FormStep.StepPane"
              x-component-props={{ title: 'Second Step' }}
            >
              <SchemaField.String
                name="bbb"
                x-decorator="FormItem"
                required
                x-component="IDUpload"
              />
            </SchemaField.Void>
            <SchemaField.Void
              type="void"
              x-component="FormStep.StepPane"
              x-component-props={{ title: 'Step 3' }}
            >
              <SchemaField.String
                name="ccc"
                x-decorator="FormItem"
                required
                x-component="Input"
              />
            </SchemaField.Void>
            <SchemaField.Void
              type="void"
              x-component="FormStep.StepPane"
              x-component-props={{ title: '完成' }}
            >
              <SchemaField.String
                name="eee"
                x-component="FormResult"
              />
            </SchemaField.Void>
          </SchemaField.Void>
        </SchemaField>
        <FormConsumer>
          {() => (
            <FormButtonGroup align="center">
              <Button
                disabled={!formStep.allowBack}
                onClick={() => {
                  formStep.back()
                }}
              >
                Previous
              </Button>
              <Button
                disabled={!formStep.allowNext}
                onClick={() => {
                  formStep.next()
                }}
              >
                Next step
              </Button>
              <Button
                disabled={formStep.allowNext}
                onClick={() => {
                  formStep.submit(console.log)
                }}
              >
                submit
              </Button>
            </FormButtonGroup>
          )}
        </FormConsumer>
      </FormProvider>
      </Card>
    </div>
  )
}
