import React, { useState } from 'react'
import _ from 'lodash'
import {
  Table,
  Button,
} from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import SelectFundModal from '@/components/SelectFundModal'

const SelectSaaSchema = (props) => {
  const value = props.value
  const columns = [{
    title: value ? `已选方案: ${value.saaSchemaName}` : '',
    dataIndex: 'name',
  }]
  return (
    <>
      <SelectFundModal
        title="请选择配置方案"
        key="1"
        onChange={(selectedRows) => {
          const fund = selectedRows[0]
          const segments = fund.segments || []
          const fundList = segments.reduce((out, item) => {
            const funds = item.funds.map(fund => {
              return {
                name: fund.name,
                assetId: fund._id,
              }
            })
            return out.concat(funds)
          }, [])
          const result = {
            saaSchemaName: fund.name,
            saaSchemaId: fund._id,
            fundPools: fundList,
          }
          props.onChange(result)
        }}
        defaultTabs={[{
          name: '实盘组合',
          value: 'portfolio',
        }]}
        portfolioType="saa"
        rowSelectionType="radio"
        style={{
          display: 'block',
          marginTop: 15,
          marginBottom: 15,
        }}
      >
        <Button
          icon={<PlusOutlined />}
          type="dashed"
          size="large"
          block
        >
          选择配置方案
        </Button>
      </SelectFundModal>
      <Table
        size="small"
        columns={columns}
        dataSource={value && value.fundPools}
        pagination={false}
      />
    </>
  )
}

export default SelectSaaSchema
