import React from 'react'
import { connect, mapReadPretty } from '@formily/react'
import { Result as AntdResult, Button } from 'antd'
import { ResultProps } from 'antd/lib/result'
import Link from 'umi/link'

type ComposedResult = React.FC<ResultProps>

const ResultCompoent = (props: ResultProps) => {
  return (
    <AntdResult
      status="success"
      title="方案保存成功！"
      extra={
        <>
          {/* <Button
            type="primary"
            onClick={() => {
              form.resetFields()
              gotoStep(0)
            }}
          >
            再次创建
          </Button> */}
          <Button type="primary"><Link to="/factorschema">返回方案列表</Link></Button>
        </>
      }
    />
  )
}

export const Result: ComposedResult = connect(
  ResultCompoent,
  mapReadPretty(ResultCompoent)
)

export default Result
