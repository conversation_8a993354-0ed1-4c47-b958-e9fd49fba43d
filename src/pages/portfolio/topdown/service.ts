import request from '@/utils/request'

export async function queryPortfolioList(params) {
  return request('/api/funds', { params })
}

export async function createPortfolio(data) {
  return request('/api/portfolios/topdown', {
    method: 'POST',
    data,
  })
}

export async function updatePortfolio(id, data) {
  return request(`/api/portfolios/topdown/${id}`, {
    method: 'PUT',
    data,
  })
}

export async function queryPortfolio(id) {
  return request(`/api/portfolios/${id}`, {
    method: 'GET',
  })
}

export async function deletePortfolio(id: string): Promise<any> {
  return request(`/api/funds/${id}`, {
    method: 'delete',
  })
}
