import request from '@/utils/request'

export async function queryPortfolioList(params) {
  return request('/api/funds', { params })
}

export async function deletePortfolio(id: string): Promise<any> {
  return request(`/api/funds/${id}`, {
    method: 'delete',
  })
}

export async function queryGroupList() {
  return request(`/api/duedocs/usergroup/list`)
}

export async function authorizeUserGroups(id: string, data: any) {
  return request(`/api/portfolios/${id}/usergroups`, {
    method: 'post',
    data,
  })
}
