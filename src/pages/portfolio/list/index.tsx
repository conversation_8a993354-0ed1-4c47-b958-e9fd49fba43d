import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Table,
  Input,
  Card,
  Space,
  Tooltip,
  Popconfirm,
  Divider,
  notification,
  Spin,
  Radio,
  Row,
  Col,
  Empty,
  Pagination,
} from 'antd'
import { EyeOutlined, DeleteOutlined, TableOutlined, MenuOutlined, UsergroupAddOutlined } from '@ant-design/icons'
import PortfolioCard from '@/components/PortfolioCard'
import { queryPortfolioList, deletePortfolio, queryGroupList, authorizeUserGroups } from './service'
import buildTableColumn from '@/utils/buildTableColumn'
import AuthorizeUserGroupModal from '@/components/AuthorizeUserGroupModal'

const { Search } = Input

export default ({
  defaultParams,
  renderEdit,
  renderCreate,
}) => {
  const [input, setInput] = useState('')
  const [refreshCount, setRefreshCount] = useState(0)
  const [viewMode, setViewMode] = useState('grid')
  const [gridPage, setGridPage] = useState(1)
  const { tableProps, loading } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (viewMode === 'grid') {
      p.current = gridPage
    }
    return queryPortfolioList({ ...p, ...defaultParams, input })
  }, {
    paginated: true,
    defaultPageSize: 12,
    refreshDeps: [input, refreshCount, viewMode, gridPage],
  })
  const { run: doDeletePortfolio } = useRequest((id) => {
    return deletePortfolio(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const { data: userGroups = [] } = useRequest(() => {
    return queryGroupList()
  }, {
    cacheKey: 'queryGroupList',
  })
  const getCardActions = (record) => {
    if (!record.isAuthor) {
      return [
        <Tooltip title="查看详情">
          <a
            href={`/fof/portfolios/${record._id}/overview`}
            rel="noopener noreferrer"
            target="_blank"
          >
            <EyeOutlined />
          </a>
        </Tooltip>,
      ]
    }
    return [
      <Tooltip title="查看详情">
        <a
          href={`/fof/portfolios/${record._id}/overview`}
          rel="noopener noreferrer"
          target="_blank"
        >
          <EyeOutlined />
        </a>
      </Tooltip>,
      renderEdit(record),
      <Popconfirm
        title="确认删除吗？"
        onConfirm={() => { doDeletePortfolio(record._id) }}
        onCancel={() => {}}
        okText="确认"
        cancelText="取消"
        placement="left"
      >
        <Tooltip title="删除">
          <a><DeleteOutlined /></a>
        </Tooltip>
      </Popconfirm>,
      <AuthorizeUserGroupModal
        userGroups={userGroups}
        handleSave={() => { console.log('save') }}
        initialValues={{
          userGroupIds: record.userGroupIds,
        }}
        authorizeUserGroups={authorizeUserGroups}
        itemId={record._id}
      >
        <Tooltip title="授权用户组">
          <a
          >
            <UsergroupAddOutlined />
          </a>
        </Tooltip>
      </AuthorizeUserGroupModal>
      ,
    ]
  }

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text, record) => (
        <a
          href={`/fof/portfolios/${record._id}/overview`}
          rel="noopener noreferrer"
          target="_blank"
        >
          {record.name}
        </a>
      ),
    },
    {
      title: '成立时间',
      dataIndex: 'startDate',
      format: 'date',
      align: 'right',
      sorter: true,
    },
    {
      title: '净值结束日期',
      dataIndex: 'navEndDate',
      align: 'right',
      format: 'date',
      sorter: true,
    },
    {
      title: '累计收益',
      dataIndex: 'accReturn',
      sorter: true,
      align: 'right',
      format: 'percentage',
    },
    {
      title: '年化收益',
      dataIndex: 'yearReturn',
      sorter: true,
      align: 'right',
      format: 'percentage',
    },
    {
      title: '最大回撤',
      dataIndex: 'maxDrawdown',
      align: 'right',
      format: 'valPercentage',
      sorter: true,
    },
    {
      title: '波动率',
      dataIndex: 'vol',
      align: 'right',
      format: 'valPercentage',
      sorter: true,
    },
    {
      title: '操作',
      render: (text, record) => {
        if (!record.isAuthor) {
          return <>
            <Tooltip title="查看详情">
              <a
                href={`/fof/portfolios/${record._id}/overview`}
                rel="noopener noreferrer"
                target="_blank"
              >
                <EyeOutlined />
              </a>
            </Tooltip>
          </>
        }
        return (
          <>
            <Tooltip title="查看详情">
              <a
                href={`/fof/portfolios/${record._id}/overview`}
                rel="noopener noreferrer"
                target="_blank"
              >
                <EyeOutlined />
              </a>
            </Tooltip>
            <Divider type="vertical" />
            {renderEdit(record)}
            <Divider type="vertical" />
            <Popconfirm
              title="确认删除吗？"
              onConfirm={() => { doDeletePortfolio(record._id) }}
              onCancel={() => {}}
              okText="确认"
              cancelText="取消"
              placement="left"
            >
              <Tooltip title="删除">
                <a><DeleteOutlined /></a>
              </Tooltip>
            </Popconfirm>
          </>
        )
      },
    },
  ].map(buildTableColumn)
  const searchInput = (
    <Search
      style={{ width: '300px' }}
      placeholder="按回车进行搜索"
      onSearch={setInput}
    />
  )
  const viewModeButtonGroup = (
    <Space style={{ float: 'right' }} size={15}>
      {renderCreate()}
      <Radio.Group
        defaultValue={viewMode}
        size="small"
        onChange={(event) => setViewMode(event.target.value)}
      >
        <Radio.Button value="grid"><TableOutlined /></Radio.Button>
        <Radio.Button value="list"><MenuOutlined /></Radio.Button>
      </Radio.Group>
    </Space>
  )
  const fundListData = tableProps.dataSource || []
  if (viewMode === 'grid') {
    return (
      <Spin spinning={loading}>
        <div style={{ marginBottom: 15 }}>
          {searchInput}
          {viewModeButtonGroup}
        </div>
        <Row gutter={10}>
          {fundListData.map(item => (
            <Col key={item._id} xs={24} sm={12} md={8} lg={6} xl={6} xxl={4}>
              <div style={{ marginBottom: 15 }}>
                <PortfolioCard portfolio={item} getCardActions={getCardActions} />
              </div>
            </Col>
          ))}
        </Row>
        {fundListData.length === 0 && (
          <div style={{ height: 350 }}>
            <Empty />
          </div>
        )}
        {fundListData.length !== 0 && (
          <Pagination
            showQuickJumper
            style={{ float: 'right' }}
            size="small"
            onChange={setGridPage}
            {...tableProps.pagination}
            current={gridPage}
          />
        )}
      </Spin>
    )
  }
  return (
    <div>
      <Card
        title={
          <>
            {searchInput}
          </>
        }
        bordered={false}
        extra={
          <Space>
            {viewModeButtonGroup}
          </Space>
        }
      >
        <Table bordered size="small" columns={columns} rowKey="_id" {...tableProps} />
      </Card>
    </div>
  )
}
