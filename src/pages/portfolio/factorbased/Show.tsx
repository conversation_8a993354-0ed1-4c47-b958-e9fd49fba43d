import React, { Component, useState } from 'react'
import { connect } from 'dva'
import { useRequest, useBoolean } from '@umijs/hooks'
import Link from 'umi/link'
import _ from 'lodash'
import moment from 'moment'
import {
  Table,
  Button,
  Modal,
  Select,
  Input,
  Card,
  Form,
  notification,
  Spin,
  Row,
  Col,
  Breadcrumb,
  Radio,
  DatePicker,
  Space,
  InputNumber,
} from 'antd'
import buildTableColumn from '@/utils/buildTableColumn'
import FilterPanel from '@/components/FilterPanel'
import buildTreeData from '@/utils/buildTreeData'
import FundNavCard from '@/pages/dashboard/components/FundNavCard'
import { queryFactorList, backTest, updatePortfolio, createPortfolio, queryPortfolio } from './service'
import styles from './style.less'

const { Option } = Select

const isEmptyObjectArray = data => {
  return !data ||
    !Object.keys(data).length ||
    Object.keys(data).every(key => data[key].length === 0)
}
const factorTierNames = ['收益类', '风险类', '归因类', '策略类', '持仓类', '基金经理类', '基金公司类']
const factorTierSortFn = ((pre, nex) => factorTierNames.indexOf(pre.title) - factorTierNames.indexOf(nex.title))
const getFundFilters = factorList => [
  {
    name: '资产配置分类',
    type: 'tagSelect',
    formKey: 'allocationType',
    data: [
      { name: '纯股型', value: '1' },
      { name: '纯债型', value: '2' },
      { name: '配置型', value: '3' },
    ],
  },
  {
    name: '基金风格分类',
    type: 'tagSelect',
    formKey: 'styleType',
    data: [
      { name: '价值型', value: '101' },
      { name: '成长型', value: '102' },
      { name: '均衡型', value: '103' },
      { name: '信用型', value: '201' },
      { name: '久期型', value: '202' },
      { name: '平衡型', value: '203' },
      { name: '积极配置型', value: '301' },
      { name: '消极配置型', value: '302' },

    ],
  },
  {
    name: '因子条件',
    type: 'tableForm',
    formKey: 'factors',
    disableOverlayLogic: true,
    componentProps: {
      tableColumns: [{
        title: '逻辑运算',
        dataIndex: 'logicalOperator',
        width: '10%',
        key: 'logicalOperator',
        format: 'LogicalOperatorSelect',
        options: [{
          title: 'TOP',
          dataIndex: 'top',
          key: 'top',
        }, {
          title: 'AND',
          dataIndex: 'and',
          key: 'and',
        }, {
          title: 'OR',
          dataIndex: 'or',
          key: 'or',
        }, {
          title: 'NOT',
          dataIndex: 'not',
          key: 'not',
        }],
      }, {
        title: '因子',
        dataIndex: 'factor',
        width: '30%',
        key: 'factor',
        format: 'Cascader',
        options: buildTreeData(factorList || [], ['class1', 'class2']).sort(factorTierSortFn),
        refOptions: factorList || [],
      }, {
        title: '指标选项',
        dataIndex: 'valueType',
        width: '15%',
        key: 'valueType',
        format: 'Select',
        options: [{
          title: '排名',
          dataIndex: 'value_rank',
          key: 'rank',
          format: 'number',
          tooltip: '取值范围1-N；降序，1表示最高',
        }, {
          title: '分位数(%)',
          dataIndex: 'value_quantile',
          key: 'quantile',
          tooltip: '取值范围0-100；降序，0表示最高',
        }, {
          title: '因子值(%)',
          dataIndex: 'factor_value',
          key: 'value',
        }],
      }, {
        title: '指标范围',
        dataIndex: 'valueRange',
        width: '25%',
        key: 'valueRange',
        format: 'NumberRange',
        refColumn: 'valueType',
      }],
      validateRow: (item, index) => {
        if (index !== 0 && !item.logicalOperator) {
          return '请选择逻辑运算'
        }
        if (!item.factor) {
          return '请选择因子'
        }
        if (!item.valueType) {
          return '请选择指标选项'
        }
        if (!item.valueRange || !item.valueRange.filter(Boolean).length) {
          return '请设置指标范围'
        }
        return null
      },
    },
  },
]

const PositionDetailTable = ({ data }) => {
  const columns = [{
    title: '序号',
    dataIndex: 'fundNo',
    width: 100,
  }, {
    title: '名称',
    dataIndex: 'name',
    hasSorter: true,
  }, {
    title: '比例',
    dataIndex: 'ratio',
    format: 'percentageSuffix',
    hasSorter: true,
  }].map(buildTableColumn)
  const dates = _.uniq(data.map(item => item.date)).sort((fst, snd) => new Date(snd) - new Date(fst))
  const [currentDate, setCurrentDate] = useState(dates[0])
  const dataSource = data.filter(item => item.date === currentDate)
  return (
    <Card
      title="选基详情"
      bordered={false}
      extra={
        <Select
          style={{ width: 120 }}
          defaultValue={currentDate}
          onChange={(date) => setCurrentDate(date)}
        >
          {dates.map(item => <Option value={item}>{item}</Option>)}
        </Select>
      }
    >
      <Table
        bordered
        size="small"
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        scroll={{ y: 450 }}
      />
    </Card>
  )
}

const getBackTestFormData = (portfolio, factorList) => {
  const {
    factorBasedFilterValues,
    factorBasedAdjustmentParams,
  } = portfolio
  const filterValues = {
    ...factorBasedFilterValues,
    factors: (factorBasedFilterValues.factors).map(item => {
      const factor = factorList.find(factor => factor.dataIndex === item.factor) || {}
      const isRank = item.valueType === 'value_rank'
      return {
        ...item,
        key: factor.dataIndex,
        factor: [factor.class1, factor.class2, factor.dataIndex],
        valueRange: item.valueRange.map(it => (isRank || !it) ? it : it * 100),
      }
    }),
    factorDims: (factorBasedFilterValues.factorDims).map(item => {
      const factor = factorList.find(factor => factor.dataIndex === item.factor) || {}
      const isRank = item.valueType === 'value_rank'
      return {
        ...item,
        key: factor.dataIndex,
        valueRange: item.valueRange.map(it => (isRank || !it) ? it : it * 100),
      }
    }),
  }
  const formValues = {
    ...factorBasedAdjustmentParams,
    startDate: moment(new Date(factorBasedAdjustmentParams.startDate)),
    endDate: moment(new Date(factorBasedAdjustmentParams.endDate)),
  }
  return {
    factorBasedFilterValues: filterValues, factorBasedAdjustmentParams: formValues,
  }
}

const getFactorBasedFilters = filters => {
  const results = filters.filter(item => {
    return item.formKey !== 'company' && item.filterId !== 'fundSwticher'
  }).map(item => {
    if (item.filterId === 'advancedFilter') {
      item.tabs = item.tabs.filter(tab => {
        return tab.formKey === 'factors'
      }).map(item => {
        item.componentProps.tableColumns = item.componentProps.tableColumns.filter(col => {
          return col.dataIndex !==  'reportDate'
        })
        return item
      })
    }
    if (item.filterId === 'factorSchema') {
      item.tabs = item.tabs.map(item => {
        if (item.formKey === 'factorSetting') {
          item.componentProps.disableDate = true
        }
        return item
      })
    }
    return item
  })
  return results
}

const Wrapper = ({
  match,
  filters,
}: {
  match: any,
}) => {
  const customFilters = getFactorBasedFilters(filters)
  const [form] = Form.useForm()
  const { setFieldsValue } = form
  const factorSchemaFilter = customFilters.find(item => item.filterId === 'factorSchema')
  const initialFormValue = {
    name: '', description: '',
  }
  const [paramsForm] = Form.useForm()
  const initialParamsFormValue = {
    dateRangeStrategy: 'fixed',
    bondSellFee: 0,
    bondBuyFee: 0.1,
    noneBondSellFee: 0.5,
    noneBondBuyFee: 0.15,
  }
  const defaultFactorSetting = factorSchemaFilter.tabs[0].defaultValues
  const [portfolioId, setPortfolioId] = useState(match.params.id)
  const [filterValues, setFilterValues] = useState(portfolioId === 'new' ? {
    factorSetting: defaultFactorSetting,
  } : null)
  const [portfolioName, setPortfolioName] = useState('新建组合')
  const { loading: loadingPortfolio, run: runQueryPortfolio } = useRequest(() => {
    return portfolioId !== 'new' ? queryPortfolio(portfolioId) : {}
  }, {
    manual: true,
    onSuccess: (data) => {
      setPortfolioName(data.name)
      setFieldsValue({
        name: data.name,
        description: data.description,
      })
      const backTestFormData = getBackTestFormData(data, factorList)
      const factorBasedAdjustmentParams = backTestFormData.factorBasedAdjustmentParams
      paramsForm.setFieldsValue({
        ...factorBasedAdjustmentParams,
        bondSellFee: data.bondSellFee || factorBasedAdjustmentParams.bondSellFee,
        bondBuyFee: data.bondBuyFee || factorBasedAdjustmentParams.bondBuyFee,
        noneBondSellFee: data.noneBondSellFee || factorBasedAdjustmentParams.noneBondSellFee,
        noneBondBuyFee: data.noneBondBuyFee || factorBasedAdjustmentParams.noneBondBuyFee,
      })
      setFilterValues(backTestFormData.factorBasedFilterValues)
      runBackTest({
        factorBasedAdjustmentParams: data.factorBasedAdjustmentParams,
        factorBasedFilterValues: data.factorBasedFilterValues,
      })
    },
  })
  const { data: factorList } = useRequest(() => {
    return queryFactorList()
  }, {
    cacheKey: 'factorList',
    onSuccess: (data) => {
      runQueryPortfolio()
    },
  })
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const { run: runBackTest, loading: runningBackTest, data: backTestResult = {} } = useRequest((data) => {
    return backTest(data)
  }, {
    manual: true,
    onSuccess: (result) => {
      if (result) {
        if (result.segments.length === 0) {
          notification.warn({ message: '没有匹配的基金，请检查回测条件' })
        } else {
          notification.success({ message: '回测成功' })
        }
      }
    },
  })
  const monthPeriodMap = {
    year: 12,
    halfYear: 6,
    quarter: 3,
    month: 1,
  }

  const getBackTestParams = callback => {
    if (isEmptyObjectArray(filterValues)) {
      notification.error({ message: '请先设置筛选条件' })
      return
    }
    paramsForm
      .validateFields()
      .then(formValues => {
        const factorBasedFilterValues = { ...filterValues }
        factorBasedFilterValues.factors = (filterValues.factors || []).map(factor => {
          const isRank = factor.valueType === 'value_rank'
          return {
            ...factor,
            factor: factor.factor[factor.factor.length - 1],
            valueRange: factor.valueRange.map(item => (isRank || !item) ? item : item / 100),
          }
        })
        factorBasedFilterValues.factorDims = (filterValues.factorDims || []).map(factor => {
          const isRank = factor.valueType === 'value_rank'
          return {
            ...factor,
            valueRange: factor.valueRange.map(item => (isRank || !item) ? item : item / 100),
          }
        })
        const factorBasedAdjustmentParams = { ...formValues }
        factorBasedAdjustmentParams.endDate = formValues.endDate.clone().format('YYYY-MM-DD')
        if (formValues.dateRangeStrategy === 'relative') {
          factorBasedAdjustmentParams.startDate = formValues.endDate.clone().subtract(
            formValues.backCount * monthPeriodMap[formValues.backCycle], 'month'
          ).format('YYYY-MM-DD')
        } else {
          factorBasedAdjustmentParams.endDate = formValues.endDate.format('YYYY-MM-DD')
        }
        callback({
          factorBasedFilterValues,
          factorBasedAdjustmentParams,
        })
      })
      .catch((error) => {
        console.log(error)
      })
  }
  const handleBackTest = () => {
    getBackTestParams((data) => {
      runBackTest(data)
    })
  }
  const fundFilters = getFundFilters(factorList)
  const positionData = (backTestResult.segments || []).reduce((out, item) => {
    const date = moment(new Date(item.startDate)).format('YYYY-MM-DD')
    const results = item.funds.map((fund, index) => {
      return {
        date,
        fundNo: index + 1,
        name: fund.name,
        ratio: fund.ratio,
      }
    })
    return out.concat(results)
  }, [])

  const onSavePortfolioSuccess = (data) => {
    setVisibleFalse()
    setPortfolioId(data._id)
    setPortfolioName(data.name)
    notification.success({
      message: '保存成功',
    })
  }
  const { loading: updatingPortfolio, run: doUpdatePortfolio} = useRequest((id, data) => {
    return updatePortfolio(id, data)
  }, {
    manual: true,
    onSuccess: onSavePortfolioSuccess,
  })
  const { loading: creatingPortfolio, run: doCreatePortfolio} = useRequest((data) => {
    return createPortfolio(data)
  }, {
    manual: true,
    onSuccess: onSavePortfolioSuccess,
  })
  const handleOpenModal = () => {
    if (backTestResult.segments && backTestResult.segments.length) {
      setVisibleTrue()
    }
  }
  const handleClickSave = (values: any) => {
    const { factorBasedFilterValues, factorBasedAdjustmentParams, segments } = backTestResult
    const paramsData = paramsForm.getFieldsValue()
    const data = {
      ...values,
      bondSellFee: paramsData.bondSellFee || factorBasedAdjustmentParams.bondSellFee,
      bondBuyFee: paramsData.bondBuyFee || factorBasedAdjustmentParams.bondBuyFee,
      noneBondSellFee: paramsData.noneBondSellFee || factorBasedAdjustmentParams.noneBondSellFee,
      noneBondBuyFee: paramsData.noneBondBuyFee || factorBasedAdjustmentParams.noneBondBuyFee,
      factorBasedFilterValues, factorBasedAdjustmentParams, segments,
    }
    if (portfolioId !== 'new') {
      doUpdatePortfolio(portfolioId, data)
    } else {
      doCreatePortfolio(data)
    }
  }
  console.log('filterValues.....', filterValues)
  return (
    <div style={{ marginBottom: 30 }}>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link to="/fof/factorbased">基于因子的组合构建</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          {portfolioName}
        </Breadcrumb.Item>
      </Breadcrumb>
      {(portfolioId === 'new' || filterValues) && false &&
      <FilterPanel
        disableInvestPool
        filters={fundFilters}
        filterValues={filterValues || {}}
        onChange={(data) => {
          setFilterValues(data)
        }}
      />}
      {(portfolioId === 'new' || filterValues) &&
      <FilterPanel
        disableInvestPool
        filters={customFilters}
        filterValues={filterValues || {}}
        onChange={(data) => {
          console.log(data)
          setFilterValues(data)
        }}
      />}
      <div style={{ marginTop: 15 }}/>
      <Spin spinning={loadingPortfolio}>
        <Form
          hideRequiredMark
          scrollToFirstError
          form={paramsForm}
          layout="vertical"
          initialValues={initialParamsFormValue}
          onFinish={(values) => { console.log(values) }}
        >
          <Card
            title="调仓参数设置"
          >
            <Row>
              <Col span={12}>
                <Form.Item
                  label="日期选择"
                  style={{ marginBottom: 0 }}
                >
                  <Form.Item
                    name="dateRangeStrategy"
                  >
                    <Radio.Group>
                      <Radio value="fixed">固定日期</Radio>
                      <Radio value="relative">相对日期</Radio>
                    </Radio.Group>
                  </Form.Item>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) => prevValues.dateRangeStrategy !== currentValues.dateRangeStrategy}
                  >
                    {({ getFieldValue }) => {
                      return getFieldValue('dateRangeStrategy') === 'fixed' ? (
                        <Form.Item
                          label="开始日期"
                          name="startDate"
                          rules={[{ required: true }]}
                        >
                          <DatePicker />
                        </Form.Item>
                      ) : (
                        <Form.Item label="开始日期">
                          <Space>
                            <span>前推</span>
                            <Form.Item
                              // style={{ display: 'inline-block', width: 'calc(50% - 12px)' }}
                              noStyle
                              name="backCount"
                              rules={[{ required: true, message: '请输入' }]}
                            >
                              <InputNumber min={1} />
                            </Form.Item>
                            <Form.Item
                              // style={{ display: 'inline-block', width: 'calc(50% - 12px)' }}
                              noStyle
                              name="backCycle"
                              rules={[{ required: true, message: '请选择' }]}
                            >
                              <Select
                                style={{ width: 100 }}
                                placeholder="前推周期"
                              >
                                <Option value="year">年</Option>
                                <Option value="halfYear">半年</Option>
                                <Option value="quarter">季度</Option>
                                <Option value="month">月</Option>
                              </Select>
                            </Form.Item>
                          </Space>
                        </Form.Item>
                      )
                    }}
                  </Form.Item>
                  <Form.Item
                    label="结束日期"
                    name="endDate"
                    style={{ marginBottom: 0 }}
                    rules={
                      [
                        {
                          required: true,
                          message: '请选择结束日期',
                        },
                      ]
                    }
                  >
                    <DatePicker />
                  </Form.Item>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                >
                  <Form.Item
                    label="调仓周期"
                    name="adjustmentPeriod"
                    rules={
                      [
                        {
                          required: true,
                          message: '请选择调仓周期',
                        },
                      ]
                    }
                  >
                    <Select
                      style={{ width: 160 }}
                      placeholder="调仓周期"
                    >
                      <Option value="year">每年</Option>
                      <Option value="halfYear">每半年</Option>
                      <Option value="quarter">每季度</Option>
                      <Option value="month">每月</Option>
                      <Option value="none">不调仓</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    label="调仓权重"
                    name="weightStrategy"
                    rules={
                      [
                        {
                          required: true,
                          message: '请选择调仓权重',
                        },
                      ]
                    }
                  >
                    <Select
                      style={{ width: 160 }}
                      placeholder="调仓权重"
                    >
                      {false &&
                      <Option value="scale">按基金规模</Option>}
                      <Option value="equalWeight">等权重</Option>
                      <Option value="opt">模型优化</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) => prevValues.weightStrategy !== currentValues.weightStrategy}
                  >
                    {({ getFieldValue }) => {
                      return getFieldValue('weightStrategy') === 'opt' ?
                        <Row>
                          <Col span={12}>
                            <Form.Item
                              label="历史数据区间"
                              name="optPeriod"
                              style={{ marginBottom: 0 }}
                              rules={[{ required: true, message: '请选择' }]}
                            >
                              <Select
                                style={{ width: 160 }}
                                placeholder="历史数据区间"
                              >
                                <Option value="3m">3个月</Option>
                                <Option value="6m">6个月</Option>
                                <Option value="1y">1年</Option>
                                <Option value="2y">2年</Option>
                                <Option value="3y">3年</Option>
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              name="volType"
                              label="波动率类别"
                              style={{ marginBottom: 0 }}
                              rules={[{ required: true, message: '请选择' }]}
                            >
                              <Select
                                style={{ width: 160 }}
                                placeholder="波动率类别"
                              >
                                <Option value="vol">波动率</Option>
                                <Option value="downsideVol">下行波动率</Option>
                              </Select>
                            </Form.Item>
                          </Col>
                        </Row> : null
                    }}
                    </Form.Item>
                </Form.Item>
              </Col>
            </Row>
          </Card>
          <Card
            title="费率设置"
            style={{ marginTop: 15 }}
          >
            <Row>
              <Col span={6}>
                <Form.Item
                  label="非债型申购(%)"
                  name="noneBondBuyFee"
                  rules={[{ required: true, message: '请输入' }]}
                >
                  <InputNumber step={0.1}/>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="非债型赎回(%)"
                  name="noneBondSellFee"
                  rules={[{ required: true, message: '请输入' }]}
                >
                  <InputNumber step={0.1}/>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="债券型申购(%)"
                  name="bondBuyFee"
                  rules={[{ required: true, message: '请输入' }]}
                >
                  <InputNumber step={0.1}/>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="债券型赎回(%)"
                  name="bondSellFee"
                  rules={[{ required: true, message: '请输入' }]}
                >
                  <InputNumber step={0.1}/>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Form>
      </Spin>
      <div style={{ marginTop: 15 }}/>
      <Spin spinning={runningBackTest}>
        {backTestResult && backTestResult.nets &&
          <FundNavCard navigatorEnabled height={350} funds={[{ ...backTestResult, name: '净值曲线' }]} title="回测结果" />
        }
        <div style={{ marginTop: 15 }}/>
        {positionData.length !== 0 &&
          <PositionDetailTable data={positionData} />
        }
      </Spin>
      <div
        className={styles.formActionButton}
      >
        <Space size="large">
          <Button type="primary" htmlType="submit" onClick={handleBackTest} loading={runningBackTest}>
            组合回测
          </Button>
          <Button type="primary" htmlType="submit" disabled={!backTestResult.segments || !backTestResult.segments.length} onClick={handleOpenModal}>
            保存
          </Button>
        </Space>
      </div>
      <Modal
        title={
          <>
            <span>{portfolioId !== 'new' ? '编辑组合' : '创建组合'}</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        footer={[
          <Button
            type="primary"
            loading={updatingPortfolio || creatingPortfolio}
            onClick={() => form.submit()}
          >
            保存
          </Button>,
        ]}
      >
        <Spin spinning={updatingPortfolio || creatingPortfolio}>
          <Form
            hideRequiredMark
            form={form}
            layout="vertical"
            initialValues={initialFormValue}
            onFinish={handleClickSave}
          >
            <Form.Item
              label="名称"
              name="name"
              rules={[
                {
                  required: true,
                  message: '请输入名称',
                },
              ]}
            >
              <Input placeholder="名称"/>
            </Form.Item>
            <Form.Item
              label="描述"
              name="description"
            >
              <Input placeholder="描述"/>
            </Form.Item>
          </Form>
        </Spin>
      </Modal>
    </div>
  )
}

@connect(
  ({
    loading,
    fund,
  }: {
    loadingOptions: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    loadingOptions: loading.effects['fund/fetchQueryOptions'],
    filters: fund.mutualFundFilters,
  }),
)
class LoadingWrapper extends Component {
  render() {
    if (this.props.loadingOptions) {
      return <Spin spinning/>
    }
    return <Wrapper {...this.props}/>
  }
}
export default LoadingWrapper
