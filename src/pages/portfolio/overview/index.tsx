import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { FundModelState } from '@/models/fund'
import { formatMessage } from 'umi-plugin-react/locale'
import NavPortfolioInformation from './components/NavPortfolioInformation'

const t = (id: string, params?: any) => formatMessage({ id }, params)

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  portfolio: FundModelState;
  navFundList: any;
  portfolioFundWeights: any;
  location: any;
}

@connect(
  ({
    fund,
    loading,
  }: {
    fund: FundModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    portfolio: fund.currentFund,
    navFundList: fund.navFundList,
    portfolioFundWeights: fund.portfolioFundWeights,
    loading: loading.effects['fund/getNavList'],
  }),
)
export default class Overview extends Component<ComponentProps> {
  componentDidMount() {
    if (this.props.portfolio.portfolioType === 'css') {
      this.getCssPortfolioWeightPenetrate(this.props.portfolio._id)
    }
  }

  getCssPortfolioWeightPenetrate = (id: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'fund/getPortfolioWeightPenetrate',
      payload: {
        id,
      },
    })
  };

  render() {
    return <NavPortfolioInformation t={t} {...this.props} />
  }
}
