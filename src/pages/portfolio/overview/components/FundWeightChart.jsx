import React, { Component } from 'react'
import _ from 'lodash'
import Chart from '@/components/Chart/Chart'
import moment from 'moment'
import uniqBy from 'lodash/uniqBy'
import groupBy from 'lodash/groupBy'
import mapValues from 'lodash/mapValues'
import changeFrequency from '@/utils/changeFrequency'
import { Card } from 'antd'

export default class FundWeightChart extends Component {
  shouldComponentUpdate() {
    return false
  }

  buildWeightData(portfolio) {
    const { segments, nets } = portfolio
    const weights = segments.reduce((out, segment) => {
      const { endToNow } = segment
      const startDate = +moment(segment.startDate).startOf('date')
      const endDate = endToNow
        ? +moment().startOf('date')
        : +moment(segment.endDate).startOf('date')
      const segmentData = nets.filter(item => item.date >= startDate && item.date < endDate)
      const results = segment.funds.reduce((ret, fund, index) => {
        const data = segmentData.map(item => {
          return {
            fundId: fund._id,
            date: item.date,
            value: item.weights[index],
          }
        })
        return ret.concat(data)
      }, [])
      return out.concat(results)
    }, [])
    const weightData = mapValues(groupBy(weights, 'fundId'), values => {
      return values
        .sort((fst, snd) => fst.date - snd.date)
        .map(item => [item.date, item.value * 100])
    })
    return {
      weightData,
      total: weights.length,
    }
  }

  buildSeries(portfolio) {
    const { segments } = portfolio
    const funds = uniqBy(segments.reduce((out, item) => out.concat(item.funds), []), '_id')
    const { weightData, total } = this.buildWeightData(portfolio)
    const shouldChangeFreq = total > 5000 && funds.length > 50 && false
    return funds.map(fund => {
      const data = shouldChangeFreq
        ? changeFrequency(weightData[fund._id] || [], 'week')
        : weightData[fund._id] || []
      return {
        name: fund.name,
        data: data,
        yAxis: 0,
      }
    })
  }

  buildSeries1(portfolio) {
    const { segments } = portfolio
    const funds = uniqBy(segments.reduce((out, item) => out.concat(item.funds), []), '_id')
    const weightData = (portfolio.weightData || []).reduce((out, item) => {
      const date = +moment(item.date).startOf('date')
      const ret = _.map(item.weights, (weight, fundId) => {
        return [date, weight * 100, fundId]
      })
      return out.concat(ret)
    }, [])
    const weightDataMap = _.groupBy(weightData, 2)
    return funds.map(fund => {
      const data = (weightDataMap[fund._id] || []).sort((fst, snd) => fst[0] - snd[0])
      return {
        name: fund.name,
        data: data,
        yAxis: 0,
      }
    })
  }

  renderChart() {
    const { portfolio } = this.props
    const config = {
      chart: {
        type: 'column',
        height: 400,
      },
      yAxis: [
        {
          title: {
            text: '组合配比变化',
          },
          labels: {
            format: '{value}%',
          },
          max: 100,
        },
      ],
      plotOptions: {
        column: {
          stacking: 'normal',
        },
      },
      series: this.buildSeries(portfolio),
    }
    return <Chart options={config} constructorType="stockChart" />
  }

  renderChart1() {
    const { t, portfolio } = this.props
    const config = {
      chart: {
        type: 'column',
        height: 400,
      },
      yAxis: [
        {
          title: {
            text: '组合配比变化',
          },
          labels: {
            format: '{value}%',
          },
          max: 100,
        },
      ],
      plotOptions: {
        column: {
          stacking: 'normal',
        },
      },
      series: this.buildSeries1(portfolio),
    }
    return <Chart options={config} constructorType="stockChart" />
  }

  render() {
    const { t, portfolio } = this.props
    return (
      <Card
        title={
          <div>
            <span>
              {portfolio.portfolioType === 'css'
                ? '战略战术配比变化'
                : '组合配比变化'}
            </span>
          </div>
        }
      >
        {/* {this.renderChart()} */}
        {this.renderChart1()}
      </Card>
    )
  }
}
