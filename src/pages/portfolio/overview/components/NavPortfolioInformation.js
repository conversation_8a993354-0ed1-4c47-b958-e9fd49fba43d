import React, { Component } from 'react'
import Chart from '@/components/Chart/Chart'
import DataTable from '@/components/DataTable'
import moment from 'moment'
import uniqBy from 'lodash/uniqBy'
import groupBy from 'lodash/groupBy'
import map from 'lodash/map'
import uniq from 'lodash/uniq'
import { Card, Select } from 'antd'
import renderFundQuota from '@/utils/renderFundQuota'
import math from '@/utils/math'
import FundWeightChart from './FundWeightChart'

const Option = Select.Option

export default class NavPortfolioInformation extends Component {
  constructor(props) {
    super(props)
    const {
      portfolio: { segments, navPortfolioType },
    } = props
    let funds = segments
      .reduce((out, segment) => {
        const items = segment[`${navPortfolioType !== 'cash' ? 'funds' : 'injections'}`].map(
          item => {
            return {
              _id: item._id,
              name: item.name,
              isSaa: item.isSaa,
            }
          },
        )
        return out.concat(items)
      }, [])
      .filter(item => item._id !== 'cash')
    funds = uniqBy(funds, '_id')
    const defaultSelectedFund = funds.sort((fst, snd) => snd.isSaa - fst.isSaa)[0]
    this.state = {
      funds,
      chart: {},
      navFundList: [],
      selectedIds: defaultSelectedFund ? [defaultSelectedFund._id] : [],
    }
  }

  componentDidMount() {
    this.loadFundNavList(this.state.selectedIds.join(','))
  }

  componentWillReceiveProps(nextProps) {
    const { navFundList } = nextProps
    const {
      portfolio: { navStartDate, navEndDate },
    } = this.props
    if (navFundList && navFundList.length) {
      const newFunds = navFundList
        .filter(item => !this.state.navFundList.some(fund => fund._id === item._id))
        .map(item => {
          return {
            _id: item._id,
            name: item.name,
            portfolioType: item.portfolioType,
            nets: item.nets.filter(nav => nav.date >= navStartDate && nav.date <= navEndDate),
          }
        })
      if (newFunds.length) {
        this.setState(
          {
            navFundList: this.state.navFundList.concat(newFunds),
          },
          this.renderNavChart,
        )
      }
    }
  }

  onSelectFund = value => {
    const newValue = value || []
    const { navFundList } = this.state
    this.setState({ selectedIds: newValue }, () => {
      const ids = newValue.filter(id => !navFundList.some(item => item._id === id))
      if (ids.length) {
        this.loadFundNavList(ids.join(','))
      } else {
        this.renderNavChart()
      }
    })
  };

  loadFundNavList = ids => {
    const { dispatch } = this.props
    dispatch({
      type: 'fund/getNavList',
      payload: {
        ids,
      },
    })
  };

  shouldBalance = (cycle, balanceAt, date) => {
    let rebalance = false
    const currentDate = moment(new Date(date))
    const balanceDate = moment(new Date(balanceAt))
    switch (cycle) {
      case 'week':
        if (currentDate.isoWeekday() === balanceAt) {
          rebalance = true
        }
        break
      case 'month':
        if (currentDate.date() === balanceAt) {
          rebalance = true
        }
        break
      case 'year':
        if (balanceDate.format('MM-DD') === currentDate.format('MM-DD')) {
          rebalance = true
        }
        break
      default:
        break
    }
    return rebalance
  };

  buildAssetSerieData(fund, portfolio) {
    const { segments, nets, scale } = portfolio
    return segments.reduce((out, segment) => {
      const { endToNow, injections, balanceAt, cycle, autoBalance } = segment
      const injectionMap = groupBy(injections, '_id')
      const fundIds = segment.funds.map(item => item._id)
      const index = fundIds.indexOf(fund._id)
      if (index === -1) {
        return out
      }
      const startDate = +moment(segment.startDate).startOf('date')
      const endDate = endToNow
        ? +moment().startOf('date')
        : +moment(segment.endDate).startOf('date')
      const segmentNets = nets.filter(item => item.date >= startDate && item.date < endDate)
      const data = scale
        .filter(item => item.date >= startDate && item.date < endDate)
        .map((item, key, arr) => {
          const ret = {
            date: item.date,
            value: segmentNets[key].weights[index] * item.value,
            w: segmentNets[key].weights[index],
            t: item.value,
            d: moment(new Date(item.date)).format('YYYYMMDD'),
          }
          if (startDate === item.date && injectionMap[fund._id]) {
            ret.injection = injectionMap[fund._id][0].injection
          } else if (
            autoBalance &&
            this.shouldBalance(cycle, balanceAt, item.date) &&
            injectionMap[fund._id]
          ) {
            ret.injection = injectionMap[fund._id][0].injection
          }
          return ret
        })
      return out.concat(data)
    }, [])
  }

  getCashPortfolioFunds(portfolio) {
    const { segments } = portfolio
    const funds = uniqBy(segments.reduce((out, item) => out.concat(item.funds), []), '_id')
    return funds.map(fund => {
      const scale = this.buildAssetSerieData(fund, portfolio)
      return {
        ...fund,
        scale,
      }
    })
  }

  renderAssetChart() {
    const { t, portfolio } = this.props
    const funds = this.getCashPortfolioFunds(portfolio)
    const series = funds.map(fund => {
      return {
        name: fund.name,
        data: fund.scale.map(item => [item.date, item.value]),
        yAxis: 0,
        stacking: 'normal',
      }
    })
    series.push({
      type: 'line',
      name: '组合净资产',
      data: portfolio.scale.map(item => [item.date, item.value]),
      yAxis: 1,
    })
    const config = {
      chart: {
        type: 'area',
        height: 300,
      },
      tooltip: {
        pointFormat:
          '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.2f}</b><br/>',
      },
      yAxis: [
        {
          title: {
            text: t('portfolio.netAssets'),
          },
          labels: {
            format: '{value}',
          },
        },
        {
          title: {
            text: '组合净资产',
          },
          labels: {
            format: '{value}',
          },
          opposite: true,
        },
      ],
      series: series,
    }
    return <Chart options={config} constructorType="stockChart" />
  }

  renderFundWeightChart() {
    const { portfolioFundWeights, t } = this.props
    const config = {
      chart: {
        type: 'area',
        height: 300,
      },
      yAxis: [
        {
          title: {
            text: t('portfolio.proportionChange'),
          },
          labels: {
            format: '{value}%',
          },
        },
      ],
      series: portfolioFundWeights,
    }
    return <Chart options={config} constructorType="stockChart" />
  }

  renderBalanceRangeChart(props) {
    const {
      portfolio: { segments, navPortfolioType },
    } = props
    const ranges = segments.reduce((out, segment) => {
      const items = segment[`${navPortfolioType !== 'cash' ? 'funds' : 'injections'}`].map(item => {
        return {
          startDate: moment(segment.startDate).format('YYYY-MM-DD'),
          ratio: item.ratio,
          _id: item._id,
          name: item.name,
        }
      })
      return out.concat(items)
    }, [])
    const fundDateRatioMap = ranges.reduce((out, item) => {
      out[`${item._id}-${item.startDate}`] = item.ratio
      return out
    }, {})
    const dates = uniq(ranges.map(item => item.startDate)).sort((fst, snd) => fst - snd)
    const series = map(groupBy(ranges, '_id'), values => {
      const fund = values[0]
      const data = dates.map(date => fundDateRatioMap[`${fund._id}-${date}`] || null)
      return {
        name: fund.name,
        data: data,
      }
    })
    const config = {
      chart: {
        type: 'column',
        height: 300,
      },
      xAxis: {
        categories: dates,
      },
      plotOptions: {
        column: {
          stacking: 'normal',
        },
      },
      series: series,
    }
    return <Chart options={config} />
  }

  renderNavChart = () => {
    const { portfolio, t } = this.props
    const { selectedIds, navFundList } = this.state
    const funds = navFundList.filter(item => selectedIds.some(id => id === item._id))
    const config = {
      chart: {
        type: 'line',
        height: 300,
      },
      yAxis: [
        {
          title: {
            text: t('portfolio.cumulIncrease'),
          },
          labels: {
            format: '{value}%',
          },
        },
      ],
      series: [portfolio, ...funds].map(fund => {
        return {
          name: fund.name,
          data: fund.nets.map(item => [item.date, item.value]),
          compare: 'percent',
          yAxis: 0,
          tooltip: {
            pointFormat: '{series.name}: <b>{point.y:.4f}({point.change:.2f}%)</b><br/>',
          },
        }
      }),
    }
    return <Chart options={config} constructorType="stockChart" />
  };

  renderBalanceCycle() {
    const { t } = this.props
    const segment = this.props.portfolio.segments[0]
    const { balanceAt, cycle } = segment
    const cycleMap = {
      week: t('portfolio.weekly'),
      month: t('portfolio.monthly'),
      year: t('portfolio.yearly'),
    }
    const weekMap = {
      0: t('portfolio.sunday'),
      1: t('portfolio.monday'),
      2: t('portfolio.tuesday'),
      3: t('portfolio.wednesday'),
      4: t('portfolio.thursday'),
      5: t('portfolio.friday'),
      6: t('portfolio.saturday'),
    }
    let balanceAtText
    if (cycle === 'week') {
      balanceAtText = weekMap[balanceAt]
    } else if (cycle === 'month') {
      balanceAtText = `${balanceAt}${t('portfolio.day')}`
    } else {
      balanceAtText = moment(new Date(balanceAt)).format(
        `MM${t('portfolio.month')}DD${t('portfolio.day')}`,
      )
    }
    return `${cycleMap[cycle]}${balanceAtText}`
  }

  renderBlanceRangeTitle() {
    const {
      portfolio: { segments },
      t,
    } = this.props
    const autoBalance = segments[0].autoBalance
    return (
      <div>
        <span>
          <span>{t('portfolio.ratioRebalInterval')}</span>
          {autoBalance && (
            <small>
              ({t('portfolio.autoRebalCycle')}
              {this.renderBalanceCycle()})
            </small>
          )}
        </span>
      </div>
    )
  }

  renderProfitTable = () => {
    const { portfolio, t } = this.props
    const funds = this.getCashPortfolioFunds(portfolio).filter(item => item._id !== 'cash')
    funds.forEach(fund => {
      const scale = fund.scale
      const scaleValues = scale.map(item => item.value)
      fund.navEndOfTerm = scaleValues[scaleValues.length - 1]
      // Tricky fix navStartOfTerm
      fund.navStartOfTerm = scale[0].injection || scale[0].value
      const injectionSum = math.sum(
        fund.scale.map((item, index) => (index === 0 ? 0 : item.injection) || 0),
      )
      fund.accumulatedIncome = fund.navEndOfTerm - fund.navStartOfTerm - injectionSum
    })
    const sum = (list, field) => list.reduce((out, item) => out + (item[field] || 0), 0)
    const accumulatedIncomeSum = sum(funds, 'accumulatedIncome')
    const netAssetSum = sum(funds, 'navEndOfTerm')
    funds.forEach(fund => {
      fund.accumulatedIncomeRatio = fund.accumulatedIncome / accumulatedIncomeSum
      fund.weight = fund.navEndOfTerm / netAssetSum
    })
    const total = {
      name: t('quota.total'),
      navEndOfTerm: sum(funds, 'navEndOfTerm'),
      weight: sum(funds, 'weight'),
      accumulatedIncome: sum(funds, 'accumulatedIncome'),
      accumulatedIncomeRatio: sum(funds, 'accumulatedIncomeRatio'),
    }
    funds.push(total)
    const quotas = [
      {
        name: t('portfolio.fund'),
        value: 'name',
        // render: (text, fund) => (
        //   <span>
        //     <Tooltip id={`analysis-nav-tooltip-${fund._id}`} title={t('common.fundDetails')}>
        //       <span className={styles.fundName} onClick={() => window.open(`/analysis/overview/${fund._id}`)}>{fund.name}</span>
        //     </Tooltip>
        //   </span>
        // ),
        width: 215,
      },
      {
        name: t('portfolio.posMarketValue'),
        value: 'navEndOfTerm',
        format: 'commaNumber',
        width: 215,
      },
      {
        name: t('portfolio.positionShare'),
        value: 'weight',
        format: 'percentage',
        width: 215,
      },
      {
        name: t('portfolio.accumulatedIncome'),
        value: 'accumulatedIncome',
        format: 'commaNumber',
        width: 215,
      },
      {
        name: t('portfolio.cumulativeIncomeShare'),
        value: 'accumulatedIncomeRatio',
        format: 'percentage',
        width: 214,
      },
    ]
    return (
      <Card
        title={
          <div>
            <span>{t('portfolio.profitAnalysis')}</span>
          </div>
        }
      >
        <DataTable data={funds} quotas={quotas} yScrollHeight={342} />
      </Card>
    )
  };

  renderOverviewTable = () => {
    const { portfolio, t } = this.props
    const { navPortfolioType, segments, portfolioType } = portfolio
    const isCashPortfolio = navPortfolioType === 'cash' || portfolioType === 'simulate'
    const quotas = [
      {
        name: t('portfolio.startDate'),
        value: 'maxDrawdownStartDate',
        format: 'date',
        width: 110,
        render: (value, row) => {
          const { theKey, len } = row
          return {
            children: renderFundQuota({ value: 'maxDrawdownStartDate', format: 'date' }, value),
            props: {
              rowSpan: theKey === 0 ? len : 0,
            },
          }
        },
      },
      {
        name: t('portfolio.fund'),
        value: 'name',
        format: 'text',
        width: '50%',
      },
      {
        name: !isCashPortfolio ? t('portfolio.ratio') : t('portfolio.fundAmount'),
        value: !isCashPortfolio ? 'ratio' : 'injection',
        format: !isCashPortfolio ? 'percentage' : 'commaNumber',
        width: 464,
      },
    ]

    const data = segments.reduce((out, segment) => {
      segment[`${!isCashPortfolio ? 'funds' : 'injections'}`].forEach((item, key, arr) => {
        const res = {
          theKey: key,
          len: arr.length,
          maxDrawdownStartDate: segment.startDate,
          ...item,
          ratio: item.ratio / 100,
        }
        out.push(res)
      })
      return out
    }, [])
    return (
      <Card
        title={
          <div>
            <span>{t('portfolio.basInfo')}</span>
          </div>
        }
      >
        <DataTable data={data} quotas={quotas} yScrollHeight={342} />
      </Card>
    )
  };

  render() {
    const { t, portfolio } = this.props
    const { funds, selectedIds } = this.state
    const isCashPortfolio =
      portfolio.portfolioType === 'simulate' || portfolio.navPortfolioType === 'cash'
    return (
      <div>
        <Card
          title={
            <div>
              <span>{t('portfolio.combinationNetChange')}</span>
              <Select
                mode="multiple"
                style={{ width: '50%', marginLeft: '30px' }}
                placeholder={t('portfolio.selectReferFund')}
                onChange={this.onSelectFund}
                value={selectedIds}
                optionFilterProp="label"
              >
                {funds.map(item => (
                  <Option key={item._id} label={`${item.name}-${item._qutkeId}`}>{item.name}</Option>
                ))}
              </Select>
            </div>
          }
        >
          {this.renderNavChart()}
        </Card>
        <FundWeightChart portfolio={portfolio} t={t} />
        {portfolio.portfolioType === 'css' && (
          <Card
            title={
              <div>
                <span>{t('portfolio.subFundRatioChange')}</span>
              </div>
            }
          >
            {this.renderFundWeightChart()}
          </Card>
        )}
        {isCashPortfolio && (
          <Card
            title={
              <div>
                <span>{t('portfolio.combinedNetAssetChange')}</span>
              </div>
            }
          >
            {this.renderAssetChart()}
          </Card>
        )}
        {isCashPortfolio && this.renderProfitTable()}
        {['nav', 'pinganannuity', 'simulate', 'factorbased'].includes(portfolio.portfolioType) &&
          this.renderOverviewTable()}
      </div>
    )
  }
}
