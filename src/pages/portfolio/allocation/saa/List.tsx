import React from 'react'
import { Dispatch } from 'redux'
import { DeleteOutlined, EditOutlined, MenuOutlined, PlusOutlined, TableOutlined } from '@ant-design/icons';
import { Breadcrumb, Divider, Button, Input, Card, Popconfirm, Tooltip, Row, Col } from 'antd';
import router from 'umi/router'
import Link from 'umi/link'
import { connect } from 'dva'
import StandardTable, {
  TableListData,
  StandardTableColumnProps,
  TableListParams,
} from '@/components/StandardTable'
import NavPortfolioModal from '@/components/NavPortfolioModal'
import NavPortfolioCard from '@/components/NavPortfolioCard'
import { ModelState } from './model'
import { FundModelState } from '@/models/fund'
import getRealPathName from '@/utils/getRealPathname'
import { updateIframeHeight, resetIframeScrolling } from '@/utils/iframe'
import styles from './style.less'
import moment from 'moment'
import classnames from 'classnames'
import { formatMessage } from 'umi-plugin-react/locale'

const t = (id: string, params?: any) => formatMessage({ id }, params)
const { Search } = Input
const ButtonGroup = Button.Group

interface ComponentProps {
  dispatch: Dispatch<any>;
  match: any;
  schemaData?: TableListData;
  portfolioListData?: TableListData;
  frameworkData?: TableListData;
  location?: any;
  loading?: Boolean;
}

@connect(
  ({
    saa,
    fund,
    loading,
  }: {
    saa: ModelState;
    fund: FundModelState;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    schemaData: saa.schemaData,
    portfolioListData: fund.portfolioListData,
    frameworkData: saa.frameworkData,
    loading: loading.models.saa || loading.models.fund,
  }),
)
export default class List extends React.Component<ComponentProps> {
  constructor(props: ComponentProps) {
    super(props)
    const {
      location: { pathname },
    } = props
    const { schemaColumns, strategyColumns } = this
    const saaTabs = [
      {
        name: '配置方案',
        value: 'saaSchema',
        action: 'saa/fetchSchemas',
        columns: schemaColumns,
        dataKey: 'schemaData',
      },
      {
        name: 'SAA策略',
        value: 'saaStrategy',
        action: 'fund/fetchSaaStrategies',
        columns: strategyColumns,
        dataKey: 'portfolioListData',
      },
      // {
      //   name: '分析框架',
      //   value: 'framework',
      //   action: 'saa/fetchFrameworks',
      //   columns: frameworkColumns,
      //   dataKey: 'frameworkData',
      // }
    ]
    const taaTabs = [
      {
        name: '配置方案',
        value: 'taaSchema',
        action: 'fund/fetchTaaSchema',
        columns: strategyColumns,
        dataKey: 'portfolioListData',
      },
      {
        name: 'TAA策略',
        value: 'taaStrategy',
        action: 'fund/fetchTaaStrategies',
        columns: strategyColumns,
        dataKey: 'portfolioListData',
      },
    ]
    const parentPath = /\/taa\//.test(pathname) ? 'taa' : 'saa'
    this.state = {
      parentPath,
      collapsed: false,
      viewMode: 'grid',
      tabs: parentPath === 'taa' ? taaTabs : saaTabs,
    }
  }

  schemaColumns: StandardTableColumnProps[] = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text, record) => {
        return <Link to={getRealPathName(`/assetallocation/saa/schema/${record._id}`)}>{text}</Link>
      },
    },
    {
      title: '分析框架',
      dataIndex: 'historyName',
    },
    {
      title: '计算模型',
      render: (text, record) => {
        const model = record.model.name
        if (model === 'opt') {
          return (
            <Button ghost size="small" style={{ color: '#E49831', borderColor: '#E49831' }}>
              Mean Variance
            </Button>
          )
        }
        if (model === 'parity') {
          return (
            <Button ghost size="small" style={{ color: '#E85655', borderColor: '#E85655' }}>
              Risk Parity
            </Button>
          )
        }
        if (model === 'bl') {
          return (
            <Button ghost size="small" style={{ color: '#46A3DF', borderColor: '#46A3DF' }}>
              Black Litterman
            </Button>
          )
        }
        return null
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      format: 'date',
      align: 'right',
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      align: 'right',
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => (
        <>
          <Link to={getRealPathName(`/assetallocation/saa/schema/${record._id}`)}>
            <Tooltip title="编辑">
              <EditOutlined />
            </Tooltip>
          </Link>
          <Divider type="vertical" />
          <Popconfirm
            title={`${t('portfolio.delTip')}${record.name}${t('portfolio.questionEnd')}？`}
            onConfirm={() => this.removeSchema(record._id)}
            onCancel={() => {}}
            okText={t('portfolio.confirm')}
            cancelText={t('portfolio.cancel')}
          >
            <Tooltip title="删除">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>
        </>
      ),
    },
  ];

  frameworkColumns: StandardTableColumnProps[] = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text, record) => {
        return (
          <Link to={getRealPathName(`/portfolio/allocation/saa/framework/${record._id}`)}>
            {record.name}
          </Link>
        )
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      format: 'date',
      align: 'right',
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      align: 'right',
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        if (record.visibility === 'public') {
          return false
        }
        return <>
          <Link to={getRealPathName(`/portfolio/allocation/saa/framework/${record._id}`)}>
            <Tooltip title="编辑">
              <EditOutlined />
            </Tooltip>
          </Link>
          <Divider type="vertical" />
          <Popconfirm
            title={`${t('portfolio.delTip')}${record.name}${t('portfolio.questionEnd')}？`}
            onConfirm={() => this.removeFramework(record._id)}
            onCancel={() => {}}
            okText={t('portfolio.confirm')}
            cancelText={t('portfolio.cancel')}
          >
            <Tooltip title="删除">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>
        </>;
      },
    },
  ];

  strategyColumns: StandardTableColumnProps[] = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text, record) => (
        <a href={`/ocgaas/kym/fof/${record._id}`} rel="noopener noreferrer" target="_blank">
          {record.name}
        </a>
      ),
    },
    {
      title: '成立时间',
      dataIndex: 'startDate',
      format: 'date',
      align: 'right',
      sorter: true,
    },
    {
      title: '净值结束日期',
      dataIndex: 'navEndDate',
      format: 'date',
      align: 'right',
      sorter: true,
    },
    {
      title: '累计收益',
      dataIndex: 'accReturn',
      sorter: true,
      align: 'right',
      format: 'percentage',
    },
    {
      title: '年化收益',
      dataIndex: 'yearReturn',
      sorter: true,
      align: 'right',
      format: 'percentage',
    },
    {
      title: '最大回撤',
      dataIndex: 'maxDrawdown',
      align: 'right',
      format: 'valPercentage',
      sorter: true,
    },
    {
      title: '波动率',
      dataIndex: 'vol',
      align: 'right',
      format: 'valPercentage',
      sorter: true,
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => (
        <>
          <NavPortfolioModal
            isEdit
            fundData={record}
            t={t}
            fundType={record.portfolioType}
            navPortfolioType={
              record.portfolioType === 'nav' ? record.navPortfolioType || 'nav' : undefined
            }
          >
            <Tooltip title="编辑">
              <EditOutlined />
            </Tooltip>
          </NavPortfolioModal>
          <Divider type="vertical" />
          <Popconfirm
            title={`${t('portfolio.delTip')}${record.name}${t('portfolio.questionEnd')}？`}
            onConfirm={() => this.removePortfolio(record._id)}
            onCancel={() => {}}
            okText={t('portfolio.confirm')}
            cancelText={t('portfolio.cancel')}
          >
            <Tooltip title="删除">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>
        </>
      ),
    },
  ];

  componentDidMount() {
    const currentTab = this.getCurrentTab()
    this.loadListData(currentTab, {})
  }

  componentDidUpdate() {
    updateIframeHeight('#allocationList')
  }

  componentWillUnmount() {
    resetIframeScrolling()
  }

  getRealUrl = (url: string) => {
    const {
      location: { pathname },
    } = this.props
    const isPage = /\/page\//.test(pathname)
    if (isPage) {
      return `/page${url}`
    }
    return url
  };

  removePortfolio = (id: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'fund/deleteNavPortfolio',
      payload: { id },
    })
  };

  removeFramework = (id: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'saa/deleteFramework',
      payload: { id },
    })
  };

  removeSchema = (id: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'saa/deleteSchema',
      payload: { id },
    })
  };

  loadListData = (currentTab: any, params: Partial<TableListParams>) => {
    const { dispatch } = this.props
    dispatch({
      type: `${currentTab.action}`,
      payload: params,
    })
  };

  getCurrentTab = () => {
    const { tabs } = this.state
    const {
      match: {
        params: { tab },
      },
    } = this.props
    const currentTab = tabs.find(item => item.value === tab)
    return currentTab || tabs[0]
  };

  handleTableChange = (params: Partial<TableListParams> = {}) => {
    const currentTab = this.getCurrentTab()
    this.loadListData(currentTab, params)
  };

  handleSeachInput = (value: string) => {
    const currentTab = this.getCurrentTab()
    this.loadListData(currentTab, {
      input: value,
      page: 1,
    })
  };

  handleTabClick = (tab: any) => () => {
    const { parentPath } = this.state
    router.push(getRealPathName(`/fof/${parentPath}/${tab.value}`))
    this.loadListData(tab, { page: 1 })
  };

  handleViewModeClick = (viewMode: string) => () => {
    this.setState({ viewMode })
  };

  renderActions = (currentTab: any) => {
    const { tabs } = this.state
    return tabs.map(item => {
      if (item.value === currentTab.value) {
        return <Button type="primary">{item.name}</Button>
      }
      return <Button onClick={this.handleTabClick(item)}>{item.name}</Button>
    })
  };

  renderCreateButton = currentTab => {
    const title = `创建${currentTab.name}`
    const tabValue = currentTab.value
    if (tabValue === 'saaStrategy') {
      return false
    } else if (tabValue === 'saaSchema') {
      return (
        <Link to={getRealPathName('/assetallocation/saa/schema/new')}>
          <PlusOutlined />
          {title}
        </Link>
      );
    } else if (tabValue === 'framework') {
      return (
        <Link to={getRealPathName('/portfolio/allocation/saa/framework/new')}>
          <PlusOutlined />
          {title}
        </Link>
      );
    } else {
      const fundType = tabValue === 'taaSchema' ? 'css' : 'taa'
      return (
        <NavPortfolioModal fundType={fundType} t={t}>
          <a>
            <PlusOutlined />
            {title}
          </a>
        </NavPortfolioModal>
      );
    }
  };

  render() {
    const { parentPath, viewMode } = this.state
    const currentTab = this.getCurrentTab()
    const data = this.props[currentTab.dataKey]
    const isTaaSchema = currentTab.value === 'taaSchema'
    const hasGrid = currentTab.value === 'taaSchema' || currentTab.value === 'saaStrategy'
    const { loading } = this.props
    return (
      <div id="allocationList">
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>经典大类资产配置方案</Breadcrumb.Item>
        </Breadcrumb>
        {false &&
        <div className={styles.buttonWrapper}>
          {this.renderActions(currentTab)}
          {!hasGrid && (
            <Search
              style={{ float: 'right', width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={this.handleSeachInput}
            />
          )}
          {hasGrid && (
            <div style={{ float: 'right' }}>
              {this.renderCreateButton(currentTab)}
              <ButtonGroup className={styles.viewMode}>
                <Button
                  className={classnames({ [styles.active]: viewMode === 'grid' })}
                  icon={<TableOutlined />}
                  onClick={this.handleViewModeClick('grid')}
                />
                <Button
                  className={classnames({ [styles.active]: viewMode === 'list' })}
                  icon={<MenuOutlined />}
                  onClick={this.handleViewModeClick('list')}
                />
              </ButtonGroup>
            </div>
          )}
        </div>}
        {hasGrid && viewMode === 'grid' ? (
          <Row gutter={10}>
            {data &&
              data.list.map(item => (
                <Col xs={24} sm={12} md={8} lg={6} xl={6} xxl={4}>
                  <NavPortfolioCard t={t} portfolio={item} removePortfolio={this.removePortfolio} />
                </Col>
              ))}
          </Row>
        ) : (
          <Card
            title={
              <Search
                style={{ width: '300px' }}
                placeholder="按回车进行搜索"
                onSearch={this.handleSeachInput}
              />
            }
            bordered={false}
            extra={
              <>
                {!isTaaSchema && this.renderCreateButton(currentTab)}
              </>
            }
          >
            <StandardTable
              disableRowSlection
              loading={loading}
              columns={currentTab.columns}
              data={data}
              selectedRows={[]}
              onChange={this.handleTableChange}
              size="small"
            />
          </Card>
        )}
      </div>
    );
  }
}
