import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function querySchemas(params: TableListParams) {
  return request('/api/allocations', {
    params,
  })
}

export async function deleteSchema(id: string) {
  return request(`/api/allocations/${id}`, {
    method: 'delete',
  })
}

export async function queryFrameWorks(params: TableListParams) {
  return request('/api/historystates', {
    params,
  })
}

export async function queryFunds(params: TableListParams) {
  return request('/api/funds', {
    params,
  })
}

export async function queryFrameWork(id: string) {
  return request(`/api/historystates/${id}`)
}

export async function deleteFrameWork(id: string) {
  return request(`/api/historystates/${id}`, {
    method: 'delete',
  })
}
