import { Effect } from 'dva'
import { Reducer } from 'redux'
import { notification } from 'antd'
import { TableListData } from '@/components/StandardTable'
import {
  querySchemas,
  queryFrameWork,
  queryFrameWorks,
  deleteFrameWork,
  deleteSchema,
} from './service'

export interface ModelState {
  schemaData?: TableListData;
  strategyData?: TableListData;
  frameworkData?: TableListData;
  framework?: any;
}

export interface ModelType {
  namespace: 'saa';
  state: ModelState;
  effects: {
    fetchSchemas: Effect;
    fetchFrameworks: Effect;
    fetchFramework: Effect;
    deleteFramework: Effect;
    deleteSchema: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
  };
}

function buildListPaylod(params: any, response: any) {
  const { list, totalNum } = response
  const { page, per_page } = params
  return {
    list,
    pagination: {
      total: totalNum,
      pageSize: per_page || 10,
      current: page || 1,
    },
  }
}

const SaaModel: ModelType = {
  namespace: 'saa',

  state: {
    schemaData: {
      list: [],
      pagination: {},
    },
    strategyData: {
      list: [],
      pagination: {},
    },
    frameworkData: {
      list: [],
      pagination: {},
    },
  },

  effects: {
    *fetchSchemas({ payload }, { call, put }) {
      const response = yield call(querySchemas, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          schemaData: data,
        },
      })
    },
    *fetchFrameworks({ payload }, { call, put }) {
      const response = yield call(queryFrameWorks, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          frameworkData: data,
        },
      })
    },
    *fetchFramework({ payload: { id } }, { call, put }) {
      const response = yield call(queryFrameWork, id)
      yield put({
        type: 'save',
        payload: {
          framework: response,
        },
      })
    },
    *deleteFramework({ payload: { id } }, { call, put }) {
      yield call(deleteFrameWork, id)
      yield put({
        type: 'removeFromList',
        payload: { id },
      })
      notification.success({ message: '删除成功！' })
    },
    *deleteSchema({ payload: { id } }, { call, put }) {
      yield call(deleteSchema, id)
      yield put({
        type: 'removeSchemaFromList',
        payload: { id },
      })
      notification.success({ message: '删除成功！' })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    removeFromList(state: any, action) {
      const { frameworkData } = state
      const newList = frameworkData.list.filter(item => item._id !== action.payload.id)
      return {
        ...state,
        frameworkData: {
          ...frameworkData,
          list: newList,
        },
      }
    },
    removeSchemaFromList(state: any, action) {
      const { schemaData } = state
      const newList = schemaData.list.filter(item => item._id !== action.payload.id)
      return {
        ...state,
        schemaData: {
          ...schemaData,
          list: newList,
        },
      }
    },
  },
}

export default SaaModel
