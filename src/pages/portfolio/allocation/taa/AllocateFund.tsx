import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { ModelState as KymModelState } from '@/models/taa'
import { Card, Row, Col, Button, Radio, InputNumber, Divider } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import styles from './style.less'
import EditableTable from '@/components/EditableTable'
import NavPortfolioModal from '@/components/NavPortfolioModal'
import StyleTag from './components/StyleTag'
import SelectDate from './components/SelectDate'
import SelectFundModal from '@/components/SelectFundModal'
import { formatMessage } from 'umi-plugin-react/locale'

const t = (id: string, params?: any) => formatMessage({ id }, params)

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  taaScore: {
    scoreListData: any;
    corrListData: any;
    dateList: any;
  };
  fundSelectScopes: string;
  fundWeightStrategy: string;
  fundEqualWeightLimitValue: number;
  similarAssetsFundNumLimit: number;
  currentDate?: string;
  fundWeightData?: any;
}

interface ComponentState {
  currentStrategy?: number;
  styleData?: any;
  currentStep?: string;
}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    loading,
    taa,
  }: {
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
    taa: KymModelState;
  }) => ({
    loading: loading.models.taa,
    taaScore: taa.taaScore,
    fundSelectScopes: taa.fundSelectScopes,
    fundWeightStrategy: taa.fundWeightStrategy,
    fundEqualWeightLimitValue: taa.fundEqualWeightLimitValue,
    similarAssetsFundNumLimit: taa.similarAssetsFundNumLimit,
    currentDate: taa.currentDate,
    fundWeightData: taa.fundWeightData,
  }),
)
class AllocateFund extends Component<ComponentProps, ComponentState> {
  state = {
    currentStrategy: 1,
    styleData: ['股票价值型', '股票均衡型', '股票成长型', '债券久期型', '债券信用型', '现金'].map(
      (item, index) => {
        return {
          key: `${index}`,
          styleName: item,
        }
      },
    ),
  };

  componentDidMount() {
    const { currentDate } = this.props
    if (currentDate) {
      this.loadFundList(currentDate)
    }
  }

  loadFundList = (date: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'taa/fetchFundList',
      payload: {
        params: {
          date: date,
        },
      },
    })
  };

  handleFundSelectScopeChange = (event: any) => {
    this.updateState({
      fundSelectScopes: event.target.value,
    })
    this.updateTaaStyleWeights()
  };

  handleFundWeightStrategyChange = (event: any) => {
    this.updateState({
      fundWeightStrategy: event.target.value,
    })
    this.updateTaaStyleWeights()
  };

  handleTableDataChange = data => {
    this.updateState({
      fundWeightData: data,
    })
  };

  handleEqualWeightLimitChange = (value: number) => {
    this.updateState({
      fundEqualWeightLimitValue: value,
    })
    this.updateTaaStyleWeights()
  };

  handleSimilarAssetsFundNumChange = (value: number) => {
    this.updateState({
      similarAssetsFundNumLimit: value,
    })
    this.updateTaaStyleWeights()
  };

  handleSelectCustomFunds = (funds: any) => {
    const classNameMap = {
      1: '股票价值型',
      2: '股票均衡型',
      3: '股票成长型',
      4: '债券久期择时型',
      5: '债券信用选择型',
    }
    this.updateState({
      customFundList: funds.map(item => {
        return {
          ...item,
          class_name: classNameMap[item.style_type],
          fund_abbr_name: item.name,
        }
      }),
    })
    this.updateTaaStyleWeights()
  }

  updateState = (newState: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'taa/save',
      payload: newState,
    })
  };

  updateTaaStyleWeights = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'taa/updateFundWeights',
    })
  };

  render() {
    const radioStyle = {
      display: 'block',
      height: '40px',
      lineHeight: '40px',
    }
    const {
      loading,
      taaScore: { dateList },
      currentDate,
      dispatch,
      fundWeightStrategy,
      fundSelectScopes,
      fundEqualWeightLimitValue,
      similarAssetsFundNumLimit,
      fundWeightData,
    } = this.props
    const columns = [
      {
        title: '资产类别',
        dataIndex: 'class_name',
        width: '150px',
        align: 'center',
        render: (text: string) => {
          return <StyleTag title={text} />
        },
      },
      {
        title: '基金代码',
        dataIndex: 'fund_id',
        width: '100px',
        align: 'center',
      },
      {
        title: '基金名称',
        dataIndex: 'fund_abbr_name',
        width: '150px',
        align: 'center',
      },
      {
        title: '对应权重(%)',
        align: 'center',
        dataIndex: 'weight',
        editable: true,
        format: 'sliderWithInput',
      },
    ]
    const hasWeightData = (data: any) =>
      data.length !== 0 && data.every(item => item.weight !== undefined)
    const weights = fundWeightData.reduce((out, item) => {
      out[item._id] = item.weight || 0
      return out
    }, {})
    return (
      <div>
        <Row gutter={16} className={styles.taa}>
          <Col lg={6} md={24}>
            <Card>
              同类资产最多配置基金个数为{' '}
              <InputNumber
                onChange={this.handleSimilarAssetsFundNumChange}
                value={similarAssetsFundNumLimit}
                size="small"
                style={{ width: 70, marginLeft: 10 }}
              />
              <Divider />
              <h4>基金选择范围</h4>
              <Radio.Group onChange={this.handleFundSelectScopeChange} value={fundSelectScopes}>
                <Radio style={radioStyle} value="kymGold">
                  KYM金牌
                </Radio>
                <Radio style={radioStyle} value="kymGoldProspect">
                  KYM金牌+展望
                </Radio>
                <Radio style={radioStyle} value="all">
                  该类别下全部基金
                </Radio>
                <Radio style={radioStyle} value="custom">
                  自定义
                </Radio>
              </Radio.Group>
              {fundSelectScopes === 'custom' &&
              <SelectFundModal dispatch={this.props.dispatch} onChange={this.handleSelectCustomFunds}>
                <Button ghost type="primary" icon={<PlusOutlined />} size="small">
                  选择基金
                </Button>
              </SelectFundModal>}
              <Divider />
              <h4>基金权重配置方案</h4>
              <Radio.Group
                onChange={this.handleFundWeightStrategyChange}
                value={fundWeightStrategy}
              >
                <Radio style={radioStyle} value="equalWeight">
                  等权配置
                </Radio>
                <Radio style={radioStyle} value="useKymScore">
                  与KYM评分成正比
                </Radio>
                <Radio style={radioStyle} value="limitedEqualWeight">
                  <span>等权配置下单支基金不少于</span>
                  <InputNumber
                    min={0}
                    max={100}
                    disabled={fundWeightStrategy !== 'limitedEqualWeight'}
                    onChange={this.handleEqualWeightLimitChange}
                    value={fundEqualWeightLimitValue}
                    size="small"
                    style={{ width: 70, marginLeft: 10 }}
                  />{' '}
                  %
                </Radio>
                <Radio style={radioStyle} value={4} disabled>
                  自定义
                </Radio>
              </Radio.Group>
              {hasWeightData(fundWeightData) && (
                <div style={{ textAlign: 'center', marginTop: '30px' }}>
                  <NavPortfolioModal funds={fundWeightData} defaultWeights={weights} t={t}>
                    <Button type="primary">创建模拟组合</Button>
                  </NavPortfolioModal>
                </div>
              )}
            </Card>
          </Col>
          <Col lg={18} md={24}>
            <Card
              extra={
                <SelectDate {...{ dispatch, currentDate, dateList }} onChange={this.loadFundList} />
              }
            >
              <EditableTable
                loading={loading}
                columns={columns}
                size="small"
                pagination={false}
                dataSource={fundWeightData}
                onDataChange={this.handleTableDataChange}
                scroll={{
                  y: 470,
                }}
              />
            </Card>
          </Col>
        </Row>
      </div>
    )
  }
}

export default AllocateFund
