import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import moment from 'moment'
import { ModelState as KymModelState } from '@/models/taa'
import { Card, Row, Col, Button, Spin, Rate, Table } from 'antd'
import Echart from '@/components/Chart/Echarts'
import Chart from '@/components/Chart/Chart'
import SelectDate from './components/SelectDate'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  taaScore: {
    scoreListData: any;
    corrListData: any;
    dateList: any;
  };
  currentDate?: string;
  saaWeightData: any;
}

interface ComponentState {
  currentStyle?: string;
}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    loading,
    taa,
  }: {
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
    taa: KymModelState;
  }) => ({
    loading: loading.models.taa,
    taaScore: taa.taaScore,
    currentDate: taa.currentDate,
    saaWeightData: taa.saaWeightData,
  }),
)
class Taa extends Component<ComponentProps, ComponentState> {
  state = {
    currentStyle: '股票价值型',
  };

  componentDidMount() {
    this.loadTaaScoreData()
  }

  handleDateChange = (date: string) => {
    this.updateState({ currentDate: date })
  };

  handleStyleChange = (styleName: string) => () => {
    this.setState(
      {
        currentStyle: styleName,
      },
      this.loadTaaScoreData,
    )
  };

  loadTaaScoreData() {
    const { dispatch } = this.props
    const { currentStyle } = this.state
    const params = {
      styleName: currentStyle,
    }
    dispatch({
      type: 'taa/fetchTaaScore',
      payload: {
        params,
      },
    })
  }

  updateState = (newState: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'taa/save',
      payload: newState,
    })
  };

  renderScoreChart() {
    const {
      taaScore: { scoreListData },
      currentDate,
    } = this.props
    const scoreItem = scoreListData.find((item: any) => item.the_date === currentDate) || {}
    const options = {
      tooltip: {
        formatter: '{a} <br/>{b} : {c}%',
      },
      legend: {
        show: false,
      },
      series: [
        {
          name: 'TAA评分',
          type: 'gauge',
          min: 0,
          max: 5,
          splitNumber: 1,
          detail: {
            formatter: function(value: number) {
              return value.toFixed(2)
            },
            color: '#ddd',
          },
          axisLabel: {
            show: true,
            color:'#fff',
          },// 刻度标签
          axisTick: {
            show: false,
            lineStyle: {
              color: '#fff',
              width: 1,
            },
            length: -8,
          },// 刻度样式
          splitLine: {
            show: false,
            length: -20,
            lineStyle: {
              color: '#fff',
            },
          },// 分隔线样式
          axisLine: {
            // lineStyle: {
            //   color: [
            //     [0.2, '#009688'],
            //     [0.4, '#03a9f4'],
            //     [0.6, '#ffeb3b'],
            //     [0.8, '#9c27b0'],
            //     [1, '#ff5722'],
            //   ],
            // },
            lineStyle: {
              width: 30,
              color: [
                [
                  1, new Echart.echarts.graphic.LinearGradient(
                    0, 0, 1, 0, [{
                      offset: 0,
                      color: '#009688',
                    }, {
                      offset: 0.3,
                      color: '#03a9f4',
                    }, {
                      offset: 0.7,
                      color: '#9c27b0',
                    }, {
                      offset: 1,
                      color: '#ff5722',
                    }]
                  ),
                ],
              ],
            },
          },
          title: {
            show: false,
          },
          data: [{ value: scoreItem.taa_score, name: '分值' }],
        },
      ],
    }

    let realValue = scoreItem.taa_score
    if (realValue) {
      const n = Math.floor(realValue)
      const diff = realValue - n >= 0.5 ? 0.5 : 0
      realValue = n + diff
    }

    return (
      <>
        <Echart options={options} />
        <div style={{ textAlign: 'center' }}>
          <span>
            <span style={{ marginRight: '8px' }}>看空</span>
            <Rate
              disabled
              allowHalf
              tooltips={['0-1 强烈看空', '1-2 看空', '2-3 中性', '3-4 看多', '4-5 强烈看多']}
              value={realValue}
            />
            <span className="ant-rate-text">看多</span>
          </span>
        </div>
      </>
    )
  }

  renderWeightProgress(weight: number) {
    const progressColor = weight > 0 ? '#e85555' : '#3ec09d'
    const leftMargin = weight < 0 ? 50 - Math.abs(weight) / 2 : 50
    const positiveRadius = '0 10px 10px 0'
    const negtiveRadius = '10px 0px 0px 10px'
    return (
      <div className="ant-progress ant-progress-line ant-progress-status-normal ant-progress-show-info ant-progress-default">
        <div>
          <div className="ant-progress-outer" style={{ width: '90%' }}>
            <div className="ant-progress-inner" style={{ backgroundColor: '#373e4a' }}>
              <div
                className="ant-progress-bg"
                style={{
                  left: `${leftMargin}%`,
                  width: `${Math.abs(weight / 2)}%`,
                  height: '8px',
                  backgroundColor: progressColor,
                  borderRadius: weight > 0 ? positiveRadius : negtiveRadius,
                }}
              ></div>
            </div>
          </div>
          <span className="ant-progress-text" title="60%">
            {weight.toFixed(2)}%
          </span>
        </div>
      </div>
    )
  }

  renderFactorWeight() {
    const {
      taaScore: { corrListData },
      currentDate,
    } = this.props
    const data = corrListData.filter((item: any) => item.the_date === currentDate)
    const columns = [
      {
        title: '显著宏观因子',
        dataIndex: 'macro_factor',
        width: '30%',
      },
      {
        title: '权重',
        dataIndex: 'weight',
        render: (text: any, record: any) => {
          return this.renderWeightProgress(record.corr_weight * 100)
        },
      },
    ]
    return <Table columns={columns} dataSource={data} size="small" pagination={false} />
  }

  renderSeriesChart(quotas: any) {
    const {
      taaScore: { scoreListData },
      currentDate,
    } = this.props
    const data = scoreListData.filter((item: any) => currentDate && item.the_date <= currentDate)
    const chartConfig = {
      chart: { height: 350 },
      navigator: {
        enabled: false,
      },
      scrollbar: {
        enabled: false,
      },
      tooltip: {
        pointFormat:
          '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y}</b><br/>',
      },
      yAxis: {
        labels: {
          format: '{value}',
        },
      },
      series: quotas.map((quota: any) => {
        return {
          name: quota.name,
          type: quota.type || 'line',
          data: data.map((item: any) => [
            +moment(item.the_date).startOf('date'),
            item[quota.value],
          ]),
        }
      }),
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  render() {
    const {
      loading,
      taaScore: { dateList },
      currentDate,
      dispatch,
      saaWeightData,
    } = this.props
    return (
      <div>
        <Spin spinning={loading} delay={500}>
          <Card
            title={
              <div>
                {saaWeightData.map((item: any) => (
                  <Button
                    key={item.styleName}
                    onClick={this.handleStyleChange(item.styleName)}
                    size="small"
                    type={this.state.currentStyle === item.styleName ? 'primary' : 'default'}
                  >
                    {item.styleName}
                  </Button>
                ))}
              </div>
            }
            extra={<SelectDate {...{ dispatch, currentDate, dateList }} />}
          >
            <Row gutter={16}>
              <Col lg={6} md={24}>
                {this.renderScoreChart()}
              </Col>
              <Col lg={18} md={24}>
                {this.renderFactorWeight()}
              </Col>
            </Row>
            <div style={{ marginTop: '15px' }} />
            <Row gutter={16}>
              <Col lg={12} md={24}>
                {this.renderSeriesChart([
                  {
                    name: 'TAA评分',
                    value: 'taa_score',
                    type: 'area',
                  },
                  {
                    name: '资产价格',
                    value: 'value_nav',
                  },
                ])}
              </Col>
              <Col lg={12} md={24}>
                {this.renderSeriesChart([
                  {
                    name: 'TAA多空预测',
                    value: 'taa_nav',
                  },
                  {
                    name: '资产价格',
                    value: 'value_nav',
                  },
                ])}
              </Col>
            </Row>
          </Card>
        </Spin>
      </div>
    )
  }
}

export default Taa
