import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import router from 'umi/router'
import { ModelState as KymModelState } from '@/models/taa'
import EditableTable from '@/components/EditableTable'
import { Card, Row, Col, Radio, InputNumber, Divider, Table, Button } from 'antd'
import styles from './style.less'
import StyleTag from './components/StyleTag'
import SelectDate from './components/SelectDate'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  taaScore: {
    scoreListData: any;
    corrListData: any;
    dateList: any;
  };
  currentDate?: string;
  taaWeightStrategy: string;
  taaWeightData?: any;
  saaWeightData?: any;
  totalWeightLimitValue: number;
}

interface ComponentState {}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    loading,
    taa,
  }: {
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
    taa: KymModelState;
  }) => ({
    loading: loading.models.taa,
    taaScore: taa.taaScore,
    currentDate: taa.currentDate,
    taaWeightStrategy: taa.taaWeightStrategy,
    taaWeightData: taa.taaWeightData,
    saaWeightData: taa.saaWeightData,
    totalWeightLimitValue: taa.totalWeightLimitValue,
  }),
)
class AllocateStyle extends Component<ComponentProps, ComponentState> {
  componentDidMount() {
    const { currentDate } = this.props
    if (currentDate) {
      this.loadTaaScoreData(currentDate)
    }
  }

  loadTaaScoreData = (date: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'taa/fetchTaaScoreByDate',
      payload: {
        params: {
          date: date,
        },
      },
    })
  };

  handleTaaStrategyChange = (event: any) => {
    this.updateState({
      taaWeightStrategy: event.target.value,
    })
    this.updateTaaStyleWeights()
  };

  handleTableDataChange = (data: any) => {
    this.updateState({
      taaWeightData: data,
    })
  };

  handleTotalWeightLimitChange = (value: number) => {
    this.updateState({
      totalWeightLimitValue: value,
    })
    this.updateTaaStyleWeights()
  };

  updateState = (newState: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'taa/save',
      payload: newState,
    })
  };

  updateTaaStyleWeights = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'taa/updateTaaStyleWeights',
    })
  };

  render() {
    const radioStyle = {
      display: 'block',
      height: '40px',
      lineHeight: '40px',
    }
    const {
      taaScore: { dateList },
      currentDate,
      dispatch,
      taaWeightStrategy,
      taaWeightData,
      saaWeightData,
      totalWeightLimitValue,
    } = this.props
    const columns = [
      {
        title: '资产类别',
        dataIndex: 'styleName',
        width: '30%',
        align: 'center',
        render: (text: string) => {
          return <StyleTag title={text} />
        },
      },
      {
        title: '对应权重(%)',
        align: 'center',
        editable: true,
        dataIndex: 'weight',
        format: 'sliderWithInput',
      },
    ]
    const saaColumns = [
      {
        title: '资产类别',
        dataIndex: 'styleName',
        width: '30%',
        align: 'center',
        render: (text: string) => {
          return <StyleTag title={text} size="small" />
        },
      },
      {
        title: '对应权重(%)',
        align: 'center',
        dataIndex: 'weight',
        render: (text: number) => {
          return `${text.toFixed(2)}%`
        },
      },
    ]
    const hasWeightData = (data: any) =>
      data.length !== 0 && data.every(item => item.weight !== undefined)
    return (
      <div className={styles.taa}>
        <Row gutter={16}>
          <Col lg={6} md={24}>
            <Card title="TAA应用方法">
              <Radio.Group onChange={this.handleTaaStrategyChange} value={taaWeightStrategy}>
                <Radio style={radioStyle} value="useTaaScore">
                  直接应用TAA结果
                </Radio>
                <Radio style={radioStyle} value="limitedTotalWeight">
                  <span>总资产最大调整权重为</span>
                  <InputNumber
                    min={0}
                    max={100}
                    disabled={taaWeightStrategy !== 'limitedTotalWeight'}
                    onChange={this.handleTotalWeightLimitChange}
                    value={totalWeightLimitValue}
                    size="small"
                    style={{ width: 70, marginLeft: 10 }}
                  />{' '}
                  %
                </Radio>
                <Radio style={radioStyle} value={3} disabled>
                  待定
                </Radio>
              </Radio.Group>
              <Divider orientation="left">当前SAA权重</Divider>
              <Table
                columns={saaColumns}
                dataSource={saaWeightData}
                pagination={false}
                size="small"
              />
              {hasWeightData(taaWeightData) && (
                <div style={{ textAlign: 'center', marginTop: '30px' }}>
                  <Button type="primary" onClick={() => router.push('/fof/taa/allocatefund')}>
                    下一步
                  </Button>
                </div>
              )}
            </Card>
          </Col>
          <Col lg={18} md={24}>
            <Card
              extra={
                <SelectDate
                  {...{ dispatch, currentDate, dateList }}
                  onChange={this.loadTaaScoreData}
                />
              }
            >
              <EditableTable
                columns={columns}
                size="small"
                pagination={false}
                dataSource={taaWeightData}
                onDataChange={this.handleTableDataChange}
                scroll={{
                  y: 400,
                }}
              />
            </Card>
          </Col>
        </Row>
      </div>
    )
  }
}

export default AllocateStyle
