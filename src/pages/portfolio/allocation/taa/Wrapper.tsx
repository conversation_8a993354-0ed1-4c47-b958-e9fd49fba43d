import React from 'react'
import { Dispatch } from 'redux'
import router from 'umi/router'
import { connect } from 'dva'
import { Card, Breadcrumb, Steps } from 'antd'

const { Step } = Steps

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  match: any;
  location: any;
  taaWeightData: any;
  fundWeightData: any;
  taaScore: any;
}

const Wrapper: React.FC<ComponentProps> = props => {
  const {
    children,
    location: { pathname },
    taaWeightData,
    taaScore,
  } = props
  const steps = ['analyze', 'allocatestyle', 'allocatefund']
  const activeKey = pathname.split('/').pop()
  const handleStepChange = (current: number) => router.push(`/fof/taa/${steps[current]}`)
  const hasWeightData = (data: any) =>
    data.length !== 0 && data.every((item: any) => item.weight !== undefined)
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>组合构建</Breadcrumb.Item>
        <Breadcrumb.Item>战术资产配置</Breadcrumb.Item>
      </Breadcrumb>
      <Card style={{ marginBottom: '15px' }}>
        <Steps size="small" current={steps.indexOf(activeKey)} onChange={handleStepChange}>
          <Step title="TAA实时分析" />
          <Step title="TAA权重配置" disabled={!taaScore.scoreListData.length} />
          <Step title="基金选择" disabled={!hasWeightData(taaWeightData)} />
          {/* <Step title="创建组合" disabled={!hasWeightData(fundWeightData)} /> */}
        </Steps>
      </Card>
      <div>{children}</div>
    </div>
  )
}

export default connect(
  ({
    taa,
    loading,
  }: {
    taa: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    loading: loading.models.taa,
    taaWeightData: taa.taaWeightData,
    fundWeightData: taa.fundWeightData,
    taaScore: taa.taaScore,
  }),
)(Wrapper)
