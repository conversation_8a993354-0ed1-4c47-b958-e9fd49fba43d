import React from 'react'
import { Tag } from 'antd'

interface StyleTagProps {
  title: string;
  size?: string;
}
const StyleTag: React.FC<StyleTagProps> = props => {
  const { title, size } = props
  const styleColorMap = {
    股票价值: '#009688',
    股票均衡: '#108ee9',
    股票成长: '#87d068',
    债券久期择时: '#9c27b0',
    债券信用选择: '#ff5722',
    可转债: '#00bcd4',
    现金: '#2db7f5',
  }
  return (
    <Tag size={size} color={styleColorMap[title]}>
      {title}
    </Tag>
  )
}

export default StyleTag
