import React from 'react'
import { Select } from 'antd'

const { Option } = Select

interface SelectDateProps {
  currentDate?: string;
  dateList?: any;
  dispatch: any;
  onChange?: any;
}
const SelectDate: React.FC<SelectDateProps> = props => {
  const { currentDate, dateList, dispatch, onChange } = props
  const handleDateChange = (date: string) => {
    dispatch({
      type: 'taa/save',
      payload: {
        currentDate: date,
      },
    })
    if (onChange) {
      onChange(date)
    }
  }
  return (
    <div>
      <span style={{ marginRight: '10px' }}>时间选择:</span>
      <Select
        showSearch
        style={{ width: 150 }}
        placeholder="请选择时间"
        onChange={handleDateChange}
        value={currentDate}
      >
        {dateList.map((item: string) => (
          <Option value={item}>{item}</Option>
        ))}
      </Select>
    </div>
  )
}

export default SelectDate
