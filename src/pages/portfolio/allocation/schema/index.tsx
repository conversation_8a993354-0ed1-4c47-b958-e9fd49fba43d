import React from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { PlusOutlined } from '@ant-design/icons'
import { Layout, Tabs, Breadcrumb, Empty, <PERSON><PERSON>, DatePicker, Card, Divider, Spin } from 'antd'
import Link from 'umi/link'
import SelectFundModal from '@/components/SelectFundModal'
import SelectFrameworkModal from './components/SelectFrameworkModal'
import ModelParamsPane from './components/ModelParamsPane'
import AllocationResult from './components/AllocationResult'
import AssetPerformanceTable from './components/AssetPerformanceTable'
import StandardTable, { StandardTableColumnProps, TableListItem } from '@/components/StandardTable'
import { Fund } from './data.d'
import { ModelState } from './model'
import allocationImg from '@/assets/fundallocation.png'
import styles from './style.less'
import moment from 'moment'
import { formatMessage } from 'umi-plugin-react/locale'
import getRealPathName from '@/utils/getRealPathname'
import { updateIframeHeight, resetIframeScrolling } from '@/utils/iframe'

const t = (id: string, params?: any) => formatMessage({ id }, params)

const { TabPane } = Tabs
const { Content, Sider } = Layout

interface ComponentProps {
  dispatch: Dispatch<any>;
  match: any;
  allocationSchema: any;
  allocationResult: any;
  portfolio: any;
  loading?: boolean;
  running?: boolean;
}

@connect(
  ({
    schema,
    loading,
  }: {
    schema: ModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    portfolio: schema.portfolio,
    allocationResult: schema.allocationResult,
    allocationSchema: schema.report,
    loading: !!loading.models.schema,
    running: !!loading.effects['schema/runModel'] || !!loading.effects['schema/addFunds'],
  }),
)
export default class Schema extends React.Component<ComponentProps> {
  state = {
    collapsed: false,
    activeKey: '1',
  };

  componentDidMount() {
    const {
      dispatch,
      match: {
        params: { id },
      },
    } = this.props
    if (id === 'new') {
      dispatch({
        type: 'schema/fetchDefaultFramework',
      })
      setTimeout(() => {
        const ids = [
          'Value_benchmark',
          'Growth_benchmark',
          'Blend_benchmark',
          'CredSlect_benchmark',
          'DuraTiming_benchmark',
        ]
        // const ids = ['Value_benchmark', 'Growth_benchmark', 'Blend_benchmark', 'CredSlect_benchmark', 'DuraTiming_benchmark', 'ConvBd_benchmark']
        dispatch({
          type: 'schema/addFunds',
          payload: {
            ids: ids.join(','),
          },
        })
      }, 1000)
    } else {
      dispatch({
        type: 'schema/fetchAllocationSchema',
        payload: { id },
      })
    }
  }

  componentWillReceiveProps(newProps) {
    if (this.props.allocationSchema._id !== newProps.allocationSchema._id) {
      this.state.activeKey = '3'
      setTimeout(() => this.runModel(), 200)
    }
    if (!this.props.allocationResult && newProps.allocationResult) {
      setTimeout(this.dispatchResizeEvent, 1000)
    }
  }

  componentDidUpdate() {
    updateIframeHeight('#allocationSchema')
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'schema/reset',
    })
    resetIframeScrolling()
  }

  onCollapse = collapsed => {
    this.setState({ collapsed }, this.dispatchResizeEvent)
  };

  dispatchResizeEvent = () => {
    try {
      setTimeout(() => {
        const event = new Event('resize')
        window.dispatchEvent(event)
      }, 200)
    } catch (error) {}
  };

  runModel = () => {
    const {
      allocationSchema: { model },
    } = this.props
    this.props.dispatch({
      type: 'schema/runModel',
      payload: {
        ...model,
        name: model.name,
      },
    })
  };

  handleSelectFramework = (list: TableListItem[]) => {
    const { dispatch } = this.props
    if (list.length) {
      dispatch({
        type: 'schema/changeHistoryState',
        payload: list[0],
      })
    }
  };

  handleSelectFund = (fundList: TableListItem[]) => {
    const {
      dispatch,
      allocationSchema: { funds },
    } = this.props
    const fundIds = funds.map(item => item._id)
    const newFunds = fundList.filter(item => !fundIds.includes(item._id))
    if (newFunds.length) {
      const newIds = newFunds.map(item => item._id)
      dispatch({
        type: 'schema/addFunds',
        payload: {
          ids: newIds.join(','),
        },
      })
    }
  };

  handleRemoveFund = (fund: Fund) => () => {
    const { dispatch } = this.props
    dispatch({
      type: 'schema/removeFund',
      payload: {
        id: fund._id,
      },
    })
  };

  handleTabChange = (activeKey: string) => {
    this.setState({ activeKey })
  };

  handleStartDateChange = startDate => {
    const { dispatch } = this.props
    dispatch({
      type: 'schema/updateTimeRange',
      payload: {
        startDate: +startDate,
      },
    })
  };

  handleEndDateChange = endDate => {
    const { dispatch } = this.props
    dispatch({
      type: 'schema/updateTimeRange',
      payload: {
        endDate: +endDate,
      },
    })
  };

  renderSelectFundsPane = () => {
    const {
      allocationSchema: { funds },
    } = this.props
    if (!funds.length) {
      return (
        <Empty
          className={styles.emptyFunds}
          image={allocationImg}
          description={
            <SelectFundModal title="请选择大类资产" defaultTabs={[{ name: '风格指数', value: 'styleBenchmark' }]} dispatch={this.props.dispatch} onChange={this.handleSelectFund}>
              <Button type="primary" icon={<PlusOutlined />} style={{ width: '140px' }}>
                添加资产
              </Button>
            </SelectFundModal>
          }
        />
      )
    }
    const columns: StandardTableColumnProps[] = [
      {
        title: '名称',
        dataIndex: 'name',
        width: '25%',
      },
      {
        title: '成立时间',
        dataIndex: 'startDate',
        format: 'date',
        width: '15%',
      },
      {
        title: '累计收益',
        dataIndex: 'accReturn',
        align: 'right',
        format: 'percentage',
        width: '15%',
      },
      {
        title: '年化收益',
        dataIndex: 'yearReturn',
        align: 'right',
        format: 'percentage',
        width: '15%',
      },
      {
        title: '波动率',
        dataIndex: 'vol',
        format: 'valPercentage',
        align: 'right',
        width: '15%',
      },
      {
        title: '操作',
        align: 'center',
        width: '15%',
        render: (text, record) => (
          <>
            <a onClick={this.handleRemoveFund(record)}>删除</a>
          </>
        ),
      },
    ]
    return (
      <Card
        bordered={false}
        title="已选资产"
        extra={
          <SelectFundModal title="请选择大类资产" defaultTabs={[{ name: '风格指数', value: 'styleBenchmark' }]} dispatch={this.props.dispatch} onChange={this.handleSelectFund}>
            <Button type="primary" icon={<PlusOutlined />} size="small">
              添加资产
            </Button>
          </SelectFundModal>
        }
      >
        <StandardTable
          disableRowSlection
          bordered
          columns={columns}
          data={{
            list: funds,
            pagination: false,
          }}
          scroll={{
            y: 350,
          }}
          size="small"
        />
        <div style={{ textAlign: 'center', marginTop: '30px' }}>
          <Button type="primary" onClick={() => this.handleTabChange('2')}>
            下一步
          </Button>
        </div>
      </Card>
    )
  };

  renderCalculationSettingPane = () => {
    const {
      allocationSchema: { historyState, startDate, endDate, funds },
    } = this.props
    return (
      <>
        <div className={styles.settingPaneHeader}>
          <span>数据区间：</span>
          <DatePicker
            size="small"
            style={{ width: '140px' }}
            placeholder="起始日期"
            value={startDate && moment(new Date(startDate))}
            onChange={this.handleStartDateChange}
          />
          <span style={{ marginRight: '15px' }}></span>
          <DatePicker
            size="small"
            style={{ width: '140px' }}
            placeholder="结束日期"
            value={endDate && moment(new Date(endDate))}
            onChange={this.handleEndDateChange}
          />
          <span style={{ marginRight: '15px' }}></span>
          <SelectFrameworkModal onChange={this.handleSelectFramework}>
            <Button type="primary" size="small">
              选择分析框架
            </Button>
          </SelectFrameworkModal>
        </div>
        <Card
          bordered={false}
          title="资产表现"
          extra={historyState ? `分析框架：${historyState.name}` : ''}
        >
          <AssetPerformanceTable funds={funds} />
          <div style={{ color: '#949FB6' }}>
            <Divider orientation="left">{historyState && historyState.name}</Divider>
            <p>{historyState && historyState.description}</p>
          </div>
          <div style={{ textAlign: 'center', marginTop: '30px' }}>
            <Button type="primary" onClick={() => this.handleTabChange('3')}>
              下一步
            </Button>
          </div>
        </Card>
      </>
    )
  };

  render() {
    const { activeKey, collapsed } = this.state
    const {
      portfolio,
      allocationSchema: { funds, historyState, name },
      dispatch,
      allocationResult,
    } = this.props
    const tab2Disabled = !funds || funds.length === 0
    const tab3Disabled = tab2Disabled || !historyState
    const collapsible = !!allocationResult
    return (
      <>
        <Breadcrumb className={styles.breadcrumb}>
          <Breadcrumb.Item>
            <Link to={getRealPathName('/assetallocation/saa')}>经典大类资产配置方案</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{name || '新建方案'}</Breadcrumb.Item>
        </Breadcrumb>
        <Layout style={{ minHeight: '100vh' }}>
          <Sider
            collapsible={collapsible}
            collapsedWidth={0}
            width={collapsible ? 500 : '100%'}
            collapsed={this.state.collapsed}
            onCollapse={this.onCollapse}
          >
            <Spin spinning={this.props.loading}>
              <Tabs
                activeKey={activeKey}
                className={styles.tabs}
                onChange={this.handleTabChange}
                animated={false}
              >
                <TabPane tab="选择资产" key="1">
                  {this.renderSelectFundsPane()}
                </TabPane>
                <TabPane tab="设置计算参数" key="2" disabled={tab2Disabled}>
                  {this.renderCalculationSettingPane()}
                </TabPane>
                <TabPane tab="选择模型" key="3" disabled={tab3Disabled}>
                  <ModelParamsPane
                    t={t}
                    dispatch={dispatch}
                    allocationSchema={this.props.allocationSchema}
                    allocationResult={allocationResult}
                  />
                </TabPane>
              </Tabs>
            </Spin>
          </Sider>
          <Layout>
            <Content style={{ marginLeft: '16px' }} id="allocationSchema">
              <Spin spinning={this.props.loading}>
                {allocationResult ? (
                  <AllocationResult
                    {...{
                      collapsed,
                      dispatch,
                      t,
                      portfolio,
                      allocation: allocationResult,
                      allocationSchema: this.props.allocationSchema,
                    }}
                  />
                ) : (
                  <Card style={{ height: 500, paddingTop: 150 }}>
                    <Empty image={allocationImg} />
                  </Card>
                )}
              </Spin>
            </Content>
          </Layout>
        </Layout>
      </>
    )
  }
}
