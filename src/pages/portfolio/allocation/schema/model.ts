import { Effect } from 'dva'
import { Reducer } from 'redux'
import { notification } from 'antd'
import round from 'lodash/round'
import cloneDeep from 'lodash/cloneDeep'
import extend from 'lodash/extend'
import * as calculator from '@/utils/calculator'
import guessFrequency from '@/utils/guessFrequency'
import * as math from 'simple-statistics'
import { Framework, Fund, FundNav } from './data.d'
import {
  queryDefaultFrameWork,
  queryNavList,
  queryFrameWorks,
  runAllocationModel,
  runPortfolioBackTest,
  queryAllocationSchema,
  createAllocationSchema,
  updateAllocationSchema,
} from './service'
import {
  intersection,
  stateDistribute,
  groupDateCondition,
  distributeFromState,
} from '@/utils/allocation'

export interface ModelState {
  historyItem?: Framework;
  report?: any;
  frameworkData?: TableListData;
  allocationResult?: any;
  portfolio?: any;
}

export interface ModelType {
  namespace: 'schema';
  state: ModelState;
  effects: {
    fetchDefaultFramework: Effect;
    addFunds: Effect;
    fetchFrameworks: Effect;
    runModel: Effect;
    runBackTest: Effect;
    fetchAllocationSchema: Effect;
    editAllocationSchema: Effect;
    addAllocationSche: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
    updateFunds: Reducer<ModelState>;
    changeHistoryState: Reducer<ModelState>;
    updateHistoryState: Reducer<ModelState>;
    updateTimeRange: Reducer<ModelState>;
  };
}

const annualReturnFallback = 'estimateReturn'

const getTimeRangeFromFunds = (report: any, funds: Fund[]) => {
  const { endToNow } = report
  let { startDate, endDate } = report
  if (!startDate) {
    startDate = Math.max.apply(null, funds.map(item => item.navStartDate))
  }
  if (!endDate) {
    endDate = Math.min.apply(null, funds.map(item => item.navEndDate))
  }
  if (endToNow) {
    endDate = Date.now()
  }
  return { startDate, endDate, endToNow }
}

const dateFilterFn = (startDate: number, endDate: number) => (item: FundNav) =>
  item.date >= startDate && item.date <= endDate

const isSufficientData = (funds: any) => funds.every(item => item.nets && item.nets.length > 2)

const calculateFundQuotas = (fund: Fund) => {
  if (!fund.nets || !fund.nets.length) {
    return fund
  }
  const navList = fund.nets
  const navValues = fund.nets.map(item => item.value)
  const frequency = guessFrequency(navList)
  const retList = calculator.calculateReturns(navList)
  const retValues = retList.map(item => item.value)
  fund.yearReturn = calculator.yearReturn(
    new Date(navList[0].date),
    new Date(navList[navList.length - 1].date),
    navValues,
    annualReturnFallback,
  )
  fund.vol = calculator.vol(retValues, frequency)
  return fund
}

function calculate(item) {
  const endDate = Math.max(...item.funds.map(fund => fund.nets[fund.nets.length - 1].date))
  if (item.historyState && item.historyState.stateType === 'date') {
    item.historyState.states = groupDateCondition(item.historyState.states)
  }
  if (!item.historyState) {
    return {
      ...item,
    }
  }
  const dist =
    item.historyState && item.historyState.stateType === 'date'
      ? distributeFromState(item.historyState.states)
      : stateDistribute(item.quotas, item.historyState.states)
  dist.fix(endDate)
  const netDist = item.funds.map(fund => calculator.distributeNets(fund.intersectionNets, dist))
  const stateNames = netDist[0] ? Object.keys(netDist[0]) : ''
  const correlation = {}
  const total = {}
  const corMean = {}
  // calculate correlation between each two state and total
  for (let row = 0; row < netDist.length; row++) {
    for (let col = row; col < netDist.length; col++) {
      let correlationValue
      for (let stateIdx = stateNames.length - 1; stateIdx >= 0; stateIdx--) {
        if (!correlation[stateNames[stateIdx]]) {
          correlation[stateNames[stateIdx]] = {}
        }
        if (col === row) {
          correlation[stateNames[stateIdx]][`${row}-${col}`] = 1
          continue
        }
        correlationValue = math.sampleCorrelation(
          netDist[row][stateNames[stateIdx]].map(net => net.returnValue),
          netDist[col][stateNames[stateIdx]].map(net => net.returnValue),
        )
        correlation[stateNames[stateIdx]][`${row}-${col}`] = isNaN(correlationValue)
          ? 0
          : Number(correlationValue.toFixed(4))
      }
      if (col === row) {
        total[`${row}-${col}`] = 1
        continue
      }
      correlationValue = math.sampleCorrelation(
        item.funds[row].intersectionNets.map(net => net.returnValue),
        item.funds[col].intersectionNets.map(net => net.returnValue),
      )
      total[`${row}-${col}`] = isNaN(correlationValue) ? 0 : Number(correlationValue.toFixed(4))
    }
  }
  // calculate weighted mean of all state correlation
  Object.keys(correlation).forEach(key => {
    Object.keys(correlation[key]).forEach(corKey => {
      corMean[corKey] = corMean[corKey]
        ? math.sum([
            Number(correlation[key][corKey]) *
              item.historyState.states.find(state => state.name === key).probability,
            corMean[corKey],
          ])
        : Number(
            correlation[key][corKey] *
              item.historyState.states.find(state => state.name === key).probability,
          )
      corMean[corKey] = isNaN(corMean[corKey]) ? 'Error' : Number(corMean[corKey].toFixed(4))
    })
  })
  // funds returns
  const { historyState } = item
  const analysis = item.funds.map(fund => {
    let quota
    if (historyState && historyState.stateType === 'date' && historyState.states.length === 1) {
      quota = {
        [historyState.name]: {
          returns: fund.yearReturn,
          volatility: fund.vol,
        },
      }
    } else {
      quota = calculator.calculateReturnsWithState(fund.nets, dist, annualReturnFallback)
    }
    const result = {
      returns: [],
      volatility: [],
    }
    for (const stateName in quota) {
      const currentState = item.historyState.states.find(state => state.name === stateName)
      if (currentState) {
        result.returns.push(quota[stateName].returns * currentState.probability) // eslint-disable
        result.volatility.push(quota[stateName].volatility * currentState.probability) // eslint-disable
      }
    }
    return {
      ...fund,
      quotasWithState: quota,
      quotasTotal: {
        returns: calculator.yearReturn(
          fund.nets[0].date,
          fund.nets[fund.nets.length - 1].date,
          fund.nets.map(net => net.value),
          annualReturnFallback,
        ),
        volatility: calculator.vol(
          calculator.calculateReturns(fund.nets).map(net => net.value),
          guessFrequency(fund.nets),
        ),
      },
      quotaMean: {
        returns: math.sum(result.returns),
        volatility: math.sum(result.volatility),
      },
    }
  })
  item.calculatedQuotas = {
    corTotal: total,
    corMean,
    correlation,
  }
  item.funds = analysis

  // dejson model config
  if (item.model) {
    item.model.config =
      typeof item.model.config === 'string' ? JSON.parse(item.model.config) : item.model.config
    const result = {}
    const customFunds = item.funds.filter(fund => fund.isCustom)
    let funds = item.funds.map(fund => cloneDeep(fund))
    funds.forEach(fund => {
      fund.quotaMean.returns = +round(fund.quotaMean.returns, 4)
      fund.quotaMean.volatility = +round(fund.quotaMean.volatility, 4)
      fund.upperLimit = 100
      fund.lowerLimit = 0
    })
    funds = funds.concat(customFunds)
    // b-l view matrix validate

    const cov = funds.reduce(out => {
      out.push([])
      return out
    }, [])
    funds.forEach((fst, ii) => {
      funds.forEach((snd, jj) => {
        const cor =
          ii === jj ? 1 : Number(corMean[`${ii}-${jj}`]) || Number(corMean[`${jj}-${ii}`])
        cov[ii][jj] = fst.quotaMean.volatility * snd.quotaMean.volatility * cor
      })
    })
    result.cor = cov
    result.covariance_matrix = cov
    result.lower_limit = item.model.config.lower_limit || funds.map(() => 0)
    result.upper_limit = item.model.config.upper_limit || funds.map(() => 1)
    result.returns = funds.map(fund => Number(fund.quotaMean.returns))
    item.model.config = {
      ...item.model.config,
      ...result,
    }
  }
  return item
}

function buildListPaylod(params: any, response: any) {
  const { list, totalNum } = response
  const { page, per_page } = params
  return {
    list,
    pagination: {
      total: totalNum,
      pageSize: per_page || 10,
      current: page || 1,
    },
  }
}

const initialState = {
  report: {
    funds: [],
    model: {
      name: 'opt',
      config: {
        upper_limit: [],
        lower_limit: [],
      },
    },
  },
  frameworkData: {
    list: [],
    pagination: {},
  },
  allocationResult: null,
}

const SchemaModel: ModelType = {
  namespace: 'schema',

  state: initialState,

  effects: {
    *fetchDefaultFramework(_, { call, put }) {
      const response = yield call(queryDefaultFrameWork)
      yield put({
        type: 'changeHistoryState',
        payload: response,
      })
    },
    *fetchFrameworks({ payload }, { call, put }) {
      const response = yield call(queryFrameWorks, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          frameworkData: data,
        },
      })
    },
    *fetchAllocationSchema({ payload: { id } }, { call, put }) {
      const response = yield call(queryAllocationSchema, id)
      yield put({
        type: 'updateSchema',
        payload: response,
      })
    },
    *addFunds({ payload }, { call, put }) {
      const funds = yield call(queryNavList, payload)
      yield put({
        type: 'updateFunds',
        payload: funds,
      })
    },
    *runModel({ payload }, { call, put }) {
      const response = yield call(runAllocationModel, payload.name, payload.config)
      yield put({
        type: 'handleModelResult',
        payload: {
          model: payload,
          allocationResult: response,
        },
      })
    },
    *runBackTest({ payload }, { call, put }) {
      const response = yield call(runPortfolioBackTest, payload)
      yield put({
        type: 'save',
        payload: {
          portfolio: response,
        },
      })
    },
    *editAllocationSchema({ payload: { id, data } }, { call, put }) {
      const response = yield call(updateAllocationSchema, id, data)
      yield put({
        type: 'updateSchemaInfo',
        payload: response,
      })
      notification.success({ message: '更新成功！' })
    },
    *addAllocationSche({ payload: { data } }, { call, put }) {
      const response = yield call(createAllocationSchema, data)
      yield put({
        type: 'updateSchemaInfo',
        payload: response,
      })
      notification.success({ message: '创建成功！' })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    reset() {
      return {
        ...initialState,
      }
    },
    updateHistoryState(state: ModelState, action) {
      if (action.payload && action.payload.stateType === 'date') {
        return {
          ...state,
          historyItem: {
            ...action.payload,
            states: groupDateCondition(action.payload.states),
          },
        }
      }
      return {
        ...state,
        loading: false,
        historyItem: action.payload,
      }
    },
    changeHistoryState(state: ModelState, action) {
      const { payload } = action
      if (state.report && state.report.funds.length) {
        return {
          ...state,
          allocationResult: null,
          report: calculate({
            ...state.report,
            historyState: payload,
            historyId: payload._id,
            quotas: payload.quotas,
          }),
        }
      }
      return {
        ...state,
        report: {
          ...state.report,
          historyState: payload,
          historyId: payload._id,
          quotas: payload.quotas,
        },
      }
    },
    updateTimeRange(state: ModelState, action) {
      let newReport = { ...(state.report || {}), ...action.payload }
      if (newReport.funds && newReport.funds.length && newReport.startDate && newReport.endDate) {
        newReport.funds = newReport.funds.map((fund: Fund) => {
          fund.nets = fund._nets.filter(dateFilterFn(newReport.startDate, newReport.endDate))
          return fund
        })
        const timeRangeInnersection = intersection(newReport.funds.map((fund: Fund) => fund.nets))
        newReport.funds = newReport.funds.map((fund, idx) => {
          fund.intersectionNets = timeRangeInnersection[idx]
          return calculateFundQuotas(fund)
        })
        if (!isSufficientData(newReport.funds)) {
          notification.warn({ message: '基金数据没有交集或交集不在分析框架内' })
          return {
            ...state,
          }
        }
        newReport = calculate(newReport)
      }
      return {
        ...state,
        report: { ...newReport },
      }
    },
    removeFund(state: ModelState, action: any) {
      const { payload } = action
      if (!state.report.model) {
        return {
          ...state,
          allocationResult: null,
          report: {
            ...state.report,
            assets: state.report.assets.filter((asset: string) => asset !== payload.id),
            funds: state.report.funds.filter((fund: Fund) => fund._id !== payload.id),
          },
        }
      }
      const index = state.report.assets.indexOf(payload.id)
      const newUpperLimitter = [...state.report.model.config.upper_limit]
      const newLowerLimitter = [...state.report.model.config.lower_limit]
      const picks = state.report.model.config.picks
      let newPick = {}
      if (picks && picks.length > 0) {
        newPick = {
          picks: [],
          pick_outperform: [],
          confidence: [],
        }
      }
      newUpperLimitter.splice(index, 1)
      newLowerLimitter.splice(index, 1)
      return {
        ...state,
        allocationResult: null,
        report: calculate({
          ...state.report,
          assets: state.report.assets.filter((asset: string) => asset !== payload.id),
          funds: state.report.funds.filter((fund: Fund) => fund._id !== payload.id),
          model: {
            ...state.report.model,
            config: {
              ...state.report.model.config,
              upper_limit: newUpperLimitter,
              lower_limit: newLowerLimitter,
              ...newPick,
            },
          },
        }),
      }
    },
    updateFunds(state: ModelState, action) {
      let addedFunds = action.payload
      const addedTimeRange = getTimeRangeFromFunds(state.report, addedFunds)
      addedFunds = addedFunds.map((fund: Fund) => {
        fund._nets = [...fund.nets]
        fund.nets = fund.nets.filter(
          dateFilterFn(addedTimeRange.startDate, addedTimeRange.endDate),
        )
        return fund
      })
      const newFunds = [...state.report.funds, ...addedFunds]
      const newInnersection = intersection(newFunds.map(fund => fund.nets))
      if (newInnersection[0].length < 3) {
        notification.warn({
          message: '基金数据没有交集或交集不在分析框架内',
        })
        return {
          ...state,
        }
      }
      if (state.report && state.report.historyState) {
        const newReport = {
          ...state.report,
          ...addedTimeRange,
          assets: [...newFunds.map(fund => fund._id)],
          funds: newFunds.map((fund, index) => {
            fund.intersectionNets = newInnersection[index]
            return calculateFundQuotas(fund)
          }),
        }
        if (state.report.model) {
          const picks = state.report.model && state.report.model.config.picks
          let newPick = {}
          if (picks && picks.length > 0) {
            newPick = {
              picks: [],
              pick_outperform: [],
              confidence: [],
            }
          }
          newReport.model = {
            ...state.report.model,
            config: {
              ...state.report.model.config,
              upper_limit: newFunds.map(
                (fund, index) => state.report.model.config.upper_limit[index] || 1,
              ),
              lower_limit: newFunds.map(
                (fund, index) => state.report.model.config.lower_limit[index] || 0,
              ),
              ...newPick,
            },
          }
        }
        return {
          ...state,
          allocationResult: null,
          report: calculate(newReport),
        }
      }
      return {
        ...state,
        report: {
          ...state.report,
          ...addedTimeRange,
          assets: [...newFunds.map(fund => fund._id)],
          funds: newFunds.map((fund, index) => {
            fund.intersectionNets = newInnersection[index]
            return calculateFundQuotas(fund)
          }),
        },
      }
    },
    updateSchema(state: ModelState, action) {
      const report = action.payload
      const timeRange = getTimeRangeFromFunds(report, report.funds)
      report.funds = report.funds.map(fund => {
        fund._nets = [...fund.nets]
        fund.nets = fund.nets.filter(dateFilterFn(timeRange.startDate, timeRange.endDate))
        return fund
      })
      const innerdefault = intersection(report.funds.map(fund => fund.nets))
      report.funds = report.funds.map((fund, index) => {
        fund.intersectionNets = innerdefault[index]
        return calculateFundQuotas(fund)
      })
      if (!isSufficientData(report.funds)) {
        notification.warn({ message: '基金数据没有交集或交集不在分析框架内' })
        return {
          ...state,
        }
      }
      return {
        ...state,
        loading: false,
        report: {
          ...timeRange,
          ...calculate(report),
        },
      }
    },
    updateModelConfig(state: ModelState, action) {
      const newConfig = extend(state.report.model.config, action.payload)
      return {
        ...state,
        report: {
          ...state.report,
          model: {
            ...state.report.model,
            config: newConfig,
          },
        },
      }
    },
    updateSchemaInfo(state: ModelState, action) {
      const payload = action.payload
      return {
        ...state,
        report: {
          ...state.report,
          _id: payload._id,
          name: payload.name,
        },
      }
    },
    handleModelResult(state: ModelState, action) {
      const { allocationResult, model } = action.payload
      return {
        ...state,
        allocationResult,
        report: {
          ...state.report,
          model: {
            ...state.report.model,
            name: model.name,
          },
        },
      }
    },
  },
}

export default SchemaModel
