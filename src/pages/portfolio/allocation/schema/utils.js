import moment from 'moment'
import findIndex from 'lodash/findIndex'
import cloneDeep from 'lodash/cloneDeep'
import intersectionBy from 'lodash/intersectionBy'

/**
 * BASIC DATA STRUCTOR
 * dsCondition => destructed condtions(from states)
 *   [
 *     [
 *       // condtion composition, contain all conditions for a precisely quota or date
 *       condition1: [...conditions],
 *       condition2: [...conditions],
 *       condition3: [...conditions]
 *     ]
 *   ]
 */

/**
 * change state, add endDate to the last state of origin state , add a new state whit state name and startDate
 * state structor: [[stateName, startDate, endDate]...]
 * @param  {Array} originState
 * @param  {String} newStateName
 * @param  {Object} index
 * @return {void}
 */
function changeState(originState, newState, date) {
  if (originState.length === 0) {
    return originState.push([newState, date])
  }
  if (newState === originState[originState.length - 1][0]) {
    // state not change
    return
  }
  if (originState[originState.length - 1][1] === date) {
    // same date
    originState[originState.length - 1][0] = newState
    return
  }
  originState[originState.length - 1].push(date)
  originState.push([newState, date])
}

function conditionStateMechine(value, condition, restrict) {
  switch (condition) {
    case 'gt':
      return value > restrict
    case 'gte':
      return value >= restrict
    case 'lt':
      return value < restrict
    case 'lte':
      return value <= restrict
    case 'eq':
      return value === restrict
    case 'neq':
      return value !== restrict
    case 'none':
    default:
      return true
  }
}

/**
 * find next data point, and update pointer
 * @param  {Array[number]}   pointer
 * @param  {Array}   original quotas
 * @return {Object}         return the next caculate needed data point
 */
function next(pointer, quotas) {
  const result = quotas.map((quota, index) => ({
    date: quota.data[pointer[index]].date,
    value: quota.data[pointer[index]].value,
    _id: quota._id,
  }))
  const minDate = moment.min(result.map(data => moment(data.date)))
  const index = findIndex(result, data => minDate.isSame(data.date))
  pointer[index]++
  return result[index]
}
/**
 * check should change current state through current data
 * @param  {Object} currentState
 * @param  {Object} data
 * @param  {states} states       all states
 * @return {Object}              new state, if state shouldn`t change, return false
 */
function checkStateChange(currentState, data, states, distribution) {
  // we only deal one data one time, so state change only attribute to data`s relational condition
  // find all state which only have difference with currentSate in relational condition
  // then find next state in relational state accroding to input data value
  const relationalState = states.filter(state =>
    state.conditions
      .filter(condition => condition.quotaId !== data._id)
      .every(condition =>
        currentState.conditions.some(
          currentCondition =>
            currentCondition.name === condition.name &&
            currentCondition.restrict === condition.restrict &&
            currentCondition.quotaId === condition.quotaId,
        ),
      ),
  )
  const newState = relationalState.find(state =>
    state.conditions
      .filter(condition => condition.quotaId === data._id)
      .every(condition => conditionStateMechine(data.value, condition.name, condition.restrict)),
  )
  return newState
}

/**
 * get start date accroding to states, if there are restrict less than 2000,
 * generate start date from them, if none, set start date to 2000-01-01
 * @param  {Object} states
 * @return {moment} moment object
 */
function getStartDate(states) {
  let minDate = states
    .reduce((pre, curr) => {
      return pre.concat(curr.conditions.map(condition => condition.restrict))
    }, [])
    .map(stamp => moment(stamp))
  minDate = moment.min(minDate)
  if (minDate.isBefore('2000-01-01')) {
    return minDate.subtract(1, 'y')
  }
  return moment('2000-01-01')
}

/**
 * compose conditions to states
 * @param  {Array} dsConditions
 * @return {Array} states
 */
export function composeState(dsConditions) {
  return dsConditions.reduce((pre, curr) => {
    const result = []
    // pass all empty array
    if (pre.length === 0) {
      return curr
    }
    if (curr.length === 0) {
      return pre
    }
    pre.forEach(lastConditions => {
      curr.forEach(currCondition => {
        result.push(Object.assign([], lastConditions).concat(currCondition))
      })
    })
    return result
  }, [])
}

const f = (a, b) => [].concat(...a.map(a => b.map(b => [].concat(a, b))))
const cartesian = (a, b, ...c) => (b ? cartesian(f(a, b), ...c) : a)

/**
 * combine all conditions to states, the result states is cartesian product of all condtions
 * @param  {Object} conditions
 * @return {Array}            states
 */
export function combineConditons(conditions) {
  return cartesian(...Object.values(conditions))
}

export function groupDateCondition(states) {
  const newState = cloneDeep(states)
    .sort((pre, curr) => pre.group - curr.group)
    .map(state => {
      const dateConditions = []
      for (let i = 0; i < state.conditions.length; i += 2) {
        dateConditions.push([state.conditions[i], state.conditions[i + 1]])
      }
      return {
        ...state,
        dateConditions: dateConditions,
      }
    })
  return newState
}

export function distributeFromState(dateStates) {
  let distribution = []
  dateStates.forEach(state => {
    state.dateConditions.forEach(condition => {
      distribution.push([state.name, condition[0].restrict, condition[1].restrict])
    })
  })
  Object.defineProperty(distribution, 'fix', {
    configurable: false,
    enumerable: false,
    writable: false,
    value: function(endDate) {
      if (this.length <= 0) {
        return undefined
      } else if (endDate && endDate > this[this.length - 1][1]) {
        this[this.length - 1][2] = endDate
      }
    },
  })
  distribution = distribution.sort((pre, curr) => pre[1] - curr[1])
  return distribution
}

export function isDateValidateError(dateStates) {
  const distribution = distributeFromState(dateStates).sort((pre, curr) => pre[1] - curr[1])
  let error = false
  for (let i = distribution.length - 1; i > 0; i--) {
    if (distribution[i][1] !== distribution[i - 1][2]) {
      error = true
    }
  }
  return error
}

/**
 * destructure of states
 * this function will automaticlly distinct condition composition
 * @param  {Array} states
 * @return {Array}       matched conditions
 */
export function destructure(states) {
  const conditions = {}
  const keys = []
  states[0].conditions.forEach(condition => {
    if (keys.indexOf(condition.quotaId || 'date') < 0) {
      keys.push(condition.quotaId || 'date')
    }
  })
  keys.forEach(key => {
    const midArr = []
    states.forEach(state => {
      const satisfiedCond = state.conditions.filter(
        condition => key === (condition.quotaId || 'date'),
      )
      if (
        midArr.every(
          composition =>
            composition[0].name !== satisfiedCond[0].name ||
            composition[0].restrict !== satisfiedCond[0].restrict ||
            composition[1].name !== satisfiedCond[1].name ||
            composition[1].restrict !== satisfiedCond[1].restrict,
        )
      ) {
        midArr.push(cloneDeep(satisfiedCond))
      }
    })
    conditions[key] = midArr
  })
  return conditions
}

export function dateQuota(quotas, states) {
  // we only generate date quota when there are no other date quotas
  if (quotas.some(quota => quota.isDate)) {
    return quotas
  }
  const customQuotas = [].concat(quotas)
  const hasDateCondition = states.some(state =>
    state.conditions.some(condition => condition.isDate),
  )
  const start =
    customQuotas.length >= 1
      ? moment.max(customQuotas.map(quota => moment(quota.data[0].date)))
      : getStartDate(states)
  const end = moment.min(customQuotas.map(quota => moment(quota.data[quota.data.length - 1].date)))
  const dataQuota = []
  // if states have date condition, generate a date quota between start and end
  if (hasDateCondition) {
    const millisecondOfDay = 1000 * 60 * 60 * 24
    let startStamp = +start
    const endStamp = +end
    while (startStamp <= endStamp) {
      dataQuota.push({
        date: startStamp,
        value: startStamp,
      })
      startStamp += millisecondOfDay
    }
    customQuotas.push({
      name: 'time',
      data: dataQuota,
      isDate: true,
    })
  }
  return customQuotas
}

/**
 * given an indexes data and distrubute conditions, return the indexes distrubution of time
 * @param  {Array} indexes   [{date, value}...]
 * @param  {Array} conditions [{condtion}...]
 * @return {Array}           the distribution of time according to conditions
 */
export function stateDistribute(quotas, states) {
  // assume all date field is sorted, otherwise it will cause problem when draw chart
  // use deep copy of quotas instead of original quotas, because we may add a date quota in it
  const customQuotas = [].concat(quotas)
  const hasDateCondition = states.some(state =>
    state.conditions.some(condition => condition.isDate),
  )
  const start =
    customQuotas.length >= 1
      ? moment.max(customQuotas.map(quota => moment(quota.data[0].date)))
      : getStartDate(states)
  const end = moment.min(customQuotas.map(quota => moment(quota.data[quota.data.length - 1].date)))
  const dataQuota = []
  // if states have date condition, generate a date quota between start and end
  if (hasDateCondition) {
    const millisecondOfDay = 1000 * 60 * 60 * 24
    let startStamp = +start
    const endStamp = +end
    while (startStamp <= endStamp) {
      dataQuota.push({
        date: startStamp,
        value: startStamp,
      })
      startStamp += millisecondOfDay
    }
    customQuotas.push({
      name: 'time',
      data: dataQuota,
    })
  }
  // currentPoint [pointer of each quota]
  const currentPointer = []
  const distribution = []
  customQuotas.forEach(quota => {
    const index = findIndex(quota.data, data => moment(data.date).isSameOrAfter(start))
    currentPointer.push(index)
  })
  // initialize first state
  const before = currentPointer.map((point, index) => {
    if (moment(customQuotas[index].data[point].date).isSame(start)) {
      return point
    }
    return point - 1
  })
  // to get first state, we have to deal with mutipal data to multipal conditions relationship
  let currentState = states.find(state => {
    // check all conditions of this state
    if (
      state.conditions.every(condition =>
        before.every((count, index) => {
          // deal all init data with all relational condition
          // if current condition belongs to other quota, pass the validate
          // we only check validate of relational condition
          if (customQuotas[index]._id === condition.quotaId) {
            return conditionStateMechine(
              customQuotas[index].data[count].value,
              condition.name,
              condition.restrict,
            )
          }
          return true
        }),
      )
    ) {
      // current state satisfied all conditions
      return true
    }
    return false
  })
  // do not contain last points, because
  while (
    currentPointer.some(
      (pointer, index) =>
        customQuotas[index].data[pointer] &&
        moment(customQuotas[index].data[pointer].date).isBefore(end),
    )
  ) {
    const nextData = next(currentPointer, customQuotas)
    const newState = checkStateChange(currentState, nextData, states, distribution)
    changeState(distribution, newState.name, nextData.date)
    currentState = newState || currentState
  }

  Object.defineProperty(distribution, 'fix', {
    configurable: false,
    enumerable: false,
    writable: false,
    value: function(endDate) {
      if (this.length <= 0) {
        return undefined
      } else if (endDate && endDate > this[this.length - 1][1]) {
        this[this.length - 1][2] = endDate
      } else if (this[this.length - 1][1] === +end) {
        this.pop()
      } else {
        this[this.length - 1][2] = +end
      }
    },
  })

  return distribution
}

/**
 * insersection of mutipal arrays of Array
 * for example:
 *
 *   [ {x:1, y:2} {x:2, y:3} {x:3, z:4} ]
 *   [ {x:-1}     {x:2, z:2} {x:3, z:4} ]
 *   [ {x:1}      {x:2, z:1} {x:3, z:4} ]
 *
 *   innerjoin with function item => item.x
 *
 *   [            {x:2, y:3} {x:3, z:4} ]
 * =>[            {x:2, z:2} {x:3, z:4} ]
 *   [            {x:2, z:1} {x:3, z:4} ]
 *
 * @param  {Array} data
 * @param  {Function|String} path
 * @return {Array}
 */
export function intersection(data) {
  const intersections = intersectionBy(...data, 'date').map(net => net.date)
  return data.map(nets =>
    nets
      .map((net, index) => {
        if (index === 0) {
          net.returnValue = 0
        } else {
          net.returnValue = net.value / nets[index - 1].value - 1
        }
        return net
      })
      .filter(net => intersections.indexOf(net.date) >= 0),
  )
}
