export interface FrameworkState {
  name: string;
  probability: number;
  conditions: Array<any>;
}

export interface Framework {
  _id: string;
  authorId: string;
  description: string;
  name: string;
  stateType: string;
  states: FrameworkState[];
}

export interface FundNav {
  date: number;
  value: number;
}

export interface FundReturn {
  date: number;
  value: number;
}

export interface Fund {
  _id: string;
  name: string;
  authorId: string;
  nets: FundNav[];
  _nets: FundNav[];
  returns: FundReturn[];
  navEndDate: number;
  navStartDate: number;
  yearReturn: number;
  vol: number;
}
