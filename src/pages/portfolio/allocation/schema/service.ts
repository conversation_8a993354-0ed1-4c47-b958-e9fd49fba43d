import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryDefaultFrameWork() {
  return request('/api/historystates/default')
}

export async function queryNavList(params: { ids: string }) {
  return request('/api/funds/listNav', { params })
}

export async function queryFrameWorks(params: TableListParams) {
  return request('/api/historystates', {
    params,
  })
}

export async function runAllocationModel(model: string, config: any) {
  return request(`/api/portfolios/${model}`, {
    method: 'POST',
    data: config,
  })
}

export async function runPortfolioBackTest(data: any) {
  return request('/api/funds/portfolio/temp', {
    method: 'POST',
    data,
  })
}

export async function queryAllocationSchema(id: string) {
  return request(`/api/allocations/${id}`)
}

export async function updateAllocationSchema(id: string, data: any) {
  return request(`/api/allocations/${id}`, {
    method: 'PUT',
    data,
  })
}

export async function createAllocationSchema(data: any) {
  return request(`/api/allocations`, {
    method: 'POST',
    data,
  })
}
