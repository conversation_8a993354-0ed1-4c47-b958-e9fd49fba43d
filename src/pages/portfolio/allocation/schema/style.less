.tabs {
  :global(.ant-tabs-bar) {
    text-align: center;
  }
  :global {
    .ant-tabs-nav-wrap {
      justify-content: center;
    }
    .ant-tabs-tab {
      display: block;
    }
  }
  :global(.ant-card) {
    max-width: 1000px;
    margin: 0 auto;
  }
  button {
    width: 100px;
  }
}

.settingPaneHeader {
  text-align: center;
  margin-bottom: 15px;
}

.emptyFunds {
  padding: 100px 0;
  :global(.ant-empty-image) {
    margin-bottom: 25px;
    height: 100% !important;
    img {
      height: 100%;
      width: 200px;
    }
  }
}

.cardItem {
  padding: 7px 15px;
  border-bottom: 1px solid #2f353e;
}

.weightChart {
  text-align: center;
  .text {
    margin-right: 30px;
  }
}

.breadcrumb {
  font-size: 16px;
  color: #d5dfeb;
  font-weight: bold;
  margin-bottom: 15px;
}
