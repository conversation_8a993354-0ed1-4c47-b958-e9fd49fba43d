import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { Modal, Button, Input } from 'antd'
import { connect } from 'dva'
import StandardTable, {
  StandardTableColumnProps,
  TableListItem,
  TableListParams,
} from '@/components/StandardTable'

const { Search } = Input

interface ComponentProps {
  dispatch: Dispatch<any>;
  className?: string;
  frameworkData?: any;
  loading?: boolean;
  onChange: (rows: TableListItem[]) => void;
}
interface ComponentState {
  selectedRows: TableListItem[];
  show: boolean;
  input: string;
}
@connect(
  ({
    schema,
    loading,
  }: {
    schema: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    frameworkData: schema.frameworkData,
    loading: loading.models.schema,
  }),
)
export default class SelectFrameworkModal extends Component<ComponentProps, ComponentState> {
  state = {
    show: false,
    selectedRows: [],
    input: '',
  };

  loadData = (params: Partial<TableListParams> = {}) => {
    const { dispatch } = this.props
    dispatch({
      type: 'schema/fetchFrameworks',
      payload: params,
    })
  };

  handleSelectRows = (rows: TableListItem[]) => {
    this.setState({
      selectedRows: rows,
    })
  };

  handleSeachInput = (value: string) => {
    this.loadData({
      input: value,
      page: 1,
    })
  };

  handleSelect = () => {
    this.setState({ show: false })
    this.props.onChange(this.state.selectedRows)
  };

  close = () => {
    this.setState({ show: false })
  };

  open = () => {
    this.setState({ show: true }, () => {
      this.loadData({ page: 1 })
    })
  };

  render() {
    const columns: StandardTableColumnProps[] = [
      {
        title: '名称',
        dataIndex: 'name',
        width: '10%',
      },
      {
        title: '描述',
        dataIndex: 'description',
        width: '90%',
      },
    ]
    const { frameworkData, loading, children, className } = this.props

    const { selectedRows } = this.state
    return (
      <div style={{ display: 'inline-block' }} className={className}>
        <div onClick={this.open}>{children}</div>
        <Modal
          title={
            <>
              <span>请选择分析框架</span>
              <Search
                style={{ float: 'right', width: '300px', marginRight: '25px' }}
                size="small"
                placeholder="按回车进行搜索"
                onSearch={this.handleSeachInput}
                enterButton
              />
            </>
          }
          visible={this.state.show}
          onCancel={this.close}
          width={1024}
          footer={[
            <Button onClick={this.handleSelect} type="primary">
              确定
            </Button>,
          ]}
        >
          <StandardTable
            selectedRows={selectedRows}
            loading={loading}
            data={frameworkData}
            columns={columns}
            onSelectRow={this.handleSelectRows}
            onChange={this.loadData}
            size="small"
            rowKey="_id"
            rowSelectionType="radio"
            bordered
          />
        </Modal>
      </div>
    )
  }
}
