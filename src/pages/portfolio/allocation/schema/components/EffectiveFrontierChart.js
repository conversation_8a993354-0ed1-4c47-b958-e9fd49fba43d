
import React, { Component } from 'react'
import ChartComp from '@/components/Chart/Chart'
import { Row, Col } from 'antd'
import classnames from 'classnames'
import styles from '../style.less'

const Chart = ChartComp.Highcharts.Chart

global.highChartsInstances = []

function sortQuota({ array, returns, weights }) {
  for (let i = 0; i < array.length - 1; i++) {
    //eslint-disable-line
    const current = array[i]
    const next = array[i + 1]
    const currentR = returns[i]
    const nextR = returns[i + 1]
    const currentW = weights[i]
    const nextW = weights[i + 1]

    if (current > next) {
      array[i + 1] = current
      array[i] = next
      returns[i + 1] = currentR
      returns[i] = nextR
      weights[i + 1] = currentW
      weights[i] = nextW
      i = -1
    }
  }

  return {
    array,
    returns,
    weights,
  }
}

export default class EffectiveFrontierChart extends Component {
  state = {
    currentQuota: {
      name: '波动率',
      value: 'risks',
    },
  };

  componentDidMount() {
    if (this.props.allocation) {
      this.generateAllocation(this.props.allocation)
    }
    const weights = this.props.allocation.weights[0]
    this.props.onWeightsChange(weights)
  }

  componentWillReceiveProps(newProps) {
    if (this.props.allocation !== newProps.allocation && newProps.allocation) {
      this.generateAllocation(newProps.allocation)
    }
  }

  componentWillUnmount() {
    global.highChartsInstances = global.highChartsInstances.filter(
      chart =>
        chart.container !== this.chart.container &&
        chart.container !== this.allocationChart.container,
    )
  }

  round(number, count) {
    return Number(number).toFixed(count || 2)
  }

  generateAllocation(data) {
    const allocation = ['risks', 'var', 'cvar'].reduce((out, key) => {
      if (data[key]) {
        out[key] = sortQuota({
          array: [...data[key]],
          returns: [...data.returns],
          weights: [...data.weights],
        })
      }
      return out
    }, {})
    const {
      currentQuota: { value },
    } = this.state
    this.setState(
      {
        allocation,
        currentPoint: {
          y: data.req && data.req[0] ? data.req[1] : data.returns[data.returns.length - 1],
          x: data.req && data.req[0] ? data.req[2] : data[value][data[value].length - 1],
          index: data.req && data.req[0] ? 0 : data.returns.length - 1,
        },
        clickPoint: {
          y: data.req && data.req[0] ? data.req[1] : data.returns[data.returns.length - 1],
          x: data.req && data.req[0] ? data.req[2] : data[value][data[value].length - 1],
          index: data.req && data.req[0] ? 0 : data.returns.length - 1,
        },
      },
      () => {
        this.renderChart()
        if (data.req) {
          return this.renderAllocationChart(0, false, data.req[0])
        }
        this.renderAllocationChart(data.weights.length - 1)
      },
    )
  }

  generateBaseConfig(type) {
    return {
      chart: {
        type,
        height: 300,
      },
      plotOptions: {
        series: {},
      },
    }
  }

  renderChart() {
    const { funds, modelConfig } = this.props
    const req = this.props.allocation.req
    const retBl = this.props.allocation.ret_bl
    const sigmaBl = this.props.allocation.sigma_bl
    let points
    if (modelConfig.name === 'bl') {
      points = retBl.map((ret, index) => ({
        name: funds[index].name,
        data: [sigmaBl[index], ret],
      }))
    } else {
      points = funds.map((fund, index) => ({
        name: fund.name,
        data: [
          modelConfig.config.volatilities
            ? +modelConfig.config.volatilities[index]
            : +this.round(fund.quotaMean.volatility, 4),
          +modelConfig.config.returns[index] || +this.round(fund.quotaMean.return, 4),
        ],
      }))
    }
    const { currentQuota } = this.state
    const allocation = this.state.allocation[currentQuota.value]
    const { array, returns, weights } = allocation
    const baseConfig = this.generateBaseConfig('line')
    const config = {
      ...baseConfig,
      tooltip: {
        shared: false,
        formatter: function() {
          // eslint-disable-line
          return `
          <b>${this.series.name === '有效边界' ? '点击进行组合回测' : this.series.name}</b><br/>
          <span>\u25CF</span> ${currentQuota.name}: <b>${Number(
            (this.point.x * 100).toFixed(2),
          )}%</b><br/>
          <span style="color:${this.point.color}">\u25CF</span> 收益率: <b>${Number(
            (this.point.y * 100).toFixed(2),
          )}%</b>
          `
        },
      },
      xAxis: {
        labels: {
          formatter: function() {
            // eslint-disable-line
            return `${Number((this.value * 100).toFixed(2))}%`
          },
        },
      },
      yAxis: {
        title: {
          text: '收益率',
        },
        labels: {
          formatter: function() {
            // eslint-disable-line
            return `${Number((this.value * 100).toFixed(2))}%`
          },
        },
      },
      series: [
        {
          name: '有效边界',
          data: array.map((item, index) => [item, returns[index]]),
        },
        ...[...points]
          .sort((pre, curr) => pre[1] - curr[1])
          .map(fund => ({
            name: fund.name,
            data: [fund.data],
            tooltip: {
              shared: false,
              formatter: function() {
                // eslint-disable-line
                return `
            <b>${fund.name}</b><br/>
            <span>\u25CF</span> ${currentQuota.name}: <b>${Number(
                  (this.point.x * 100).toFixed(2),
                )}%</b><br/>
            <span style="color:${this.point.color}">\u25CF</span> 收益率: <b>${Number(
                  (this.point.y * 100).toFixed(2),
                )}%</b>
            `
              },
              valueDecimals: 2,
            },
            lineWidth: 0,
            marker: {
              enabled: true,
              radius: 4,
            },
            states: {
              hover: {
                lineWidthPlus: 0,
              },
            },
            point: {
              events: {
                click: () => {},
                mouseOver: () => {},
              },
            },
          })),
      ],
    }
    const self = this
    config.plotOptions.series.stickyTracking = false
    config.plotOptions.series.cursor = 'pointer'
    config.plotOptions.series.point = {
      events: {
        click: event => {
          this.setState({
            currentPoint: {
              x: event.point.x,
              y: event.point.y,
              index: event.point.index,
            },
            clickPoint: {
              x: event.point.x,
              y: event.point.y,
              index: event.point.index,
            },
          })
          self.renderAllocationChart(event.point.index, false)
          if (this.props.onWeightsChange) {
            this.props.onWeightsChange(weights[event.point.index])
          }
          // self.props.generatePortfolio()
        },
        mouseOver: function mouseOver() {
          const point = this
          self.setState({
            currentPoint: {
              x: point.x,
              y: point.y,
              index: point.index,
            },
          })
          self.renderAllocationChart(point.index, false)
        },
        mouseOut: function mouseOut() {
          const { clickPoint } = self.state
          self.setState({
            currentPoint: clickPoint,
          })
          self.renderAllocationChart(clickPoint.index, false)
        },
      },
    }
    if (req && req[0]) {
      config.series.push({
        name: '预期最优点',
        data: [[req[2], req[1]]],
        lineWidth: 0,
        marker: {
          enabled: true,
          radius: 4,
        },
        tooltip: {
          shared: false,
          valueDecimals: 2,
        },
        states: {
          hover: {
            lineWidthPlus: 0,
          },
        },
        point: {
          events: {
            click: () => {
              this.setState({
                currentPoint: {
                  x: req[2],
                  y: req[1],
                  index: 0,
                },
                clickPoint: {
                  x: req[2],
                  y: req[1],
                  index: 0,
                },
              })
              this.renderAllocationChart(0, false, req[0])
              this.props.generatePortfolio()
            },
            mouseOver: () => {
              this.setState({
                currentPoint: {
                  x: req[2],
                  y: req[1],
                  index: 0,
                },
              })
              this.renderAllocationChart(0, false, req[0])
            },
            mouseOut: () => {
              const { clickPoint } = this.state
              this.setState({
                currentPoint: clickPoint,
              })
              this.renderAllocationChart(0, false, req[0])
            },
          },
        },
      })
    }
    if (this.chart) {
      global.highChartsInstances = global.highChartsInstances.filter(
        chart => chart.container !== this.chart.container,
      )
    }
    this.chart = new Chart('analyse-chart', config)
    if (this.props.onChartChange) {
      this.props.onChartChange(this.chart)
    }
    global.highChartsInstances.push(this.chart)
  }

  renderAllocationChart(index, animation = true, defaultWeights) {
    const { funds } = this.props
    const { currentQuota } = this.state
    const allocation = this.state.allocation[currentQuota.value]
    const { weights } = allocation
    const datas = defaultWeights || weights[index]
    const baseConfig = this.generateBaseConfig('pie')
    const config = {
      ...baseConfig,
      tooltip: {
        pointFormat: '',
      },
      series: [
        {
          name: 'Allocation',
          animation: animation,
          innerSize: '60%',
          data:
            datas &&
            datas.map((data, idx) => ({
              name: `${funds[idx].name}  ${this.round(data * 100, 1)}%`,
              y: data,
              // sliced: !idx,
              // selected: !idx,
            })),
        },
      ],
    }
    if (this.allocationChart) {
      global.highChartsInstances = global.highChartsInstances.filter(
        chart => chart.container !== this.allocationChart.container,
      )
    }
    this.allocationChart = new Chart('allocation-chart', config)
    global.highChartsInstances.push(this.allocationChart)
  }

  render() {
    const { currentPoint, currentQuota } = this.state
    return (
      <Row gutter={16}>
        <Col span={16}>
          <div
            dangerouslySetInnerHTML={{
              __html: '<div id="analyse-chart"></div>',
            }}
          />
        </Col>
        <Col span={8}>
          {currentPoint && (
            <div className={styles.weightChart}>
              <span className={classnames(styles.text)}>
                收益率：{' '}
                <span className={currentPoint.y > 0 ? styles.red : styles.green}>
                  {this.round(currentPoint.y * 100, 2)}%
                </span>
              </span>
              <span className={classnames(styles.text)}>
                {`${currentQuota.name}`}:{' '}
                <span className={currentPoint.x > 0 ? styles.red : styles.green}>
                  {this.round(currentPoint.x * 100, 2)}%
                </span>
              </span>
            </div>
          )}
          <div
            dangerouslySetInnerHTML={{
              __html: '<div id="allocation-chart"></div>',
            }}
          />
        </Col>
      </Row>
    )
  }
}
