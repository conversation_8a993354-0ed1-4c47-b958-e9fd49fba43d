import React from 'react'
import { Button, Modal, Input, message, Form } from 'antd'

const TextArea = Input.TextArea

interface ComponentProps {
  allocationSchema?: any;
  dispatch: any;
  currentModel?: string;
}

class SaveAllocationSchemaModal extends React.Component<ComponentProps> {
  constructor(props: ComponentProps) {
    super(props)
    const { allocationSchema } = props
    this.state = {
      show: false,
      isEdit: !!allocationSchema._id,
      name: allocationSchema.name,
      description: allocationSchema.description,
    }
  }

  modelConfig(currentModel) {
    const { allocationSchema } = this.props
    if (typeof allocationSchema.model.config === 'string') {
      return allocationSchema.model.config
    }
    switch (currentModel) {
      case 'bl':
        return JSON.stringify({
          upper_limit: allocationSchema.model.config.upper_limit,
          lower_limit: allocationSchema.model.config.lower_limit,
          confidence: allocationSchema.model.config.confidence,
          N: allocationSchema.model.config.N,
          pick_outperform: allocationSchema.model.config.pick_outperform,
          picks: allocationSchema.model.config.picks,
        })
      case 'opt':
        return JSON.stringify({
          upper_limit: allocationSchema.model.config.upper_limit,
          lower_limit: allocationSchema.model.config.lower_limit,
          confidence: allocationSchema.model.config.confidence,
          N: allocationSchema.model.config.N,
          expected_return: allocationSchema.model.config.expected_return,
          expected_risk: allocationSchema.model.config.expected_risk,
        })
      case 'parity':
      default:
        return '{}'
    }
  }

  handleNameChange = event => {
    this.setState({ name: event.target.value })
  };

  handleDescriptionChange = event => {
    this.setState({ description: event.target.value })
  };

  close = () => {
    this.setState({ show: false })
  };

  open = () => {
    this.setState({ show: true })
  };

  save = () => {
    const { name, description, isEdit } = this.state
    if (!name) {
      message.error('请填写名字')
      return
    }
    const { allocationSchema, dispatch, currentModel } = this.props
    const data = {
      name,
      description,
      historyId: allocationSchema.historyId,
      model: {
        config: this.modelConfig(currentModel),
        name: currentModel,
      },
      assets: allocationSchema.assets,
      startDate: allocationSchema.startDate,
      endDate: allocationSchema.endDate,
      endToNow: allocationSchema.endToNow,
    }
    const action = isEdit ? 'schema/editAllocationSchema' : 'schema/addAllocationSche'
    dispatch({
      type: action,
      payload: {
        id: allocationSchema._id,
        data,
      },
    })
    this.close()
  };

  render() {
    const {
      // allocationSchema: {
      //   _id,
      //   funds,
      //   historyState,
      //   model,
      // },
      children,
    } = this.props
    const { name, description, isEdit } = this.state

    return (
      <div style={{ display: 'inline-block' }}>
        <div onClick={this.open}>{children}</div>
        <Modal
          title={
            <>
              <span>{isEdit ? '更新配置方案' : '创建配置方案'}</span>
            </>
          }
          visible={this.state.show}
          onCancel={this.close}
          width={700}
          footer={[
            <Button type="primary" onClick={this.save}>
              保存
            </Button>,
          ]}
        >
          <Form layout="vertical">
            <Form.Item label="名称">
              <Input placeholder="名称" value={name} onChange={this.handleNameChange} />
            </Form.Item>
            <Form.Item label="描述">
              <TextArea
                placeholder="描述"
                value={description}
                onChange={this.handleDescriptionChange}
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    )
  }
}

export default SaveAllocationSchemaModal
