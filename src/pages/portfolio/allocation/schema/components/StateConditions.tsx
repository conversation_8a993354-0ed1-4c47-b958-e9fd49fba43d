import React from 'react'
import { Table } from 'antd'
import moment from 'moment'

interface ComponentProps {
  allocationSchema?: any;
}

const conditionDistribute = name => {
  switch (name) {
    case 'gt':
      return '>'
    case 'gte':
      return '>='
    case 'lt':
      return '<'
    case 'lte':
      return '<='
    case 'eq':
      return '='
    case 'neq':
      return '≠'
    case 'none':
    default:
      return false
  }
}

const formatCondition = (name, conditions) => {
  if (conditions[0].name === 'none') {
    return 'else'
  }
  const isDate = conditions[0].isDate
  const format1 = `${name} ${conditionDistribute(conditions[0].name)} ${
    isDate ? moment(conditions[0].restrict).format('YYYY-MM-DD') : conditions[0].restrict
  }`
  const format2 =
    conditions[1].name === 'none'
      ? ''
      : ` and ${name} ${conditionDistribute(conditions[1].name)} ${
          isDate ? moment(conditions[1].restrict).format('YYYY-MM-DD') : conditions[1].restrict
        }`
  return format1 + format2
}

const getQuotaName = (dsCond, quotas) => {
  return dsCond.isDate ? '日期' : quotas.find(quota => quota._id === dsCond.quotaId).name
}

const StateConditions: React.SFC<ComponentProps> = props => {
  const {
    allocationSchema: {
      historyState: { states },
      quotas,
    },
  } = props
  const columns = [
    {
      title: '日期',
      dataIndex: 'date',
    },
    {
      title: '状态名',
      dataIndex: 'name',
      render: (value, row) => {
        const obj = {
          children: value,
          props: {
            rowSpan: row.rowSpan || 0,
          },
        }
        return obj
      },
    },
    {
      title: '概率',
      dataIndex: 'probability',
      render: (value, row) => {
        const obj = {
          children: value,
          props: {
            rowSpan: row.rowSpan || 0,
          },
        }
        return obj
      },
    },
  ]
  const data = states.reduce((out, state) => {
    const conditions = state.dateConditions.map((condition, index) => {
      const ret = {
        date: formatCondition(getQuotaName(condition[0], quotas), condition),
        name: state.name,
        probability: state.probability,
      }
      if (index === 0) {
        ret.rowSpan = state.dateConditions.length
      }
      return ret
    })
    return out.concat(conditions)
  }, [])
  return (
    <Table
      columns={columns}
      dataSource={data}
      pagination={false}
      size="small"
      bordered={false}
      scroll={{ y: 350 }}
    />
  )
}

export default StateConditions
