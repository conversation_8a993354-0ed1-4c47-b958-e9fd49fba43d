import React from 'react'
import StandardTable, { StandardTableColumnProps } from '@/components/StandardTable'

interface AnalyzeCardProps {
  funds?: any;
}

const AssetPerformanceTable: React.SFC<AnalyzeCardProps> = props => {
  const { funds } = props
  const columns: StandardTableColumnProps[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: '25%',
      render: (value, row) => {
        const obj = {
          children: value,
          props: {
            rowSpan: row.rowSpan || 0,
          },
        }
        return obj
      },
    },
    {
      title: '状态',
      dataIndex: 'stateName',
      width: '25%',
    },
    {
      title: '收益率',
      dataIndex: 'yearReturn',
      align: 'right',
      format: 'percentage',
      width: '25%',
    },
    {
      title: '波动率',
      dataIndex: 'vol',
      align: 'right',
      format: 'valPercentage',
      width: '25%',
    },
  ]
  const data = funds.reduce((out, fund) => {
    const { quotasWithState, quotaMean } = fund
    const keys = Object.keys(quotasWithState)
    const results = keys.map((key, index) => {
      const values = quotasWithState[key]
      return {
        name: fund.name,
        stateName: key,
        yearReturn: values.returns,
        vol: values.volatility,
        rowSpan: index === 0 ? keys.length + 1 : 0,
      }
    })
    results.push({
      name: fund.name,
      stateName: '期望',
      yearReturn: quotaMean.returns,
      vol: quotaMean.volatility,
      rowSpan: 0,
    })
    return out.concat(results)
  }, [])
  return (
    <StandardTable
      disableRowSlection
      bordered
      columns={columns}
      data={{
        list: data,
        pagination: false,
      }}
      scroll={{
        y: 350,
      }}
      size="small"
    />
  )
}

export default AssetPerformanceTable
