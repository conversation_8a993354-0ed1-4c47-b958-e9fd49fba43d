
import React, { Component } from 'react'
import classnames from 'classnames'
import Chart from '@/components/Chart/Chart'
import styles from '../style.less'
import isEqual from 'lodash/isEqual'

export default class RiskParityChart extends Component {
  componentDidMount() {
    this.chartRef = React.createRef()
    const { allocation } = this.props
    const weights = allocation.weight || allocation.weights
    this.props.onWeightsChange(weights)
  }

  componentWillReceiveProps(newProps) {
    const { allocation } = this.props
    const weights = allocation.weight || allocation.weights
    const prevWeights = newProps.allocation.weight || newProps.allocation.weights
    if (!isEqual(weights, prevWeights)) {
      this.props.onWeightsChange(weights)
    }
  }

  round(number, count) {
    return Number(number).toFixed(count || 2)
  }

  renderAllocationChart() {
    const { funds, allocation } = this.props
    const weight = allocation.weight || allocation.weights
    const config = {
      chart: {
        type: 'pie',
      },
      tooltip: {
        pointFormat: '',
      },
      series: [
        {
          name: 'Allocation',
          data: weight.map((data, idx) => ({
            name: `${funds[idx].name}  ${this.round(data * 100, 1)}%`,
            y: data,
            // sliced: !idx,
            // selected: !idx,
          })),
        },
      ],
    }
    return <Chart options={config} ref={this.chartRef} />
  }

  render() {
    const { allocation } = this.props
    return (
      <div
        className={classnames(
          'panel panel-default',
          { hidden: !this.props.allocation },
          styles.riskParityChart,
        )}
      >
        <div style={{ textAlign: 'center' }}>
          {allocation.risks &&
            false && [
              <label
                key="risk-label"
                className="col-sm-2 control-label"
                style={{ marginTop: '7px' }}
              >
                波动率
              </label>,
              <div key="risk-value" className="col-sm-10">
                <p
                  className={classnames(
                    'form-control-static',
                    allocation.risks > 0 ? styles.red : styles.green,
                  )}
                >
                  {this.round(allocation.risks * 100, 2)}%
                </p>
              </div>,
            ]}
          {allocation.returns &&
            false && [
              <label
                key="return-label"
                className="col-sm-2 control-label"
                style={{ marginTop: '7px' }}
              >
                收益率
              </label>,
              <div key="return-value" className="col-sm-10">
                <p
                  className={classnames(
                    'form-control-static',
                    allocation.returns > 0 ? styles.red : styles.green,
                  )}
                >
                  {this.round(allocation.returns * 100, 2)}%
                </p>
              </div>,
            ]}
          {this.renderAllocationChart()}
        </div>
      </div>
    )
  }
}
