import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { <PERSON>, Button, Collapse } from 'antd'
import isEqual from 'lodash/isEqual'
import CorrelationTable from '@/components/CorrelationTable'
import EffectiveFrontierChart from './EffectiveFrontierChart'
import AssetPerformanceTable from './AssetPerformanceTable'
import HistoryStateChart from './HistoryStateChart'
import StateConditions from './StateConditions'
import PortfolioBackTest from './PortfolioBackTest'
import RiskParityChart from './RiskParityChart'

interface ComponentProps {
  t: any;
  allocationSchema: any;
  portfolio: any;
  dispatch: Dispatch;
  collapsed?: boolean;
}
interface ComponentState {
  weights?: any;
}

export default class AllocationResult extends Component<ComponentProps, ComponentState> {
  state = {
    portfolio: this.props.portfolio,
  };

  componentDidUpdate(prevProps, prevState) {
    if (!isEqual(prevState.weights, this.state.weights)) {
      this.generatePortfolio()
    }
  }

  componentWillReceiveProps(newProps) {
    const weights = this.getWeightFromAllocation(newProps)
    const prevWeights = this.getWeightFromAllocation(this.props)
    if (!isEqual(weights, prevWeights)) {
      this.onWeightsChange(weights)
    }
  }

  getWeightFromAllocation = props => {
    const {
      allocationSchema: { model },
      allocation,
    } = props
    let weights
    if (model.name === 'parity') {
      weights = allocation.weights
    } else {
      weights = allocation.weights[0]
    }
    return weights
  };

  onWeightsChange = weights => {
    const {
      allocationSchema: { funds },
    } = this.props
    const newWeights = weights ? weights.map(item => Number(item.toFixed(4))) : []
    const restSum = newWeights.slice(1).reduce((out, item) => out + item, 0)
    newWeights[0] = 1 - restSum
    this.setState({
      weights: funds.reduce((out, fund, index) => {
        out[fund._id] = newWeights[index] * 100
        return out
      }, {}),
    })
  };

  generatePortfolio = () => {
    const {
      allocationSchema: { startDate, endDate },
      dispatch,
    } = this.props
    const { weights } = this.state
    const funds = Object.keys(weights).map(id => ({
      _id: id,
      ratio: weights[id],
    }))
    const portfolio = {
      segments: [
        {
          startDate,
          endDate,
          funds,
        },
      ],
    }
    dispatch({
      type: 'schema/runBackTest',
      payload: portfolio,
    })
  };

  getNetsSeries() {
    const {
      allocationSchema: { funds },
      t,
    } = this.props
    return funds.map(fund => ({
      data: fund.nets.map(net => [net.date, net.value]),
      name: fund.name,
      isQuota: fund.fundNature === t('fund.quotas') && !fund.isPortfolio,
    }))
  }

  render() {
    const {
      allocationSchema: { funds, model, calculatedQuotas, historyState, quotas },
      t,
      portfolio,
      collapsed,
      dispatch,
    } = this.props
    const stateNames = Object.keys(calculatedQuotas.correlation)
    return (
      <>
        <Card
          title={
            <>
              <span>配置结果</span>
              {model.name !== 'parity' && (
                <Button
                  ghost
                  style={{ color: '#E85655', borderColor: '#E85655', marginLeft: '15px' }}
                >
                  提示：点击有效边界查看回测结果
                </Button>
              )}
            </>
          }
        >
          {model.name !== 'parity' ? (
            <EffectiveFrontierChart
              {...this.props}
              funds={funds}
              modelConfig={model}
              onWeightsChange={this.onWeightsChange}
              generatePortfolio={this.generatePortfolio}
              collapsed={collapsed}
            />
          ) : (
            <RiskParityChart
              allocation={this.props.allocation}
              funds={funds}
              onWeightsChange={this.onWeightsChange}
              collapsed={collapsed}
            />
          )}
        </Card>
        {portfolio && (
          <PortfolioBackTest
            funds={funds}
            portfolio={portfolio}
            weights={this.state.weights}
            t={t}
            collapsed={collapsed}
            dispatch={dispatch}
          />
        )}
        <Card title="分析框架条件分布">
          <StateConditions allocationSchema={this.props.allocationSchema} />
        </Card>
        <Card title="资产概述">
          <h4>状态分布</h4>
          <HistoryStateChart
            stateType={historyState.stateType}
            series={this.getNetsSeries()}
            states={historyState.states}
            quotas={quotas || []}
            t={t}
            collapsed={collapsed}
          />
          <h4>资产表现</h4>
          <AssetPerformanceTable funds={funds} />
        </Card>
        <Card title="资产相关性">
          <Collapse>
            {stateNames.map(stateName => (
              <Collapse.Panel header={stateName} key={stateName}>
                <CorrelationTable
                  funds={funds}
                  cor={calculatedQuotas.correlation[stateName]}
                  t={t}
                />
              </Collapse.Panel>
            ))}
            <Collapse.Panel header="期望" key="allocationPerspect">
              <CorrelationTable funds={funds} cor={calculatedQuotas.corMean} t={t} />
            </Collapse.Panel>
          </Collapse>
        </Card>
      </>
    )
  }
}
