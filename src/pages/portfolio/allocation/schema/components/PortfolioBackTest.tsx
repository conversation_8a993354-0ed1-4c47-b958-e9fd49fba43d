import React from 'react'
import { <PERSON>, Button, Statistic, Row, Col } from 'antd'
import router from 'umi/router'
import NavPortfolioModal from '@/components/NavPortfolioModal'
import Chart from '@/components/Chart/Chart'
import renderFundQuota from '@/utils/renderFundQuota'
import styles from '../style.less'

interface ComponentProps {
  funds?: any;
  weights?: any;
  portfolio: any;
  t?: any;
  dispatch?: any;
}

const getChartOptions = (portfolio: any, t) => {
  const config = {
    chart: {
      type: 'line',
    },
    yAxis: [
      {
        labels: {
          format: '{value}%',
        },
      },
    ],
    tooltip: {
      pointFormat: '{series.name}: <b>{point.y:.4f}({point.change:.2f}%)</b><br/>',
    },
    series: [
      {
        name: '净值',
        data: portfolio.nets && portfolio.nets.map(item => [item.date, item.value]),
        yAxis: 0,
      },
    ],
    plotOptions: {
      series: {
        compare: 'percent',
      },
    },
  }
  return config
}

const styleBenchmarks = [
  {
    name: '股票价值型',
    value: 'Value_benchmark',
  },
  {
    name: '股票均衡型',
    value: 'Growth_benchmark',
  },
  {
    name: '股票成长型',
    value: 'Blend_benchmark',
  },
  {
    name: '债券久期择时型',
    value: 'CredSlect_benchmark',
  },
  {
    name: '债券信用选择型',
    value: 'DuraTiming_benchmark',
  },
]

const hasTaaButton = (funds: any) => {
  const ids = styleBenchmarks.map(item => item.value)
  return funds.every((fund: any) => ids.includes(fund._qutkeId))
}

const PortfolioBackTest: React.SFC<ComponentProps> = props => {
  const { funds, weights, portfolio, t, dispatch } = props
  const options = getChartOptions(portfolio, t)
  const infoQuotas = [
    {
      name: '最新净值',
      value: 'unitNavEndOfTerm',
      format: 'number',
    },
    {
      name: '年化收益率',
      value: 'yearReturn',
      format: 'percentage',
    },
    {
      name: '波动性',
      value: 'vol',
      format: 'percentage',
    },
    {
      name: '最大回撤',
      value: 'maxDrawdown',
      format: 'percentage',
    },
  ]
  const handleTaaClick = (funds, weights) => () => {
    const idMap = funds.reduce((out, item) => {
      out[item._id] = item._qutkeId
      return out
    }, {})
    const nameMap = styleBenchmarks.reduce((out, item) => {
      out[item.value] = item.name
      return out
    }, {})
    const saaWeightData = Object.keys(weights).map((id, index) => {
      const name = nameMap[idMap[id]]
      return {
        key: `${index}`,
        styleName: name,
        weight: weights[id],
      }
    })
    dispatch({
      type: 'taa/save',
      payload: {
        saaWeightData,
      },
    })
    router.push('/fof/taa/analyze')
  }
  return (
    <Card
      title="回测结果"
      extra={
        <>
          <NavPortfolioModal defaultTabs={[{ name: '风格指数', value: 'styleBenchmark' }]} fundType="saa" funds={funds} defaultWeights={weights} t={t}>
            <Button ghost size="small" type="primary">
              保存为SAA策略
            </Button>
          </NavPortfolioModal>
          {hasTaaButton(funds) && false && (
            <Button
              ghost
              onClick={handleTaaClick(funds, weights)}
              size="small"
              type="danger"
              style={{ marginLeft: 15 }}
            >
              TAA实时分析
            </Button>
          )}
        </>
      }
    >
      <Row gutter={16}>
        <Col span={18}>
          <Chart options={options} constructorType="stockChart" />
        </Col>
        <Col span={6}>
          {infoQuotas.map(quota => (
            <div key={`quota-${quota.value}`} className={styles.cardItem}>
              <Statistic title={quota.name} formatter={() => renderFundQuota(quota, portfolio)} />
            </div>
          ))}
        </Col>
      </Row>
    </Card>
  )
}

export default PortfolioBackTest
