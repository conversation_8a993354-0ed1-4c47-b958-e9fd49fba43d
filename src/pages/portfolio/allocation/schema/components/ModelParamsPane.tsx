import React, { Component } from 'react'
import { Dispatch } from 'redux'
import round from 'lodash/round'
import { DownOutlined } from '@ant-design/icons';
import { Radio, InputNumber, Table, Card, Button, Dropdown, Menu, Divider, message } from 'antd';
import TableForm from '@/components/TableForm'
import styles from '../style.less'
import SaveAllocationSchemaModal from './SaveAllocationSchemaModal'

interface ComponentProps {
  allocationSchema: any;
  dispatch: Dispatch;
  allocationResult?: any;
  t: any;
}
interface ComponentState {
  currentModel?: string;
}
export default class ModelParamsPane extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    this.state = {
      currentModel: props.allocationSchema.model.name,
      viewpoints: [],
    }
  }

  handleRunModel = () => {
    const { currentModel } = this.state
    const {
      allocationSchema: { model },
    } = this.props
    if (currentModel === 'bl' && (!model.config.picks || model.config.picks.length === 0)) {
      return message.error('请先添加观点')
    }
    this.props.dispatch({
      type: 'schema/runModel',
      payload: {
        ...model,
        name: currentModel,
      },
    })
  };

  handleChangeModel = event => {
    const name = event.target.value
    this.setState({
      currentModel: name,
    })
  };

  handleWeightLimitChange = (type, index) => value => {
    const result = {
      [type]: this.props.allocationSchema.model.config[type] || [],
    }
    result[type][index] = value / 100
    this.props.dispatch({
      type: 'schema/updateModelConfig',
      payload: result,
    })
  };

  handleViewpointsChange = viewpoints => {
    const {
      allocationSchema: { funds },
    } = this.props
    const viewpointsResult = viewpoints.filter(item => item.currentFund && item.viewType)
    const picks = viewpointsResult.map(item => {
      const { currentFund, viewFund, viewType } = item
      return funds.map((fund, index) => {
        if (currentFund._id === fund._id) {
          return 1
        } else if (viewType.value === 'willOutperform' && viewFund && viewFund._id === fund._id) {
          return -1
        }
        return 0
      })
    })
    const result = {
      picks,
      pick_outperform: viewpointsResult.map(item => item.value / 100),
      picks_keys: viewpointsResult.map(item => item.key),
    }
    this.props.dispatch({
      type: 'schema/updateModelConfig',
      payload: result,
    })
  };

  transformViewpoints = () => {
    const {
      allocationSchema: {
        funds,
        model: { config },
      },
    } = this.props
    if (
      !config.picks ||
      !config.picks.length ||
      !config.pick_outperform ||
      !config.pick_outperform.length
    ) {
      return []
    }
    return config.picks
      .map((item, index) => {
        const curIndex = item.indexOf(1)
        const relativeIndex = item.indexOf(-1)
        const curFund = funds[curIndex]
        const relativeFund = funds[relativeIndex]
        if (!curFund) {
          return null
        }
        const pickKeys = config.picks_keys || []
        return {
          currentFund: curFund,
          viewFund: relativeFund,
          viewType: {
            value: relativeFund ? 'willOutperform' : 'willReturn',
            name: relativeFund ? '优于' : '收益率为',
          },
          value: round(config.pick_outperform[index] * 100, 2),
          key: pickKeys[index] || `pick_${index}`,
        }
      })
      .filter(Boolean)
  };

  render() {
    const {
      allocationSchema: { funds, model },
    } = this.props
    const { currentModel } = this.state
    const dataSource = funds.map((item, index) => {
      item.index = index
      return item
    })
    const lowers = model.config.lower_limit || []
    const upers = model.config.upper_limit || []
    const columns = [
      {
        title: '名称',
        dataIndex: 'name',
        width: '50%',
      },
      {
        title: 'Min%',
        width: '25%',
        align: 'center',
        render: record => {
          return (
            <InputNumber
              style={{ width: '100%' }}
              size="small"
              value={round(lowers[record.index] * 100, 2) || 0}
              min={0}
              max={100}
              precision={2}
              onChange={this.handleWeightLimitChange('lower_limit', record.index)}
            />
          )
        },
      },
      {
        title: 'Max%',
        width: '25%',
        align: 'center',
        render: record => {
          return (
            <InputNumber
              style={{ width: '100%' }}
              size="small"
              value={round(upers[record.index] * 100, 2) || 100}
              min={0}
              max={100}
              precision={2}
              onChange={this.handleWeightLimitChange('upper_limit', record.index)}
            />
          )
        },
      },
    ].filter((item, index) => {
      return currentModel !== 'parity' || index === 0
    })
    const viewpointColumns = [
      {
        title: '观点',
        dataIndex: 'view',
        key: 'view',
        dataList: funds,
        render: (record, col, onChange) => {
          if (!record.editable) {
            const { currentFund, viewType, viewFund } = record
            return (
              <>
                <span>{currentFund.name}</span>
                <a style={{ margin: '0 3px' }}>{viewType.name}</a>
                {viewType.value === 'willOutperform' && <span>{viewFund.name}</span>}
              </>
            )
          }
          const currentFund = record.currentFund || {}
          const menu = (
            <Menu
              onClick={({ key }) => {
                const fund = funds.find(item => item._id === key)
                onChange(fund, 'currentFund', record.key)
              }}
            >
              {funds.map(fund => (
                <Menu.Item key={fund._id}>{fund.name}</Menu.Item>
              ))}
            </Menu>
          )
          const menuData = [
            {
              name: '收益率为',
              value: 'willReturn',
            },
            {
              name: '优于',
              value: 'willOutperform',
            },
          ]
          const viewType = record.viewType || {}
          const viewMenu = (
            <Menu
              onClick={({ key }) => {
                const view = menuData.find(item => item.value === key)
                onChange(view, 'viewType', record.key)
              }}
            >
              {menuData.map(item => (
                <Menu.Item key={item.value}>{item.name}</Menu.Item>
              ))}
            </Menu>
          )
          const fundMenuData = funds.filter(item => !currentFund || item._id !== currentFund._id)
          const viewFund = record.viewFund || {}
          const fundMenu = (
            <Menu
              onClick={({ key }) => {
                const fund = fundMenuData.find(item => item._id === key)
                onChange(fund, 'viewFund', record.key)
              }}
            >
              {fundMenuData.map(fund => (
                <Menu.Item key={fund._id}>{fund.name}</Menu.Item>
              ))}
            </Menu>
          )
          return <>
            <Dropdown overlay={menu} trigger={['click']}>
              <a>
                {currentFund.name || '选择资产'} <DownOutlined />
              </a>
            </Dropdown>
            <Divider type="vertical" />
            <Dropdown overlay={viewMenu} trigger={['click']}>
              <a>
                {viewType.name || '选择观点'} <DownOutlined />
              </a>
            </Dropdown>
            {viewType.value === 'willOutperform' && <Divider type="vertical" />}
            {viewType.value === 'willOutperform' && (
              <Dropdown overlay={fundMenu} trigger={['click']}>
                <a>
                  {viewFund.name || '选择资产'} <DownOutlined />
                </a>
              </Dropdown>
            )}
          </>;
        },
      },
      {
        title: '观点值(%)',
        dataIndex: 'value',
        key: 'value',
        width: 80,
      },
    ]
    return (
      <>
        <div className={styles.settingPaneHeader}>
          <Radio.Group size="small" value={currentModel} onChange={this.handleChangeModel}>
            <Radio.Button value="opt">Mean Variance</Radio.Button>
            <Radio.Button value="parity">Risk Parity</Radio.Button>
            <Radio.Button value="bl">Black Litterman</Radio.Button>
          </Radio.Group>
        </div>
        <Card
          bordered={false}
          title="分配限额"
          // extra={<a>编辑</a>}
        >
          <Table
            bordered
            columns={columns}
            dataSource={dataSource}
            scroll={{
              y: 350,
            }}
            size="small"
            pagination={false}
          />
        </Card>
        {currentModel === 'bl' && (
          <Card bordered={false} title="观点调整">
            <TableForm
              iconAction
              isEdditing
              // key={String(model.config.picks_keys)}
              tableColumns={viewpointColumns}
              validateRow={item => {
                const { currentFund, viewType, viewFund, value } = item
                if (!currentFund) {
                  return '请选择资产'
                } else if (!viewType) {
                  return '请选择观点'
                } else if (viewType.value === 'willOutperform' && !viewFund) {
                  return '请选择比较资产'
                } else if (value === undefined) {
                  return '请输入观点值'
                }
                const valid = !Number.isNaN(Number(value))
                if (!valid) {
                  return '观点值不正确'
                }
                return null
              }}
              t={this.props.t}
              size="small"
              value={this.transformViewpoints()}
              onChange={this.handleViewpointsChange}
            />
          </Card>
        )}
        <div style={{ textAlign: 'center', marginTop: '30px' }}>
          <Button onClick={this.handleRunModel} type="primary" style={{ marginRight: '30px' }}>
            运行
          </Button>
          <SaveAllocationSchemaModal
            currentModel={currentModel}
            dispatch={this.props.dispatch}
            allocationSchema={this.props.allocationSchema}
          >
            <Button type="primary" disabled={!this.props.allocationResult}>
              保存方案
            </Button>
          </SaveAllocationSchemaModal>
        </div>
      </>
    )
  }
}
