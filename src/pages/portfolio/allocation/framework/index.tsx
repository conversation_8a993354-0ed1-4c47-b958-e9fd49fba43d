import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { Button, Breadcrumb, Card, message } from 'antd'
import Link from 'umi/link'
import EditState from './components/EditState'
import SaveFrameworkModal from './components/SaveFrameworkModal'
import { connect } from 'dva'
import cloneDeep from 'lodash/cloneDeep'
import groupBy from 'lodash/groupBy'
import map from 'lodash/map'
import styles from './style.less'
import { formatMessage } from 'umi-plugin-react/locale'
import { ModelState } from './model'
import moment from 'moment'
import getRealPathName from '@/utils/getRealPathname'
import HistoryStateChart from '../schema/components/HistoryStateChart'

const t = (id: string, params?: any) => formatMessage({ id }, params)

interface ComponentProps {
  dispatch: Dispatch<any>;
  match: any;
  location?: any;
  framework?: any;
}

@connect(
  ({
    loading,
    framework,
  }: {
    framework: ModelState;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    framework: framework.framework,
    loading: loading.models.framework,
  }),
)
export default class FrameWork extends Component<ComponentProps> {
  state = {
    states: [
      {
        isTemp: true,
        conditions: [],
      },
    ],
  };

  componentDidMount() {
    const {
      dispatch,
      match: {
        params: { id },
      },
    } = this.props
    if (id !== 'new') {
      dispatch({
        type: 'framework/fetchFramework',
        payload: { id },
      })
    }
  }

  componentWillReceiveProps(newProps) {
    if (!this.props.framework && newProps.framework) {
      this.resetStates(newProps.framework)
    }
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'framework/save',
      payload: {
        framework: null,
      },
    })
  }

  resetStates = framework => {
    const tempStates = cloneDeep(framework.states)
    const states = tempStates.map(item => {
      const conditions = map(groupBy(item.conditions, 'group'), values => {
        const sortedValues = values.sort((fst, snd) => fst.restrict - snd.restrict)
        return {
          startDate: moment(new Date(sortedValues[0].restrict)),
          endDate: moment(new Date(sortedValues[1].restrict)),
        }
      })
      return {
        ...item,
        probability: `${item.probability}`,
        conditions,
      }
    })
    this.setState({ states })
  };

  dateChange = format => newDate => {
    if (newDate === 'Invalid date') {
      return
    }
    this.setState({
      [format]: +newDate,
    })
  };

  addState = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'framework/addState',
      payload: {
        dateConditions: [
          [
            {
              name: 'gt',
              restrict: undefined,
            },
            {
              name: 'lte',
              restrict: undefined,
            },
          ],
        ],
        name: '',
        probability: 0,
      },
    })
  };

  handleRemoveState = index => () => {
    const states = this.state.states.filter((item, idx) => idx !== index)
    this.setState({
      states,
    })
  };

  handleUpdateState = index => data => {
    const states = this.state.states.map((item, idx) => {
      if (index !== idx) {
        return item
      }
      return {
        ...data,
        isTemp: false,
      }
    })
    this.setState({
      states,
    })
  };

  handleAddState = () => {
    const newState = {
      isTemp: true,
      conditions: [],
    }
    this.setState({
      states: [...this.state.states, newState],
    })
  };

  changeStateName = (index, refinement) => {
    const value = refinement.name
    const nameError = Object.assign([], this.state.nameError)
    if (this.props.item.states.some((state, count) => state.name === value && index !== count)) {
      message.warning(t('allocation.changeStateTip'))
      nameError[index] = true
      this.setState({
        nameError,
      })
      return
    }
    nameError[index] = false
    this.setState({
      nameError,
    })
    this.props.changeState(index, refinement)
  };

  render() {
    const { framework, dispatch } = this.props
    const name = framework && framework.name
    const { states } = this.state
    const canSave = states.filter(item => !item.isTemp).length !== 0
    return (
      <div
        className="ant-pro-grid-content wide"
        ref={node => {
          this.container = node
        }}
      >
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>
            <Link to={getRealPathName('/portfolio/allocation/saa')}>战略资产配置</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={getRealPathName('/portfolio/allocation/saa/framework')}>分析框架</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{name || '新建分析框架'}</Breadcrumb.Item>
        </Breadcrumb>
        {framework && (
          <Card title="状态分布">
            <HistoryStateChart
              stateType={framework.stateType}
              series={[]}
              states={framework.states}
              quotas={[]}
              t={t}
              collapsed={false}
            />
          </Card>
        )}
        {states.map((state, index) => (
          <>
            <EditState
              t={t}
              stateData={state}
              framework={framework}
              onSave={this.handleUpdateState(index)}
              onDelete={this.handleRemoveState(index)}
            />
            <div style={{ marginBottom: '15px' }} />
          </>
        ))}
        {(!framework || framework.visibility !== 'public') && (
          <div className={styles.fromStateFooter}>
            <Button onClick={this.handleAddState} type="primary" icon={<PlusOutlined />}>
              新增状态
            </Button>
            {!!framework && (
              <SaveFrameworkModal dispatch={dispatch} framework={framework} states={states}>
                <Button
                  disabled={!canSave}
                  style={{ marginLeft: '30px' }}
                  onClick={this.addState}
                  type="primary"
                  icon={<SaveOutlined />}
                >
                  保存框架
                </Button>
              </SaveFrameworkModal>
            )}
            {!framework && (
              <SaveFrameworkModal {...{ dispatch, states }} framework={{}}>
                <Button
                  disabled={!canSave}
                  style={{ marginLeft: '30px' }}
                  onClick={this.addState}
                  type="primary"
                  icon={<SaveOutlined />}
                >
                  保存框架
                </Button>
              </SaveFrameworkModal>
            )}
          </div>
        )}
      </div>
    );
  }
}
