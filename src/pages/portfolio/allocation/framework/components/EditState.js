import React from 'react'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Row, Col, message, Card, Divider } from 'antd';
import TableForm from '@/components/TableForm'

message.config({
  top: 150,
})

class EditState extends React.Component {
  state = {
    isEdditing: this.props.stateData.isTemp,
  };

  componentDidMount() {
    const { stateData, form } = this.props
    if (stateData) {
      form.setFieldsValue({
        ...stateData,
      })
    }
  }

  componentWillReceiveProps(newProps) {
    if (this.props.stateData.name !== newProps.stateData.name) {
      this.initState(newProps.stateData)
    }
  }

  initState(stateData) {
    this.state.isEdditing = stateData.isTemp
    this.props.form.setFieldsValue({
      ...stateData,
      conditions: stateData.conditions.map((item, index) => {
        return {
          ...item,
          key: `cod_${index}`,
        }
      }),
    })
  }

  startEdit = () => {
    this.setState({ isEdditing: !this.state.isEdditing })
  };

  save = () => {
    this.props.form.validateFieldsAndScroll(error => {
      if (error) {
        message.error('请填写完整信息')
        return
      }
      const data = this.props.form.getFieldsValue()
      this.setState({
        isEdditing: false,
      })
      data.probability = Number(data.probability)
      this.props.onSave(data)
    })
  };

  render() {
    const tableColumns = [
      {
        title: '开始时间',
        dataIndex: 'startDate',
        width: '40%',
        key: 'startDate',
        format: 'DatePicker',
      },
      {
        title: '结束时间',
        dataIndex: 'endDate',
        width: '40%',
        key: 'endDate',
        format: 'DatePicker',
      },
    ]
    const { stateData, framework } = this.props
    const { getFieldDecorator } = this.props.form
    const { isEdditing } = this.state
    return (
      <Card
        title={stateData.name || '新状态'}
        extra={
          !framework || framework.visibility !== 'public' ? (
            <>
              {isEdditing && <a onClick={this.save}>保存</a>}
              {!isEdditing && <a onClick={this.startEdit}>编辑</a>}
              <Divider type="vertical" />
              <a onClick={this.props.onDelete}>删除</a>
            </>
          ) : (
            false
          )
        }
      >
        <Form layout="vertical" hideRequiredMark onSubmit={this.submit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="状态名">
                {getFieldDecorator('name', {
                  rules: [{ required: true, message: '请输入状态名', whitespace: true }],
                })(<Input placeholder="状态名" disabled={!isEdditing} />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="概率">
                {getFieldDecorator('probability', {
                  rules: [
                    {
                      required: true,
                      message: '请输入概率',
                      whitespace: true,
                    },
                    {
                      validator: (rule, value, callback) => {
                        const number = Number(value)
                        const valid = !Number.isNaN(Number(value)) && number >= 0 && number <= 1
                        if (valid) {
                          callback()
                        } else {
                          callback(true)
                        }
                      },
                      message: '请输入0-1的数字',
                    },
                  ],
                })(<Input placeholder="概率" disabled={!isEdditing} />)}
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            {getFieldDecorator('conditions', {
              initialValue: [],
              rules: [{ required: true, message: '请设置状态条件' }],
            })(
              <TableForm
                isEdditing={isEdditing}
                tableColumns={tableColumns}
                validateRow={item => {
                  if (!item.startDate || !item.endDate) {
                    return '请选择日期'
                  }
                  return null
                }}
                t={this.props.t}
                size="small"
              />,
            )}
          </Form.Item>
        </Form>
      </Card>
    )
  }
}

const EditStateForm = Form.create()(EditState)

export default EditStateForm
