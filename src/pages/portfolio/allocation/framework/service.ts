import request from '@/utils/request'

export async function queryFrameWork(id: string) {
  return request(`/api/historystates/${id}`)
}

export async function deleteFramework(id: string) {
  return request(`/api/historystates/${id}`, {
    method: 'delete',
  })
}

export async function updateFramework(id: string, data: any) {
  return request(`/api/historystates/${id}`, {
    method: 'put',
    data,
  })
}

export async function createFramework(data: any) {
  return request(`/api/historystates`, {
    method: 'post',
    data,
  })
}
