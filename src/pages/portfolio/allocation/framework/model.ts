import { Effect } from 'dva'
import { Reducer } from 'redux'
import moment from 'moment'
import { notification } from 'antd'
import { queryFrameWork, updateFramework, createFramework } from './service'

export interface ModelState {
  framework?: any;
}

export interface ModelType {
  namespace: 'framework';
  state: ModelState;
  effects: {
    fetchFramework: Effect;
    addFramework: Effect;
    editFramework: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
  };
}

function newDateQuota(startDate: any, endDate: any) {
  let start = +moment(startDate || '2010-01-01')
  const end = endDate ? +moment(endDate) : +moment()
  const day = 1000 * 60 * 60 * 24
  const result = []
  while (start < end) {
    result.push({
      date: start,
      value: start,
    })
    start += day
  }
  return {
    isDate: true,
    name: 'time',
    data: result,
  }
}

const FrameworkModel: ModelType = {
  namespace: 'framework',

  state: {},

  effects: {
    *fetchFramework({ payload: { id } }, { call, put }) {
      const response = yield call(queryFrameWork, id)
      yield put({
        type: 'save',
        payload: {
          framework: response,
        },
      })
    },
    *addFramework({ payload: { data } }, { call, put }) {
      const response = yield call(createFramework, data)
      yield put({
        type: 'save',
        payload: {
          framework: response,
        },
      })
      notification.success({ message: '创建成功！' })
    },
    *editFramework({ payload: { id, data } }, { call, put }) {
      const response = yield call(updateFramework, id, data)
      yield put({
        type: 'save',
        payload: {
          framework: response,
        },
      })
      notification.success({ message: '更新成功！' })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    addState(state, action) {
      return {
        ...state,
        historyItem: {
          ...state.historyItem,
          quotas: state.historyItem ? state.historyItem.quotas : [newDateQuota()],
          states: Object.assign([], state.historyItem ? state.historyItem.states : null).concat(
            action.payload,
          ),
        },
      }
    },
  },
}

export default FrameworkModel
