import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'dva'
import { Link } from 'umi'
import classnames from 'classnames'
import Question from '@/components/Question'
import SurveyTab from '@/components/SurveyTab'
import GetPdfAndPng from '@/components/GetPdfAndPng'
import calculateRatings from '../../utils/calculateRatings'
import { parse } from 'qs'
import { getToken } from '@/utils/utils'
import styles from './style.less'
import { AnswerModelState } from './model'
import { CurrentUser, UserModelState } from '@/models/user'
import { Button, Breadcrumb } from 'antd'
import Chart from '@/components/Chart/Chart'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  match: any;
  currentUser?: CurrentUser;
  survey: any,
}

interface ComponentState {
  ratings: any;
  rating: any;
  rated: Boolean;
  tabs: any;
  currentTab: String
}

const RATING_QUOTAS = ['C1', 'C2', 'P1', 'P2', 'P3', 'P4']
const RATING_QUOTAS_MAP = {
  C1: '公司情况',
  C2: '客户服务',
  P1: '人员情况',
  P2: '投资理念',
  P3: '投资流程',
  P4: '投资业绩'
}
@connect(
  ({
    user,
    answer,
    loading,
  }: {
    user: UserModelState,
    answer: AnswerModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }
  ) => {
    return ({
      survey: answer.survey,
      currentUser: user.currentUser,
      loading: loading.effects['answer/fetchAnswer'],
    })
  }
)
export default class Answer extends Component<ComponentProps, ComponentState> {
  static propTypes = {
    survey: PropTypes.object,
    match: PropTypes.object.isRequired,
    rating: PropTypes.func.isRequired,
    currentUser: PropTypes.object.isRequired,
    location: PropTypes.object.isRequired,
  }

  state = this.buildState(this.props)

  async componentDidMount() {
    const { location } = this.props
    const id = location && location.pathname.split('/').slice(-1)[0]
    if (id) {
      await this.loadSurvey(id, {})
    }

  }

  loadSurvey = (id: any, params: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'answer/fetchSurvey',
      payload: { id, params },
    })
  }

  rating = (id: string, data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'answer/postRating',
      payload: { id, data },
    })
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.survey != this.props.survey) {
      this.setState(this.buildState(nextProps))
      this.setState({ currentTab: nextProps.survey.tabs[0].id })
    }
  }
  buildState(props) {
    const {
      survey,
      currentUser
    } = props
    const ret = { ratings: {}, rating: {} }
    const ratings = survey && survey.ratings
    const tabs = survey && survey.tabs
    if (ratings) {
      ratings.some(item => {
        if (item.userId === currentUser._id) {
          ret.rating = item.rating
          ret.rated = true
          return true
        }
        return false
      })
    }
    ret.tabs = tabs || [{}]
    ret.currentTab = tabs ? tabs[0].id : ''
    ret.show = survey && survey.peerEvaluation
    return ret
  }

  rate = (quota: any) => (score: any) => () => {
    this.setState({
      rating: {
        ...this.state.rating,
        [quota]: score
      }
    })
  }

  switchTab = tab => {
    this.setState({ currentTab: tab })
  }

  submit = () => {

    const {
      match: {
        params: { id }
      },
      currentUser
    } = this.props
    const ratings = this.props.survey.ratings.filter(
      item => item.userId !== currentUser._id
    )
    ratings.push({
      userId: currentUser._id,
      rating: this.state.rating
    })
    const rating = calculateRatings(ratings.map(item => item.rating)).reduce(
      (out, item, index) => {
        out[RATING_QUOTAS[index]] = item
        return out
      },
      {}
    )
    this.rating(id, { currentRating: this.state.rating, rating })
    this.setState(
      {
        rated: true
      })
  }

  renderChartRate = () => {
    const { rating } = this.state
    const { survey: { ratings } } = this.props
    const currentRatings = RATING_QUOTAS.map(quota => rating[quota] || 0)
    const ratingResult = (ratings || []).map(item => item.rating)
    const series = [
      {
        name: '我的评价',
        data: currentRatings
      },
      {
        name: '总体评价',
        data: calculateRatings(ratingResult)
      }
    ]
    const chartConfig = {
      chart: {
        polar: true,
        type: 'line',
        height: 250,
        width: 250,
        backgroundColor: 'transparent'
      },
      title: {
        text: ''
      },
      pane: {
        size: '80%'
      },
      legend: {
        backgroundColor: 'transparent'
      },
      xAxis: {
        categories: RATING_QUOTAS.map(key => RATING_QUOTAS_MAP[key]).map(item => item.replace(/\s/g, '')),
        tickmarkPlacement: 'on',
        lineWidth: 0
      },
      yAxis: {
        gridLineInterpolation: 'polygon',
        lineWidth: 0,
        min: 0
      },
      tooltip: {
        shared: true,
        pointFormat:
          '<span style="color:{series.color}">{series.name}: <b>{point.y:,.2f}</b><br/>'
      },
      credits: {
        enabled: false
      },
      exporting: {
        enabled: false
      },
      series: series.map(serie => ({ ...serie, pointPlacement: 'on' }))
    }
    return <Chart options={chartConfig} />
  }

  render() {
    if (!this.props.survey) {
      return false
    }
    const pdfFormat =
      (!!this.props.location.search &&
        parse(this.props.location.search.substr(1)).pdfFormat) ||
      (this.props.location.query && this.props.location.query.pdfFormat)
    const isNotA4 = pdfFormat !== 'A4'
    const {
      match: {
        params: { id }
      },
    } = this.props
    const token = getToken()
    const { tabs, currentTab } = this.state
    const { survey } = this.props
    const {
      survey: { title, description, peerEvaluation }
    } = this.props
    const answers = this.props.survey.answers || {}
    const remarks = this.props.survey.remarks || {}
    const otherAnswers = this.props.survey.otherAnswers || {}
    const answerTitle = `${survey.answerAuthor && survey.answerAuthor.nickname}（${survey.answerAuthor && survey.answerAuthor.company
      }）`
    const questions = (this.props.survey.questions || []).filter(
      question => !isNotA4 || question.tab === currentTab
    )
    questions
      .filter(question => question.type === 'sort')
      .forEach(question => {
        if (answers[question._id]) {
          const indexMap = answers[question._id].reduce((map, item) => {
            map[item.question] = item.answer
            return map
          }, {})
          question.options = question.options.sort(
            (fst, snd) => indexMap[fst.value] - indexMap[snd.value]
          )
        }
      })
    const { rating } = this.state
    const indexMap = (questions || [])
      .filter(question => question.type !== 'paragraph')
      .reduce((out, question, index) => {
        out[question._id] = index + 1
        return out
      }, {})
    return (
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Breadcrumb>
            <Breadcrumb.Item><Link style={{ color: 'orange' }} to='/duediligence/history'>问卷列表</Link></Breadcrumb.Item>
            <Breadcrumb.Item>
              <Link style={{ color: 'orange' }} to={`/duediligence/surveyDetail/${survey._id}`}>{title}</Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{answerTitle}</Breadcrumb.Item>
          </Breadcrumb>
          {isNotA4 && (
            <div style={{ display: 'flex' }}>
              <a
                target="_blank"
                href={`/api/admin/answers/${id}/excel?token=${token.slice(7)}`}
              >
                <Button style={{ marginRight: 10 }}><i className="fa fa-file-excel-o" /> 导出Excel</Button>
              </a>
              <GetPdfAndPng
                id="surveyPreview-pdfAndPng"
              />
            </div>
          )}
        </div>
        <div className={styles.surveyWapper}>
          {isNotA4 && tabs.length > 1 && (
            <SurveyTab
              switchSurveyTab={this.switchTab}
              {...{ tabs, currentTab }}
            />
          )}
          <div className={classnames(styles.surveyMain)}>
            {currentTab === tabs[0].id && (
              <div className={styles.surveyTitle}>
                <div className="inner">
                  <h3>{`${title} - ${answerTitle}`}</h3>
                </div>
              </div>
            )}
            {currentTab === tabs[0].id && (
              <div className={styles.surveyDescription}>
                <div className="inner">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: description
                    }}
                  />
                </div>
              </div>
            )}
            {peerEvaluation && currentTab === tabs[0].id && (
              <div className={classnames('inner', styles.ratingWapper)}>
                <div className={styles.ratingTitle}>
                  发表评价
                </div>
                <div className="row">
                  <div className="col-md-6">
                    {!this.state.rated && (
                      <div className={styles.tipMessage}>
                        <i className="fa fa-eye-slash" />{' '}
                        发表评价才能查看其它人的评价
                      </div>
                    )}
                    {
                      this.state.show &&
                      this.renderChartRate()
                    }
                  </div>
                  <div className="col-md-6">
                    <ul className="list-group">
                      {RATING_QUOTAS.map(quota => (
                        <li className="list-group-item" key={quota}>
                          <span className={styles.rateItemTitle}>
                            {RATING_QUOTAS_MAP[quota]}
                          </span>
                          <span className={styles.rating}>
                            {[10, 8, 6, 4, 2].map(score => (
                              <span
                                onClick={this.rate(quota)(score)}
                                key={score}
                                className={classnames('fa fa-star', {
                                  [styles.active]: rating[quota] >= score
                                })}
                              />
                            ))}
                          </span>
                        </li>
                      ))}
                    </ul>
                    <button
                      className="btn btn-primary"
                      disabled={Object.keys(rating).length !== 6}
                      onClick={this.submit}
                    >
                      提交评价
                    </button>
                  </div>
                </div>
              </div>
            )}
            <div className={styles.surveyContainer}>
              {questions.map((question, index) => {
                return (
                  <div key={'show' + question._id + index}>
                    <Question
                      readOnly
                      question={question}
                      index={indexMap[question._id]}
                      answer={answers[question._id] || ''}
                      otherAnswer={otherAnswers[question._id] || ''}
                      answerRemark={remarks[question._id] || ''}
                    />
                    {(index + 1) % 3 === 0 && <div className="pagebreak" />}
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    )
  }
}
