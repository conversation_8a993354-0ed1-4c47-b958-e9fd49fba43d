
.breadcrumb {
  margin-bottom: -10px;
  background-color:#343434 !important ;
}

.button {
  float: right;
  margin-top: -30px;
  margin-right: 15px;
}

.surveyWapper {
  display: inline-block;
  zoom: 1;
  vertical-align: middle;
  white-space: normal;
  margin-top: 20px;
  position: relative;
  padding-top: 0;
  font-size: 14px;
  text-align: left;
  width: 100%;
  :global(.inner) {
    margin-left: auto;
    margin-right: auto;
  }
  .ratingWapper {
    :global(.list-group-item) {
      border: none;
      padding: 2px;
      background-color: transparent;
    }
    :global(.col-md-6) {
      text-align: center;
    }
    :global(.highcharts-container) {
      margin: 0 auto;
    }
    .tipMessage {
      margin-bottom: 10%;
      margin-top: 10%;
    }
  }
  .rateItemTitle {
    margin-right: 7px;
  }
  .ratingTitle {
    border-left: 2px solid orange;
    padding: 5px 10px;
    margin: 10px 0;
  }
  .rating {
    unicode-bidi: bidi-override;
    direction: rtl;
    font-size: 20px;
    :global(.fa) {
      margin-right: 5px;
    }
    span:global(.fa) {
      cursor: pointer;
    }
    span:global(.fa).active {
      color: #e3cf7a;
    }
    span:global(.fa):hover:before,
    span:global(.fa):hover~span:global(.fa):before {
      color: #e3cf7a;
    }
  }
}
.surveyMain {
  padding: 10px 15px;
  color: white;;
  background-color: rgb(36, 36, 36);
  .surveyTitle, .surveyDescription {
    padding: 5px 0;
  }
  .surveyTitle {
    text-align: center;
  }
  .surveyDescription {
    font-size: 15px;
    line-height: 1.6;
    p {
      white-space: pre-wrap;
    }
  }
  .buttonWapper {
    text-align: center;
    margin-top: 20px;
  }
}
.surveyContainer {
}

@media screen and (max-width: 992px) {
  .surveyWapper {
    padding-top: 0;
    background-color: transparent;
    border: none;
  }
}

@media (min-width: 992px) {
  .surveyWapper {
    .ratingWapper {
      .tipMessage {
        margin-top: 25%;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .surveyWapper {
    .ratingWapper {
      padding: 0 20px;
    }
  }
}
