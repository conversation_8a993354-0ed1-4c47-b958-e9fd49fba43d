import { Effect } from 'dva'
import { Reducer } from 'redux'
import { load, rating } from './service'

export interface AnswerModelState {
  survey?: any
}

export interface ModelType {
  namespace: 'answer';
  state: AnswerModelState;
  effects: {
    fetchSurvey: Effect;
    postRating: Effect;
  };
  reducers: {
    save: Reducer<AnswerModelState>;
  };
}

const AnswerModel: ModelType = {
  namespace: 'answer',
  state: {
    survey: []
  },
  effects: {
    *fetchSurvey({ payload: { id, params } }, { call, put }) {
      const response = yield call(load, id, params)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
    },
    *postRating({ payload: { id, data } }, { call, put }) {
      yield call(rating, id, data)
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
  },
}

export default AnswerModel
