import { Effect } from 'dva'
import { Reducer } from 'redux'
import { queryFactorList, queryDateList, queryPortfolio } from './service'

export interface ModelState {
  dateList: string[];
  factorList: any;
  currentDate?: string;
  currentPortfolio?: any;
}

export interface ModelType {
  namespace: 'smartfof';
  state: ModelState;
  effects: {
    fetchFactorList: Effect;
    fetchDateList: Effect;
    fetchPortfolio: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
  };
}

const SmartFofModel: ModelType = {
  namespace: 'smartfof',

  state: {
    dateList: [],
    factorList: [],
  },

  effects: {
    *fetchFactorList({ payload: { params } }, { call, put }) {
      const response = yield call(queryFactorList, params)
      yield put({
        type: 'save',
        payload: {
          factorList: response,
        },
      })
    },
    *fetchDateList({ payload: { params } }, { call, put }) {
      const response = yield call(queryDateList, params)
      const currentDate = response[0]
      const factorList = yield call(queryFactorList, { date: currentDate })
      yield put({
        type: 'save',
        payload: {
          currentDate,
          dateList: response,
          factorList: factorList,
        },
      })
    },
    *fetchPortfolio({ payload: { params } }, { call, put }) {
      const response = yield call(queryPortfolio, params)
      yield put({
        type: 'save',
        payload: {
          currentPortfolio: response,
        },
      })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
  },
}

export default SmartFofModel
