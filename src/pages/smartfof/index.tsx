import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import moment from 'moment'
import { ModelState } from './model'
import { Card, Row, Col, Select, Spin } from 'antd'
import StandardTable, { StandardTableColumnProps } from '@/components/StandardTable'
import Chart from '@/components/Chart/Chart'

const { Option } = Select

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  dateList: string[];
  factorList: any;
  currentDate?: string;
  currentPortfolio?: any;
}

interface ComponentState {}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    loading,
    smartfof,
  }: {
    smartfof: ModelState;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    loading: loading.models.smartfof,
    dateList: smartfof.dateList,
    factorList: smartfof.factorList,
    currentDate: smartfof.currentDate,
    currentPortfolio: smartfof.currentPortfolio,
  }),
)
class SmartFof extends Component<ComponentProps, ComponentState> {
  componentDidMount() {
    const { dispatch } = this.props
    dispatch({
      type: 'smartfof/fetchDateList',
      payload: {},
    })
    dispatch({
      type: 'smartfof/fetchPortfolio',
      payload: {},
    })
  }

  loadFactorList(date: string) {
    const { dispatch } = this.props
    dispatch({
      type: 'smartfof/fetchFactorList',
      payload: {
        params: { date },
      },
    })
  }

  handleDateChange = (date: string) => {
    this.updateState({
      currentDate: date,
    })
    this.loadFactorList(date)
  };

  updateState = (newState: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'smartfof/save',
      payload: newState,
    })
  };

  renderPortfolioCard() {
    const { currentPortfolio } = this.props
    const benchmark = currentPortfolio.benchmarkData || { nets: [] }
    const columns = [
      {
        title: 'AI优选因子Smart FOF组合',
        dataIndex: 'name',
      },
      {
        title: '',
        dataIndex: 'value',
      },
    ]
    const formatPercentageValue = (field: string) =>
      `${(currentPortfolio[field] * 100).toFixed(2)}%`
    const data = [
      {
        name: '起始日期',
        value: moment(new Date(currentPortfolio.navStartDate)).format('YYYY/MM/DD'),
      },
      {
        name: '累计收益',
        value: formatPercentageValue('cucmulativeReturn'),
      },
      {
        name: '年化收益',
        value: formatPercentageValue('yearReturn'),
      },
      {
        name: '今年以来收益',
        value: formatPercentageValue('ytdReturn'),
      },
      {
        name: '今年以来年化收益',
        value: formatPercentageValue('ytdYearReturn'),
      },
      {
        name: '当月收益',
        value: formatPercentageValue('mtdReturn'),
      },
      {
        name: '最大回撤',
        value: formatPercentageValue('maxDrawdown'),
      },
      {
        name: '夏普比',
        value: currentPortfolio.sharpeRatio.toFixed(2),
      },
    ]
    const config = {
      chart: {
        type: 'line',
        height: 320,
      },
      tooltip: {
        pointFormat:
          '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.4f}({point.change:.2f}%)</b><br/>',
      },
      yAxis: {
        labels: {
          format: '{value}%',
        },
      },
      series: [
        {
          name: '组合净值',
          data: currentPortfolio.nets,
          compare: 'percent',
        },
        {
          name: benchmark.name,
          data: benchmark.nets,
          compare: 'percent',
        },
      ],
    }
    return (
      <Card title="Smart FOF 组合推荐">
        <Row gutter={16}>
          <Col span={6}>
            <StandardTable
              disableRowSlection
              columns={columns}
              data={{ list: data }}
              size="small"
            />
          </Col>
          <Col span={18}>
            <Chart options={config} constructorType="stockChart" />
          </Col>
        </Row>
      </Card>
    )
  }

  render() {
    const columns: StandardTableColumnProps[] = [
      {
        title: '因子代号',
        dataIndex: 'factor_code',
        width: '12%',
      },
      {
        title: '因子名称',
        dataIndex: 'factor_name',
        width: '10%',
      },
      {
        title: '短期 IC mean',
        dataIndex: 'short_ic',
        width: '20%',
        render: (text, record) => (
          <Row gutter={0}>
            <Col span={4}>{text.toFixed(2)}</Col>
            <Col span={20}>
              <div
                style={{ background: '#0e92ff', width: `${record.short_ic_v}%`, height: 16 }}
              ></div>
            </Col>
          </Row>
        ),
      },
      {
        title: '长期 IC mean',
        dataIndex: 'long_ic',
        width: '20%',
        render: (text, record) => (
          <Row gutter={0}>
            <Col span={4}>{text.toFixed(2)}</Col>
            <Col span={20}>
              <div
                style={{ background: '#0e92ff', width: `${record.long_ic_v}%`, height: 16 }}
              ></div>
            </Col>
          </Row>
        ),
      },
      {
        title: '月度胜率',
        dataIndex: 'm_win_rate',
        format: 'valPercentage',
      },
      {
        title: '多空组合年化',
        dataIndex: 'multi_empty_y',
        format: 'valPercentage',
      },
      {
        title: '多空组合 Sharpe Ratio',
        dataIndex: 'multi_empty_s',
        width: '20%',
        render: (text, record) => (
          <Row gutter={0}>
            <Col span={4}>{text.toFixed(2)}</Col>
            <Col span={20}>
              <div
                style={{ background: '#0e92ff', width: `${record.multi_empty_s_v}%`, height: 16 }}
              ></div>
            </Col>
          </Row>
        ),
      },
    ]
    const { dateList, currentDate, loading, factorList, currentPortfolio } = this.props
    const virtualQuotas = ['short_ic', 'long_ic', 'multi_empty_s'].map(quota => {
      return {
        quota,
        maxValue: Math.max.apply(null, factorList.map(item => item[quota])),
      }
    })
    const data = factorList.map(item => {
      virtualQuotas.forEach(quota => {
        item[`${quota.quota}_v`] = (item[quota.quota] / quota.maxValue) * 80
      })
      return item
    })
    return (
      <div>
        <Spin spinning={loading}>
          <Card
            title="因子监控"
            extra={
              <div>
                <span style={{ marginRight: '10px' }}>时间选择:</span>
                <Select
                  showSearch
                  style={{ width: 150 }}
                  placeholder="请选择时间"
                  onChange={this.handleDateChange}
                  value={currentDate}
                >
                  {dateList.map((item: string) => (
                    <Option value={item}>{item}</Option>
                  ))}
                </Select>
              </div>
            }
          >
            <StandardTable
              disableRowSlection
              columns={columns}
              data={{ list: data }}
              size="small"
              scroll={{
                y: 400,
              }}
            />
          </Card>
        </Spin>
        {currentPortfolio && this.renderPortfolioCard()}
      </div>
    )
  }
}

export default SmartFof
