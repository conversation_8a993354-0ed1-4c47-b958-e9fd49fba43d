import request from '@/utils/request'

export async function queryFactorList(params: any): Promise<any> {
  return request(`/api/kym/smartfof/factors`, {
    method: 'GET',
    params,
  })
}

export async function queryDateList(): Promise<any> {
  return request(`/api/kym/smartfof/datelist`, {
    method: 'GET',
  })
}

export async function queryPortfolio(): Promise<any> {
  return request(`/api/kym/smartfof/portfolio`, {
    method: 'GET',
  })
}
