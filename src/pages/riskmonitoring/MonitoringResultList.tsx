import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import Link from 'umi/link'
import _ from 'lodash'
import { connect } from 'dva'
import {
  Table,
  Input,
  Card,
  Breadcrumb,
  Tag,
  Popconfirm,
  notification,
  Tooltip,
  Button,
} from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import moment from 'moment'
import { queryMonitoringMessage, deleteMonitorMessages } from './service'
import { getMonitoringRuleDesc } from './utils'
import useTableRowSelection from '@/hooks/useTableRowSelection'
import ExportData from '@/components/ExportData'
import { getToken } from '@/utils/utils'


const Search = Input.Search

const List = ({
  currentUser,
}: {
  currentUser: any,
}) => {
  const [input, setInput] = useState('')
  const [refreshCount, setRefreshCount] = useState(0)
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    return queryMonitoringMessage(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const { selectedRows, rowSelection } = useTableRowSelection([])
  const { run: doDeleteMonitorMessagges } = useRequest(() => {
    return deleteMonitorMessages(selectedRows.map(item => item._id))
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const columns = [
    {
      title: '时间',
      dataIndex: 'date',
      render: (value) => {
        return moment(new Date(value)).format('YYYY-MM-DD')
      },
    },
    {
      title: '监测方案',
      dataIndex: 'ruleName',
    },
    {
      title: '预警基金',
      dataIndex: 'outlierData',
      render: (value, record) => {
        return <Link to={`/${value._syncType === 'mutual' ? 'fund' : 'activefund'}/${value.fundId}/invest_performance`}>{value.name}</Link>
      },
    },
    {
      title: '预警详情',
      dataIndex: 'outlierData',
      render: (value, item) => {
        const ruleDesc = getMonitoringRuleDesc(item)
        const outlierData = item.outlierData || {}
        let color = '#1e80ff'
        let warningType = '蓝色预警'
        let warningValue = item.warningValue1
        if (outlierData.warningType === 2) {
          color = '#ff9800'
          warningType = '黄色预警'
          warningValue = item.warningValue2
        } else if (outlierData.warningType === 3) {
          color = '#ff4d4f'
          warningType = '红色预警'
          warningValue = item.warningValue3
        }
        const getWarningDisplayValue = value => {
          if (!value && value !==  0) {
            return ''
          }
          return ruleDesc.quota.format === 'integer' ? value : `${_.round(value * 100)}%`
        }
        let message = `${ruleDesc.title}(${getWarningDisplayValue(outlierData.value)}) ${ruleDesc.condition} ${getWarningDisplayValue(warningValue)}`
        if (outlierData.warningInformation) {
          message = outlierData.warningInformation
        }
        return <span><Tag color={color}>{warningType}</Tag> {message}</span>
      },
    },
  ]
  const handleDownload = () => {
    const token = getToken()
    const href = `/api/monitormessages/download?input=${input}&token=${token.slice(7)}`
    window.open(href)
  }
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link to="/riskmonitoring/quotas">
            风险管理
          </Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>预警结果列表</Breadcrumb.Item>
      </Breadcrumb>
      <Card
        title={
          <>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
        extra={
          <>
            {selectedRows.length !== 0 &&
            <Popconfirm
              title={`确认删除所选 ${selectedRows.length} 条预警信息吗？`}
              onConfirm={() => { doDeleteMonitorMessagges() }}
              onCancel={() => {}}
              okText="确认"
              cancelText="取消"
            >
              <Tooltip title="删除所选预警信息" placement="left">
                <Button
                  ghost
                  disabled={!selectedRows.length}
                  type="primary"
                  icon={<DeleteOutlined />}
                  size="small"
                >
                  删除
                </Button>
              </Tooltip>
            </Popconfirm>}
            <ExportData onClick={handleDownload} title="导出监控结果"/>
          </>
        }
      >
        <Table size="small" columns={columns} rowSelection={rowSelection} rowKey="_id" {...tableProps} />
      </Card>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
