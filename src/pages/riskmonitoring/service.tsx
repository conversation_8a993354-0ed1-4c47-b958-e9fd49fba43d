import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryMonitorRules(params: TableListParams) {
  return request('/api/monitorrules', {
    params,
  })
}

export async function queryMonitorRule(id: string) {
  return request(`/api/monitorrules/${id}`)
}

export async function deleteMonitorRule(id: string) {
  return request(`/api/monitorrules/${id}`, {
    method: 'delete',
  })
}

export async function updateMonitorRule(id: string, data: any) {
  return request(`/api/monitorrules/${id}`, {
    method: 'put',
    data,
  })
}

export async function copyMonitorRule(id: string) {
  return request(`/api/monitorrules/${id}/copy`, {
    method: 'post',
  })
}

export async function createMonitorRule(data: any) {
  return request(`/api/monitorrules`, {
    method: 'post',
    data,
  })
}

export async function queryLatestMonitoringReports() {
  return request('/api/monitorrules/dashboard/reports')
}

export async function queryMonitoringSummary() {
  return request('/api/monitorrules/dashboard/summary')
}

export async function queryMonitoringResults() {
  return request('/api/monitorrules/dashboard/results')
}

export async function queryMgtFeeResults() {
  return request('/api/monitorrules/dashboard/mgtfee')
}

export async function queryMonitoringMessage(params: any) {
  return request('/api/monitormessages', {
    params,
  })
}

export async function deleteMonitorMessages(ids: string[]) {
  return request(`/api/monitormessages/multi`, {
    method: 'delete',
    data: { ids },
  })
}
