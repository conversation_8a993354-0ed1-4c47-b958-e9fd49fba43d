import React from 'react'
import { Col, Row } from 'antd'
import { GridContent } from '@ant-design/pro-layout'
import MonitoringReports from './components/MonitoringReports'
// import SummaryRow from './components/SummaryRow'
import ResultsSidebar from './components/ResultsSidebar'
import { queryMgtFeeResults } from './service'

const MonitoringMgtFee = () => {
  return (
    <GridContent>
      <Row
        gutter={16}
      >
        <Col xl={18} lg={18} md={18} sm={24} xs={24}>
          {/* <SummaryRow /> */}
          <MonitoringReports queryReportList={queryMgtFeeResults} />
        </Col>
        <Col xl={6} lg={6} md={6} sm={24} xs={24}>
          <ResultsSidebar />
        </Col>
      </Row>
    </GridContent>
  )
}

export default MonitoringMgtFee
