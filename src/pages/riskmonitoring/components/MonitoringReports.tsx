import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import router from 'umi/router'
import _ from 'lodash'
import { Col, Row, Card, Table, Spin, Empty, Button, Tooltip, Tag, Modal, Popover, Space } from 'antd'
import { ExclamationCircleOutlined, HistoryOutlined, WarningOutlined, ZoomInOutlined } from '@ant-design/icons'
import renderFundQuota from '@/utils/renderFundQuota'
import buildTableColumn from '@/utils/buildTableColumn'
import thousandFormatter from '@/utils/thousandFormatter'
import { getMonitoringRuleDesc } from '../utils'
import styles from './MonitoringReports.less'
import HistoryResultModal from './HistoryResultModal'

const MonitoringReports = ({ queryReportList }) => {
  const { loading, data } = useRequest(() => {
    return queryReportList()
  })
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState({})
  const handleOpenModal = (record) => () => {
    setCurrentItem(record)
    setVisibleTrue()
  }
  let dataList = (
    <Empty
      style={{ width: '100%' }}
      description="您还未创建任何监测方案。"
    >
      <Button type="primary" onClick={() => router.push('/riskmonitoring/monitoringrules')}>新建方案</Button>
    </Empty>
  )
  const feeRender = (maxFeeKey) => (text, record) => {
    if (!text) {
      return '-'
    }
    const upperLimit = record[maxFeeKey]
    const reachedUpperLimit = (text === upperLimit) && upperLimit
    let formatedText = thousandFormatter(Number((text / 10000).toFixed(2)))
    formatedText = `${formatedText}万`
    if (!reachedUpperLimit) {
      return formatedText
    }
    return (
      <>
        <span style={{ marginRight: 5 }}>{formatedText}</span>
        <Tooltip title="已达上限">
          <WarningOutlined className={styles.wraning}/>
        </Tooltip>
      </>
    )
  }
  const floatFeeRender = (totalFee, accMgtFee) => (text, record) => {
    const floatFee = record[totalFee] - record[accMgtFee]
    if (!floatFee) {
      return '-'
    }
    const formatedText = thousandFormatter(Number((floatFee / 10000).toFixed(2)))
    return `${formatedText}万`
  }

  const positionWaringRender = (title, positions) => {
    const columns = [{
      dataIndex: 'name',
    }, {
      title: title,
      dataIndex: 'value',
      format: 'valPercentage',
      align: 'right',
    }].map(buildTableColumn)
    return (
      <>
        <Popover
          placement="right"
          content={<Table style={{ width: 400 }} scroll={{ y: 250 }} size="small" columns={columns} dataSource={positions} pagination={false} />}
        >
          <a>{positions.length}条预警信息</a>
        </Popover>
      </>
    )
  }
  if (data && data.length) {
    dataList = data.sort((fst, snd) => {
      if (fst.quota === 'mgtFeeRatio') {
        return -1
      } else {
        return 1
      }
    }).map(item => {
      const ruleDesc = getMonitoringRuleDesc(item)
      let columns = [{
        title: '组合',
        dataIndex: 'name',
        width: 120,
        fixed: 'left',
        render: (text, record) => {
          if (item.quota === 'mgtFeeRatio' && record.childFundCodes && record.childFundCodes.length) {
            return (
              <>
                <span style={{ marginRight: 5 }}>{text}</span>
                <Tooltip title="点击查看子基金费用计提">
                  <ZoomInOutlined className={styles.wraning} onClick={handleOpenModal(record)} />
                </Tooltip>
              </>
            )
          }
          return text
        },
      }]
      if (item.quota === 'mgtFeeRatio') {
        columns = columns.concat([{
          title: '截止日期',
          dataIndex: 'feeUpdateDate',
        }, {
          title: '管理费',
          dataIndex: 'accMgtFee',
          format: 'tenThousand',
        }, {
          title: '子基金管理费',
          dataIndex: 'accMgtFeeChild',
          width: 100,
          format: 'tenThousand',
        }, {
          title: '浮动计提',
          dataIndex: 'totalPerfFee',
          render: floatFeeRender('totalFee', 'accMgtFee'),
        }, {
          title: '子基金浮动计提',
          dataIndex: 'totalPerfFeeChild',
          width: 110,
          render: floatFeeRender('totalFeeChild', 'accMgtFeeChild'),
        }, {
          title: '总计提',
          dataIndex: 'totalFee',
          render: feeRender('maxTotalFee'),
        }, {
          title: '子基金总计提',
          dataIndex: 'totalFeeChild',
          width: 100,
          format: 'tenThousand',
        }, {
          title: '母基金利润',
          dataIndex: 'totalProfit',
          width: 100,
          format: 'tenThousand',
        }].map(buildTableColumn))
      }
      if (item.type !== 'momcustom') {
        columns.push({
          title: ruleDesc.title,
          dataIndex: ruleDesc.quota.dataIndex,
          width: 110,
          render: (value, record) => {
            if (!value && record.outlierData && record.outlierData.positions && record.outlierData.positions.length) {
              return positionWaringRender(ruleDesc.title, record.outlierData.positions)
            }
            return renderFundQuota(ruleDesc.quota, record)
          },
        })
      }
      columns.push({
        title: '监测结果',
        dataIndex: 'outlierData',
        width: 70,
        fixed: 'right',
        render: (value) => {
          if (!value) {
            return <Tag color="#0ebf9c">正常</Tag>
          } else if (value.warningType === 3) {
            return <Tag color="#ff4d4f">红色预警</Tag>
          } else if (value.warningType === 2) {
            return <Tag color="#ff9800">黄色预警</Tag>
          } else {
            return <Tag color="#1e80ff">蓝色预警</Tag>
          }
        },
      })
      const colSpan = item.quota === 'mgtFeeRatio' ? 24 : 12
      const getWarningDisplayValue = value => {
        return ruleDesc.quota.format === 'integer' ? value : `${_.round(value * 100, 2)}%`
      }
      return (
        <Col xl={colSpan} lg={colSpan} md={colSpan} sm={24} xs={24} className={styles.reportCard}>
          <Card
            title={item.name}
            extra={
              <Space>
                <Tooltip title={
                  item.type === 'momcustom'
                    ? <span>
                      蓝色预警：持续2个月末当年累计跑输业绩基准超100bps（含）或1个月末当年累计跑输业绩基准超200bps（含）或系统月度综合得分低于70分（含）
                      <br/>
                      <br/>
                      黄色预警：连续3个月亮蓝灯且业绩为负或1个月末当年累计跑输业绩基准达到300bps（含）或系统综合得分低于50分
                      <br/>
                      <br/>
                      红色预警：连续3个月亮黄灯且业绩为负或1个月末当年累计跑输业绩基准超600bps（含）且业绩为负或系统月度综合得分低于30分
                    </span> : item.type === 'momcustom_bond'
                    ? <span>
                      蓝色预警：1个月末跑输基准50bps以上（含，业绩按滚动6个月计算）或者系统月度综合得分低于70分
                      <br/>
                      <br/>
                      黄色预警：连续3个月跑输基准75bps以上（含，业绩按滚动6个月计算）或持续3个月系统月度综合得分低于50分
                      <br/>
                      <br/>
                      红色预警：连续6个月跑输基准100bps以上（含，业绩按滚动6个月计算）或持续6个月系统综合得分低于30分
                    </span> :
                    <span>
                      蓝色预警：{`${ruleDesc.title} ${ruleDesc.condition} ${getWarningDisplayValue(item['warningValue1'])}`}
                      <br/>
                      黄色预警：{`${ruleDesc.title} ${ruleDesc.condition} ${getWarningDisplayValue(item['warningValue2'])}`}
                      <br/>
                      红色预警：{`${ruleDesc.title} ${ruleDesc.condition} ${getWarningDisplayValue(item['warningValue3'])}`}
                    </span>
                }>
                  <ExclamationCircleOutlined/>
                </Tooltip>
                <HistoryResultModal monitorRule={item}>
                  <Tooltip title="点击查看历史预警结果">
                    <HistoryOutlined style={{ cursor: 'pointer' }}/>
                  </Tooltip>
                </HistoryResultModal>
              </Space>
            }
          >
            <Table
              size="small"
              columns={columns}
              dataSource={item.funds}
              pagination={false}
              scroll={{ y: item.quota === 'mgtFeeRatio' ? 750 : 250, x: item.quota === 'mgtFeeRatio' ? 1000 : null }}
            />
          </Card>
        </Col>
      )
    })
  }
  const childFeeDetailColumns = [{
    title: '组合',
    dataIndex: 'name',
  }, {
    title: '管理费',
    dataIndex: 'accMgtFee',
    format: 'tenThousand',
  }, {
    title: '浮动计提',
    dataIndex: 'totalPerfFee',
    render: floatFeeRender('totalFee', 'accMgtFee'),
  }, {
    title: '总计提',
    dataIndex: 'totalFee',
    render: feeRender('maxTotalFee'),
  }].map(buildTableColumn)
  return (
    <Spin spinning={loading}>
      <Row
        gutter={16}
      >
        {dataList}
      </Row>
      <Modal
        title={
          <>
            <span>{currentItem.name}</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={700}
        footer={null}
      >
        <Table
          size="small"
          columns={childFeeDetailColumns}
          dataSource={currentItem.childFunds || []}
          pagination={false}
        />
      </Modal>
    </Spin>
  )
}

export default MonitoringReports
