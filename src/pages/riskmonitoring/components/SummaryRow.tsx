import React from 'react'
import { useRequest } from '@umijs/hooks'
import router from 'umi/router'
import { Col, Row, Statistic, Card, Tooltip, Spin } from 'antd'
import { queryMonitoringSummary } from '../service'

const SummaryRow = () => {
  const { loading, data={} } = useRequest(() => {
    return queryMonitoringSummary()
  })
  const summaryData = data || {}
  return (
    <Spin spinning={loading}>
      <Row gutter={16}>
        <Col xl={8} lg={8} md={8} sm={24} xs={24}>
          <Card>
            <Statistic
              title={
                <Tooltip title="点击查看组合列表">
                  <a style={{ color: '#949FB6' }} onClick={() => router.push('/activefund')}>实盘组合</a>
                </Tooltip>
              }
              value={summaryData.activeFunds}
            />
          </Card>
        </Col>
        <Col xl={8} lg={8} md={8} sm={24} xs={24}>
          <Card>
            <Statistic
              title={
                <Tooltip title="点击查看监测方案列表">
                  <a style={{ color: '#949FB6' }} onClick={() => router.push('/riskmonitoring/monitoringrules')}>监测方案</a>
                </Tooltip>
              }
              value={summaryData.monitoringRules}
            />
          </Card>
        </Col>
        <Col xl={8} lg={8} md={8} sm={24} xs={24}>
          <Card>
            <Statistic
              title={
                <Tooltip title="点击查看预警结果列表">
                  <a style={{ color: '#949FB6' }} onClick={() => router.push('/riskmonitoring/monitoringresults')}>预警结果</a>
                </Tooltip>
              }
              value={summaryData.monitoringResults}
            />
          </Card>
        </Col>
      </Row>
    </Spin>
  )
}

export default SummaryRow
