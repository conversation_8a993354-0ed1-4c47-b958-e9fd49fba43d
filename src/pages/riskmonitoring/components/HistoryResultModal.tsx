import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import Link from 'umi/link'
import _ from 'lodash'
import { connect } from 'dva'
import {
  Table,
  Input,
  Card,
  Tag,
  Button,
  Modal,
} from 'antd'
import moment from 'moment'
import { queryMonitoringMessage } from '../service'
import { getMonitoringRuleDesc } from '../utils'
import ExportData from '@/components/ExportData'
import { getToken } from '@/utils/utils'


const Search = Input.Search

const List = ({
  currentUser,
  children,
  monitorRule,
}: {
  currentUser: any,
  children: any,
  monitorRule: any,
}) => {
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [input, setInput] = useState('')
  const [refreshCount, setRefreshCount] = useState(0)
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    p.ruleId = monitorRule._id
    return queryMonitoringMessage(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const columns = [
    {
      title: '时间',
      dataIndex: 'date',
      render: (value) => {
        return moment(new Date(value)).format('YYYY-MM-DD')
      },
    },
    // {
    //   title: '监测方案',
    //   dataIndex: 'ruleName',
    // },
    {
      title: '预警基金',
      dataIndex: 'outlierData',
      render: (value, record) => {
        return <Link to={`/${value._syncType === 'mutual' ? 'fund' : 'activefund'}/${value.fundId}/invest_performance`}>{value.name}</Link>
      },
    },
    {
      title: '预警详情',
      dataIndex: 'outlierData',
      render: (value, item) => {
        const ruleDesc = getMonitoringRuleDesc(item)
        const outlierData = item.outlierData || {}
        let color = '#1e80ff'
        let warningType = '蓝色预警'
        let warningValue = item.warningValue1
        if (outlierData.warningType === 2) {
          color = '#ff9800'
          warningType = '黄色预警'
          warningValue = item.warningValue2
        } else if (outlierData.warningType === 3) {
          color = '#ff4d4f'
          warningType = '红色预警'
          warningValue = item.warningValue3
        }
        const getWarningDisplayValue = value => {
          if (!value && value !==  0) {
            return ''
          }
          return ruleDesc.quota.format === 'integer' ? value : `${_.round(value * 100)}%`
        }
        let message = `${ruleDesc.title}(${getWarningDisplayValue(outlierData.value)}) ${ruleDesc.condition} ${getWarningDisplayValue(warningValue)}`
        if (outlierData.warningInformation) {
          message = outlierData.warningInformation
        }
        return <span><Tag color={color}>{warningType}</Tag> {message}</span>
      },
    },
  ]
  const handleModalOpen = () => {
    setVisibleTrue()
    setRefreshCount(refreshCount + 1)
  }
  const handleDownload = () => {
    const token = getToken()
    const href = `/api/monitormessages/download?ruleId=${monitorRule._id}&input=${input}&token=${token.slice(7)}`
    window.open(href)
  }
  return (
    <div>
      <div onClick={handleModalOpen}>{children}</div>
      <Modal
        className='zero-padding-modal'
        title={
          <>
            <span>{monitorRule.name}预警历史记录</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={1000}
        footer={[
          <Button type="primary" onClick={() => {
            setVisibleFalse()
          }}>
            关闭
          </Button>,
        ]}
      >
        <Card
          title={
            <>
              <Search
                style={{ width: '300px' }}
                placeholder="按回车进行搜索"
                onSearch={setInput}
              />
            </>
          }
          bordered={false}
          extra={
            <>
              <ExportData onClick={handleDownload} title="导出监控结果"/>
            </>
          }
        >
          <Table size="small" columns={columns} rowKey="_id" {...tableProps} />
        </Card>
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
