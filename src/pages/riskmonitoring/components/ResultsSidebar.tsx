import React from 'react'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import Link from 'umi/link'
import { Card, Timeline, Spin, Tag, Empty } from 'antd'
import Echarts from '@/components/Chart/Echarts'
import { queryMonitoringResults } from '../service'
import { getMonitoringRuleDesc } from '../utils'
import moment from 'moment'

const getCalendarChartConfig = (data) => {
  const option = {
    tooltip: {
      position: 'top',
      formatter: (params) => {
        const data = params.data
        return `${data[0]}：${data[1]}次`
      },
    },
    calendar: [{
      left: 10,
      height: 180,
      orient: 'vertical',
      yearLabel: {
        margin: 30,
        color: '#BCC8D7',
        show: false,
      },
      monthLabel: {
        nameMap: 'cn',
        margin: 15,
        color: '#BCC8D7',
      },
      dayLabel: {
        firstDay: 1,
        nameMap: 'cn',
        color: '#BCC8D7',
      },
      cellSize: 40,
      range: moment().format('YYYY-MM'),
      itemStyle: {
        color: '#323c48',
        borderWidth: 1,
        borderColor: '#111',
      },
    }],
    series: [{
      type: 'effectScatter',
      coordinateSystem: 'calendar',
      calendarIndex: 0,
      symbolSize: function (val) {
        return val[1] * 2
      },
      itemStyle: {
        color: '#ddb926',
      },
      data: data,
    }],
  }
  return option
}

const getTimelineData = results => {
  return _.map(_.groupBy(results, item => item.date.slice(0, 10)), (rows, date) => {
    const color = rows.some(item => item.outlierData.warningType === 2) ? '#ff4d4f' : '#ff9800'
    const data = rows.map(item => {
      const ruleDesc = getMonitoringRuleDesc(item)
      const outlierData = item.outlierData || {}
      const getWarningDisplayValue = value => {
        if (!value && value !== 0) {
          return ''
        }
        return ruleDesc.quota.format === 'integer' ? value : `${_.round(value * 100, 2)}%`
      }
      let color = '#1e80ff'
      let warningType = '蓝色预警'
      let warningValue = item.warningValue1
      if (outlierData.warningType === 2) {
        color = '#ff9800'
        warningType = '黄色预警'
        warningValue = item.warningValue2
      } else if (outlierData.warningType === 3) {
        color = '#ff4d4f'
        warningType = '红色预警'
        warningValue = item.warningValue3
      }
      let message = `${outlierData.name}: ${ruleDesc.title}(${getWarningDisplayValue(outlierData.value)}) ${ruleDesc.condition} ${getWarningDisplayValue(warningValue)}`
      if (outlierData.warningInformation) {
        message = `${outlierData.name}: ${outlierData.warningInformation}`
      }
      return {
        color: color,
        warningType: warningType,
        message,
      }
    })
    return {
      title: date,
      data,
      color,
    }
  }).sort((fst, snd) => {
    return new Date(snd.title) - new Date(fst.title)
  })
}

const ResultsSidebar = () => {
  const { loading, data } = useRequest(() => {
    return queryMonitoringResults()
  })
  const results = data || { monitoringResults: [], monthData: [] }
  const option = getCalendarChartConfig(results.monthData)
  const timelineData = getTimelineData(results.monitoringResults)
  let timelineContent = <Empty />
  if (timelineData.length) {
    timelineContent = timelineData.map(item => {
      return (
        <Timeline.Item color={item.color}>
          <h3>{item.title}</h3>
          {item.data.map(itemData => <p><Tag color={itemData.color}>{itemData.warningType}</Tag> {itemData.message}</p>)}
        </Timeline.Item>
      )
    })
  }
  return (
    <Spin spinning={loading}>
      <Card>
        <span>当月预警分布</span>
        <Echarts options={option} style={{ height: 250 }}/>
      </Card>
      <Card
        title={`预警结果 (${results.total})`}
        extra={
          <Link to="/riskmonitoring/monitoringresults">查看全部</Link>
        }
      >
        <Timeline style={{ maxHeight: 600, overflow: 'scroll' }}>
          {timelineContent}
        </Timeline>
      </Card>
    </Spin>
  )
}

export default ResultsSidebar
