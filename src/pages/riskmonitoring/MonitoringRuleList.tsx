import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import Link from 'umi/link'
import { connect } from 'dva'
import _ from 'lodash'
import { DeleteOutlined, EditOutlined, PlusOutlined, PlayCircleOutlined, PauseCircleOutlined, CopyOutlined } from '@ant-design/icons'
import {
  Table,
  Button,
  Tooltip,
  Divider,
  Popconfirm,
  Modal,
  Input,
  Card,
  Form,
  notification,
  Row,
  Col,
  Switch,
  Select,
  InputNumber,
  Spin,
  Breadcrumb,
  Cascader,
  Typography,
  Dropdown,
} from 'antd'
import { queryMonitorRules, updateMonitorRule, createMonitorRule, deleteMonitorRule, copyMonitorRule } from './service'
import t from '@/utils/t'
import { monitoringQuotas } from '@/utils/quotas'
import FundListFormItem from '@/components/FundListFormItem'
import buildTreeData from '@/utils/buildTreeData'

const navQuotasMap = _.mapValues(_.groupBy(monitoringQuotas, 'dataIndex'), values => values[0])

const Search = Input.Search
const { Paragraph } = Typography

interface MonitorRuleItem {
  _id?: string,
  name: string,
  description: string,
  company: string,
  funds: {
    name: string,
    _id: string,
  }[],
  quota: string,
  operator: string,
  warningValue1: number,
  warningValue2: number,
  warningValue3: number,
  isEnabled: boolean,
  updated_at?: string,
}

const List = ({
  currentUser,
  location,
}: {
  currentUser: any,
  location: any,
}) => {
  const [momcustomType, setMomCustomType] = useState('momcustom')
  const monitoringQuotasTreeData = buildTreeData(monitoringQuotas, ['class1', 'class2'])
  const [form] = Form.useForm()
  const { setFieldsValue } = form
  const defaultType = location.pathname.includes('mommonitoringrules') ? 'momcustom' : 'quota'
  const initialFormValue = {
    name: '', description: '', quota: '', operator: '',
    warningValue1: undefined, warningValue2: undefined, warningValue3: undefined, isEnabled: true, funds: [],
  }
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState({})
  const [refreshCount, setRefreshCount] = useState(0)
  const [input, setInput] = useState('')
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    p.type = defaultType
    return queryMonitorRules(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const onSaveMonitorRuleSuccess = () => {
    setVisibleFalse()
    setRefreshCount(refreshCount + 1)
    notification.success({
      message: '保存成功',
    })
  }
  const { loading: updatingMonitorRule, run: doUpdateMonitorRule } = useRequest((id, data) => {
    return updateMonitorRule(id, data)
  }, {
    manual: true,
    onSuccess: onSaveMonitorRuleSuccess,
  })
  const { loading: creatingMonitorRule, run: doCreateMonitorRule } = useRequest((data) => {
    return createMonitorRule(data)
  }, {
    manual: true,
    onSuccess: onSaveMonitorRuleSuccess,
  })
  const { loading: copyingMonitorRule, run: doCopyMonitorRule } = useRequest((id) => {
    return copyMonitorRule(id)
  }, {
    manual: true,
    onSuccess: () => {
      setVisibleFalse()
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '复制成功',
      })
    },
  })
  const { run: doDeleteMonitorRule } = useRequest((id) => {
    return deleteMonitorRule(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const handleOpenModal = (record: MonitorRuleItem) => () => {
    const quotaData = monitoringQuotas.find(item => item.dataIndex === record.quota) || {}
    const quotaValue = [quotaData.class1, quotaData.class2, record.quota].filter(Boolean)
    const valMultiple = quotaData.format === 'integer' ? 1 : 100
    setCurrentItem(record)
    setVisibleTrue()
    setFieldsValue({
      ...record,
      quota: quotaValue,
      warningValue1: record.warningValue1 && record.warningValue1 * valMultiple,
      warningValue2: record.warningValue2 && record.warningValue2 * valMultiple,
      warningValue3: record.warningValue3 && record.warningValue3 * valMultiple,
    })
  }
  const handleClickSave = (values: any) => {
    const quota = values.quota && values.quota.pop()
    const quotaData = monitoringQuotas.find(item => item.dataIndex === quota) || {}
    const valMultiple = quotaData.format === 'integer' ? 1 : 100
    const data = {
      ...values,
      quota,
      warningValue1: values.warningValue1 && values.warningValue1 / valMultiple,
      warningValue2: values.warningValue2 && values.warningValue2 / valMultiple,
      warningValue3: values.warningValue3 && values.warningValue3 / valMultiple,
    }
    if (currentItem._id) {
      doUpdateMonitorRule(currentItem._id, data)
    } else {
      if (defaultType === 'momcustom') {
        data.type = momcustomType
      } else {
        data.type = defaultType
      }
      doCreateMonitorRule(data)
    }
  }
  const isEmail = (val) => {
    const re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(val)
  }
  const conditionTitleMap = {
    gt: '>',
    gte: '>=',
    lt: '<',
    lte: '<=',
  }
  let columns = [
    {
      title: '状态',
      dataIndex: 'isEnabled',
      width: 100,
      sorter: true,
      render: (value: boolean) => {
        if (value) {
          return <span><PlayCircleOutlined /> 开启</span>
        }
        return <span><PauseCircleOutlined /> 暂停</span>
      },
    },
    {
      title: '名称',
      sorter: true,
      dataIndex: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '监测指标',
      sorter: true,
      dataIndex: 'quota',
      render: (value: string) => {
        const currentQuota = navQuotasMap[value]
        return currentQuota ? currentQuota.title : value
      },
    },
    {
      title: '条件',
      dataIndex: 'operator',
      render: (val: string) => {
        return conditionTitleMap[val]
      },
    },
    {
      title: '蓝色预警值',
      dataIndex: 'warningValue1',
      render: (value: number, record) => {
        if (!value && value !== 0) {
          return '-'
        }
        const currentQuota = navQuotasMap[record.quota] || {}
        return currentQuota.format === 'integer' ? value : `${_.round(value * 100, 2)}%`
      },
    },
    {
      title: '黄色预警值',
      dataIndex: 'warningValue2',
      render: (value: number, record) => {
        if (!value && value !== 0) {
          return '-'
        }
        const currentQuota = navQuotasMap[record.quota] || {}
        return currentQuota.format === 'integer' ? value : `${_.round(value * 100, 2)}%`
      },
    },
    {
      title: '红色预警值',
      dataIndex: 'warningValue3',
      render: (value: number, record) => {
        if (!value && value !== 0) {
          return '-'
        }
        const currentQuota = navQuotasMap[record.quota] || {}
        return currentQuota.format === 'integer' ? value : `${_.round(value * 100, 2)}%`
      },
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        if (currentUser._id !== record.authorId) {
          return false
        }
        return (
          <>
            <Tooltip title="编辑">
              <EditOutlined onClick={handleOpenModal(record)} />
            </Tooltip>
            <Divider type="vertical" />
            <Tooltip title="复制">
              <CopyOutlined onClick={() => doCopyMonitorRule(record._id)} />
            </Tooltip>
            <Divider type="vertical" />
            <Popconfirm
              title={`${t('portfolio.delTip')}${record.name}${t('portfolio.questionEnd')}？`}
              onConfirm={() => { doDeleteMonitorRule(record._id) }}
              onCancel={() => { }}
              okText={t('portfolio.confirm')}
              cancelText={t('portfolio.cancel')}
            >
              <Tooltip title="删除">
                <DeleteOutlined />
              </Tooltip>
            </Popconfirm>
          </>
        )
      },
    },
  ]

  if (defaultType === 'momcustom') {
    columns.splice(3, 5, {
      title: '类型',
      sorter: true,
      dataIndex: 'type',
      render: (type: string) => {
        return type === 'momcustom_bond' ? '固收' : '权益'
      },
    })
  }

  const valueFormatter = value => {
    if (value === null || value === undefined || value === '') {
      return ''
    }
    return _.round(value, 8)
  }

  const momcustomItems = [
    { label: '权益监测方案', key: 'momcustom' },
    { label: '固收监测方案', key: 'momcustom_bond' },
  ]

  const handleCustomMenuClick = ({ key }) => {
    setMomCustomType(key)
    handleOpenModal({
      ...initialFormValue,
      type: key,
    })()
  }
  
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link to="/riskmonitoring/quotas">
            风险管理
          </Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{defaultType === 'momcustom' ? 'MOM默认监控方案' : '监测方案列表'}</Breadcrumb.Item>
      </Breadcrumb>
      <Card
        title={
          <>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
        extra={
          <>
            {defaultType === 'quota' &&
            <a onClick={
              handleOpenModal(initialFormValue)
            }>
              <PlusOutlined />
              新建监测方案
            </a>}
            {defaultType === 'momcustom' &&
            <Dropdown menu={{
              items: momcustomItems,
              onClick: handleCustomMenuClick,
            }}>
              <a onClick={e => e.preventDefault()}>
                <PlusOutlined />
                新建监测方案
              </a>
            </Dropdown>}
          </>
        }
      >
        <Table size="small" columns={columns} rowKey="_id" {...tableProps} />
      </Card>
      <Modal
        title={
          <>
            <span>{currentItem && currentItem._id ? '编辑监测方案' : '新建监测方案'}</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={700}
        footer={[
          <Button
            type="primary"
            loading={updatingMonitorRule || creatingMonitorRule || copyingMonitorRule}
            onClick={() => form.submit()}
          >
            保存
          </Button>,
        ]}
      >
        <Spin spinning={updatingMonitorRule || creatingMonitorRule || copyingMonitorRule}>
          <Form
            hideRequiredMark
            form={form}
            layout="vertical"
            initialValues={initialFormValue}
            onFinish={handleClickSave}
          >
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  label="名称"
                  name="name"
                  rules={[
                    {
                      required: true,
                      message: '请输入名称',
                    },
                  ]}
                >
                  <Input placeholder="名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="描述"
                  name="description"
                >
                  <Input placeholder="描述" />
                </Form.Item>
              </Col>
            </Row>
            {currentItem.type === 'momcustom' &&
            <div>
              <Paragraph>
                监测条件说明：
              </Paragraph>

              <Paragraph>
                <ul>
                  <li>
                    红色预警：连续3个月亮黄灯且业绩为负或1个月末当年累计跑输业绩基准超600bps（含）且业绩为负或系统月度综合得分低于30分
                  </li>
                  <li>
                    黄色预警：连续3个月亮蓝灯且业绩为负或1个月末当年累计跑输业绩基准达到300bps（含）或系统综合得分低于50分
                  </li>
                  <li>
                    蓝色预警：持续2个月末当年累计跑输业绩基准超100bps（含）或1个月末当年累计跑输业绩基准超200bps（含）或系统月度综合得分低于70分（含）
                  </li>
                </ul>
              </Paragraph>
            </div>}
            {currentItem.type === 'momcustom_bond' &&
            <div>
              <Paragraph>
                监测条件说明：
              </Paragraph>

              <Paragraph>
                <ul>
                  <li>
                    红色预警：连续6个月跑输基准100bps以上（含，业绩按滚动6个月计算）或持续6个月系统综合得分低于30分
                  </li>
                  <li>
                    黄色预警：连续3个月跑输基准75bps以上（含，业绩按滚动6个月计算）或持续3个月系统月度综合得分低于50分
                  </li>
                  <li>
                    蓝色预警：1个月末跑输基准50bps以上（含，业绩按滚动6个月计算）或者系统月度综合得分低于70分
                  </li>
                </ul>
              </Paragraph>
            </div>}
            {defaultType !== 'momcustom' &&
            <Row gutter={8}>
              <Col span={8}>
                <Form.Item
                  label="监测指标"
                  name="quota"
                  rules={[
                    {
                      required: true,
                      message: '请选择监测指标',
                    },
                  ]}
                >
                  {/* <Select
                    showSearch
                    placeholder="监测指标"
                    style={{
                      width: '100%',
                    }}
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {monitoringQuotas.map(item => {
                      return (
                        <Select.Option value={item.dataIndex}>
                          {item.title}
                        </Select.Option>
                      )
                    })}
                  </Select> */}
                  <Cascader
                    showSearch
                    options={monitoringQuotasTreeData}
                    expandTrigger="hover"
                    displayRender={(label) => label[label.length - 1]}
                    onChange={() => { }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  label="条件"
                  name="operator"
                  rules={[
                    {
                      required: true,
                      message: '请选择条件',
                    },
                  ]}
                >
                  <Select
                    placeholder="条件"
                    style={{
                      width: '100%',
                    }}
                  >
                    {Object.keys(conditionTitleMap).map((key) => {
                      return (
                        <Select.Option value={key}>
                          {conditionTitleMap[key]}
                        </Select.Option>
                      )
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  label="蓝色预警值(%)"
                  name="warningValue1"
                  rules={[
                    {
                      required: true,
                      message: '请输入预警值',
                    },
                  ]}
                >
                  <InputNumber
                    placeholder="蓝色预警值"
                    formatter={valueFormatter}
                    style={{
                      width: '100%',
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  label="黄色预警值(%)"
                  name="warningValue2"
                  rules={[
                    {
                      required: true,
                      message: '请输入预警值',
                    },
                  ]}
                >
                  <InputNumber
                    placeholder="黄色预警值"
                    formatter={valueFormatter}
                    style={{
                      width: '100%',
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  label="红色预警值(%)"
                  name="warningValue3"
                  rules={[
                    {
                      required: true,
                      message: '请输入预警值',
                    },
                  ]}
                >
                  <InputNumber
                    placeholder="红色预警值"
                    formatter={valueFormatter}
                    style={{
                      width: '100%',
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>}
            <Form.Item
              name="funds"
              rules={[
                {
                  validator: (rule, values) => {
                    if (!values || values.length === 0) {
                      return Promise.reject('请添加要监控的组合')
                    } else {
                      return Promise.resolve()
                    }
                  },
                },
              ]}
            >
              <FundListFormItem />
            </Form.Item>
            <Form.Item
              label="监测结果接收邮箱"
              name="receiveEmails"
              rules={[
                {
                  validator: (rule, values) => {
                    if (values && values.length && values.some((val) => !isEmail(val))) {
                      return Promise.reject('请输入合法的邮箱')
                    } else {
                      return Promise.resolve()
                    }
                  },
                },
              ]}
            >
              <Select
                mode="tags"
                placeholder="输入邮箱，回车确认"
                style={{
                  width: '100%',
                }}
              />
            </Form.Item>
            <Form.Item
              label="是否开启"
              name="isEnabled"
              valuePropName="checked"
              labelCol={{ span: 2 }}
              wrapperCol={{ span: 12 }}
              style={{ flexDirection: 'row' }}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Form>
        </Spin>
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
