import { navQuotas, monitoringQuotas } from '@/utils/quotas'

const conditionTitleMap = {
  gt: '>',
  gte: '>=',
  lt: '<',
  lte: '<=',
}

export function getMonitoringRuleDesc(rule) {
  const quota = navQuotas.concat(monitoringQuotas).find(item => item.dataIndex === rule.quota) || { dataIndex: rule.quota }
  const condition = conditionTitleMap[rule.operator]
  return {
    title: quota.title || rule.quota,
    condition,
    quota,
  }
}
