import React from 'react'
import {
  Space,
  Divider,
} from 'antd'
import BaseChart from './BaseChart'

export default ({ data, benchmark }) => {
  const charts = [{
    title: '收益率(%)',
    chartSeries: [{
      title: '预期未来1年收益率(%)',
      dataIndex: 'est_ret_d',
    }, {
      title: `${benchmark}未来1年收益率(%)`,
      dataIndex: 'ret_f1y',
    }]
  }]

  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        {charts.map(item => {
          return <BaseChart data={data} title={item.title} chartSeries={item.chartSeries}/>
        })}
      </Space>
      <Divider orientation="left">数据说明</Divider>
      <p>
      在完成对股息率、股本变动、盈利增速以及估值变化四个分项的建模后，我们可以根据在完成对股息率、股本变动、盈利增速以及估值变化四个分项的建模后，我们可以根据Ferreira and Santa Clara(2011)股票收益对数加法模型对宽基指数未来一年的收益进行加总和预测，其中细节如下：
      </p>
      <p>1) 预期股息率：直接采用Wind提供的指数股息率(过去12个月)；</p>
      <p>2) 预期股本稀释率：基于再融资政策和ERP的股本稀释率预测模型进行预测（由于股本稀释率计算较为繁琐，近似的做法是用指数过去十年的平均稀释率替代，比如沪深300平均稀释率为2%，中证500平均稀释率为3%；</p>
      <p>3) 预期盈利增速：采用自上而下宏观方法和自下而上分析师方法的预测平均值；</p>
      <p>4) 预期估值变化：根据调整后的ERP对指数未来一年估值进行预测；</p>
      <p>5) 指数预期收益：指数未来一年预期收益 =e^((预期股息率-预期股本稀释率+预期盈利增速+预期估值变化) )-1</p>
    </div>
  )
}
