import React, { useState } from 'react'
import {
  Card,
  Affix,
  Tabs,
  Spin,
  Breadcrumb,
} from 'antd'
import router from 'umi/router'
import Link from 'umi/link'
import SearchSelect from '@/components/SearchSelect'
import { useRequest } from '@umijs/hooks'
import { queryAShareRetEstData } from './service'
import YoYProfit from './YoYProfit'
import YoYPE from './YoYPE'
import YoYRet from './YoYRet'

const { TabPane } = Tabs

const Wrapper = (props) => {
  const bemchmarkList = [{
    title: '沪深300',
    dataIndex: '000300.SH',
  }, {
    title: '沪深300(去金融地产)',
    dataIndex: '000299.SH',
  }]
  const [benchmark, setBenchmark] = useState(bemchmarkList[0].dataIndex)
  const { loading, data = [] } = useRequest(() => {
    return queryAShareRetEstData({ benchmark })
  }, {
    refreshDeps: [benchmark]
  })
  const tabs = [{
    name: '盈利增速预测',
    tab: 'yoyprofit',
  }, {
    name: '估值变化预测',
    tab: 'yoype',
  }, {
    name: '收益率预测',
    tab: 'yoyret',
  }, {
    name: '宏观框架',
    tab: 'framework'
  }]
  const [activeTab, setActiveTab] = useState(props.location.query.tab || tabs[0].tab)
  const handleTabChange = currentTab => {
    setActiveTab(currentTab)
  }
  const benchmarkName = bemchmarkList.find(item => item.dataIndex === benchmark).title
  const getTabContent = () => {
    if (activeTab === 'yoyprofit') {
      return <YoYProfit data={data} benchmark={benchmarkName}/>
    } else if (activeTab === 'yoype') {
      return <YoYPE data={data} benchmark={benchmarkName}/>
    } else if (activeTab === 'yoyret') {
      return <YoYRet data={data} benchmark={benchmarkName}/>
    } else if (activeTab === 'framework') {
      router.push(`/research/macrostrategy/framework/${props.location.query.id}`)
      return
    }
    return null
  }
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>宏观策略研究</Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link to={'/research/macrostrategy/framework'}>宏观框架</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{props.location.query.name}</Breadcrumb.Item>
      </Breadcrumb>
      <Affix offsetTop={0}>
        <Card className="nav-tab-wrapper">
          <Tabs
            animated={false}
            activeKey={activeTab}
            onTabClick={handleTabChange}
            tabBarExtraContent={
              <SearchSelect
                value={benchmark}
                options={bemchmarkList}
                onChange={setBenchmark}
                width="150px"
              />
            }
          >
            {tabs.map(item => {
              return (
                <TabPane tab={item.name} key={item.tab} />
              )
            })}
          </Tabs>
        </Card>
      </Affix>
      <div>
        <Spin spinning={loading}>
          {getTabContent()}
        </Spin>
      </div>
    </div>
  )
}

export default Wrapper
