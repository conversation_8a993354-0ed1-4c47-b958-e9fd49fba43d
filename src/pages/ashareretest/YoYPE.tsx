import React from 'react'
import {
  Space,
  Divider,
} from 'antd'
import BaseChart from './BaseChart'

export default ({ data, benchmark }) => {
  const charts = [{
    title: '估值变化率(%)',
    chartSeries: [{
      title: '预期估值变化率(%)',
      dataIndex: 'est_yoy_pe_d',
    }, {
      title: `${benchmark}未来1年估值变化率(%)`,
      dataIndex: 'yoy_pe_f1y',
    }]
  }]
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        {charts.map(item => {
          return <BaseChart data={data} title={item.title} chartSeries={item.chartSeries}/>
        })}
      </Space>
      <Divider orientation="left">数据说明</Divider>
      <p>沪深300估值调整的核心有二：</p>
      <p>
      第一是历史问题，金融行业占比过高并出现估值中枢的永久性下移；
      </p>
      <p>第二是未来的问题，在外资持续流入的背景下，需要考虑外资的持有成本。经金融结构与外资持有调整后，沪深300的ERP与沪深300未来一年估值变化一直保持着很强的相关性，所以我们考虑用300除金融指数的估值对沪深300指数2012年前的估值序列进行插补，构建回归模型如下：</p>
      <p>
      ∆PE=α+β∙(ERP+综合机会成本)+ε, 其中：
      </p>
      <p>
      ERP=EP/r=1⁄((PE_TTM∙r) )=1⁄((指数PE_TTM∙到期收益率中债十年期国债) )
      </p>
      <p>
      综合机会成本=(1-沪深300指数的外资持股比例)∙10年期国债收益率+沪深300指数的外资持股比例∙(10年期美债收益率+掉期成本)
      </p>
    </div>
  )
}
