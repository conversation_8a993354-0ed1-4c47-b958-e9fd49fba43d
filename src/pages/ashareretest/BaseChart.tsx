import React from 'react'
import _ from 'lodash'
import {
  Card,
} from 'antd'
import Echart from '@/components/Chart/Echarts'

const getChartOptions = (data, chartSeries) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      // axisPointer: {
      //   type: 'shadow',
      // },
    },
    legend: {
      data: chartSeries.map(item => item.title),
    },
    xAxis: [
      {
        type: 'category',
        data: data.map(item => item.the_month),
      },
    ],
    yAxis: [{
      type: 'value',
    }, {
      type: 'value',
    }],
    series: chartSeries.map(serie => {
      return {
        name: serie.title,
        type: 'line',
        yAxisIndex: serie.yAxisIndex,
        data: data.map(item => {
          const val = item[serie.dataIndex]
          if (val === null) {
            return  null
          }
          return _.round((val || 0) * 100, 2)
        }),
        smooth: false,
      }
    }),
  }
  return option
}

const BaseChart = ({ title, data, chartSeries}) => {
  const options = getChartOptions(data, chartSeries)
  return (
    <Card
      title={title}
    >
      <Echart style={{ height: '400px' }} options={options} />
    </Card>
  )
}

export default BaseChart
