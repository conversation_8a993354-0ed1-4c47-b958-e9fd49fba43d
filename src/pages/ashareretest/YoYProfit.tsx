import React from 'react'
import {
  Space,
  Divider,
} from 'antd'
import BaseChart from './BaseChart'

export default ({ data, benchmark }) => {
  const charts = [{
    title: '盈利增速(%)',
    chartSeries: [{
      title: '自上而下法',
      dataIndex: 'est_yoyprofit',
    }, {
      title: `${benchmark}未来1年盈利增速(%)`,
      dataIndex: 'yoyprofit_ftm',
    }]
  }, {
    title: '盈利增速(%)',
    chartSeries: [{
      title: '自下而上法',
      dataIndex: 'est_yoyprofit_ftm',
    }, {
      title: `${benchmark}未来1年盈利增速(%)`,
      dataIndex: 'yoyprofit_ftm',
    }]
  }, {
    title: '盈利增速(%)',
    chartSeries: [{
      title: '两者结合法',
      dataIndex: 'est_yoyprofit_mean',
    }, {
      title: `${benchmark}未来1年盈利增速(%)`,
      dataIndex: 'yoyprofit_ftm',
    }]
  }]
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        {charts.map(item => {
          return <BaseChart data={data} title={item.title} chartSeries={item.chartSeries}/>
        })}
      </Space>
      <Divider orientation="left">数据说明</Divider>
      <p>
      一、自上而下法：中枢与周期
      </p>
      <p>
      原理是柯布-道格拉斯生产函数，即企业盈利增速长期具有明显的均值回归效应，但短期又会被货币驱动从而离开中枢，因此自上而下宏观方法的核心在于判断长期增长中枢与短期增长波动，我们从企业内生增长的逻辑出发，认为企业盈利增速将长期收敛于ROE∙(1-派息率)，因此构建如下模型：
      </p>
      <p>
      ∆g=a∙(ROE∙(1-派息率)-g)+ β∙信贷脉冲 +ε
      </p>
      <p>其中信贷脉冲 = 社融增量占比的同比变化</p>
      <p>
      优点是：与宏观接洽，有丰富的理论模型，缺点是：无法处理宏观敏感型弱的指数
      </p>
      <p>
      二、自下而上法：分析师一致预期
      </p>
      <p>
      原理是利用分析师对企业的深度信息。
      </p>
      <p>
      优点是：可拓展性强，兼容宽基和行业。缺点是：分析师的一致预期普遍高估。
      </p>
      <p>
      三、两者结合法：取自上而下和自下而上方法的均值。
      </p>
    </div>
  )
}
