import React, { useState } from 'react'
import {
  Card,
  Table,
  Row,
  Col,
  Divider,
} from 'antd'
import _ from 'lodash'
import buildTableColumn from '@/utils/buildTableColumn'
import SelectDate from '@/components/SelectDate'
import HeatMap from '@/components/HeatMap'

export default ({
  data,
  dates,
}: {
  data: any,
  dates: any,
}) => {
  const [date, setDate] = useState(dates[0])
  const companyData = data.filter(item => item.the_date === date)
  const columns = [{
    title: '基金公司',
    dataIndex: 'company_abbr_name',
  }, {
    title: '主动偏股管理规模(亿)',
    dataIndex: 'asset_scale',
    format: 'number',
    align: 'right',
    hasSorter: true,
    width: 180,
  }, {
    title: '规模占比',
    dataIndex: 'asset_scale_pct',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
    width: 90,
  }].map(buildTableColumn)
  const chartData = companyData.map(item => {
    return {
      id: item.company_abbr_name,
      name: item.company_abbr_name,
      value: [item.asset_scale_pct * 100, item.asset_scale_pct * 100],
    }
  })
  const comments = [
    '仅统计主动股基（wind分类下普通股票型+混合型基金）',
    '在统计日期之前，已发行18个月以上',
    '过往一年权益仓位平均高于 30% 或最高仓位高于 50%'
  ]
  return (
    <Card
      title="基金公司主动偏股产品规模"
      extra={
        <SelectDate
          date={date}
          dates={dates}
          onChange={setDate}
        />
      }
    >
      <Row gutter={16}>
        <Col lg={16} md={24}>
          <HeatMap name="规模占比" data={chartData}/>
        </Col>
        <Col lg={8} md={24}>
          <Table
            pagination={false}
            columns={columns}
            dataSource={companyData}
            size="small"
            scroll={{ y: 415 }}
          />
        </Col>
      </Row>
      <Divider orientation="left">数据说明</Divider>
      {comments.map((p, index) => <p>{index+1}、{p}</p>)}
    </Card>
  )
}
