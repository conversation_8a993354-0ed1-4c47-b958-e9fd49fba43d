import React, { useState } from 'react'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
  Card,
  Row,
  Col,
  Table,
  Divider,
  Space,
  Radio,
} from 'antd'
import { queryIndustryData } from '../service'
import Echart from '@/components/Chart/Echarts'
import buildTableColumn from '@/utils/buildTableColumn'
import SelectDate from '@/components/SelectDate'

const getChartOptions = (companyData, coef) => {
  const data = companyData.map(item => [
    item.company_abbr_name,
    item.ret_1y * 100,
    item.fund_asset,
    item.fund_abbr_name,
  ])
  const option = {
    tooltip: {
      formatter: function (obj) {
        const value = obj.value
        if (!value) {
          return ''
        }
        if (obj.componentType === 'markPoint') {
          return `${obj.name}：${value}%`
        }
        return '<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'
            + value[3]
            + '</div>'
            + '产品规模:  ' + _.round(value[2], 2) + '亿<br>'
            + '近1年收益率:  ' + _.round(value[1], 2) + '%<br>'
      },
    },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '3%',
      top: '6%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        axisLabel: {
          interval: 0,
          rotate: 40,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '收益率(%)',
      },
    ],
    series: {
      type: 'scatter',
      symbolSize: function (val) {
        return val[2] * coef
      },
      data,
      itemStyle: {
        // color: '#7d6440',
        // borderColor: '#E49831',
        color: '#181f29',
        borderColor: '#E49831',
        borderType: "solid",
        borderWidth: 2,
        // opacity: 0,
      },
    },
  }

  return option
}

const EquityInvest = ({
  dates,
}) => {
  const classSizeList = [{
    title: '大规模',
    dataIndex: 'large',
    comments: [
      '仅统计主动股基（wind 分类下普通股票型+混合型基金：在统计日期之前，已发行18个月以上；过往一年权益仓位平均高于 30% 或最高仓位高于 50%）',
      '大规模组：基金公司主动股基管理规模>600亿',
      '气泡高度为近1年业绩，从左到右顺序为管理规模，气泡直径为对应产品管理规模'
    ],
    coef: 0.15
  }, {
    title: '中规模',
    dataIndex: 'middle',
    comments: [
      '仅统计主动股基（wind 分类下普通股票型+混合型基金：在统计日期之前，已发行18个月以上；过往一年权益仓位平均高于 30% 或最高仓位高于 50%）',
      '中规模组：基金公司主动股基管理规模属于200-600亿',
      '气泡高度为近1年业绩，从左到右顺序为公司整体业绩（平均规模加权）从高到低，气泡直径为对应产品管理规模'
    ],
    coef: 0.4
  }, {
    title: '小规模',
    dataIndex: 'small',
    comments: [
      '仅统计主动股基（wind 分类下普通股票型+混合型基金：在统计日期之前，已发行18个月以上；过往一年权益仓位平均高于 30% 或最高仓位高于 50%）',
      '小规模组：基金公司主动股基管理规模属于50-200亿',
      '气泡高度为近1年业绩，从左到右顺序为公司整体业绩（平均规模加权）从高到低，气泡直径为对应产品管理规模'
    ],
    coef: 1.2,
  }]
  const [classSize, setClassSize] = useState(classSizeList[0])
  const handleClassSizeChange = event => {
    const key = event.target.value
    const current = classSizeList.find(item => item.dataIndex === key) || {}
    setClassSize(current)
  }
  const [date, setDate] = useState(dates[0])
  const { loading, data = [] } = useRequest(() => {
    return queryIndustryData({ dataKey: 'companyFundRet', bizDate: date, classSize: classSize.dataIndex })
  }, {
    refreshDeps: [date, classSize.dataIndex],
  })
  const companies = _.uniq(data.map(item => item.company_abbr_name))
  const columns = [{
    title: '报告期',
    width: 110,
    dataIndex: 'the_date',
  }, {
    title: '基金公司',
    width: 150,
    dataIndex: 'company_abbr_name',
    filters: companies.map(item => {
      return {
        text: item,
        value: item,
      }
    }),
    onFilter: (value, record) => record.company_abbr_name === value,
  }, {
    title: '基金名称',
    dataIndex: 'fund_abbr_name',
  }, {
    title: '产品规模(亿)',
    dataIndex: 'fund_asset',
    format: 'number',
    align: 'right',
    hasSorter: true,
    width: 150,
  }, {
    title: '近1年收益率',
    dataIndex: 'ret_1y',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
    width: 150,
  }].map(buildTableColumn)
  const options = getChartOptions(data, classSize.coef)
  return (
    <div>
      <Spin spinning={loading}>
        <Card
          title="基金公司权益投资分析"
          extra={
            <Space>
              <Radio.Group
                value={classSize.dataIndex}
                size="small"
                onChange={handleClassSizeChange}
              >
                {classSizeList.map(item => <Radio.Button value={item.dataIndex} key={item.dataIndex}>{item.title}</Radio.Button>)}
              </Radio.Group>
              <SelectDate
                date={date}
                dates={dates}
                onChange={setDate}
              />
            </Space>
          }
        >
          <Row gutter={16}>
            <Col lg={24} md={24}>
              <Echart style={{ height: '600px' }} options={options} />
            </Col>
            <Col lg={24} md={24}>
              <Table
                pagination={false}
                columns={columns}
                dataSource={data}
                size="small"
                scroll={{ y: 415 }}
              />
            </Col>
          </Row>
          <Divider orientation="left">数据说明</Divider>
          {classSize.comments.map((p, index) => <p>{index+1}、{p}</p>)}
        </Card>
      </Spin>
    </div>
  )
}

export default EquityInvest
