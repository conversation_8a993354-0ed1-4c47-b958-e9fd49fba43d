import React from 'react'
import _ from 'lodash'
import moment from 'moment'
import {
  Card,
} from 'antd'
import Echart from '@/components/Chart/Echarts'

const getChartOptions = (data, benchmarkData, benchmarkName) => {
  const option = {
    dataZoom: [{
      type: 'slider',
    }],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const param = params[0]
        const param2 = params[1]
        return [
          param.name + ': ',
          '最大值: ' + _.round(param.data[5], 2) + '%',
          '3/4分位数: ' + _.round(param.data[4], 2) + '%',
          '中位数: ' + _.round(param.data[3], 2) + '%',
          '1/4分位数: ' + _.round(param.data[2], 2) + '%',
          '最小值: ' + _.round(param.data[1], 2) + '%',
          benchmarkName + '收益率: ' + param2.data + '%',
        ].join('<br/>')
      },
    },
    xAxis: {
      type: 'category',
      data: data.boxplotData.axisData,
      boundaryGap: true,
      nameGap: 30,
      splitArea: {
        show: false,
      },
      axisLabel: {
        formatter: '{value}',
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [{
      type: 'value',
      name: '收益率',
      // splitArea: {
      //   show: true
      // },
      // min: 'dataMin',
      axisLabel: {
        formatter:function (value) {
         return value.toFixed(1)
        },
      },
    },
    // {
    //   type: 'value',
    //   name: '沪深300',
    //   min: 'dataMin',
    // }
    ],
    series: [
      {
        name: 'boxplot',
        type: 'boxplot',
        itemStyle: {
          color: '#E49831',
          borderColor: '#905304',
        },
        data: data.boxplotData.boxData,
      },
      // {
      //   name: 'outlier',
      //   type: 'scatter',
      //   data: data.boxplotData.outliers,
      // },
      {
        name: '沪深300收益率',
        type: 'line',
        data: data.boxplotData.axisData.map(date => {
          return _.round((benchmarkData[date] || 0) * 100, 2)
        }),
        smooth: false,
        showSymbol: false,
        lineStyle: {
          width: 1,
          color: '#0EBF9C',
        },
      },
    ],
  }

  return option
}

export default ({
  title,
  data,
  benchmarkData,
  benchmarkName,
  disableCard,
}: {
  data: any,
  title?: string,
  benchmarkData: any,
  benchmarkName?: string,
  disableCard?: boolean,
}) => {
  const benchmarkDataMap = benchmarkData.reduce((out, item) => {
    out[moment(item.date).format('YYYY-MM-DD')] = item.ret
    return out
  }, {})
  const options = getChartOptions(data, benchmarkDataMap, benchmarkName)
  if (disableCard) {
    return <Echart style={{ height: '500px' }} options={options} />
  }
  return (
    <div>
      <Card
        title={`${title}与${benchmarkName}收益率`}
      >
        <Echart style={{ height: '500px' }} options={options} />
      </Card>
    </div>
  )
}
