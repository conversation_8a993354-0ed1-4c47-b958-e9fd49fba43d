import React from 'react'
import _ from 'lodash'
import {
  Card,
  Tooltip,
} from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import Echart from '@/components/Chart/Echarts'

const getChartOptions = (data) => {
  const series = data.styleData.map(item => {
    return {
      name: item.styleType,
      type: 'scatter',
      data: item.data.map((d, index) => {
        return [
          _.round(d.vol * 100, 2), _.round(d.ret * 100, 2), index, d.date,
        ]
      }),
    }
  })
  const options = {
    legend: {
      data: data.styleData.map(item => item.styleType),
    },
    tooltip: {
      padding: 10,
      borderWidth: 1,
      axisPointer: {
        type: 'cross',
      },
      formatter: function (obj) {
        const value = obj.value
        if (!value) {
          return ''
        }
        if (obj.componentType === 'markPoint') {
          return `${obj.name}：${value}%`
        }
        return '<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'
            + obj.seriesName
            + '</div>'
            + '日期：' + value[3] + '<br>'
            + '波动率：' + value[0] + '%<br>'
            + '收益率：' + value[1] + '%<br>'
      },
    },
    xAxis: {
      type: 'value',
      name: '波动率',
      axisLabel: {
        formatter: '{value}',
      },
    },
    yAxis: {
      type: 'value',
      name: '收益率',
      axisLabel: {
        formatter: '{value}',
      },
      axisPointer: {
        snap: true,
      },
    },
    visualMap: [{
      show: false,
      right: '0%',
      orient: 'horizontal',
      top: '0%',
      dimension: 2,
      itemWidth: 10,
      itemHeight: 40,
      calculable: true,
      precision: 0.01,
      min: 0,
      max: data.styleData[0] && data.styleData[0].data.length,
      text: ['日期'],
      textGap: 30,
      textStyle: {
        color: '#fff',
      },
      inRange: {
        symbolSize: [15, 60],
      },
      outOfRange: {
        symbolSize: [15, 60],
        color: ['rgba(255,255,255,.2)'],
      },
      controller: {
        inRange: {
          color: ['#0EBF9C'],
        },
        outOfRange: {
          color: ['#444'],
        },
      },
    }],
    series,
  }

  return options
}

export default ({
  title,
  data,
}: {
  data: any,
  title: string,
}) => {
  const options = getChartOptions(data)
  return (
    <div>
      <Card
        title={
          <span>
            {title}
            <Tooltip title="横轴波动率，纵轴收益率，日期越近气泡越大" placement="right">
              <span style={{ marginLeft: '5px' }}>
                <QuestionCircleOutlined />
              </span>
            </Tooltip>
          </span>
        }
      >
        <Echart style={{ height: '500px' }} options={options} />
      </Card>
    </div>
  )
}
