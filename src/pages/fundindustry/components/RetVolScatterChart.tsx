import React from 'react'
import _ from 'lodash'
import {
  Card,
} from 'antd'
import Echart from '@/components/Chart/Echarts'

const getChartOptions = (data, benchmarkData, benchmarkName) => {
  const latestData = _.maxBy(benchmarkData, 'date') || {}
  const option = {
    xAxis: {
      scale: true,
      name: '波动率',
    },
    yAxis: {
      scale: true,
      name: '收益率',
    },
    tooltip: {
      padding: 10,
      // backgroundColor: '#222',
      // borderColor: '#777',
      borderWidth: 1,
      axisPointer: {
        type: 'cross',
      },
      formatter: function (obj) {
        const value = obj.value
        if (!value) {
          return ''
        }
        if (obj.componentType === 'markPoint') {
          return `${obj.name}：${value}%`
        }
        return '<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'
            + value[2]
            + '</div>'
            + '波动率：' + _.round(value[0], 2) + '%<br>'
            + '收益率：' + _.round(value[1], 2) + '%<br>'
      },
    },
    series: [{
      type: 'scatter',
      data: data.map(item => [item.vol * 100, item.ret * 100, item.fund_name]),
    }, {
      type: 'effectScatter',
      symbolSize: 20,
      data: [
        [_.mean(data.map(item => item.vol * 100)), _.mean(data.map(item => item.ret * 100)), '基金均值'],
      ],
    }, {
      type: 'effectScatter',
      symbolSize: 20,
      data: [
        [latestData.vol * 100, latestData.ret * 100, benchmarkName],
      ],
    }],
  }

  return option
}

export default ({
  title,
  data,
  benchmarkData,
  benchmarkName,
  disableCard,
}: {
  data: any,
  benchmarkData: any,
  title?: string,
  benchmarkName?: string,
  disableCard?: boolean,
}) => {
  const options = getChartOptions(data.filter(item => item.vol), benchmarkData, benchmarkName)
  if (disableCard) {
    return <Echart style={{ height: '500px' }} options={options} />
  }
  return (
    <div>
      <Card
        title={title}
      >
        <Echart style={{ height: '500px' }} options={options} />
      </Card>
    </div>
  )
}
