import React from 'react'
import {
  Card,
} from 'antd'
import Echart from '@/components/Chart/Echarts'

const getChartOptions = (data, valueKey) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: data.statsData.map(item => item.className),
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: data.dates,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: valueKey === 'num' ? '数量(个)' : '规模(亿)',
      },
    ],
    series: data.statsData.map(item => {
      return {
        name: item.className,
        type: 'bar',
        stack: 'stack',
        data: item.dateValues.map(val => val[valueKey]),
      }
    }),
  }

  return option
}

export default ({
  title,
  data,
  valueKey,
}: {
  data: any,
  title: string,
  valueKey: string,
}) => {
  const options = getChartOptions(data, valueKey)
  return (
    <div>
      <Card
        title={title}
      >
        <Echart style={{ height: '500px' }} options={options} />
      </Card>
    </div>
  )
}
