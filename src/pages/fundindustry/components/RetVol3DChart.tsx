import React from 'react'
import _ from 'lodash'
import {
  Card,
} from 'antd'
import Echart from '@/components/Chart/Echarts'

const chartAxisStyle = {
  "axisLine": {
    "show": true,
    "lineStyle": {
      "color": "#666666",
    },
  },
  "axisTick": {
    "show": false,
    "lineStyle": {
      "color": "#333",
    },
  },
  "axisLabel": {
    "show": true,
    "textStyle": {
      "color": "#aaaaaa",
    },
  },
  "splitLine": {
    "show": true,
    "lineStyle": {
      "color": [
        "#e6e6e6",
      ],
    },
  },
  "splitArea": {
    "show": false,
    "areaStyle": {
      "color": [
        "rgba(250,250,250,0.05)",
        "rgba(200,200,200,0.02)",
      ],
    },
  },
}

const getChartOptions = (data) => {
  const option = {
    grid3D: {},
    xAxis3D: {
      name: '日期',
      type: 'category',
      ...chartAxisStyle,
    },
    yAxis3D: {
      name: '收益率',
      ...chartAxisStyle,
    },
    zAxis3D: {
      name: '波动率',
      ...chartAxisStyle,
    },
    legend: {
      data: data.styleData.map(item => item.styleType),
    },
    tooltip: {
      formatter: function (param) {
        return [
          param.marker + param.seriesName,
          '日期: ' + param.data[0],
          '收益率: ' + _.round(param.data[1], 2) + '%',
          '波动率: ' + _.round(param.data[2], 2) + '%',
        ].join('<br/>')
      },
    },
    series: data.styleData.map(item => {
      return {
        name: item.styleType,
        type: 'scatter3D',
        symbolSize: 15,
        data: item.data.map(d => {
          return [
            d.date, _.round(d.ret * 100, 2), _.round(d.vol * 100, 2),
          ]
        }),
      }
    }),
  }

  return option
}

export default ({
  title,
  data,
}: {
  data: any,
  title: string,
}) => {
  const options = getChartOptions(data)
  return (
    <div>
      <Card
        title={title}
      >
        <Echart style={{ height: '500px' }} options={options} />
      </Card>
    </div>
  )
}
