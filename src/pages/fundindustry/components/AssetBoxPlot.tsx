import React from 'react'
import _ from 'lodash'
import {
  Card,
} from 'antd'
import Echart from '@/components/Chart/Echarts'

const getChartOptions = (data) => {
  const option = {
    grid: {
      left: '8%',
      right: '8%',
    },
    xAxis: {
      type: 'category',
      data: data.boxplotData.axisData,
      boundaryGap: true,
      nameGap: 30,
      splitArea: {
        show: false,
      },
      axisLabel: {
        formatter: '{value}',
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [{
      type: 'value',
      name: '收益率',
      // splitArea: {
      //   show: true
      // },
      // min: 'dataMin',
    }, {
      type: 'value',
      name: '数量(个)',
      // min: 'dataMin',
    }],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const param = params[0]
        const param2 = params[1]
        return [
          param.name + ': ',
          '最大值: ' + _.round(param.data[5], 2) + '%',
          '3/4分位数: ' + _.round(param.data[4], 2) + '%',
          '中位数: ' + _.round(param.data[3], 2) + '%',
          '1/4分位数: ' + _.round(param.data[2], 2) + '%',
          '最小值: ' + _.round(param.data[1], 2) + '%',
          '数量: ' + param2.data,
        ].join('<br/>')
      },
    },
    series: [
      {
        name: 'boxplot',
        type: 'boxplot',
        data: data.boxplotData.boxData,
        itemStyle: {
          // color: '#7d6440',
          // borderColor: '#E49831',
          color: '#E49831',
          borderColor: '#905304',
        },
      },
      // {
      //   name: 'outlier',
      //   type: 'scatter',
      //   data: data.boxplotData.outliers,
      // },
      {
        name: '数量',
        type: 'line',
        data: data.numData,
        smooth: false,
        showSymbol: false,
        yAxisIndex: 1,
        lineStyle: {
          width: 1,
          color: '#0EBF9C',
        },
      },
    ],
  }

  return option
}

export default ({
  title,
  data,
}: {
  data: any,
  title: string,
}) => {
  const options = getChartOptions(data)
  return (
    <div>
      <Card
        title={title}
      >
        <Echart style={{ height: '350px' }} options={options} />
      </Card>
    </div>
  )
}
