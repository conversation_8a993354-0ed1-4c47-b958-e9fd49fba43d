import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Space,
  Spin,
  Row,
  Col,
  Card,
  Select,
  Affix,
} from 'antd'
import moment from 'moment'
import { queryStyleData, queryNewFundData } from './service'
import NewFundStatChart from './components/NewFundStatChart'
import RetVolScatterChart from './components/RetVolScatterChart'
import StyleBoxPlot from './components/StyleBoxPlot'
import AssetBoxPlot from './components/AssetBoxPlot'
import RetVolSeriesScatterChart from './components/RetVolSeriesScatterChart'

const { Option } = Select

const periods = [
  {
    value: 'M_M_A',
    name: '月频1月',
  },
  {
    value: 'M_3M_A',
    name: '月频3月',
  },
  {
    value: 'M_6M_A',
    name: '月频6月',
  },
  {
    value: 'M_12M_A',
    name: '月频12月',
  },
  {
    value: 'M_2Y_A',
    name: '月频2年',
  },
  {
    value: 'M_3Y_A',
    name: '月频3年',
  },
]
const factorTypes = [{
  name: '收益率',
  value: 'FdRet',
}, {
  name: '相对分类基准收益率',
  value: 'FdRltExcsRet',
}]

const factorList = factorTypes.reduce((out, factorType) => {
  const factors = periods.map(p => {
    return {
      title: `${factorType.name}${p.name}`,
      dataIndex: `${factorType.value}_${p.value}`,
    }
  })
  return out.concat(factors)
}, [])

const List = () => {
  const [current, setCurrent] = useState(factorList[0])
  const { loading, data: styleData = {
    funds: [], assetStyleData: [],
    assetSummaryData: [], styleSeriesData: [],
    benchmarkData: [],
    stockBenchmark: [],
    bondBenchmark: [],
  } } = useRequest(() => {
    return queryStyleData({ factor: current.dataIndex })
  }, {
    refreshDeps: [current],
  })
  const { loading: loadingNewFunds, data: newFunds = {} } = useRequest(() => {
    return queryNewFundData()
  })
  const handleChange = factorCode => {
    const factor = factorList.find(item => item.dataIndex === factorCode) || {}
    setCurrent(factor)
  }
  const emptyNewFunds = {
    dates: [],
    statsData: [],
  }
  const funds = styleData.funds
  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Affix offsetTop={0}>
          <Card
            title={`基金行业（数据截止日期：${moment(styleData.maxDate).format('YYYY-MM-DD')}）`}
            className="empty-card"
            extra={
              <Select
                showSearch
                placeholder="选择因子"
                style={{
                  // marginTop: '5px',
                  width: '250px',
                }}
                value={current.dataIndex}
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                onChange={handleChange}
              >
                {factorList.map(item => {
                  return (
                    <Option value={item.dataIndex}>
                      {item.title}
                    </Option>
                  )
                })}
              </Select>
            }
          />
        </Affix>
        <Spin spinning={loading}>
          <Row style={{ minHeight: 350 }}>
            {styleData.assetSummaryData.map(item => {
              return (
                <Col lg={8} md={8} sm={24}>
                  <AssetBoxPlot title={`${item.assetType}收益率与数量`} data={item} />
                </Col>
              )
            })}
          </Row>
        </Spin>
        {styleData.styleSeriesData.map(item => {
          const isBond = ['信用型', '久期型', '平衡型'].includes(item.styleType)
          return (
            <Spin spinning={loading}>
              <StyleBoxPlot
                title={`${item.styleType}收益率`}
                benchmarkName={isBond ? '中债新综合' : '沪深300'}
                data={item} benchmarkData={isBond ? styleData.bondBenchmark : styleData.stockBenchmark}
              />
            </Spin>
          )
        })}
        <Spin spinning={loading}>
          <RetVolScatterChart title="股票型基金收益率波动率散点图" benchmarkName="沪深300" benchmarkData={styleData.stockBenchmark} data={funds.filter(item => item.assetType === '股票型')} />
        </Spin>
        <Spin spinning={loading}>
          <RetVolScatterChart title="配置型基金收益率波动率散点图" benchmarkName="沪深300" benchmarkData={styleData.stockBenchmark} data={funds.filter(item => item.assetType === '配置型')} />
        </Spin>
        <Spin spinning={loading}>
          <RetVolScatterChart title="债券型基金收益率波动率散点图" benchmarkName="中债新综合" benchmarkData={styleData.bondBenchmark} data={funds.filter(item => item.assetType === '债券型')} />
        </Spin>
        {styleData.assetStyleData.map(item => {
          return (
            <Spin spinning={loading}>
              <RetVolSeriesScatterChart title={`${item.assetType}收益率波动率时序图`} data={item} />
            </Spin>
          )
        })}
        <NewFundStatChart title="近2年WIND投资分类 二级分类 新发基金成立数量" data={newFunds.class2Data || emptyNewFunds} valueKey="num"/>
        <NewFundStatChart title="近2年WIND投资分类 二级分类 新发基金成立规模" data={newFunds.class2Data || emptyNewFunds} valueKey="netAsset"/>
        <NewFundStatChart title="近2年WIND投资分类 三级分类 新发基金成立数量" data={newFunds.class3Data || emptyNewFunds} valueKey="num"/>
        <NewFundStatChart title="近2年WIND投资分类 三级分类 新发基金成立规模" data={newFunds.class3Data || emptyNewFunds} valueKey="netAsset"/>
      </Space>
    </div>
  )
}

export default List
