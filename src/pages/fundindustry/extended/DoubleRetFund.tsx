import React from 'react'
import {
  Card,
  Table,
  Row,
  Col,
} from 'antd'
import Echart from '@/components/Chart/Echarts'
import moment from 'moment'
import buildTableColumn from '@/utils/buildTableColumn'
import _ from 'lodash'

const getChartOptions = (data, columns) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '6%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: columns.map(item => item.title),
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '占比(%)',
      },
    ],
    series: {
      name: '占比',
      type: 'bar',
      data: columns.map(col => _.round((data[col.dataIndex] || 0) * 100, 2)),
    },
  }

  return option
}

export default ({
  data,
}: {
  data: any,
}) => {
  const { latestDoubleRet, doubleRetFundDetail } = data
  const latestDate = moment(doubleRetFundDetail[0] && doubleRetFundDetail[0].the_date).format('YYYY-MM-DD')
  const serieCols = [{
    title: '一年一倍',
    dataIndex: 'f_doubleretpct_1y',
  }, {
    title: '三年一倍',
    dataIndex: 'f_doubleretpct_3y',
  }, {
    title: '五年一倍',
    dataIndex: 'f_doubleretpct_5y',
  }]
  const columns = [{
    title: '截止日期',
    dataIndex: 'the_date',
    format: 'date',
  }, {
    title: '类型',
    dataIndex: 'statistic_type',
    filters: serieCols.map(item => {
      return {
        text: item.title,
        value: item.title,
      }
    }),
    onFilter: (value, record) => record.statistic_type === value,
  }, {
    title: '基金',
    dataIndex: 'fund_name',
  }, {
    title: '收益率',
    dataIndex: 'ret',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  }, {
    title: '排名',
    dataIndex: 'ret_rank',
    align: 'right',
    hasSorter: true,
  }, {
    title: '基金经理',
    dataIndex: 'manager_name',
  }, {
    title: '基金公司',
    dataIndex: 'company_abbr_name',
  },].map(buildTableColumn)
  const options = getChartOptions(latestDoubleRet[0] || {}, serieCols)
  return (
    <div>
      <div style={{ marginTop: 15 }}/>
      <Row gutter={16}>
        <Col lg={8} md={24}>
          <Card
            title={`截止 ${latestDate}, 近1年,3年,5年收益翻倍基金占比`}
          >
            <Echart style={{ height: '450px' }} options={options} />
          </Card>
        </Col>
        <Col lg={16} md={24}>
          <Card
            title="近1年,3年,5年收益翻倍基金 TOP5"
          >
            <Table
              pagination={false}
              columns={columns}
              dataSource={doubleRetFundDetail}
              size="small"
              scroll={{ y: 415 }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}
