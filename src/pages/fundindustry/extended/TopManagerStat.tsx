import React, { useState } from 'react'
import {
  Card,
  Table,
  Radio,
  Row,
  Col,
} from 'antd'
import Echart from '@/components/Chart/Echarts'
import moment from 'moment'
import buildTableColumn from '@/utils/buildTableColumn'
import _ from 'lodash'

const getChartOptions = (data, columns) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: data.map(item => item.name),
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '6%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: columns.map(item => item.title),
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '基金经理个数',
      },
    ],
    series: data.map(item => {
      return {
        name: item.name,
        type: 'bar',
        data: columns.map(col => _.round((item[col.dataIndex] || 0), 0)),
      }
    }),
  }

  return option
}

export default ({
  data,
}: {
  data: any,
}) => {
  const fundTypesMap = {
    Archimedes: ['纯股型基金', '配置型基金', '纯债型基金'],
    Wind: ['股票型基金', '混合型基金', '债券型基金']
  }
  const [typeSource, setTypeSource] = useState('Archimedes')
  const handleTypeChange = event => {
    setTypeSource(event.target.value)
  }
  const { topRetManagerDetail } = data
  const latestDate = moment(topRetManagerDetail[0] && topRetManagerDetail[0].the_date).format('YYYY-MM-DD')
  const columns = [{
    title: '截止日期',
    dataIndex: 'the_date',
    format: 'date',
  }, {
    title: '类型',
    dataIndex: 'fund_type',
    filters: fundTypesMap[typeSource].map(item => {
      return {
        text: item,
        value: item,
      }
    }),
    onFilter: (value, record) => record.fund_type === value,
  }, {
    title: '基金经理',
    dataIndex: 'manager_name',
  }, {
    title: '基金简称',
    dataIndex: 'fund_name',
  }, {
    title: '基金公司',
    dataIndex: 'company_abbr_name',
  }, {
    title: '连续年数',
    dataIndex: 'statistic_type',
  }].map(buildTableColumn)
  const sourceData = topRetManagerDetail.filter(item => item.type_source === typeSource)
  const chartData = _.map(_.groupBy(sourceData, 'statistic_type'), (values, name) => {
    const body = _.countBy(values, 'fund_type')
    return {
      name,
      ...body,
    }
  })
  const chartCols = fundTypesMap[typeSource].map(type => {
    return {
      title: type,
      dataIndex: type,
    }
  })
  const options = getChartOptions(chartData, chartCols)
  return (
    <div>
      <Card
        title={`截止 ${latestDate}, 连续3/5年管理基金收益排前30%基金经理个数`}
        extra={
          <Radio.Group
            value={typeSource}
            size="small"
            onChange={handleTypeChange}
          >
            <Radio.Button value="Archimedes">Archimedes</Radio.Button>
            <Radio.Button value="Wind">Wind</Radio.Button>
          </Radio.Group>
        }
      >
        <Row gutter={16}>
          <Col lg={8} md={24}>
            <Echart style={{ height: '450px' }} options={options} />
          </Col>
          <Col lg={16} md={24}>
            <Table
              pagination={false}
              columns={columns}
              dataSource={sourceData}
              // dataSource={_.times(100, item => ({}))}
              size="small"
              scroll={{ y: 415 }}
            />
          </Col>
        </Row>
      </Card>
    </div>
  )
}
