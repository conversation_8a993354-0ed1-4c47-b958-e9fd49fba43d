import React, { useState } from 'react'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
  Card,
  Affix,
  Tabs,
} from 'antd'
import { queryIndustryData } from './service'
import Competition from './company/Competition'
import EquityInvest from './company/EquityInvest'

const { TabPane } = Tabs

const Company = () => {
  const { loading, data = [] } = useRequest(() => {
    return queryIndustryData({ dataKey: 'companyAssetScale' })
  }, {
    cacheKey: 'companyAssetScale',
  })
  const tabs = [{
    name: '竞争格局',
    tab: 'competition',
  }, {
    name: '权益投资',
    tab: 'equityInvest',
  }]
  const dates = _.uniq(data.map(item => item.the_date)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const [activeTab, setActiveTab] = useState(tabs[0].tab)
  const handleTabChange = currentTab => {
    setActiveTab(currentTab)
  }
  const getTabContent = () => {
    if (loading) {
      return <div style={{ height: 300 }}></div>
    }
    if (activeTab === 'competition') {
      return <Competition dates={dates} data={data}/>
    } else if (activeTab === 'equityInvest') {
      return <EquityInvest dates={dates}/>
    }
    return null
  }
  return (
    <div>
      <Affix offsetTop={44}>
        <Card className="nav-tab-wrapper">
          <Tabs
            animated={false}
            activeKey={activeTab}
            onChange={handleTabChange}
            tabBarExtraContent={null}
          >
            {tabs.map(item => <TabPane tab={item.name} key={item.tab}/>)}
          </Tabs>
        </Card>
      </Affix>
      <Spin spinning={loading}>
        {getTabContent()}
      </Spin>
    </div>
  )
}

export default Company
