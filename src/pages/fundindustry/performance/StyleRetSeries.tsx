import React, { useState } from 'react'
import {
  Card,
  Radio,
  Affix,
} from 'antd'
import _ from 'lodash'
import StyleBoxPlot from '../components/StyleBoxPlot'
import RetVolSeriesScatterChart from '../components/RetVolSeriesScatterChart'

export default ({
  styleData,
}: {
  styleData: any,
}) => {
  const assetTypes = ['纯股型', '配置型', '纯债型']
  const styleTypesMap = {
    纯股型: ['价值型', '均衡型', '成长型'],
    配置型: ['积极配置型', '消极配置型'],
    纯债型: ['平衡型', '信用型', '久期型']
  }
  const [assetType, setAssetType] = useState('纯股型')
  const [styleType, setStyleType] = useState('价值型')
  const handleTypeChange = event => {
    const type = event.target.value
    setAssetType(type)
    setStyleType(styleTypesMap[type][0])
  }
  const handleStyleChange = event => {
    setStyleType(event.target.value)
  }
  const isBond = assetType === '纯债型'
  const currentStyle = styleData.styleSeriesData.find(item => item.styleType === styleType)
  const currentAsset = styleData.assetStyleData.find(item => item.assetType === assetType)
  return (
    <div>
      <Affix offsetTop={88}>
        <Card
          title="Archimedes风格类型收益统计"
          className="nav-tab-wrapper"
          extra={
            <Radio.Group
              value={assetType}
              size="small"
              onChange={handleTypeChange}
            >
              {assetTypes.map(item => <Radio.Button value={item}>{item}</Radio.Button>)}
            </Radio.Group>
          }
        >
        </Card>
      </Affix>
      <div style={{ marginTop: 15 }}/>
      <Card
        title={`${currentStyle.styleType}收益率与${isBond ? '中债新综合' : '沪深300'}收益率`}
        extra={
          <Radio.Group
            value={styleType}
            size="small"
            onChange={handleStyleChange}
          >
            {styleTypesMap[assetType].map(item => <Radio.Button value={item}>{item}</Radio.Button>)}
          </Radio.Group>
        }
      >
        {currentStyle &&
        <StyleBoxPlot
          disableCard
          benchmarkName={isBond ? '中债新综合' : '沪深300'}
          data={currentStyle} benchmarkData={isBond ? styleData.bondBenchmark : styleData.stockBenchmark}
        />}
      </Card>
      <div style={{ marginTop: 15 }}/>
      <RetVolSeriesScatterChart key={currentAsset.assetType} title={`${currentAsset.assetType}收益率波动率时序图`} data={currentAsset} />
    </div>
  )
}
