import React, { useState } from 'react'
import {
  Card,
  Radio,
  Table,
} from 'antd'
import Echart from '@/components/Chart/Echarts'
import buildTableColumn from '@/utils/buildTableColumn'
import _ from 'lodash'

const getChartOptions = (data, columns, serieNames) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: serieNames,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: data.map(item => item.date),
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '收益率(%)',
      },
    ],
    series: serieNames.map(name => {
      return {
        name: name,
        type: 'bar',
        data: data.map(item => _.round((item[name] || 0) * 100, 2)),
      }
    }),
  }
  return option
}

export default ({
  data,
}: {
  data: any,
}) => {
  const [typeSource, setTypeSource] = useState('Archimedes')
  const handleTypeChange = event => {
    setTypeSource(event.target.value)
  }
  const fundTypesMap = {
    Archimedes: ['纯股型基金', '配置型基金', '纯债型基金'],
    Wind: ['股票型基金', '混合型基金', '债券型基金']
  }
  const retColumns = fundTypesMap.Archimedes.concat(fundTypesMap.Wind).map(type => {
    return {
      title: type,
      dataIndex: type,
      format: 'percentage',
      align: 'right',
      hasSorter: true,
    }
  })
  const columns = [{
    title: '年份',
    dataIndex: 'date',
  }, ...retColumns].map(buildTableColumn)
  const options = getChartOptions(data, retColumns, fundTypesMap[typeSource])
  return (
    <div>
      <Card
        title="近10年公募基金平均年度收益"
        extra={
          <Radio.Group
            value={typeSource}
            size="small"
            onChange={handleTypeChange}
          >
            <Radio.Button value="Archimedes">Archimedes</Radio.Button>
            <Radio.Button value="Wind">Wind</Radio.Button>
          </Radio.Group>
        }
      >
      <Echart style={{ height: '450px' }} options={options} />
      <div style={{ marginTop: 15 }}/>
      <Table
        pagination={false}
        columns={columns}
        dataSource={data}
        size="small"
      />
      </Card>
    </div>
  )
}
