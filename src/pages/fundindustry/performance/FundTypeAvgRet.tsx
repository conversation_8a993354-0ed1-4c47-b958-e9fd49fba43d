import React, { useState } from 'react'
import {
  Card,
  Radio,
  Table,
} from 'antd'
import Echart from '@/components/Chart/Echarts'
import moment from 'moment'
import buildTableColumn from '@/utils/buildTableColumn'
import _ from 'lodash'

const getChartOptions = (data, columns) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: data.map(item => item.fund_type),
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: columns.map(item => item.title),
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '收益率(%)',
      },
    ],
    series: data.map(item => {
      return {
        name: item.fund_type,
        type: 'bar',
        data: columns.map(col => _.round((item[col.dataIndex] || 0) * 100, 2)),
      }
    }),
  }

  return option
}

export default ({
  data,
}: {
  data: any,
}) => {
  const [typeSource, setTypeSource] = useState('Archimedes')
  const handleTypeChange = event => {
    setTypeSource(event.target.value)
  }
  const latestDate = moment(data[0] && data[0].the_date).format('YYYY-MM-DD')
  const retColumns = [{
    title: 'YTD',
    dataIndex: 'f_avgret_ytd',
  }, {
    title: '近1年',
    dataIndex: 'f_avgannret_1y',
  }, {
    title: '近3年',
    dataIndex: 'f_avgannret_3y',
  }, {
    title: '近5年',
    dataIndex: 'f_avgannret_5y',
  }, {
    title: '近7年',
    dataIndex: 'f_avgannret_7y',
  }, {
    title: '近10年',
    dataIndex: 'f_avgannret_10y',
  }].map(item => {
    return {
      ...item,
      format: 'percentage',
      align: 'right',
      hasSorter: true,
    }
  })
  const columns = [{
    title: '日期',
    dataIndex: 'the_date',
    format: 'date',
  }, {
    title: '数据源',
    dataIndex: 'type_source',
  }, {
    title: '基金类型',
    dataIndex: 'fund_type',
  }, ...retColumns].map(buildTableColumn)
  const fundTypesMap = {
    Archimedes: ['纯股型基金', '配置型基金', '纯债型基金'],
    Wind: ['股票型基金', '混合型基金', '债券型基金']
  }
  const types = fundTypesMap[typeSource]
  const chartData = data
    .filter(item => item.type_source === typeSource)
    .sort((fst, snd) => {
      return types.indexOf(fst.fund_type) - types.indexOf(snd.fund_type)
    })
  const options = getChartOptions(chartData, retColumns)
  return (
    <div>
      <Card
        title={`截止 ${latestDate} 公募基金平均年化收益`}
        extra={
          <Radio.Group
            value={typeSource}
            size="small"
            onChange={handleTypeChange}
          >
            <Radio.Button value="Archimedes">Archimedes</Radio.Button>
            <Radio.Button value="Wind">Wind</Radio.Button>
          </Radio.Group>
        }
      >
      <Echart style={{ height: '450px' }} options={options} />
      <div style={{ marginTop: 15 }}/>
      <Table
        pagination={false}
        columns={columns}
        dataSource={data}
        size="small"
      />
      </Card>
    </div>
  )
}
