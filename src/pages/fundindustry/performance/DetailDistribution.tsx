import React, { useState } from 'react'
import {
  Card,
  Radio,
  Spin,
  Divider,
  Space,
  Select,
} from 'antd'
import _ from 'lodash'
import RetVolScatterChart from '../components/RetVolScatterChart'

const { Option } = Select

export default ({
  styleData,
  loadingStyle,
}: {
  styleData: any,
  loadingStyle: boolean,
}) => {
  const assetDims = ['Archimedes', 'Wind']
  const assetTypesMap = {
    Archimedes: ['纯股型', '配置型', '纯债型'],
    Wind: ['主动股', '主动债', '配置型']
  }
  const [assetDim, setAssetDim] = useState(assetDims[0])
  const assetTypes = assetTypesMap[assetDim]
  const [assetType, setAssetType] = useState(assetTypes[0])
  const handleTypeChange = event => {
    const type = event.target.value
    setAssetType(type)
  }
  const funds = assetDim === 'Wind' ? styleData.fundsWd : styleData.funds
  const currentFunds = (funds || []).filter(item => item.assetType === assetType)
  const isBond = (assetType === '纯债型' || assetType === '主动债')
  const benchmarkName = isBond ? '中债新综合' : '沪深300'
  const benchmarkData = isBond ? styleData.bondBenchmark : styleData.stockBenchmark
  let descriptions = [
    '1、波动率为年化波动率；收益率为区间值，非年化值',
    '2、去除AC: 即AC份额的基金只计算A',
  ]
  if (assetDim === 'Wind') {
    descriptions = [
      ...descriptions,
      '3、分类均基于wind分类下的二级分类：',
      'a)主动股：普通股票型 + 偏股混合型',
      'b)主动债：短期纯债型 + 中长期纯债型 + 混合债券型一级基金',
      'c)配置型： 平衡混合型基金 + 偏债混合型基金 + 灵活配置型基金 + 混合债券型二级基金',
      '',
    ]
  }

  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loadingStyle}>
        <Card

          title={`${assetType}基金收益率波动率散点图`}
          className="nav-tab-wrapper"
          extra={
            <Space>
              <Select
                size="small"
                style={{ width: 110 }}
                value={assetDim}
                onChange={(value) => {
                  setAssetDim(value)
                  setAssetType(assetTypesMap[value][0])
                }}
              >
                <Option value="Archimedes">Archimedes</Option>
                <Option value="Wind">Wind</Option>
              </Select>
              <Radio.Group
                value={assetType}
                size="small"
                onChange={handleTypeChange}
              >
                {assetTypes.map(item => <Radio.Button value={item}>{item}</Radio.Button>)}
              </Radio.Group>
            </Space>
          }
        >
          <RetVolScatterChart
            disableCard
            benchmarkName={benchmarkName}
            benchmarkData={benchmarkData}
            data={currentFunds}
          />
        </Card>
      </Spin>
      <Divider orientation="left" plain>
        数据说明
      </Divider>
      <div>
        {descriptions.map(item => <p>{item}</p>)}
      </div>
    </div>
  )
}
