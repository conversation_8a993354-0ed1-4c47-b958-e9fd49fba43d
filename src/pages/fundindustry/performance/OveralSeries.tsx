import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Space,
  Spin,
  Divider,
} from 'antd'
import { queryIndustryData } from '../service'
import FundTypeYearlyRet from './FundTypeYearlyRet'
import StyleRetSeries from './StyleRetSeries'

const OveralSeries = ({ styleData, loadingStyle }) => {
  const { loading, data = [] } = useRequest(() => {
    return queryIndustryData({ dataKey: 'yearlyMarketPerf' })
  }, {
    cacheKey: 'yearlyMarketPerf',
  })
  const descriptions = [
    '1、收益率3月 代表 包含截止到上月末，最近3个月的收益率',
    '2、收益率波动率时序图中：某一分类下所有基金的的年化波动率和区间收益率的平均值',
    '3、去除AC: 即AC份额的基金只计算A份额',
  ]
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        <FundTypeYearlyRet data={data} />
      </Spin>
      <div style={{ marginTop: 15 }}/>
      <Spin spinning={loadingStyle}>
        <StyleRetSeries styleData={styleData} />
      </Spin>
      <Divider orientation="left" plain>
        数据说明
      </Divider>
      <div>
        {descriptions.map(item => <p>{item}</p>)}
      </div>
    </div>
  )
}

export default OveralSeries
