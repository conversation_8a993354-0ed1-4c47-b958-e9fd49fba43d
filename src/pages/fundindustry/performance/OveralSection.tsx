import React from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
  Row,
  Col,
  Divider,
  Typography,
} from 'antd'
import AssetBoxPlot from '../components/AssetBoxPlot'
import FundTypeAvgRet from './FundTypeAvgRet'
import { queryIndustryData } from '../service'

const OveralSection = ({ assetSummaryData, loadingStyle }) => {
  const { loading: loadingMP, data: marketPerf = [] } = useRequest(() => {
    return queryIndustryData({ dataKey: 'latesMarketPerf' })
  }, {
    cacheKey: 'latesMarketPerf',
  })
  const descriptions = [
    <Typography.Title level={4} style={{ margin: 0 }}>
      1、Archimedes分类：
    </Typography.Title>,
    '1.1 纯股型大类： 对应基准为 沪深300 (000300.SH) ；其下有 价值，成长，均衡 三个风格分类',
    '大范围先选择Wind基金分类中普通股票型基金、偏股混合型基金、平衡混合型基金、灵活配置型基金和偏债混合型基金。',
    '对于非普通股票型基金需要满足以下条件：',
    'i.	过去12个月股票持仓平均权重>=70%',
    'ii.	过去24个月（去除成立6个月以内的时期）股票权重都>=60%，且期间基金类型一直在大范围内（剔除那种期间基金类型从被动型转型的）',
    '1.2 纯债型大类：对应基准为：中债新综合财富指数（CBA00201.CS）；其下有 信用，久期，平衡 三个风格分类',
    'i.	选择大范围：万德分类中的中长期纯债型基金、短期纯债型基金和混合债一二级基金、偏股混合型基金、偏债混合型基金、平衡混合型基金和灵活配置型基金。',
    'ii.	满足条件：过去12个月可转债比例<15%  股票比例=0',
    '1.3 配置型大类：对应基准为：0.4*沪深300 +0.5*中债新综合财富指数+0.1*中证转债指数（000832.CSI）；其下有积极配置，消极配置 两个风格分类',
    'i.	选择大范围：普通股票型基金、偏股混合型基金、平衡混合型基金、灵活配置型基金、偏债混合型基金、中长期纯债型基金、短期纯债型基金和混合债一二级基金，剔除港股比例大于等于10%的基金（根据基金季报前十大持股数据）',
    'ii.	满足条件: 非纯债非纯股',
    <Typography.Title level={4} style={{ margin: 0 }}>
      2、Wind分类：
    </Typography.Title>,
    '股票型基金：普通股票型基金、被动指数型基金、增强指数型基金',
    '混合型基金：偏股混合型基金、平衡混合型基金、偏债混合型基金、灵活配置型基金',
    '债券型基金：中长期纯债型基金、短期纯债型基金、混合债券型一级基金、混合债券型二级基金、被动指数型债券基金、增强指数型债券基金',
  ]
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loadingMP}>
        <FundTypeAvgRet data={marketPerf}/>
      </Spin>
      <Spin spinning={loadingStyle}>
        <Row style={{ minHeight: 350, marginTop: 15 }}>
          {assetSummaryData.map(item => {
            return (
              <Col lg={8} md={8} sm={24}>
                <AssetBoxPlot title={`${item.assetType}收益率与数量`} data={item} />
              </Col>
            )
          })}
        </Row>
      </Spin>
      <Divider orientation="left" plain>
        数据说明
      </Divider>
      <div>
        {descriptions.map(item => <p>{item}</p>)}
      </div>
    </div>
  )
}

export default OveralSection
