import React from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
} from 'antd'
import { queryIndustryData } from '../service'
import DoubleRetFund from '../extended/DoubleRetFund'
import TopManagerStat from '../extended/TopManagerStat'

const ExtendedAnalyze = () => {
  const { loading, data = {
    doubleRetFundDetail: [],
    latestDoubleRet: [],
    topRetManagerDetail: [],
  } } = useRequest(() => {
    return queryIndustryData({ dataKey: 'extendedAnalyze' })
  }, {
    cacheKey: 'extendedAnalyze',
  })
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        <DoubleRetFund data={data} />
        <div style={{ marginTop: 15 }}/>
        <TopManagerStat data={data} />
      </Spin>
    </div>
  )
}

export default ExtendedAnalyze
