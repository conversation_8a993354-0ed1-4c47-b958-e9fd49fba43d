import React from 'react'
import {
  Card,
  Affix,
  Tabs,
} from 'antd'
import router from 'umi/router'

const { TabPane } = Tabs


const Wrapper = (props) => {
  const tabs = [{
    name: '产品发行分析',
    tab: 'productrelease',
  }, {
    name: '业绩分析',
    tab: 'performance',
  }, {
    name: '持仓分析',
    tab: 'position',
  }, {
    name: '基金公司',
    tab: 'company'
  }]
  const splits = props.location.pathname.split('/')
  let activeKey = splits.pop()
  const pathPrefix = splits.join('/')
  const handleTabChange = (activeKey: string) => {
    router.push(`${pathPrefix}/${activeKey}`)
  }
  return (
    <div>
      <Affix offsetTop={0}>
        <Card className="nav-tab-wrapper">
          <Tabs
            animated={false}
            activeKey={activeKey}
            onTabClick={handleTabChange}
            tabBarExtraContent={null}
          >
            {tabs.map(item => {
              return (
                <TabPane tab={item.name} key={item.tab} />
              )
            })}
          </Tabs>
        </Card>
      </Affix>
      {props.children}
    </div>
  )
}

export default Wrapper
