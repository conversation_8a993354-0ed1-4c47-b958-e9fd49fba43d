import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Space,
  Spin,
  Card,
  Affix,
  Tabs,
} from 'antd'
import { queryNewFundData } from './service'
import NewFundStatChart from './components/NewFundStatChart'

const { TabPane } = Tabs

const ProductRelease = () => {
  const { loading, data: newFunds = {} } = useRequest(() => {
    return queryNewFundData()
  }, {
    cacheKey: 'queryNewFundData',
  })
  const emptyNewFunds = {
    dates: [],
    statsData: [],
  }
  const tabs = [{
    name: '发行数量分析',
    tab: 'num',
  }, {
    name: '发行规模分析',
    tab: 'scale',
  }]
  const [activeTab, setActiveTab] = useState(tabs[0].tab)
  const handleTabChange = currentTab => {
    setActiveTab(currentTab)
  }
  return (
    <div>
      <Affix offsetTop={44}>
        <Card className="nav-tab-wrapper">
          <Tabs
            animated={false}
            activeKey={activeTab}
            onChange={handleTabChange}
            tabBarExtraContent={null}
          >
            {tabs.map(item => <TabPane tab={item.name} key={item.tab}/>)}
          </Tabs>
        </Card>
      </Affix>
      <Spin spinning={loading}>
        {
          activeTab === 'num'
            ? <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <NewFundStatChart title="近2年WIND投资分类 二级分类 新发基金成立数量" data={newFunds.class2Data || emptyNewFunds} valueKey="num"/>
            <NewFundStatChart title="近2年WIND投资分类 三级分类 新发基金成立数量" data={newFunds.class3Data || emptyNewFunds} valueKey="num"/>
          </Space>
            : <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <NewFundStatChart title="近2年WIND投资分类 二级分类 新发基金成立规模" data={newFunds.class2Data || emptyNewFunds} valueKey="netAsset"/>
            <NewFundStatChart title="近2年WIND投资分类 三级分类 新发基金成立规模" data={newFunds.class3Data || emptyNewFunds} valueKey="netAsset"/>
          </Space>
        }
      </Spin>
    </div>
  )
}

export default ProductRelease
