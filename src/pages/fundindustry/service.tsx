import request from '@/utils/request'

export async function queryStyleData(params) {
  return request('/api/fundindustry/stylestats', {
    params,
  })
}

export async function queryNewFundData(params) {
  return request('/api/fundindustry/newfunds', {
    params,
  })
}

export async function queryIndustryData(params) {
  return request('/api/fundindustry/data', {
    params,
  })
}

export async function queryStockPriceWithWeight(params) {
  return request('/api/fundindustry/stock/price', {
    params,
  })
}
