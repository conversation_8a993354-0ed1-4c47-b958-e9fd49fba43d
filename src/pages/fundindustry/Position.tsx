import React, { useState } from 'react'
import {
  Card,
  Affix,
  Tabs,
} from 'antd'
import TopNPosition from './position/TopNPosition'
import IndustryDistribution from './position/IndustryDistribution'
import EquityPosition from './position/EquityPosition'
import AssetAllocation from './position/AssetAllocation'

const { TabPane } = Tabs

const ProductRelease = () => {
  const tabs = [{
    name: '资产配置',
    tab: 'assetAllocation',
  }, {
    name: '权益仓位',
    tab: 'equityPosition',
  }, {
    name: '行业分布',
    tab: 'stockIndustry',
  }, {
    name: '重仓股票',
    tab: 'topStockSeries',
  }]
  const [activeTab, setActiveTab] = useState(tabs[0].tab)
  const handleTabChange = currentTab => {
    setActiveTab(currentTab)
  }
  const getCardContent = () => {
    if (activeTab === 'topStockSeries') {
      return <TopNPosition/>
    } else if (activeTab === 'stockIndustry') {
      return <IndustryDistribution/>
    } else if (activeTab === 'equityPosition') {
      return <EquityPosition />
    } else if (activeTab === 'assetAllocation') {
      return <AssetAllocation />
    }
    return null
  }
  return (
    <div>
      <Affix offsetTop={44}>
        <Card className="nav-tab-wrapper">
          <Tabs
            animated={false}
            activeKey={activeTab}
            onChange={handleTabChange}
            tabBarExtraContent={null}
          >
            {tabs.map(item => <TabPane tab={item.name} key={item.tab}/>)}
          </Tabs>
        </Card>
      </Affix>
      {getCardContent()}
    </div>
  )
}

export default ProductRelease
