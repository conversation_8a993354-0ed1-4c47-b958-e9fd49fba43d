import React, { useState } from 'react'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
  Table,
  Select,
  Card,
  Divider,
} from 'antd'
import Echart from '@/components/Chart/Echarts'
import { queryIndustryData } from '../service'
import buildTableColumn from '@/utils/buildTableColumn'

const { Option } = Select

const getChartOptions = (data, seriesNames) => {
  const option = {
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: seriesNames,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: data.map(item => item.date),
      },
    ],
    yAxis: [{
      type: 'value',
      name: '占总资产比(%)',
    }],
    series: seriesNames.map(serie => {
      return {
        name: serie,
        type: 'bar',
        data: data.map(item => _.round(item[`${serie}_total_weight`] || 0, 2)),
        stack: 'stack',
      }
    }),
  }
  return option
}

const AssetAllocation = ({ assetAllocation }) => {
  const assetTypes = ['股票', '其中：A股', '债券', '基金', '权证', '现金', '其他资产', '资产总值', '资产净值']
  const chartData = _.map(_.groupBy(assetAllocation, 'the_date'), (values, date) => {
    const result = values.reduce((out, item) => {
      out[`${item.asset_class}_hold_value`] = item.hold_value || 0
      out[`${item.asset_class}_total_weight`] = item.total_weight || 0
      return out
    }, {})
    return {
      ...result,
      date,
    }
  }).sort((fst, snd) => {
    return fst.date > snd.date ? 1 : -1
  })
  const options = getChartOptions(chartData, assetTypes.filter(item => !['其中：A股', '资产总值', '资产净值'].includes(item)))
  const dates = _.uniq(assetAllocation.map(item => item.the_date)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const [date, setDate] = useState(dates[0])
  const tableData = assetAllocation
    .filter(item => item.the_date === date)
    .sort((fst, snd) => assetTypes.indexOf(fst.asset_class) - assetTypes.indexOf(snd.asset_class))
  const columns = [{
    title: '资产科目',
    dataIndex: 'asset_class',
    width: 150,
  }, {
    title: '市值(万元)',
    dataIndex: 'hold_value',
    format: 'commaNumber',
    hasSorter: true,
    align: 'right',
  }, {
    title: '占总值比(%)',
    dataIndex: 'total_weight',
    format: 'number',
    hasSorter: true,
    align: 'right',
  }, {
    title: '占净值比(%)',
    dataIndex: 'net_weight',
    format: 'number',
    hasSorter: true,
    align: 'right',
  }, {
    title: '市值增长率(%)',
    dataIndex: 'hold_value_qoq',
    format: 'number',
    align: 'right',
    hasSorter: true,
  }].map(buildTableColumn)
  const onEvents = {
    click: (action) => {
      setDate(action.name)
    },
  }
  return (
    <div style={{ marginTop: 15 }}>
      <div>
        <Echart style={{ height: '450px' }} options={options} onEvents={onEvents}/>
        <Card
          title=""
          extra={
            <Select
              showSearch
              placeholder="选择报告期"
              style={{
                // marginTop: '5px',
                width: '120px',
              }}
              value={date}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onChange={setDate}
            >
              {dates.map(item => {
                return (
                  <Option value={item}>
                    {item}
                  </Option>
                )
              })}
            </Select>
          }
        >
          <Table
            pagination={false}
            columns={columns}
            dataSource={tableData}
            size="small"
          />
        </Card>
      </div>
    </div>
  )
}

const AssetAllocationWrapper = () => {
  const { loading, data: assetAllocation = [] } = useRequest(() => {
    return queryIndustryData({ dataKey: 'assetAllocation' })
  }, {
    cacheKey: 'assetAllocation',
  })
  const descriptions = [
    '1、季度统计，全市场基金（包含所有分类在内）',
    '2、去除AC: 即AC份额的基金只计算A',
  ]
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        {!loading &&
        <AssetAllocation assetAllocation={assetAllocation}/>}
      </Spin>
      <Divider orientation="left" plain>
        数据说明
      </Divider>
      <div>
        {descriptions.map(item => <p>{item}</p>)}
      </div>
    </div>
  )
}

export default AssetAllocationWrapper
