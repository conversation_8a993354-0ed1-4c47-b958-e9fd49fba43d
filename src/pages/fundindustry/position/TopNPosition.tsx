import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import _ from 'lodash'
import moment from 'moment'
import {
  Spin,
  Table,
  Select,
  Card,
  Tooltip,
  Popover,
  Modal,
  Button,
  Divider,
} from 'antd'
import { QuestionCircleOutlined, RightOutlined } from '@ant-design/icons';
import { queryIndustryData, queryStockPriceWithWeight } from '../service'
import buildTableColumn from '@/utils/buildTableColumn'
import Chart from '@/components/Chart/Chart'

const { Option } = Select

const renderPriceChart = (priceWeightData) => {
  const { priceData, weightData } = priceWeightData
  const positionPriceData = priceData
    .map(item => [
      +moment(item.BIZ_DATE).startOf('date'), item.PRICE,
    ])
    .sort((fst, snd) => fst[0] - snd[0])

  const positionWeightData = weightData
    .map(item => [
      +moment(item.BIZ_DATE).startOf('date'), item.WEIGHT,
    ]).sort((fst, snd) => fst[0] - snd[0])
  const series = [
    {
      yAxis: 0,
      type: 'column',
      name: '占净资产比',
      data: positionWeightData,
    },
    {
      yAxis: 1,
      type: 'line',
      name: '价格',
      data: positionPriceData,
      compare: 'percent',
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y:.2f}({point.change:.2f}%)</b><br/>',
      },
    },
  ]
  const chartConfig = {
    chart: {
      height: 420,
    },
    rangeSelector: {
      enabled: false,
      inputEnabled: false,
    },
    navigator: {
      enabled: true,
    },
    scrollbar: {
      enabled: false,
    },
    tooltip: {
      pointFormat:
        `<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%<br/>`,
    },
    yAxis: [
      {
        labels: {
          format: `{value}%`,
        },
      },
      {
        labels: {
          format: `{value}%`,
        },
        opposite: true,
      },
    ],
    series,
  }
  return <Chart options={chartConfig} constructorType="stockChart" />
}

const TopNPosition = ({ data }) => {
  const dates = _.uniq(data.map(item => item.the_date)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const [date, setDate] = useState(dates[0])
  const [currentPositionItem, setCurrentPositionItem] = useState({})
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const { loading: loadingPriceSeries, data: priceWeightData = {
    priceData: [], weightData: []
  }, run: runQueryStockPriceWithWeight } = useRequest((stockId) => {
    return queryStockPriceWithWeight({ stockId })
  }, {
    manual: true,
  })
  const handleNameClick = (item) => {
    setVisibleTrue()
    runQueryStockPriceWithWeight(item.stock_id)
    setCurrentPositionItem(item)
  }
  const columns = [{
    title: '代码',
    dataIndex: 'stock_id',
    fixed: 'left',
    width: 120,
    render: (text, record) => {
      return (
        <Tooltip title="点击查看股票价格变动">
          <a onClick={() => handleNameClick(record)}>{text} <RightOutlined/></a>
        </Tooltip>
      )
    },
  }, {
    title: '名称',
    dataIndex: 'stock_name',
    fixed: 'left',
    width: 120,
  }, {
    title: '持有基金数',
    dataIndex: 'fund_num',
    format: 'number',
    hasSorter: true,
    align: 'right',
    width: 120,
  }, {
    title: '持有公司家数',
    dataIndex: 'company_num',
    format: 'number',
    hasSorter: true,
    align: 'right',
    width: 120,
  }, {
    title: '持股总量(万股)',
    dataIndex: 'hold_num',
    format: 'number',
    hasSorter: true,
    align: 'right',
    width: 150,
  }, {
    title: '季报持仓变动(万股)',
    dataIndex: 'hold_change_q',
    format: 'number',
    hasSorter: true,
    align: 'right',
    width: 160,
  }, {
    title: '持股占流通股比(%)',
    dataIndex: 'tradable_share_ratio',
    format: 'number',
    hasSorter: true,
    align: 'right',
    width: 150,
  }, {
    title: '持股总市值(万元)',
    dataIndex: 'hold_value',
    format: 'number',
    hasSorter: true,
    align: 'right',
    width: 150,
  }, {
    title: '持股占基金净值比(%)',
    dataIndex: 'net_weight',
    format: 'number',
    hasSorter: true,
    align: 'right',
    width: 160,
  }, {
    title: '持股占基金股票投资市值比(%)',
    dataIndex: 'stock_weight',
    format: 'number',
    hasSorter: true,
    align: 'right',
    width: 220,
  }, {
    title: '所属行业',
    dataIndex: 'sw_industry_name',
    hasSorter: true,
    align: 'right',
    width: 120,
  }].map(buildTableColumn)
  const tableData = data.filter(item => item.the_date === date)
  return (
    <div>
      <Card
        title={
          <span>
            <span>公募基金持仓市值 TOP20</span>
            <Popover
              content={
                <div>
                  <p>
                    1、季度统计重仓股，半年/年报统计全部持仓
                  </p>
                  <p>
                    2、行业分类为申万一级（2021版）行业分类
                  </p>
                </div>
              }
              placement="right"
            >
              <span style={{ marginLeft: '5px' }}>
                <QuestionCircleOutlined />
              </span>
            </Popover>
          </span>
        }
        extra={
          <Select
            showSearch
            placeholder="选择报告期"
            style={{
              // marginTop: '5px',
              width: '120px',
            }}
            value={date}
            filterOption={(input, option) =>
              option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            onChange={setDate}
          >
            {dates.map(item => {
              return (
                <Option value={item}>
                  {item}
                </Option>
              )
            })}
          </Select>
        }
      >
        <Table
          pagination={false}
          columns={columns}
          dataSource={tableData}
          size="small"
          scroll={{ x: 700 }}
        />
      </Card>
      <Modal
        title={
          <>
            <span>{`${currentPositionItem.stock_name}价格变动`}</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={1000}
        footer={[
          <Button
            type="primary"
            onClick={setVisibleFalse}
          >
            关闭
          </Button>,
        ]}
      >
        <Spin spinning={loadingPriceSeries}>
          {renderPriceChart(priceWeightData)}
        </Spin>
      </Modal>
    </div>
  )
}

const TopNPositionWapper = () => {
  const { loading, data = [] } = useRequest(() => {
    return queryIndustryData({ dataKey: 'topStockSeries' })
  }, {
    cacheKey: 'topStockSeries',
  })
  const descriptions = [
    '1、每个季度统计一次： 全市场基金（包含所有分类在内）；季度统计重仓股，半年/年报统计全部持仓',
    '2、行业分类为申万一级（2021版）行业分类',
  ]
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        {data.length !== 0 &&
        <TopNPosition data={data}/>}
      </Spin>
      <Divider orientation="left" plain>
        数据说明
      </Divider>
      <div>
        {descriptions.map(item => <p>{item}</p>)}
      </div>
    </div>
  )
}

export default TopNPositionWapper
