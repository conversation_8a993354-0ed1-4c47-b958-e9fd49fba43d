import React from 'react'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
  Divider,
} from 'antd'
import Echart from '@/components/Chart/Echarts'
import { queryIndustryData } from '../service'

const getChartOptions = (data, chartSeries) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      // axisPointer: {
      //   type: 'shadow',
      // },
    },
    legend: {
      data: chartSeries.map(item => item.title),
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: data.map(item => item.the_date),
      },
    ],
    yAxis: [{
      type: 'value',
      name: '股票仓位(%)',
    }, {
      type: 'value',
      name: '沪深300',
    }],
    series: chartSeries.map(serie => {
      return {
        name: serie.title,
        type: serie.type || 'line',
        yAxisIndex: serie.yAxisIndex,
        data: data.map(item => _.round(item[serie.dataIndex] || 0, 2)),
        smooth: false,
      }
    }),
  }
  return option
}

const EquityPosition = () => {
  const { loading, data: equityPosition = [] } = useRequest(() => {
    return queryIndustryData({ dataKey: 'equityPosition' })
  }, {
    cacheKey: 'equityPosition',
  })
  const chartSeries = [{
    title: '纯股型基金',
    dataIndex: 'purestock',
  }, {
    title: '配置型基金',
    dataIndex: 'assetallocation',
  }, {
    title: '普通股票型基金',
    dataIndex: 'stock',
  }, {
    title: '偏股混合型基金',
    dataIndex: 'stockmixed',
  }, {
    title: '沪深300',
    dataIndex: 'hs300_price',
    yAxisIndex: 1,
  }]
  const options = getChartOptions(equityPosition, chartSeries)
  const descriptions = [
    '1、季度统计，其中普通股票型基金 为 wind 股票型基金下的二级分类；偏股混合型基金为 wind混合型基金下的二级分类',
    '2、去除AC: 即AC份额的基金只计算A',
    '3、计算方式为：算术平均',
  ]
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        <Echart style={{ height: '500px' }} options={options} />
      </Spin>
      <Divider orientation="left" plain>
        数据说明
      </Divider>
      <div>
        {descriptions.map(item => <p>{item}</p>)}
      </div>
    </div>
  )
}

export default EquityPosition
