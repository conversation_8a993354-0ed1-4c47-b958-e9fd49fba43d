import React, { useState } from 'react'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
  Card,
  Table,
  Popover,
  Select,
  Divider,
} from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons';
import { queryIndustryData } from '../service'
import IndustryHeatMap from '../../persona/components/IndustryHeatMap'
import buildTableColumn from '@/utils/buildTableColumn'

const { Option } = Select

const IndustryDetail = ({ data }) => {
  const dates = _.uniq(data.map(item => item.the_date)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const [date, setDate] = useState(dates[0])
  const columns = [{
    title: '行业',
    dataIndex: 'sw_industry_name',
  }, {
    title: '市值(万元)',
    dataIndex: 'hold_value',
    format: 'number',
    hasSorter: true,
    align: 'right',
  }, {
    title: '占净值比(%)',
    dataIndex: 'net_weight',
    format: 'number',
    hasSorter: true,
    align: 'right',
  }, {
    title: '占股票投资市值比(%)',
    dataIndex: 'stock_weight',
    format: 'number',
    hasSorter: true,
    align: 'right',
  }, {
    title: '股票市场标准行业配置比例(%)',
    dataIndex: 'industry_weight_bm',
    format: 'number',
    hasSorter: true,
    align: 'right',
  }, {
    title: '相对标准行业配置比例(%)',
    dataIndex: 'active_industry_allocation',
    format: 'number',
    hasSorter: true,
    align: 'right',
  }, {
    title: '市值增长率(%)',
    dataIndex: 'hold_value_hoh',
    format: 'number',
    align: 'right',
    hasSorter: true,
  }].map(buildTableColumn)
  const tableData = data.filter(item => item.the_date === date)
  return (
    <div style={{ minHeight: 300 }}>
      <Card
        title={
          <span>
            <span>行业持仓明细</span>
            <Popover
              content={
                <div>
                  <p>
                    行业选择为申万一级行业31个分类，股票市场标准选择为：沪深300
                  </p>
                </div>
              }
              placement="right"
            >
              <span style={{ marginLeft: '5px' }}>
                <QuestionCircleOutlined />
              </span>
            </Popover>
          </span>
        }
        extra={
          <Select
            showSearch
            placeholder="选择报告期"
            style={{
              // marginTop: '5px',
              width: '120px',
            }}
            value={date}
            filterOption={(input, option) =>
              option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            onChange={setDate}
          >
            {dates.map(item => {
              return (
                <Option value={item}>
                  {item}
                </Option>
              )
            })}
          </Select>
        }
      >
        <Table
          pagination={false}
          columns={columns}
          dataSource={tableData}
          size="small"
          scroll={{ x: 700 }}
        />
      </Card>
    </div>
  )
}

const IndustryDistribution = () => {
  const { loading, data: stockIndustry = [] } = useRequest(() => {
    return queryIndustryData({ dataKey: 'stockIndustry' })
  }, {
    cacheKey: 'stockIndustry',
  })

  const stockIndustrySort = stockIndustry.sort((fst, snd) => {
    return fst.INDUSTRY_BOARD > snd.INDUSTRY_BOARD ? 1 : -1
  })
  const descriptions = [
    '1、半年度统计；全市场基金（包含所有分类在内）；行业选择为申万（2021版）一级行业31个分类，股票市场标准选择为：沪深300',
    '2、板块与申万一级行业对应关系：',
    'TMT: 	  电子、计算机、传媒、通信；',
    '公共产业：建筑装饰、综合、公用事业、交通运输、环保；',
    '医药生物：医药生物；',
    '周期上游：煤炭、石油石化、有色金属；',
    '周期下游：电力设备、机械设备、国防军工；',
    '周期中游：建筑材料、基础化工、钢铁；',
    '消费：	  美容护理、家用电器、食品饮料、纺织服饰、轻工制造、商贸零售、社会服务、汽车、农林牧渔；',
    '金融：	  银行、房地产、非银金融',
    '3、去除AC: 即AC份额的基金只计算A',
  ]
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        {stockIndustry.length !== 0 && <IndustryHeatMap isSectionData dataType="industry" product={{}} data={stockIndustry} />}
        {stockIndustry.length !== 0 && <IndustryHeatMap isSectionData dataType="industryBoard" product={{}} data={stockIndustry} />}
        {stockIndustry.length !== 0 && <IndustryDetail data={stockIndustrySort} />}
      </Spin>
      <Divider orientation="left" plain>
        数据说明
      </Divider>
      <div>
        {descriptions.map(item => <p>{item}</p>)}
      </div>
    </div>
  )
}

export default IndustryDistribution
