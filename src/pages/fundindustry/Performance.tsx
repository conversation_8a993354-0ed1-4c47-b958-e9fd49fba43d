import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Card,
  Affix,
  Tabs,
  Radio,
  Space,
  Select,
} from 'antd'
import OveralSection from './performance/OveralSection'
import OveralSeries from './performance/OveralSeries'
import DetailDistribution from './performance/DetailDistribution'
import ExtendedAnalyze from './performance/ExtendedAnalyze'
import { queryStyleData } from './service'

const { TabPane } = Tabs
const { Option } = Select

const periods = [
  {
    value: 'M_M_A',
    name: '月频1月',
  },
  {
    value: 'M_3M_A',
    name: '月频3月',
  },
  {
    value: 'M_6M_A',
    name: '月频6月',
  },
  {
    value: 'M_12M_A',
    name: '月频12月',
  },
  {
    value: 'M_2Y_A',
    name: '月频2年',
  },
  {
    value: 'M_3Y_A',
    name: '月频3年',
  },
]
const factorTypes = [{
  name: '收益率',
  value: 'FdRet',
}, {
  name: '相对分类基准收益率',
  value: 'FdRltExcsRet',
}]

const factorList = factorTypes.reduce((out, factorType) => {
  const factors = periods.map(p => {
    return {
      title: `${factorType.name}${p.name}`,
      dataIndex: `${factorType.value}_${p.value}`,
    }
  })
  return out.concat(factors)
}, [])

const ProductRelease = () => {
  const [factor, setFactor] = useState(factorList[0])
  const { loading: loadingStyle, data: styleData = {
    funds: [], assetStyleData: [],
    assetSummaryData: [], styleSeriesData: [],
    benchmarkData: [],
    stockBenchmark: [],
    bondBenchmark: [],
  } } = useRequest(() => {
    return queryStyleData({ factor: factor.dataIndex })
  }, {
    refreshDeps: [factor.dataIndex],
    cacheKey: `styleData_${factor.dataIndex}`,
  })
  const tabs = [{
    name: '整体分布',
    tab: 'overal',
  }, {
    name: '明细分布',
    tab: 'detail',
  }, {
    name: '扩展分析',
    tab: 'extendedAnalyze',
  }]
  const [activeTab, setActiveTab] = useState(tabs[0].tab)
  const handleTabChange = currentTab => {
    setActiveTab(currentTab)
  }
  const [viewType, setViewType] = useState('section')
  const handleTypeChange = event => {
    setViewType(event.target.value)
  }
  const handleFactorChange = factorCode => {
    const factor = factorList.find(item => item.dataIndex === factorCode) || {}
    setFactor(factor)
  }
  const getTabContent = () => {
    if (activeTab === 'overal') {
      if (viewType === 'series') {
        return <OveralSeries styleData={styleData} loadingStyle={loadingStyle}/>
      }
      return <OveralSection loadingStyle={loadingStyle} assetSummaryData={styleData.assetSummaryData}/>
    } else if (activeTab === 'detail') {
      return <DetailDistribution styleData={styleData} loadingStyle={loadingStyle} />
    } else if (activeTab === 'extendedAnalyze') {
      return <ExtendedAnalyze/>
    }
    return null
  }
  return (
    <div>
      <Affix offsetTop={44}>
        <Card className="nav-tab-wrapper">
          <Tabs
            animated={false}
            activeKey={activeTab}
            onChange={handleTabChange}
            tabBarExtraContent={
              <Space>
                {activeTab === 'overal' &&
                <Radio.Group
                  value={viewType}
                  size="small"
                  onChange={handleTypeChange}
                >
                  <Radio.Button value="section">截面数据</Radio.Button>
                  <Radio.Button value="series">时间序列</Radio.Button>
                </Radio.Group>}
                <Select
                  showSearch
                  placeholder="选择因子"
                  style={{
                    // marginTop: '5px',
                    width: '250px',
                  }}
                  value={factor.dataIndex}
                  filterOption={(input, option) =>
                    option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  onChange={handleFactorChange}
                >
                  {factorList.map(item => {
                    return (
                      <Option value={item.dataIndex}>
                        {item.title}
                      </Option>
                    )
                  })}
                </Select>
              </Space>
            }
          >
            {tabs.map(item => <TabPane tab={item.name} key={item.tab}/>)}
          </Tabs>
        </Card>
      </Affix>
      {getTabContent()}
    </div>
  )
}

export default ProductRelease
