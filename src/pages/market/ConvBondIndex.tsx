import React from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
  Row,
  Col,
  Card,
  Breadcrumb,
  Table,
  Space,
} from 'antd'
import _ from 'lodash'
import moment from 'moment'
import FundNavCard from '@/pages/dashboard/components/FundNavCard'
import buildTableColumn from '@/utils/buildTableColumn'
import { queryIndexData } from './service'
import ExportData from '@/components/ExportData'

export default () => {
  const { loading, data = [] } = useRequest(() => {
    return queryIndexData()
  })
  const swData = data.filter(item => item.name.includes('申万'))
  const wdData = data.filter(item => item.name.includes('wd'))
  const categoryFilters = [{
    text: '申万可转债指数',
    value: '申万',
  }, {
    text: 'Wind可转债指数',
    value: 'wd',
  }]
  const columns = [{
    title: '名称',
    dataIndex: 'name',
    width: '150',
    filters: categoryFilters,
    onFilter: (value, record) => record.name.includes(value),
    ellipsis: true,
  }, {
    title: '收益率(1day)',
    dataIndex: 'last1DAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(1Mo)',
    dataIndex: 'last1MAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(3Mo)',
    dataIndex: 'last3MAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(6Mo)',
    dataIndex: 'last6MAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(YTD)',
    dataIndex: 'ytdAccReturn',
    format: 'percentage',
  }].map(buildTableColumn)
  const lastMonthDate = moment().subtract(1, 'month').format('YYYYMMDD')
  const latestQuotas = columns.slice(1).map(item => item.dataIndex)
  const latestData = data.map(item => {
    const endDate = moment(item.navEndDate).format('YYYYMMDD')
    if (endDate < lastMonthDate) {
      return _.omit(item, latestQuotas)
    }
    return item
  })
  return (
    <Spin spinning={loading}>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>市场跟踪</Breadcrumb.Item>
        <Breadcrumb.Item>可转债指数</Breadcrumb.Item>
      </Breadcrumb>
      <Row gutter={12}>
        <Col lg={24} md={24}>
          {swData.length !== 0 &&
          <FundNavCard navigatorEnabled fundFilterEnabled height={400} funds={swData} title="申万可转债指数行情" />}
        </Col>
      </Row>
      <div style={{ marginTop: 15 }} />
      <Row gutter={12}>
        <Col lg={24} md={24}>
          <FundNavCard navigatorEnabled height={400} funds={wdData} title="Wind可转债指数行情" />
        </Col>
      </Row>
      <div style={{ marginTop: 15 }} />
      <Row gutter={12}>
        <Col lg={24} md={24}>
          <Card
            title={<span>最新行情</span>}
            extra={
              <Space>
                <ExportData columns={columns} dataSource={latestData} filename="可转债指数最新行情" />
              </Space>
            }
          >
            <Table size="small" columns={columns} dataSource={latestData} pagination={false} scroll={{ y: 600 }}/>
          </Card>
        </Col>
      </Row>
    </Spin>
  )
}
