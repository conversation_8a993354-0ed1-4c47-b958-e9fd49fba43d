import React from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
  Row,
  Col,
  Card,
  Breadcrumb,
} from 'antd'
import FundNavCard from '@/pages/dashboard/components/FundNavCard'
import StyleCorrelation from '@/pages/dashboard/components/StyleCorrelation'
import StandardTable from '@/components/StandardTable'
import { calculateReturns } from '@/utils/calculator'
import { queryIndexData } from './service'

export default () => {
  const { loading, data = [] } = useRequest(() => {
    return queryIndexData({ type: 'style' })
  })
  const styleData = data.map((item: any) => {
    item.returns = calculateReturns(item.nets)
    return item
  })
  const stockStyles = styleData.filter((item: any) =>
    ['Value_benchmark', 'Growth_benchmark', 'Blend_benchmark'].includes(item._qutkeId),
  )
  const bondStyles = styleData.filter((item: any) =>
    ['CredSlect_benchmark', 'DuraTiming_benchmark'].includes(item._qutkeId),
  )
  const styleQuotas = [{
    title: '名称',
    dataIndex: 'name',
    width: '150',
    ellipsis: true,
  }, {
    title: '收益率(1day)',
    dataIndex: 'last1DAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(1Mo)',
    dataIndex: 'last1MAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(3Mo)',
    dataIndex: 'last3MAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(6Mo)',
    dataIndex: 'last6MAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(YTD)',
    dataIndex: 'ytdAccReturn',
    format: 'percentage',
  }]
  return (
    <Spin spinning={loading}>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>市场跟踪</Breadcrumb.Item>
        <Breadcrumb.Item>风格指数</Breadcrumb.Item>
      </Breadcrumb>
      <Row gutter={12}>
        <Col lg={12} md={24}>
          <FundNavCard navigatorEnabled funds={stockStyles} title="风格历史(股票)" />
        </Col>
        <Col lg={12} md={24}>
          <FundNavCard navigatorEnabled funds={bondStyles} title="风格历史(债券)" />
        </Col>
      </Row>
      <div style={{ marginTop: 15 }} />
      <Row gutter={12}>
        <Col lg={12} md={24}>
          <Card title={<span>最新行情</span>}>
            <StandardTable disableRowSlection size="small" columns={styleQuotas} data={{
              list: styleData,
            }}/>
          </Card>
        </Col>
        <Col lg={12} md={24}>
          <StyleCorrelation funds={styleData} />
        </Col>
      </Row>
    </Spin>
  )
}
