import request from '@/utils/request'

export async function queryDueDocs(params?: any) {
  return request('/api/toptaaviews', {
    params,
  })
}

export async function queryDueDoc(id: string) {
  return request(`/api/toptaaviews/${id}`)
}

export async function queryRefDueDoc(params?: any) {
  return request(`/api/toptaaviews/ref/list`, {
    params,
  })
}

export async function deleteDueDoc(id: string) {
  return request(`/api/toptaaviews/${id}`, {
    method: 'delete',
  })
}

export async function updateDueDoc(id: string, data: any) {
  return request(`/api/toptaaviews/${id}`, {
    method: 'put',
    data,
  })
}

export async function createDueDoc(data: any) {
  return request(`/api/toptaaviews`, {
    method: 'post',
    data,
  })
}

export async function queryGroupList() {
  return request(`/api/toptaaviews/usergroup/list`)
}