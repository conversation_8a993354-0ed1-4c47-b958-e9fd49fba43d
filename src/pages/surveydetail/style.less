.spinnerWapper {
  position: fixed;
  margin-left: -54px;
  top: 30%;
  left: 50%;
  z-index: 10000;
}

.detailTitle {
  height: 100%;
  width: 100%;
  display: flex;
  .onePanel {
    height: 80px;
    width:100%;
    color:white;
    background-color: #262626;
    display:flex;
    justify-content: space-evenly;
    align-items: center;
    .title {
      font-size: 28px;
    }
    .edit {
      cursor: pointer;
      font-size: 25px;
    }
  }
}
.detailColumn {
  height: 100%;
  width: 100%;
  display: flex;
}
.fourPanel {
  height: 100px;
  width: 25%;
  color:white;
  background-color: #181f29;
  margin: 8px;
  .fourContent {
    margin-top: 30px;
    text-align: center;
    font-size: 23px;
    .on {
      color: orange;
    }
  }
  .fourTitle {
    margin-top: 10px;
    text-align: center;
  }
}
.twoPanel {
  min-height: 150px;
  width:50%;
  background-color: #181f29;
  margin: 8px;
  color: white;
  display: flex;
  flex-direction: column;
  .switchWapper {
    margin-bottom: 0px;
  }
  .actionLine {
    display: flex;
    justify-content: space-between;
    margin: 15px;
    min-height: 28px;
    .tags {
      :global(.label) {
        margin-left: 8px;
        margin-bottom: 8px;
      }
      :global(.fa-pencil) {
        cursor: pointer;
      }
    }
    a {
      cursor: pointer;
    }
  }
}

.answerList {
  position: relative;
}

.answerTitle {
  font-size: 20px;
  margin: 20px 0 10px 10px;
}

.answerButton {
  position: absolute;
  top: 0;
  right: 0;
}

.itemWapper {
  cursor: pointer;
  a {
    color: #777;
  }
  .on {
    color:#ffc107;
  }
  .waiting {
    color: #ffc107;
  }
  .controls {
    display: none;
    margin-left: 10px;
    font-size: 16px;
    .controlItem {
      cursor: pointer;
      display: inline;
      margin-right: 10px;
    }
    a {
      color: #777;
    }
  }
}

.itemHeader {
  cursor: default;
  li {
    cursor: text;
  }
}

.itemHeader:hover {
  background-color: #fff;
}

.pagination {
  text-align: center;
}
