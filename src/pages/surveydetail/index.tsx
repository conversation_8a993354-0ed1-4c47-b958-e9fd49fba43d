import React, { Component } from 'react'
import classnames from 'classnames'
import moment from 'moment'
import router from 'umi/router'
import ISwitch from '@/components/ISwitch'
import CreateAnswerByAdminModal from '@/components/CreateAnswerByAdminModal'
import EditTagsModal from '@/components/EditTagsModal'
import SetCategoriesModal from '@/components/SetCategoriesModal'
import SetWhitelistModal from '@/components/SetWhitelistModal'
import ConfirmModal from '@/components/ConfirmModal'
import { Tooltip, Table, Button, Empty, Breadcrumb } from 'antd'
import Link from 'umi/link'
import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { connect } from 'dva'
import { getToken } from '@/utils/utils'
import { CurrentUser, UserModelState } from '@/models/user'
import { DetailModelState } from './model'
import styles from './style.less'
import ExportData from '@/components/ExportData'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  match: any;
  currentUser?: CurrentUser,
  answers: any,
  survey?: any
  global?: any,
  users?: any,
  tags?: any,
  categories?: any,
  inviteResult?: any
}

interface ComponentState {
  page: number,
  showAdminAnswerModal: boolean,
  surveyId: String,
  showWhitelistModal: boolean,
  showCategoriesModal: boolean
}

@connect(
  ({
    user,
    surveydetail,
    loading,
  }
    : {
      user: UserModelState,
      surveydetail: DetailModelState;
      loading: {
        effects: {
          [key: string]: boolean;
        };
      };
    }
  ) => {
    return ({
      survey: surveydetail.survey,
      tags: surveydetail.tags,
      inviteResult: surveydetail.inviteResult,
      categories: surveydetail.categories,
      users: surveydetail.users,
      currentUser: user.currentUser,
      answers: surveydetail.answers,
      loading: loading.effects['surveydetail/fetchAnswers'],
    })
  }
)

export default class SurveyDetail extends Component<ComponentProps, ComponentState> {

  constructor(props: ComponentProps) {
    super(props)
    const {
      match: {
        params: { id }
      }
    } = props
    this.state = {
      page: 1,
      showAdminAnswerModal: false,
      surveyId: id || '',
      showWhitelistModal: false,
      showCategoriesModal: false
    }
  }
  componentDidMount() {
    const { surveyId } = this.state
    this.loadSurvey(surveyId, {})
    this.loadAnswerList(surveyId, {})
    this.getUsers()
    this.getTags()
    this.loadCategories()

  }



  loadSurvey = (id: any, params: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveydetail/fetchSurvey',
      payload: { id, params },
    })
  }

  getUsers = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveydetail/fetchUsers',
    })
  }

  loadAnswerList = (id: any, params: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveydetail/fetchAnswers',
      payload: { id, params },
    })
  }
  delAnswer = (id: String) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveydetail/deleteAnswer',
      payload: { id },
    })
  }
  loadCategories = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveydetail/fetchCategories',
      payload: {},
    })
  }
  updateSurvey = (id: string, data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveydetail/fetchEdit',
      payload: { id, data },
    })
  }

  resetAdminState = (data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveydetail/resetSurveyState',
      payload: { data },
    })
  }

  updateCategory = (data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveydetail/updateCategories',
      payload: { data },
    })
  }

  inviteJoin = (id: string, data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveydetail/joinSurvey',
      payload: { id, data },
    })
  }


  getTags = () => {
    const { dispatch } = this.props
    console.log(2222)
    dispatch({
      type: 'surveydetail/fetchTags',
    })
  }

  editAnswer = item => () => {
    const { surveyId } = this.state
    const { author: { _id } } = item
    router.push(`/duediligence/survey/${surveyId}?authorId=${_id}`)
  }
  onExport = item => () => {
    const token = getToken()
    window.open(`/api/admin/answers/${item._id}/excel?token=${token.slice(7)}`)
  }




  onPageChange = page => {
    const {
      match: {
        params: { id }
      }
    } = this.props
    this.setState({ page })
    this.loadAnswerList(id, { page })
  }

  removeAnswer = (item: Object) => () => {
    this.delAnswer(item._id)
  }

  closeWhitelistModal = () => {
    this.setState({ showWhitelistModal: false })
    this.resetAdminState({ inviteResult: null })
  }
  closeAdminAnswerModal = () => {
    this.setState({ showAdminAnswerModal: false })
  }

  totalReceiver(survey, users) {
    const filteredUsers = (users || []).filter(
      user =>
        ~(user.user_scopes && user.user_scopes.indexOf('researcher')) &&
        user.fof_status === 'actived'
    )
    return survey.isSpecial || survey.isOpen
      ? filteredUsers && filteredUsers.length
      : survey.whitelist && survey.whitelist.length
  }

  startRecycle = () => {
    const { survey } = this.props
    const status = survey.status === 'on' ? 'off' : 'on'
    this.updateSurvey(survey._id, { status })
  }

  toggleResubmit = () => {
    const { survey } = this.props
    const resubmit = !survey.resubmit
    console.log('resubmit', resubmit)
    this.updateSurvey(survey._id, { resubmit })
  }

  updateSurveyTags = survey => tags => {
    this.updateSurvey(survey._id, { tags })
  }

  updateSetting = setting => {
    const { survey, categories } = this.props
    const { category } = setting
    this.updateSurvey(survey._id, setting)
    if (category && !~categories.indexOf(category)) {
      this.updateCategory(category)
    }
  }

  closeCategoriesModal = () => {
    this.setState({ showCategoriesModal: false })
  }


  getColumns = () => {
    const { currentUser } = this.props
    return [
      {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
        render: (text, row) => {
          return row.answers ?
            <Link style={{ color: 'orange' }} to={`/duediligence/answers/${row._id}`}>{row.author.nickname}</Link >
            :
            <span>{row.author.nickname}</span>
        }
      },
      {
        title: '联系方式',
        dataIndex: 'contact',
        key: 'contact',
        render: (text, row) => {
          return <span>{row.author.contact}</span>
        }
      },
      {
        title: '公司',
        dataIndex: 'company',
        key: 'company',
        render: (text, row) => {
          return <span>{row.author.company}</span>
        }
      },
      {
        title: '状态',
        dataIndex: 'answerStatus',
        key: 'answerStatus',
        render: (text, row) => {
          return <span>
            <i
              className={classnames('fa fa-circle', {
                [styles[`${row.submitAuthorId ? 'waiting' : 'on'}`]]: row.answers
              })}
            />{' '}
            {row.answers
              ? '已提交'
              : '未提交'}
          </span>
        }
      },
      {
        title: '提交时间',
        dataIndex: 'name',
        key: 'name',
        render: (text, row) => {
          return row.updated_at ? moment(row.updated_at).from(Date.now()) : '-'
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        render: (text, row) => {
          return <span className={styles.controls} style={{ display: 'flex' }}>
            {row.submitAuthorId === currentUser._id && <Tooltip
              placement="top"
              title='编辑'
            >
              <EditOutlined onClick={this.editAnswer(row)} />
            </Tooltip>}
            <Tooltip
              placement="top"
              title='导出excel'
            >
              <ExportData style={{ margin: '0 10px' }}  onClick={this.onExport(row)}/>
            </Tooltip>
            <ConfirmModal
              submit={this.removeAnswer(row)}
              className={styles.controlItem}
              message='确认删除？'
            >
              <Tooltip
                placement="top"
                title='删除'
              >
                <DeleteOutlined />
              </Tooltip>
            </ConfirmModal>
          </span>
        }

      },
    ]
  }

  render() {
    const {
      survey,
      answers: { list, totalNum },
      users,
      tags,
      categories,
      currentUser,
      inviteResult
    } = this.props
    const { page } = this.state
    const columns = this.getColumns()
    if (!survey) {
      return <Empty />
    }

    return (
      <div>
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>
            <Link to="/duediligence/history">问卷列表</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            {survey.title}
          </Breadcrumb.Item>
        </Breadcrumb>
        <div className="container">
          <div className={styles.detailColumn}>
            <div className={styles.fourPanel}>
              <div className={styles.fourContent}>
                <i
                  className={classnames('fa fa-circle', {
                    [styles.on]: survey.status === 'on'
                  })}
                />{' '}
                {survey.status === 'on'
                  ? '正在回收'
                  : '暂停回收'}
              </div>
              <div className={styles.fourTitle}>状态</div>
            </div>
            <div className={styles.fourPanel}>
              <div className={styles.fourContent}>
                {survey.category || '未分类'}
              </div>
              <div className={styles.fourTitle}>
                分类
              </div>
            </div>
            <div className={styles.fourPanel}>
              <div className={styles.fourContent}>
                {survey.feedbacks && survey.feedbacks.length}/
                {this.totalReceiver(survey, users)}
              </div>
              <div className={styles.fourTitle}>回收量</div>
            </div>
            <div className={styles.fourPanel}>
              <div className={styles.fourContent}>
                {moment(survey.updated_at).from(Date.now())}
              </div>
              <div className={styles.fourTitle}>更新时间</div>
            </div>
          </div>
          <div className={styles.detailColumn}>
            <div className={styles.twoPanel}>
              <div className={styles.actionLine}>
                <span>编辑问卷</span>
                <a
                  style={{ color: 'orange' }}
                  onClick={() => {
                    router.push(`/duediligence/survey/edit?id=${survey._id}`)
                  }}
                >
                  点击编辑
                </a>
              </div>
              <div className={styles.actionLine}>
                <span>回收开始/暂停</span>
                <ISwitch
                  className={styles.switchWapper}
                  onToggle={this.startRecycle}
                  active={survey.status === 'on'}
                  label={''}
                />
              </div>
              <div className={styles.actionLine}>
                <span>允许重复提交问卷</span>
                <ISwitch
                  className={styles.switchWapper}
                  onToggle={this.toggleResubmit}
                  active={survey.resubmit}
                  label={''}
                />
              </div>
              <div className={styles.actionLine}>
                <span>统计分析</span>
                <a
                  style={{ color: 'orange' }}
                  onClick={() => {
                    router.push(`/duediligence/stat/${survey._id}`)
                  }}
                >
                  点击查看
                </a>
              </div>
            </div>
            <div className={styles.twoPanel}>
              <div className={styles.actionLine}>
                <span>预览问卷</span>
                <a
                  style={{ color: 'orange' }}
                  onClick={() => {
                    router.push(`/duediligence/preview/${survey._id}`)
                  }}
                >
                  点击预览
                </a>
              </div>
              <div className={styles.actionLine}>
                <span style={{ minWidth: 30 }}>标签</span>
                <span className={styles.tags}>
                  {survey.tags &&
                    survey.tags.map(tag => (
                      <div
                        key={`${survey._id}-tag-${tag}`}
                        className="label label-default"
                        style={{ display: 'inline-block' }}
                      >
                        {tag}
                      </div>
                    ))}
                  <EditTagsModal
                    id={survey._id}
                    save={this.updateSurveyTags(survey)}
                    items={tags}
                    selectedItems={survey.tags}
                  >
                    <Tooltip
                      placement="top"
                      title='编辑标签'
                    >
                      <span style={{ background: '#181818' }} className="label label-primary">
                        <i style={{ color: 'orange' }} className="fa fa-pencil" />
                      </span>
                    </Tooltip>
                  </EditTagsModal>
                </span>
              </div>
              <div className={styles.actionLine}>
                <span>分类</span>
                <a
                  style={{ color: 'orange' }}
                  onClick={() => {
                    this.setState({ showCategoriesModal: true })
                  }}
                >
                  点击设置
                </a>
              </div>
              <div className={styles.actionLine}>
                <span>白名单</span>
                <a
                  style={{ color: 'orange' }}
                  onClick={() => {
                    this.setState({ showWhitelistModal: true })
                  }}
                >
                  点击设置
                </a>
              </div>
            </div>
          </div>
          {survey &&
            <SetCategoriesModal
              show={this.state.showCategoriesModal}
              submit={this.updateSetting}
              {...{ survey, categories }}
              close={this.closeCategoriesModal}
            />
          }

          <div className={styles.detailColumns}>
            {users && survey &&
              [
                <CreateAnswerByAdminModal
                  show={this.state.showAdminAnswerModal}
                  {...{
                    users,
                    survey
                  }}
                  close={this.closeAdminAnswerModal}
                />,
                <SetWhitelistModal
                  show={this.state.showWhitelistModal}
                  submit={this.updateSetting}
                  {...{
                    users,
                    survey,
                    inviteJoin: this.inviteJoin,
                    resetAdminState: this.resetAdminState,
                    inviteResult,
                    currentUser
                  }}
                  close={this.closeWhitelistModal}
                />
              ]
            }
          </div>
          <div className={styles.answerList}>
            <div className={styles.answerTitle}>答案列表</div>
            <Button className={styles.answerButton} onClick={() => this.setState({ showAdminAnswerModal: true })}>录入答案</Button>
            {list && list.length > 0 &&
              <div className={styles.itemWapper}>
                <Table
                  bordered
                  columns={columns}
                  dataSource={list}
                  pagination={{
                    current: page,
                    total: totalNum,
                    onChange: (page) => {
                      this.setState({ page })
                    }
                  }}
                />
              </div>
            }
          </div>
        </div>

      </div>
    )
  }
}
