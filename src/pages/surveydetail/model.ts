import { Effect } from 'dva'
import { Reducer } from 'redux'
import {
  loadAnswerList, load, getUsers, removeAnswer, editSurvey,
  getTags, inviteNewUser, inviteJoinSurvey, queryCategories
} from './service'
import { notification } from 'antd'

export interface DetailModelState {
  survey?: any,
  answers?: any,
  users?: any,
  tags?: any,
  inviteResult?: any,
  categories?: any
}

export interface ModelType {
  namespace: 'surveydetail';
  state: DetailModelState;
  effects: {
    fetchSurvey: Effect;
    fetchAnswers: Effect;
    fetchUsers: Effect;
    deleteAnswer: Effect;
    fetchEdit: Effect;
    fetchTags: Effect;
    inviteUser: Effect;
    joinSurvey: Effect;
    fetchCategories: Effect;
  };
  reducers: {
    resetSurveyState: Reducer<DetailModelState>;
    save: Reducer<DetailModelState>;
    del: Reducer<DetailModelState>;
    savetags: Reducer<DetailModelState>;
    newUser: Reducer<DetailModelState>;
    updateCategories: Reducer<DetailModelState>;
  };
}

const SurveyDetailModel: ModelType = {
  namespace: 'surveydetail',
  state: {
    answers: {},
    survey: {},
    tags: [],
    inviteResult: [],
    categories: []
  },
  effects: {
    *fetchSurvey({ payload: { id, params } }, { call, put }) {
      const response = yield call(load, id, params)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
    },
    *fetchAnswers({ payload: { id, params } }, { call, put }) {
      const response = yield call(loadAnswerList, id, params)
      yield put({
        type: 'save',
        payload: {
          answers: response,
        },
      })
    },
    *fetchUsers(_, { call, put }) {
      const response = yield call(getUsers)
      yield put({
        type: 'save',
        payload: {
          users: response,
        },
      })
    },
    *deleteAnswer({ payload: { id } }, { call, put }) {
      const response = yield call(removeAnswer, id)
      yield put({
        type: 'del',
        payload: {
          id: response._id,
        },
      })
    },
    *fetchEdit({ payload: { data, id } }, { call, put }) {
      const response = yield call(editSurvey, id, data)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
      notification.success({ message: '修改成功！' })
    },
    *fetchTags(_, { call, put }) {
      console.log(1111)
      const response = yield call(getTags)
      yield put({
        type: 'savetags',
        payload: {
          tags: response,
        },
      })
    },
    *fetchCategories({ payload: { params } }, { call, put }) {
      const response = yield call(queryCategories, params)
      yield put({
        type: 'save',
        payload: {
          categories: response,
        },
      })
    },
    *inviteUser({ payload: { data } }, { call, put }) {
      const response = yield call(inviteNewUser, data)
      yield put({
        type: 'newUser',
        payload: {
          data: response,
        },
      })
    },
    *joinSurvey({ payload: { id, data } }, { call, put }) {
      const response = yield call(inviteJoinSurvey, id, data)
      yield put({
        type: 'newUser',
        payload: {
          data: response,
        },
      })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    resetSurveyState(state, action) {
      return {
        ...state,
        ...action.payload.data,
      }
    },
    del(state, action) {
      let id = action.payload.id
      let list = state.answers.list.filter(
        item => item._id !== id
      )
      return {
        ...state,
        answers: {
          ...state.answers,
          list
        }
      }
    },
    savetags(state, action) {
      const list = [...state.tags, action.payload.data.tags]
      return {
        ...state,
        tags: list,
      }
    },
    newUser(state, action) {
      const list = [...state.users, action.payload.data.fundUsers]
      return {
        ...state,
        users: list,
      }
    },
    updateCategories(state, action) {
      return {
        ...state,
        categories: [...state.categories, action.payload.category]
      }
    }
  },
}

export default SurveyDetailModel
