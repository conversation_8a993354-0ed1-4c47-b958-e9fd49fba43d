import request from '@/utils/request'
export async function load(id: string, params: any): Promise<any> {
  return request(`/api/admin/survey/${id}`, {
    method: 'GET',
    params,
  })
}

export async function loadAnswerList(id: string, params: any): Promise<any> {
  return request(`/api/admin/survey/${id}/answers`, {
    method: 'GET',
    params,
  })
}
export async function getUsers(): Promise<any> {
  return request(`/api/survey/users/list`, {
    method: 'GET',
  })
}

export async function removeAnswer(id: string): Promise<any> {
  return request(`/api/admin/answers/${id}`, {
    method: 'delete',
  })
}

export function editSurvey(id: string, data: any): Promise<any> {
  return request(`/api/admin/survey/${id}`, {
    method: 'PUT',
    data,
  })
}

export function getTags(): Promise<any> {
  return request(`/api/admin/survey/tags`, {
    method: 'GET',
  })
}

export function inviteNewUser(data: any): Promise<any> {
  return request(`/admin/users/invite`, {
    method: 'POST',
    data,
  })
}


export function inviteJoinSurvey(id: any, data: any): Promise<any> {
  return request(`/admin/users/invite/survey/${id}`, {
    method: 'POST',
    data
  })
}


export async function queryCategories(): Promise<any> {
  return request(`/api/admin/survey/categories`, {
    method: 'GET',
  })
}