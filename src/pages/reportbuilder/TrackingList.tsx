import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import {
  Breadcrumb,
} from 'antd'
import { ModelState } from '@/models/investpool'
import { TableListItem, TableListParams } from '@/components/StandardTable'
import InvestPoolList from '@/components/investpool/List'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  investPoolListData: any;
  location: any;
  mutualFundFilters: any;
}

interface ComponentState {
  selectedRows: TableListItem[];
  isPage?: boolean;
  defaultIds?: string;
  type: string;
  dataType: string;
}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    investpool,
    fund,
    loading,
    user,
  }: {
    investpool: ModelState;
    fund: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
    user: any;
  }) => ({
    investPoolListData: investpool.investPoolListData,
    mutualFundFilters: fund.mutualFundFilters,
    loading: loading.effects['investpool/fetch'],
    groupList: investpool.groupList,
    currentUser: user.currentUser,
  }),
)
class InvestPool extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    const {
      location: { pathname },
    } = props
    this.state = {
      selectedRows: [],
      type: pathname.includes('/static') ? 'static' : 'dynamic',
      dataType: pathname.includes('/fund') ? 'fund' : 'manager',
    }
  }

  componentDidMount() {
    this.loadData({})
  }

  loadData = (params: Partial<TableListParams>) => {
    const { dispatch } = this.props
    dispatch({
      type: 'investpool/fetch',
      payload: {
        ...params,
        // type: this.state.type,
        // dataType: 'fund',
      },
    })
  }

  render() {
    const { investPoolListData, loading, dispatch, mutualFundFilters, groupList, currentUser } = this.props

    return (
      <div>
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>
            工作报表
          </Breadcrumb.Item>
          <Breadcrumb.Item>跟踪名单</Breadcrumb.Item>
        </Breadcrumb>
        <InvestPoolList
          {...{ dispatch, investPoolListData, loading, mutualFundFilters, groupList, currentUser }}
          loadData={this.loadData}
          type={this.state.type}
          dataType="fund"
          isMixedList
        />
      </div>
    )
  }
}

export default InvestPool
