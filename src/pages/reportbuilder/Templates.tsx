import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import Link from 'umi/link'
import { connect } from 'dva'
import moment from 'moment'
import { DeleteOutlined, CopyOutlined, EditOutlined } from '@ant-design/icons'
import {
  Table,
  Tooltip,
  Divider,
  Popconfirm,
  Input,
  Card,
  notification,
  Breadcrumb,
  Form,
  Row,
  Col,
  Spin,
  Modal,
  Button,
} from 'antd'
import { getTemplateList, deleteTemplate, copyTemplate, updateTemplate } from './service'

const { Search, TextArea } = Input

const List = ({
  currentUser,
  location,
}: {
  currentUser: any,
  location: any,
}) => {
  const { pathname } = location
  const type = pathname.includes('/reporttemplate') ? 'report' : 'factor'
  const [form] = Form.useForm()
  const deafultValues = { name: '', description: '' }
  const { setFieldsValue } = form
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState(null)
  const [refreshCount, setRefreshCount] = useState(0)
  const [input, setInput] = useState('')
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    p.type = type
    return getTemplateList(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const { run: doDelete } = useRequest((id) => {
    return deleteTemplate(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const { loading: copying, run: doCopy} = useRequest((id) => {
    return copyTemplate(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '复制成功',
      })
    },
  })
  const { loading: updatingTemplate, run: doUpdateTemplate} = useRequest((id, data) => {
    return updateTemplate(id, data)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      setVisibleFalse()
      setFieldsValue(deafultValues)
      notification.success({
        message: '更新成功',
      })
    },
  })
  const handleOpenModal = (record) => () => {
    setCurrentItem(record)
    setVisibleTrue()
    setFieldsValue(record)
  }
  const handleClickSave = (values: any) => {
    if (currentItem._id) {
      doUpdateTemplate(currentItem._id, values)
    }
  }
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 200,
      render: (text, record) => {
        return <Link to={`/reportbuilder/editor?${record.templateType === 'report' ? 'reportTplId' : 'factorTplId'}=${record._id}`}>{text}</Link>
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      align: 'right',
      width: 200,
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      align: 'right',
      width: 200,
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      align: 'center',
      width: 200,
      render: (text, record) => {
        if (currentUser._id !== record.authorId) {
          return (
            <Tooltip title="复制">
              <a><CopyOutlined spin={copying} onClick={() => doCopy(record._id)} /></a>
            </Tooltip>
          )
        }
        return (
          <>
            <Tooltip title="编辑">
              <EditOutlined onClick={handleOpenModal(record)} />
            </Tooltip>
            <Divider type="vertical" />
            <Tooltip title="复制">
              <CopyOutlined spin={copying} onClick={() => doCopy(record._id)} />
            </Tooltip>
            <Divider type="vertical" />
            <Popconfirm
              title="确认删除吗？"
              onConfirm={() => { doDelete(record._id) }}
              onCancel={() => {}}
              okText="确认"
              cancelText="取消"
              placement="left"
            >
              <Tooltip title="删除">
                <DeleteOutlined />
              </Tooltip>
            </Popconfirm>
          </>
        )
      },
    },
  ]

  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link>工作报表</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{type === 'report' ? '报表模板' : '跟踪指标'}</Breadcrumb.Item>
      </Breadcrumb>
      <Card
        title={
          <>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
      >
        <Table size="small" columns={columns} rowKey="_id" {...tableProps} />
      </Card>
      <Modal
        title={
          <>
            <span>{currentItem && currentItem._id ? '编辑报表' : '新建报表'}</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={700}
        footer={[
          <Button
            type="primary"
            loading={updatingTemplate}
            onClick={() => form.submit()}
          >
            保存
          </Button>,
        ]}
      >
        <Spin spinning={updatingTemplate}>
          <Form
            hideRequiredMark
            form={form}
            layout="vertical"
            onFinish={handleClickSave}
          >
            <Row gutter={8}>
              <Col span={24}>
                <Form.Item
                  label="名称"
                  name="name"
                  rules={[
                    {
                      required: true,
                      message: '请输入名称',
                    },
                  ]}
                >
                  <Input placeholder="名称"/>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="描述"
                  name="description"
                >
                  <TextArea placeholder="描述"/>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Spin>
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
