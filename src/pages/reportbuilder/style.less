@import '~antd/es/style/themes/default.less';

.factorTree, .factorListWrapper {
  :global(.ant-tree) {
    max-height: 700px;
    overflow-y: scroll;
    :global(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
      background-color: #4a3619;
    }
  }
  .addBtn {
    width: 90%;
    position: absolute;
    bottom: 10px;
    right: 5%;
  }
  .topAddBtn {
    width: 100%;
    margin-bottom: 15px;
  }
  .iconBtn {
    float: right;
    margin-top: 3px;
  }
}

.factorList {
  max-height: 700px;
  overflow-y: scroll;
}

.tableFundItem,.factorListItem {
  cursor: pointer;
  .icon {
    display: none;
  }
  &:hover {
    .icon {
      display: inline;
    }
  }
}

// :global(.ant-table-row) {
//   .tableFundItem {
//     cursor: pointer;
//     .icon {
//       display: none;
//     }
//     &:hover {
//       .icon {
//         display: inline;
//       }
//     }
//   }
//   &:hover {
//     .tableFundItem {
//       cursor: pointer;
//       .icon {
//         display: inline;
//       }
//     }
//   }
// }

.main {
  margin-top: 16px;
  min-height: 520px;
  :global(.ant-card) {
    height: 100%;
  }
}
