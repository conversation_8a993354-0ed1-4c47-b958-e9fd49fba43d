import React, { useState } from 'react'
import {
  Select,
  Popconfirm,
  message,
} from 'antd'

export default ({ sendEmail, title, children }) => {
  const [visible, setVisible] = useState(false)
  const [emails, setEmails] = useState([])
  const handleOk = () => {
    if (!emails || !emails.length) {
      message.warn('请输入邮箱')
      return
    }
    setVisible(false)
    setEmails([])
    sendEmail(emails)
  }

  const handleCancel = () => {
    setVisible(false)
  }

  const handleSelectChange = (values) => {
    setEmails(values)
  }

  return (
    <Popconfirm
      onConfirm={handleOk}
      onCancel={handleCancel}
      visible={visible}
      okText="发送"
      title={
        <div>
          <h4>{title}</h4>
          <Select
            mode="tags"
            placeholder="输入邮箱，回车确认"
            style={{
              width: '400px',
            }}
            onChange={handleSelectChange}
            value={emails}
          />
        </div>
      }
    >
      <span onClick={() => { setVisible(true) }}>{children}</span>
    </Popconfirm>
  )
}
