import React, { useState } from 'react'
import {
  Input,
  Popconfirm,
  message,
} from 'antd'

export default ({ createTemplate, templateType, title, content, children }) => {
  const [visible, setVisible] = useState(false)
  const [name, setName] = useState(null)
  const handleOk = () => {
    if (!name) {
      message.warn('请输入名字')
      return
    }
    setVisible(false)
    setName(null)
    createTemplate({ name, templateType, content })
  }

  const handleCancel = () => {
    setVisible(false)
  }

  return (
    <Popconfirm
      placement="left"
      onConfirm={handleOk}
      onCancel={handleCancel}
      visible={visible}
      title={
        <div>
          <h4>{title}</h4>
          <Input
            value={name}
            size="small"
            placeholder="请输入名字"
            onChange={(event) => setName(event.target.value) }
          />
        </div>
      }
    >
      <span onClick={() => { setVisible(true) }}>{children}</span>
    </Popconfirm>
  )
}
