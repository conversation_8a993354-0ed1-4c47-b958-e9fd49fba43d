import React, { useState } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import {
  Input,
  Popconfirm,
  message,
} from 'antd'

export default ({ onChange }) => {
  const [visible, setVisible] = useState(false)
  const [name, setName] = useState(null)
  const handleOk = () => {
    if (!name) {
      message.warn('请输入名字')
      return
    }
    setVisible(false)
    setName(null)
    onChange(name)
  }

  const handleCancel = () => {
    setVisible(false)
  }

  return (
    <Popconfirm
      onConfirm={handleOk}
      onCancel={handleCancel}
      visible={visible}
      title={
        <div>
          <h4>添加新Sheet</h4>
          <Input
            value={name}
            size="small"
            placeholder="请输入名字"
            onChange={(event) => setName(event.target.value) }
          />
        </div>
      }
    >
      <PlusOutlined onClick={() => { setVisible(true) }}/>
    </Popconfirm>
  )
}
