import React, { useState } from 'react'
import { Tree, Input, Button, Tooltip } from 'antd'
import { DoubleRightOutlined } from '@ant-design/icons'
import styles from '../style.less'

const { Search } = Input

const getParentKey = (key, tree) => {
  let parentKey
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.children) {
      if (node.children.some(item => item.key === key)) {
        parentKey = node.key
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children)
      }
    }
  }
  return parentKey
}

const loop = (data, searchValue) => {
  return data.map(item => {
    const index = item.title.indexOf(searchValue)
    const beforeStr = item.title.substr(0, index)
    const afterStr = item.title.substr(index + searchValue.length)
    const title =
      index > -1 ? (
        <span>
          {beforeStr}
          <span className="site-tree-search-value">{searchValue}</span>
          {afterStr}
        </span>
      ) : (
        <span>{item.title}</span>
      )
    if (item.children) {
      return { title, key: item.value, selectable: false, children: loop(item.children, searchValue) }
    }
    return {
      title: title,
      key: item.value,
    }
  })
}

const SearchTree = ({ treeData, data, onChange }) => {
  const [selectedKeys, setSelectedKeys] = useState([])
  const [expandedKeys, setExpandedKeys] = useState([])
  const [searchValue, setSearchValue] = useState('')
  const [autoExpandParent, setAutoExpandParent] = useState(true)

  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys)
    setAutoExpandParent(false)
  }

  const onSearch = value => {
    const expandedKeys = data
      .map(item => {
        if (!value) {
          return null
        }
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.dataIndex, treeData)
        }
        return null
      })
      .filter((item, i, self) => item && self.indexOf(item) === i)
    setExpandedKeys(expandedKeys)
    setSearchValue(value)
    setAutoExpandParent(true)
  }

  const onSelect = (keys) => {
    setSelectedKeys(keys)
  }

  const onButtonClick = () => {
    onChange(selectedKeys)
    setSelectedKeys([])
  }

  return (
    <div className={styles.factorTree}>
      <Tooltip title="点击添加指标">
        <Button className={styles.topAddBtn} onClick={onButtonClick}>已选指标({selectedKeys.length})<DoubleRightOutlined className={styles.iconBtn}/></Button>
      </Tooltip>
      <Search style={{ marginBottom: 8 }} placeholder="Search" onSearch={onSearch} />
      <Tree
        multiple
        onExpand={onExpand}
        selectedKeys={selectedKeys}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        treeData={loop(treeData, searchValue)}
        onSelect={onSelect}
      />
      {false &&
      <Tooltip title="点击添加指标">
        <Button className={styles.addBtn} onClick={onButtonClick}>已选指标({selectedKeys.length})<DoubleRightOutlined className={styles.iconBtn}/></Button>
      </Tooltip>}
    </div>
  )
}

export default SearchTree
