import React, { useState } from 'react'
import _ from 'lodash'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Space,
  Menu,
  Dropdown,
  Tabs,
  Table,
} from 'antd'
import SelectFundModal from '@/components/SelectFundModal'
import AddTabButton from './AddTabButton'

const { TabPane } = Tabs

export default ({ tabData, value, onChange, factors }) => {
  const defaultTabs = tabData || ['组合报表']
  const [tabs, setTabs] = useState(defaultTabs)
  const [activeTabKey, setActiveTabKey] = useState(defaultTabs[0])
  const [tabTableData, setTabTableData] = useState((value || []).reduce((out, item) => {
    out[item.factorTier] = item.factors
    return out
  }, {}))
  const handleTabChange = newTabKey => {
    setActiveTabKey(newTabKey)
  }
  const handleAddTab = tabItem => {
    setTabs(_.uniq([...tabs, tabItem.key]))
    handleTabChange(tabItem.key)
  }
  const handleRemoveTab = targetKey => {
    const newTabs = tabs.filter(item => item !== targetKey)
    setTabs(newTabs)
    setActiveTabKey(newTabs[0])
    const newData = _.omit(tabTableData, [targetKey])
  }
  const handleEditTab = (targetKey, action) => {
    if (action === 'remove') {
      handleRemoveTab(targetKey)
    }
  }
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      fixed: 'left',
    },
    ...factors,
  ]
  return (
    <Tabs
      type="editable-card"
      activeKey={activeTabKey}
      addIcon={
        <AddTabButton onChange={(name) => { handleAddTab({ key: name }) }}/>
      }
      onEdit={handleEditTab}
      onChange={handleTabChange}
      tabBarExtraContent={
        <SelectFundModal title="请选择组合" onChange={() => {}}>
          <Button size="small" icon={<PlusOutlined />}>
            添加资产
          </Button>
        </SelectFundModal>
      }
    >
      {tabs.map(tab => (
        <TabPane tab={tab} key={tab}>
          <Table size="small" columns={columns} scroll={{ x: 1300 }}/>
        </TabPane>
      ))}
    </Tabs>
  )
}
