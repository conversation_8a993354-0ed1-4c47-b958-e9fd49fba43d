import request from '@/utils/request'

export async function getFactorList() {
  return request('/api/factorschemas/workreportfactors')
}

export async function getTemplateList(params) {
  return request('/api/reporttemplates', { params })
}

export async function getReportTemplates() {
  return request('/api/reporttemplates/all')
}

export async function deleteTemplate(id: string) {
  return request(`/api/reporttemplates/${id}`, {
    method: 'delete',
  })
}

export async function copyTemplate(id: string) {
  return request(`/api/reporttemplates/${id}/copy`, {
    method: 'post',
  })
}

export async function updateTemplate(id: string, data: any) {
  return request(`/api/reporttemplates/${id}`, {
    method: 'put',
    data,
  })
}

export async function updateFactorSchema(id: string, data: any) {
  return request(`/api/factorschemas/${id}`, {
    method: 'put',
    data,
  })
}

export async function createReportTemplate(data) {
  return request('/api/reporttemplates', {
    method: 'POST',
    data,
  })
}

export async function refreshDailyData(data) {
  return request('/api/reporttemplates/data/daily', {
    method: 'POST',
    data,
  })
}

export async function refreshSeriesData(data) {
  return request('/api/reporttemplates/data/series', {
    method: 'POST',
    data,
  })
}

export async function sendEmail(data) {
  return request('/api/reporttemplates/email', {
    method: 'POST',
    data,
  })
}
