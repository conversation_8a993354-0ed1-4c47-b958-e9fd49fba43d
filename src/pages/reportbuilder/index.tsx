import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import moment from 'moment'
import _ from 'lodash'
import {
  DeleteOutlined, MailOutlined,
  SearchOutlined, PlusOutlined, SaveOutlined, SettingOutlined,
  ArrowDownOutlined, ArrowUpOutlined,
} from '@ant-design/icons'
import {
  Table,
  Button,
  Tooltip,
  Select,
  Card,
  DatePicker,
  Space,
  Row,
  Col,
  Tabs,
  Radio,
  List,
  message,
  Switch,
  Divider,
} from 'antd'
import {
  getFactorList, getReportTemplates, createReportTemplate, refreshDailyData, refreshSeriesData, sendEmail,
} from './service'
import buildTreeData from '@/utils/buildTreeData'
import SelectFundModal from '@/components/SelectFundModal'
import buildTableColumn from '@/utils/buildTableColumn'
import { exportMultiTableAsExcel, getSheetData } from '@/utils/exportAsExcel'
import AddTabButton from './components/AddTabButton'
import FactorTree from './components/FactorTree'
import SaveTemplateButton from './components/SaveTemplateButton'
import SendEmailButton from './components/SendEmailButton'
import ExportData from '@/components/ExportData'
import styles from './style.less'

const { TabPane } = Tabs
const { RangePicker } = DatePicker
const { Option } = Select

const textColumns = [
  'startDate', 'managers', 'company', 'asset_type', 'style_type',
  'fundNature', 'investType', 'isIndustryFund', 'industryLabels', 'isClosed',
  'investStyle', 'benchmark'
]

const periods = [
  {
    value: '1D',
    name: '1天',
  },
  {
    value: '1W',
    name: '1周',
  },
  {
    value: '1M',
    name: '1月',
  },
  {
    value: '3M',
    name: '3月',
  },
  {
    value: '6M',
    name: '6月',
  },
  {
    value: '1Y',
    name: '1年',
  },
  {
    value: '2Y',
    name: '2年',
  },
  {
    value: '3Y',
    name: '3年',
  },
  {
    value: '5Y',
    name: '5年',
  },
  {
    value: 'ytd',
    name: 'YTD',
  },
]

const factorTypes = [{
  class1: '收益类',
  class2: '收益率',
  value: 'accReturn',
  format: 'percentage',
  startPeriod: '1D',
  extraFactors: [{
    title: '收益率任职以来',
    dataIndex: 'FdRet_M_STO_A',
  }],
}, {
  class1: '收益类',
  class2: '年化收益率',
  value: 'yearReturn',
  format: 'percentage',
}, {
  class1: '收益类',
  class2: '超额收益率',
  value: 'excessReturn',
  format: 'percentage',
  startPeriod: '1D',
  extraFactors: [{
    title: '相对收益率任职以来',
    dataIndex: 'FdRltExcsRet_M_STO_A',
  }],
}, {
  class1: '收益类',
  class2: '年化超额收益率',
  value: 'accessReturn',
  format: 'percentage',
},
/*
{
  class1: '收益类',
  class2: '夏普比率',
  value: 'sharpeRatio',
  format: 'number',
}, {
  class1: '收益类',
  class2: '下行捕获率',
  value: 'downsideCapture',
  format: 'percentage',
}, {
  class1: '收益类',
  class2: '信息比率',
  value: 'informationRatio',
  format: 'number',
}, {
  class1: '收益类',
  class2: 'Jensen系数',
  value: 'jensen',
  format: 'number',
},
*/
{
  class1: '风险类',
  class2: '下行风险',
  value: 'downsideDev',
  format: 'percentage',
  extraFactors: [{
    title: '下行风险任职以来',
    dataIndex: 'FdDwnRisk_M_STO_A',
  }],
}, {
  class1: '风险类',
  class2: '最大回撤',
  value: 'maxDrawdown',
  format: 'percentage',
  startPeriod: '1M',
  extraFactors: [{
    title: '最大回撤任职以来',
    dataIndex: 'FdDb_M_STO_A',
  }],
}, {
  class1: '风险类',
  class2: '最大回撤恢复天数',
  value: 'maxDrawdownRT',
  format: 'number',
  startPeriod: '1M',
  extraFactors: [{
    title: '最大回撤恢复天数任职以来',
    dataIndex: 'FdDbRecvrDs_M_STO_A',
  }],
}, {
  class1: '风险类',
  class2: 'BETA',
  value: 'beta',
  format: 'number',
  extraFactors: [{
    title: 'BETA任职以来',
    dataIndex: 'FdBeta_M_STO_A',
  }],
}, {
  class1: '风险类',
  class2: 'R-Squared',
  value: 'rSquared',
  format: 'number',
  extraFactors: [{
    title: 'R-Squared任职以来',
    dataIndex: 'FdRSqre_M_STO_A',
  }],
}, {
  class1: '风险类',
  class2: '波动率',
  value: 'vol',
  format: 'percentage',
  extraFactors: [{
    title: '波动率任职以来',
    dataIndex: 'FdAnnVol_M_STO_A',
  }],
},
/*
{
  class1: '风险类',
  class2: 'VaR',
  value: 'varHistory',
  format: 'percentage',
  startPeriod: '1M',
}*/
]

const defaultFactors = [{
  title: '基金代码',
  dataIndex: '_qutkeId',
},
{
  title: '成立日期',
  dataIndex: 'startDate',
  format: 'date',
  width: 100,
},
{
  title: '基金经理(现任)',
  dataIndex: 'managers',
  format: 'curManagerList',
},
{
  title: '基金公司',
  dataIndex: 'company',
},
{
  title: '资产分类(平安)',
  dataIndex: 'asset_type',
},
{
  title: '策略分类(平安)',
  dataIndex: 'style_type',
},
{
  title: '万德分类(一级)',
  dataIndex: 'fundNature',
},
{
  title: '万德分类(二级)',
  dataIndex: 'investType',
},
{
  title: '是否行业基金',
  dataIndex: 'isIndustryFund',
  format: 'bool',
},
{
  title: '偏好行业',
  dataIndex: 'industryLabels',
  render: (text) => {
    return (text || []).join(',')
  },
},
{
  title: '最新规模',
  dataIndex: 'latestScale',
  format: 'hundredMillion',
  width: 150,
},
{
  title: '是否封闭',
  dataIndex: 'isClosed',
  format: 'bool',
},
{
  title: '净值日期',
  dataIndex: 'date',
},
{
  title: '单位净值',
  dataIndex: 'unitNavEndOfTerm',
  format: 'number',
  width: 100,
},
{
  title: '复权单位净值',
  dataIndex: 'resNavEndOfTerm',
  format: 'number',
  width: 100,
},
{
  title: '累计净值',
  dataIndex: 'accNavEndOfTerm',
  format: 'number',
  width: 100,
},
{
  title: '投资风格',
  dataIndex: 'investStyle',
},
{
  title: '比较基准',
  dataIndex: 'benchmark',
}].map(item => {
  return {
    ...item,
    class1: '基本信息',
  }
})

const defaultSeriesFactors = [{
  title: '基金代码',
  dataIndex: '_qutkeId',
},
{
  title: '净值日期',
  dataIndex: 'date',
},
{
  title: '单位净值',
  dataIndex: 'unitNavEndOfTerm',
  format: 'number',
  width: 100,
},
{
  title: '复权单位净值',
  dataIndex: 'resNavEndOfTerm',
  format: 'number',
  width: 100,
},
{
  title: '累计净值',
  dataIndex: 'accNavEndOfTerm',
  format: 'number',
  width: 100,
}].map(item => {
  return {
    ...item,
    class1: '基本信息',
  }
})

const getLatestFactorDate = (date, freq) => {
  const endOFM = moment(date).endOf('month').format('YYYYMMDD')
  const endOFQ = moment(date).endOf('quarter').format('YYYYMMDD')
  const endOF1M = moment(date).subtract(1, 'month').endOf('month').format('YYYYMMDD')
  const endOF1Q = moment(date).subtract(1, 'quarter').endOf('quarter').format('YYYYMMDD')

  if (freq === 'M') {
    if (date === endOFM) {
      return date
    }
    return endOF1M
  } else if (freq === 'Q') {
    if (date === endOFQ) {
      return date
    }
    return endOF1Q
  } else if (freq === 'H') {
    const monthDay = date.slice(-4)
    if (['0630', '1231'].includes(monthDay)) {
      return date
    }
    const year = moment(date).year()
    const lastQ = moment(date).subtract(1, 'quarter').endOf('quarter').format('YYYYMMDD').slice(-4)
    const lastHY = ['0930', '0630'].includes(lastQ) ? `${year}0630` : `${year - 1}1231`
    return lastHY
  }
}

const customFactors = factorTypes.reduce((out, factorType) => {
  const factors = periods.filter(p => {
    if (factorType.startPeriod) {
      if (factorType.startPeriod !== '1D') {
        return p.value !== '1D' && p.value !== '1W'
      }
      return true
    }
    return !['1D', '1W', '1M', '3M', '6M', 'ytd'].includes(p.value)
  }).map(p => {
    const dataIndex = `${p.value === 'ytd' ? '' : 'last'}${p.value}${_.upperFirst(factorType.value)}`
    return {
      class1: factorType.class1,
      class2: factorType.class2,
      title: `${factorType.class2}${p.name}`,
      dataIndex,
      format: factorType.format,
      freq: p.value,
    }
  })
  factors.unshift({
    class1: factorType.class1,
    class2: factorType.class2,
    title: `${factorType.class2}`,
    dataIndex: factorType.value,
    format: factorType.format,
    freq: 'all',
  })
  const extraFactors = (factorType.extraFactors || []).map(item => {
    return {
      class1: factorType.class1,
      class2: factorType.class2,
      format: factorType.format,
      ...item,
    }
  })
  return out.concat(factors).concat(extraFactors)
}, [])
// const accFactorValues = customFactors.filter(item => item.freq === 'all').map(item => item.dataIndex)
// const rangeFactorValues = customFactors.filter(item => item.freq !== 'all').map(item => item.dataIndex)
// console.log(JSON.stringify(accFactorValues), JSON.stringify(rangeFactorValues))
const getSeriesTableColumns = (funds, columns) => {
  const results = columns.filter(item => {
    return !textColumns.includes(item.dataIndex)
  }).map((item) => {
    return {
      title: item.title,
      children: funds.map(fund => {
        return {
          title: fund.name,
          dataIndex: `${item.dataIndex}_${fund._id}`,
          width: 80,
          format: item.format,
        }
      }).map(buildTableColumn),
    }
  })
  return [
    {
      title: '日期',
      dataIndex: 'date',
      fixed: 'left',
      width: 120,
    },
    ...results,
  ]
}

const getExportSeriesTableColumns = (funds, columns) => {
  const results = columns.filter(item => {
    return !textColumns.includes(item.dataIndex)
  }).reduce((out, item) => {
    const cols = funds.map(fund => {
      return {
        title: `${item.title}-${fund.name}`,
        dataIndex: `${item.dataIndex}_${fund._id}`,
      }
    })
    return out.concat(cols)
  }, [])
  return [
    {
      title: '日期',
      dataIndex: 'date',
      fixed: 'left',
      width: 120,
    },
    ...results,
  ]
}

const getDefaultTabs = (factors) => {
  return [{
    key: _.uniqueId(),
    isDefault: true,
    name: '组合报表',
    factors: factors,
    funds: [],
  }]
}

export default ({
  location,
}: {
  location: any,
}) => {
  const [reportTpl, setReportTpl] = useState('')
  const [factorTpl, setFactorTpl] = useState('')
  const [editorCollapsed, setEditorCollapsed] = useState(false)
  const [dataLoaded, setDataLoaded] = useState(false)
  const { data: factorList = [] } = useRequest(() => {
    return getFactorList()
  })
  const { data: templates = [], run: runGetReportTemplate } = useRequest(() => {
    return getReportTemplates()
  }, {
    onSuccess: (results) => {
      const { query: { reportTplId, factorTplId } } = location
      const reportTpl = results.find(item => item._id === reportTplId)
      const factorTpl = results.find(item => item._id === factorTplId)
      if (reportTpl) {
        changeReportTemplate(reportTpl, true)
        setEditorCollapsed(true)
      }
      if (factorTpl) {
        changeFactorTemplate(factorTpl)
      }
    },
  })
  const { run: runCreateTemplate, loading: creatingTemplate } = useRequest((data) => {
    return createReportTemplate(data)
  }, {
    manual: true,
    onSuccess: () => {
      message.success('模板保存成功')
      runGetReportTemplate()
    },
  })
  const { run: runRefreshDailyData, loading: loadingDailyData } = useRequest((data) => {
    return refreshDailyData(data)
  }, {
    manual: true,
    onSuccess: (tabData) => {
      message.success('数据刷新成功')
      const newTabs = tabs.map(tab => {
        return {
          ...tab,
          funds: tabData[tab.key] || tab.funds,
        }
      })
      setDataLoaded(true)
      setTabs(newTabs)
      setActiveTab(newTabs[0])
    },
  })
  const { run: runRefreshSeriesData, loading: loadingSeriesData } = useRequest((data) => {
    return refreshSeriesData(data)
  }, {
    manual: true,
    onSuccess: (tabData) => {
      const newTabs = tabs.map(tab => {
        return {
          ...tab,
          fundSeries: tabData[tab.key] || [],
        }
      })
      setTabs(newTabs)
      setActiveTab(newTabs[0])
    },
  })
  const { run: runSendEmail, loading: sendingEmail } = useRequest((data) => {
    return sendEmail(data)
  }, {
    manual: true,
    onSuccess: () => {
      message.success('邮件发送成功')
    },
  })
  const allFactors = defaultFactors.concat(customFactors).concat(factorList.filter(item => {
    return ![
      '基金收益率月频', '基金相对收益率月频', '基金下行风险月频',
      '基金回撤月频', '基金回撤恢复天数月频', '基金Beta系数月频',
      '基金Rsqre月频', '基金年化波动率月频',
      /*
      '基金年化索提诺比率月频',  '基金Jensen系数月频', '基金月度下行捕获率月频',
      '基金VaR月频', '基金年化夏普比率月频',
      '基金年化信息比率月频', '基金基准Jensen系数月频', '基金季度下行捕获率季频',
      */
    ].includes(item.class2)
  })).map((item, index) => {
    item.sortIndex = index + 1
    return item
  })
  const reportTemplates = templates.filter(item => item.templateType === 'report')
  const factorTemplates = templates.filter(item => item.templateType === 'factor')
  const [viewType, setViewType] = useState('crosssection')
  const defaultTabs = getDefaultTabs(defaultFactors)
  const [tabs, setTabs] = useState(defaultTabs)
  const [activeTab, setActiveTab] = useState(defaultTabs[0])
  const handleTabChange = newTabKey => {
    const newTab = tabs.filter(item => item.key === newTabKey)[0]
    setActiveTab(newTab)
  }
  const handleAddTab = name => {
    const names = tabs.map(item => item.name)
    if (names.includes(name)) {
      message.error('Sheet名字不能重复')
      return
    }
    const newTab = {
      name,
      key: _.uniqueId(),
      factors: defaultFactors,
      funds: [],
    }
    setTabs([...tabs, newTab])
    setActiveTab(newTab)
  }
  const handleRemoveTab = targetKey => {
    const newTabs = tabs.filter(item => item.key !== targetKey)
    setTabs(newTabs)
    if (newTabs[0]) {
      setActiveTab(newTabs[0])
    }
  }
  const handleEditTab = (targetKey, action) => {
    if (action === 'remove') {
      handleRemoveTab(targetKey)
    }
  }
  const updateTabData = (tabData) => {
    const newTab = { ...activeTab, ...tabData }
    const index = _.findIndex(tabs, item => item.key === newTab.key)
    tabs.splice(index, 1, newTab)
    setTabs(tabs)
    setActiveTab(newTab)
  }
  const onAddFactors = (factorValues) => {
    const oldValues = activeTab.factors.map(item => item.dataIndex)
    const values = _.uniq(oldValues.concat(factorValues))
    const newFactors = [...allFactors].filter(item => values.includes(item.dataIndex))
    updateTabData({ factors: newFactors })
  }
  const onRemoveFactor = factor => {
    const newFactors = activeTab.factors.filter(item => item.dataIndex !== factor.dataIndex)
    updateTabData({ factors: newFactors })
  }
  const swapItems = (arr, index1, index2) => {
    arr[index1] = arr.splice(index2, 1, arr[index1])[0]
    return arr
  }
  const onMoveUpFactor = factor => {
    const index = activeTab.factors.findIndex(item => item.dataIndex === factor.dataIndex)
    const newFactors = swapItems(activeTab.factors, index, index - 1)
    updateTabData({ factors: newFactors })
  }
  const onMoveDownFactor = factor => {
    const index = activeTab.factors.findIndex(item => item.dataIndex === factor.dataIndex)
    const newFactors = swapItems(activeTab.factors, index, index + 1)
    updateTabData({ factors: newFactors })
  }
  const onClearFactors = () => {
    updateTabData({ factors: [] })
  }
  const fundFields = ['_id', '_qutkeId', '_syncType', 'name', 'managers', 'benchmark', 'fundNature']
  const onAddFunds = (funds) => {
    const oldIds = activeTab.funds.map(item => item._id)
    const newFunds = funds
      .filter(item => !oldIds.includes(item._id))
      .map(item => _.pick(item, fundFields))
    updateTabData({ funds: activeTab.funds.concat(newFunds) })
  }
  const onRemoveFund = fund => {
    const newFunds = activeTab.funds.filter(item => item._id !== fund._id)
    updateTabData({ funds: newFunds })
  }
  const [date, setDate] = useState(null)
  const onDateChange = date => {
    setDate(date)
  }
  const [dateRange, setDateRange] = useState([])
  const onDateRangeChange = dateRange => {
    setDateRange(dateRange)
  }
  const [frequency, setFrequency] = useState('monthly')
  const changeFactorTemplate = template => {
    setFactorTpl(template)
    const factors = JSON.parse(template.content).factors
    updateTabData({ factors: factors })
  }
  const onFactorTemplateChange = templateId => {
    const template = templates.find(item => item._id === templateId)
    changeFactorTemplate(template)
  }
  const onViewTypeChange = event => {
    const viewType = event.target.value
    setViewType(viewType)
    // if (tabs.length === 1 && tabs[0].isDefault) {
    //   const newTabs = getDefaultTabs(viewType === 'crosssection' ? defaultFactors : defaultSeriesFactors)
    //   setTabs(newTabs)
    //   setActiveTab(newTabs[0])
    // }
  }
  const changeReportTemplate = (template, forceRefresh) => {
    const content = JSON.parse(template.content)
    const { frequency, date, dateRange, reportType, tabs } = content
    setReportTpl(template)
    setTabs(tabs)
    setActiveTab(tabs[0])
    setViewType(reportType)
    if (reportType === 'series') {
      setFrequency(frequency)
      setDateRange(dateRange.map(item => moment(item)))
    } else {
      setDate(moment(date))
    }
    if (forceRefresh) {
      const data = {
        frequency,
        date,
        dateRange,
        tabs,
        viewType: reportType,
      }
      runRefreshDailyData(data)
      if (reportType === 'series') {
        runRefreshSeriesData(data)
      }
    }
  }
  const onReportTemplateChange = templateId => {
    const template = templates.find(item => item._id === templateId)
    changeReportTemplate(template)
  }
  const onRefreshData = () => {
    if (viewType === 'crosssection' && !date) {
      message.warn('请选择日期')
      return
    }
    if (viewType === 'series' && (!dateRange || !dateRange[0] || !dateRange[1])) {
      message.warn('请选择日期')
      return
    }
    const allFunds = tabs.reduce((out, item) => {
      return out.concat(item.funds)
    }, [])
    if (!allFunds.length) {
      message.warn('请添加组合')
      return
    }
    const data = {
      frequency,
      date,
      dateRange,
      tabs,
      viewType,
    }
    runRefreshDailyData(data)
    if (viewType === 'series') {
      setTimeout(() => {
        runRefreshSeriesData(data)
      }, 1500)
    }
  }
  const getFileDateStr = (date) => {
    if (!date) {
      return ''
    }
    return date.format('YYYYMMDD')
  }
  const getFileName = () => {
    let reportName = reportTpl ? reportTpl.name : ''
    if (!reportName) {
      if (viewType === 'series') {
        reportName = '时间序列报表'
      } else {
        reportName = '截面数据报表'
      }
    }
    return `${reportName}-${moment().format('YYYYMMDD')}`
  }
  const getFileDate = () => {
    let filename = ''
    if (viewType === 'series') {
      filename = `${getFileDateStr(dateRange[0])}~${getFileDateStr(dateRange[1])}`
    } else {
      filename = `${getFileDateStr(date)}`
    }
    return filename
  }
  const getExportData = () => {
    const infoSheet = {
      name: '报表信息',
      columns: [{
        title: '',
        dataIndex: 'name',
      }, {
        title: '',
        dataIndex: 'value',
      }],
      dataSource: [{
        name: '统计时间',
        value: getFileDate(),
      }, {
        name: '报告模板',
        value: reportTpl ? reportTpl.name : '',
      }, {
        name: '指标模板',
        value: factorTpl ? factorTpl.name : '',
      }],
    }
    return tabs.reduce((out, tab) => {
      const data = [{
        name: tab.name,
        dataSource: tab.funds,
        columns: [{ title: '组合名称', dataIndex: 'name' }, ...tab.factors],
      }]
      if (viewType === 'series') {
        const seriesData = {
          name: `${tab.name}时间序列数据`,
          dataSource: tab.fundSeries || [],
          columns: getExportSeriesTableColumns(tab.funds, tab.factors),
        }
        data.push(seriesData)
      }
      return out.concat(data)
    }, [infoSheet])
  }
  const onSendEmail = (to) => {
    if (!dataLoaded) {
      message.warn('请先查询报表数据')
      return
    }
    const subject = getFileName()
    const data = getExportData().map(tab => {
      return {
        name: tab.name,
        data: getSheetData(tab.dataSource, tab.columns),
      }
    })
    runSendEmail({
      to,
      subject,
      text: subject,
      data,
    })
  }
  const onDownloadClick = () => {
    if (!dataLoaded) {
      message.warn('请先查询报表数据')
      return
    }
    const filename = getFileName()
    const tables = getExportData()
    exportMultiTableAsExcel(filename, tables)
  }
  const columns = [
    {
      title: '组合名称',
      dataIndex: 'name',
      fixed: 'left',
      ellipsis: true,
      width: 150,
      render: ((text, record) => {
        return (
          <Space className={styles.tableFundItem}>
            <span>{text}</span>
            <Tooltip title="删除">
              <DeleteOutlined className={styles.icon} onClick={() => onRemoveFund(record)}/>
            </Tooltip>
          </Space>
        )
      }),
    },
    ...activeTab.factors,
  ].map(factor => {
    const item = _.cloneDeep(factor)
    const factorCode = item.dataIndex
    item.origTitle = item.title
    const theDate = viewType === 'series' ? dateRange[1] : date
    if (theDate) {
      const today = moment().format('YYYYMMDD')
      let dateStr = moment(theDate).format('YYYYMMDD')
      if (dateStr > today) {
        dateStr = today
      }
      let suffix = ''
      if (factorCode.includes('_M_')) {
        suffix = getLatestFactorDate(dateStr, 'M')
      } else if (factorCode.includes('_Q_')) {
        // suffix = getLatestFactorDate(dateStr, 'Q')
        suffix = getLatestFactorDate(dateStr, 'M')
      } else if (factorCode.includes('_H_')) {
        // suffix = getLatestFactorDate(dateStr, 'H')
        suffix = getLatestFactorDate(dateStr, 'M')
      }
      if (suffix) {
        item.title = `${item.title}(${suffix})`
      }
    }
    if (!item.width) {
      item.width = 150
      item.ellipsis = true
    }
    return item
  }).map(buildTableColumn)
  const seriesColumns = getSeriesTableColumns(activeTab.funds, activeTab.factors)
  return (
    <div>
      <Row gutter={8}>
        <Col xxl={21} xl={19} lg={19} md={19} sm={24}>
          <Card
            title="参数设置"
            extra={
              <Switch checked={!editorCollapsed} checkedChildren="指标面板" unCheckedChildren="指标面板" onChange={checked => setEditorCollapsed(!checked)} />
            }
          >
            <Space>
              <Radio.Group value={viewType} onChange={onViewTypeChange}>
                <Radio.Button value="crosssection">截面数据</Radio.Button>
                <Radio.Button value="series">时间序列</Radio.Button>
              </Radio.Group>
              {viewType === 'crosssection' &&
              <DatePicker onChange={onDateChange} value={date}/>}
              {viewType === 'series' &&
              <RangePicker onChange={onDateRangeChange} value={dateRange}/>}
              {viewType === 'series' &&
              <div>
                <span>数据频率：</span>
                <Select
                  onChange={(frequency) => { setFrequency(frequency) }}
                  style={{ width: 50 }}
                  size="small"
                  value={frequency}
                >
                  <Option value="daily">日</Option>
                  <Option value="weekly">周</Option>
                  <Option value="monthly">月</Option>
                  <Option value="quarterly">季</Option>
                  <Option value="yearly">年</Option>
                </Select>
              </div>}
              <div>
                <span>报告模板：</span>
                <Select
                  value={reportTpl && reportTpl._id}
                  style={{ width: 150 }}
                  onChange={onReportTemplateChange}
                >
                  {reportTemplates.map(item => <Option value={item._id}>{item.name}</Option>)}
                </Select>
              </div>
              <div>
                <span>指标模板：</span>
                <Select
                  value={factorTpl && factorTpl._id}
                  style={{ width: 150 }}
                  onChange={onFactorTemplateChange}
                >
                  {factorTemplates.map(item => <Option value={item._id}>{item.name}</Option>)}
                </Select>
              </div>
            </Space>
          </Card>
        </Col>
        <Col xxl={3} xl={5} lg={5} md={5} sm={24}>
          <Card title="操作" style={{ textAlign: 'center' }}>
            <Space>
              <Tooltip title="查询报表数据">
                <Button onClick={onRefreshData} shape="circle" icon={<SearchOutlined />} />
              </Tooltip>
              <SaveTemplateButton
                createTemplate={runCreateTemplate}
                title="保存报告模板"
                templateType="report"
                content={JSON.stringify({
                  reportType: viewType,
                  frequency,
                  date,
                  dateRange,
                  tabs: tabs.map(item => {
                    return {
                      ...item,
                      funds: item.funds.map(fund => _.pick(fund, fundFields)),
                      fundSeries: [],
                    }
                  }),
                })}
              >
                <Tooltip title="保存为报告模板">
                  <Button shape="circle" icon={<SaveOutlined />} />
                </Tooltip>
              </SaveTemplateButton>
              <Tooltip title="下载报表">
                <ExportData shape="circle" onClick={onDownloadClick}/>
              </Tooltip>
              <SendEmailButton sendEmail={onSendEmail} title="发送报表到邮箱">
                <Tooltip title="邮件发送">
                  <Button loading={sendingEmail} shape="circle" icon={<MailOutlined/>} />
                </Tooltip>
              </SendEmailButton>
              {false &&
              <Tooltip title="模板管理">
                <Button onClick={() => window.open('/reportbuilder/templates')} shape="circle" icon={<SettingOutlined/>} />
              </Tooltip>}
            </Space>
          </Card>
        </Col>
      </Row>
      <Row gutter={8} className={styles.main}>
        {!editorCollapsed &&
        <Col xxl={3} lg={5} md={4} sm={24}>
          <Card title="可选指标" size="small">
            <FactorTree
              data={allFactors}
              treeData={buildTreeData(allFactors, ['class1', 'class2', 'class3'], true)}
              onChange={onAddFactors}
            />
          </Card>
        </Col>}
        <Col xxl={editorCollapsed ? 24 : 18} lg={editorCollapsed ? 24 : 14} md={editorCollapsed ? 24 : 16} sm={24}>
          <Card>
            <Tabs
              type="editable-card"
              activeKey={activeTab.key}
              addIcon={
                <AddTabButton onChange={(name) => { handleAddTab(name) }}/>
              }
              onEdit={handleEditTab}
              onChange={handleTabChange}
              tabBarExtraContent={
                <SelectFundModal title="请选择组合" onChange={(funds) => { onAddFunds(funds) }}>
                  <Button size="small" icon={<PlusOutlined />}>
                    添加组合
                  </Button>
                </SelectFundModal>
              }
            >
              {tabs.map(tab => (
                <TabPane tab={tab.name} key={tab.key}>
                  <Table sticky bordered pagination={{ defaultPageSize: 20 }} loading={loadingDailyData} size="small" columns={columns} scroll={{ x: 1300, y: 600 }} dataSource={activeTab.funds}/>
                  <div style={{ marginBottom: 15 }}></div>
                  {viewType === 'series' &&
                  <Table sticky bordered pagination={{ defaultPageSize: 20 }} loading={loadingSeriesData} size="small" columns={seriesColumns} scroll={{ x: 1300, y: 600 }} dataSource={activeTab.fundSeries || []}/>}
                </TabPane>
              ))}
            </Tabs>
          </Card>
        </Col>
        {!editorCollapsed &&
        <Col xxl={3} lg={5} md={4} sm={24}>
          <Card
            title={`已选指标(${activeTab.factors.length})`}
            size="small"
            className={styles.factorListWrapper}
            extra={
              activeTab.factors.length > 0 && <a onClick={onClearFactors}>清空</a>
            }
          >
            <SaveTemplateButton
              createTemplate={runCreateTemplate}
              title="保存指标模板"
              templateType="factor"
              content={JSON.stringify({
                factors: activeTab.factors,
              })}
            >
              <Tooltip title="保存为指标模板">
                <Button loading={creatingTemplate} className={styles.topAddBtn} onClick={() => {}}>
                  保存指标模板
                </Button>
              </Tooltip>
            </SaveTemplateButton>
            <List
              size="small"
              className={styles.factorList}
              dataSource={activeTab.factors}
              renderItem={(item, index) => {
                return (
                  <List.Item
                    className={styles.factorListItem}
                    extra={
                      <Space direction="horizontal" size="small">
                        {index !== 0 &&
                        <Tooltip title="上移" placement="bottom">
                          <ArrowUpOutlined className={styles.icon} onClick={() => onMoveUpFactor(item)}/>
                        </Tooltip>}
                        {index !== activeTab.factors.length - 1 &&
                        <Tooltip title="下移" placement="top">
                          <ArrowDownOutlined className={styles.icon} onClick={() => onMoveDownFactor(item)}/>
                        </Tooltip>}
                        <Tooltip title="删除">
                          <DeleteOutlined className={styles.icon} onClick={() => onRemoveFactor(item)}/>
                        </Tooltip>
                      </Space>
                    }
                  >{item.title}</List.Item>
                )
              }}
            />
          </Card>
        </Col>}
      </Row>
      <Divider orientation="left" plain>
        帮助文档
      </Divider>
      <p><a href="/files/ArchimedesFactorProfile-V1.0.pdf" target="_blank">阿基米德系统因子说明文档</a></p>
    </div>
  )
}
