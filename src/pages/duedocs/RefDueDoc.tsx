import React, { useState } from 'react'
import _ from 'lodash'
import moment from 'moment'
import { connect } from 'dva'
import {
  Table,
  Tooltip,
  Card,
  Tag,
  Space,
  Input,
} from 'antd'
import ExportData from '@/components/ExportData'
import { useRequest } from '@umijs/hooks'
import { queryRefDueDoc } from './service'
import { getToken } from '@/utils/utils'

const Search = Input.Search

const RefDueDoc = ({
  currentFund, currentManager,
  location: { pathname }
}) => {
  const isFund = /fund|activefund|portfolios/.test(pathname)
  const currentData = isFund ? currentFund : currentManager
  const refType = isFund ? 'fund' : 'manager'
  const refId = currentData._id
  const [input, setInput] = useState('')
  const { tableProps, loading } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize, refType, refId }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    return queryRefDueDoc(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [input],
  })
  const handleClickDownload = (record) => {
    const href = `/api/duedocs/${record._id}/download?token=${getToken().slice(7)}`
    window.open(href)
  }
  const handleClickFund = (item) => {
    const href = `/${item.fundType === 'mutual' ? 'fund' : 'activefund'}/${item.id}/ref_due_doc`
    window.open(href)
  }
  const handleClickManager = (item) => {
    const href = `/manager/persona/${item.id}/ref_due_doc`
    window.open(href)
  }
  const columns = [
    {
      title: '文档属性',
      dataIndex: 'docType',
      fixed: 'left',
      width: 90,
    },
    {
      title: '调研对象',
      dataIndex: 'researchObject',
      fixed: 'left',
      width: 120,
    },
    {
      title: '所属公司',
      dataIndex: 'affiliatedCompany',
      width: 120,
    },
    {
      title: '资产类别',
      dataIndex: 'assetCategory',
      width: 120,
    },
    {
      title: '策略简述',
      dataIndex: 'strategyDescription',
      width: 200,
    },
    {
      title: '报告人',
      dataIndex: 'reporter',
      width: 120,
    },
    {
      title: '报告时间',
      dataIndex: 'reportingTime',
      width: 120,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD')
      }
    },
    {
      title: '优先级评级',
      dataIndex: 'priorityRating',
      width: 120,
    },
    {
      title: '评级时间',
      dataIndex: 'ratingTime',
      width: 120,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD')
      }
    },
    // {
    //   title: '关联内容',
    //   dataIndex: 'refFunds',
    //   width: 200,
    //   render: (text, record) => {
    //     const refFunds = record.refFunds.map(item => {
    //       return <Tag style={{ cursor: 'pointer' }} color="red" onClick={() => handleClickFund(item)}>{item.name}</Tag>
    //     })
    //     const refManagers = record.refManagers.map(item => {
    //       return <Tag style={{ cursor: 'pointer' }} color="blue" onClick={() => handleClickManager(item)}>{item.name}</Tag>
    //     })
    //     return refFunds.concat(refManagers)
    //   }
    // },
    {
      title: '创建人',
      dataIndex: 'author',
      width: 120,
      render: (val) => {
        return val && val.nickname
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      width: 150,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD hh:mm')
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (text, record) => {
        return (
          <>
            <Space>
              <ExportData isIcon title={record.fileInfo ? record.fileInfo.name : '下载'} onClick={() => handleClickDownload(record)}/>
            </Space>
          </>
        )
      },
    },
  ]
  return (
    <div style={{ minHeight: 400 }}>
      <Card
        className="zero-padding-card"
        title={
          <>
            <Search
              size="small"
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
      >
        <Table
          size="small"
          columns={columns}
          rowKey="_id"
          {...tableProps}
          scroll={{ x: 700 }}
        />
      </Card>
    </div>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    manager: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    currentManager: fund.currentManager,
    loading: loading.models.fund,
  }),
)(RefDueDoc)
