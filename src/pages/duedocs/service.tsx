import request from '@/utils/request'

export async function queryDueDocs(params?: any) {
  return request('/api/duedocs', {
    params,
  })
}

export async function queryDueDoc(id: string) {
  return request(`/api/duedocs/${id}`)
}

export async function queryRefDueDoc(params?: any) {
  return request(`/api/duedocs/ref/list`, {
    params,
  })
}

export async function deleteDueDoc(id: string) {
  return request(`/api/duedocs/${id}`, {
    method: 'delete',
  })
}

export async function updateDueDoc(id: string, data: any) {
  return request(`/api/duedocs/${id}`, {
    method: 'put',
    data,
  })
}

export async function createDueDoc(data: any) {
  return request(`/api/duedocs`, {
    method: 'post',
    data,
  })
}

export async function queryGroupList() {
  return request(`/api/duedocs/usergroup/list`)
}