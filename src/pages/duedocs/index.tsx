import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import { connect } from 'dva'
import _ from 'lodash'
import moment from 'moment'
import { DeleteOutlined, EditOutlined, PlusOutlined, InboxOutlined } from '@ant-design/icons'
import {
  Table,
  Button,
  Tooltip,
  Popconfirm,
  Modal,
  Input,
  Card,
  Form,
  notification,
  Spin,
  Breadcrumb,
  Upload,
  Tag,
  message,
  Space,
  Row,
  Col,
  DatePicker,
  Select,
} from 'antd'
import ExportData from '@/components/ExportData'
import { queryDueDocs, updateDueDoc, createDueDoc, deleteDueDoc, queryGroupList } from './service'
import t from '@/utils/t'
import FundListFormItem from '@/components/FundListFormItem'
import ManagerListFormItem from '@/components/ManagerListFormItem'
import { getToken } from '@/utils/utils'

const Search = Input.Search

const List = ({
  currentUser,
}: {
  currentUser: any,
}) => {
  const [form] = Form.useForm()
  const { setFieldsValue, getFieldValue } = form
  const initialFormValue = {
    name: '', description: '', fileInfo: null, refFunds: [], refManagers: [],
    userGroupIds: [], userGroups: [],
    researchObject: '', // 调研对象
    affiliatedCompany: '', // 所属公司
    assetCategory: '', // 资产类别
    strategyDescription: '', // 策略简述
    reporter: '', // 报告人
    reportingTime: moment(), // 报告时间
    priorityRating: '', // 优先级评级
    ratingTime: moment(), // 评级时间
  }
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState({})
  const [refreshCount, setRefreshCount] = useState(0)
  const [uploadCount, setUploadCount] = useState(0)
  const [input, setInput] = useState('')
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    return queryDueDocs(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const onSaveDueDocSuccess = () => {
    handleOpenClose()
    setRefreshCount(refreshCount + 1)
    notification.success({
      message: '保存成功',
    })
  }
  const { loading: updatingDueDoc, run: doUpdateDueDoc} = useRequest((id, data) => {
    return updateDueDoc(id, data)
  }, {
    manual: true,
    onSuccess: onSaveDueDocSuccess,
  })
  const { loading: creatingDueDoc, run: doCreateDueDoc} = useRequest((data) => {
    return createDueDoc(data)
  }, {
    manual: true,
    onSuccess: onSaveDueDocSuccess,
  })
  const { run: doDeleteDueDoc } = useRequest((id) => {
    return deleteDueDoc(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const { data: groups = [] } = useRequest(() => {
    return queryGroupList()
  }, {
    cacheKey: 'queryGroupList',
  })
  const [uploadStatus, setUploadStatus] = useState({
    uploading: false,
    hasError: false,
  })
  const handleOpenModal = (record) => () => {
    setCurrentItem(record)
    setVisibleTrue()
    setFieldsValue({
      ...record,
      reportingTime: moment(record.reportingTime),
      ratingTime: moment(record.ratingTime),
    })
  }
  const handleOpenClose = () => {
    setVisibleFalse()
    setFieldsValue(initialFormValue)
  }
  const handleClickSave = (values: any) => {
    const userGroupIds = values.userGroupIds
    values.userGroups = groups
      .filter(item => userGroupIds.includes(item._id))
      .map(item => {
        return {
          name: item.name,
          id: item._id,
        }
      })
    if (currentItem._id) {
      doUpdateDueDoc(currentItem._id, values)
    } else {
      doCreateDueDoc(values)
    }
  }
  const handleClickDownload = (record) => {
    const href = `/api/duedocs/${record._id}/download?token=${getToken().slice(7)}`
    window.open(href)
  }
  const handleClickFund = (item) => {
    const href = `/${item.fundType === 'mutual' ? 'fund' : 'activefund'}/${item.id}/ref_due_doc`
    window.open(href)
  }
  const handleClickManager = (item) => {
    const href = `/manager/persona/${item.id}/ref_due_doc`
    window.open(href)
  }
  const assetCategoryList = [
    '权益',
    '固收',
    '混合',
  ]
  const priorityRatingList = ['A', 'B', 'C', 'D']
  const docTypes = [
    '长名单推荐报告',
    '长名单评审记录',
    '短名单推荐报告',
    '短名单评审记录',
    '投顾交流材料',
    '定期检视汇报',
    '聘用名单准入材料',
    '投委会评审记录',
    '仓位调整方案及评审记录',
    '会议纪要',
    '解聘申请及审批',
  ]
  const columns = [
    {
      title: '文档属性',
      dataIndex: 'docType',
      fixed: 'left',
      width: 90,
      filters: docTypes.map(item => {
        return {
          text: item,
          value: item,
        }
      })
    },
    {
      title: '调研对象',
      dataIndex: 'researchObject',
      fixed: 'left',
      width: 120,
    },
    {
      title: '所属公司',
      dataIndex: 'affiliatedCompany',
      width: 120,
    },
    {
      title: '资产类别',
      dataIndex: 'assetCategory',
      width: 120,
      filters: assetCategoryList.map(item => {
        return {
          text: item,
          value: item,
        }
      })
    },
    {
      title: '策略简述',
      dataIndex: 'strategyDescription',
      width: 200,
    },
    {
      title: '报告人',
      dataIndex: 'reporter',
      width: 120,
    },
    {
      title: '报告时间',
      dataIndex: 'reportingTime',
      width: 120,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD')
      }
    },
    {
      title: '优先级评级',
      dataIndex: 'priorityRating',
      width: 120,
      filters: priorityRatingList.map(item => {
        return {
          text: item,
          value: item,
        }
      })
    },
    {
      title: '评级时间',
      dataIndex: 'ratingTime',
      width: 120,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD')
      }
    },
    {
      title: '关联内容',
      dataIndex: 'refFunds',
      width: 200,
      render: (text, record) => {
        const refFunds = record.refFunds.map(item => {
          return <Tag style={{ cursor: 'pointer' }} color="red" onClick={() => handleClickFund(item)}>{item.name}</Tag>
        })
        const refManagers = record.refManagers.map(item => {
          return <Tag style={{ cursor: 'pointer' }} color="blue" onClick={() => handleClickManager(item)}>{item.name}</Tag>
        })
        return refFunds.concat(refManagers)
      }
    },
    {
      title: '创建人',
      dataIndex: 'author',
      width: 120,
      render: (val) => {
        return val && val.nickname
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      width: 150,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD hh:mm')
      }
    },
    {
      title: '可见用户组',
      dataIndex: 'userGroups',
      width: 150,
      render: (userGroups, record) => {
        if (currentUser._id !== record.authorId) {
          return false
        }
        const list = (userGroups || [])
        if (!list.length) {
          return  <Tag>仅自己</Tag>
        }
        return list.map(item => {
          return <Tag>{item.name}</Tag>
        })
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (text, record) => {
        if (currentUser._id !== record.authorId) {
          return (
            <ExportData isIcon title={record.fileInfo ? record.fileInfo.name : '下载'} onClick={() => handleClickDownload(record)}/>
          )
        }
        return (
          <>
            <Space>
              <Tooltip title="编辑">
                <EditOutlined onClick={handleOpenModal(record)} />
              </Tooltip>
              <ExportData isIcon title={record.fileInfo ? record.fileInfo.name : '下载'} onClick={() => handleClickDownload(record)}/>
              <Popconfirm
                title={`${t('portfolio.delTip')}${t('portfolio.questionEnd')}？`}
                onConfirm={() => { doDeleteDueDoc(record._id) }}
                onCancel={() => {}}
                okText={t('portfolio.confirm')}
                cancelText={t('portfolio.cancel')}
              >
                <Tooltip title="删除">
                  <DeleteOutlined />
                </Tooltip>
              </Popconfirm>
            </Space>
          </>
        )
      },
    },
  ]

  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e
    }
    const list = e.fileList.map(item => item.response).filter(Boolean)
    return list[list.length - 1]
  }

  const normFundManager = (funds: any) => {
    return funds.map(item => {
      return {
        name: item.name,
        id: item.id,
      }
    })
  }

  const beforeFileUpload = (file) => {
    const fileType = file.type
    const typeList = ['doc', 'docx', 'xls', 'xlsx', 'png', 'jpg', 'jpeg', 'zip', 'pdf', 'tar.gz']
    const valid = typeList.some(type => fileType.includes(type))
    if (!valid) {
      message.error('只能上传后缀为doc,docx,xls,xlsx,png,jpg,jpeg,zip,pdf的文件')
    }
    return valid || Upload.LIST_IGNORE
  }
  const fileInfoValue = getFieldValue('fileInfo')
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>智能尽调</Breadcrumb.Item>
        <Breadcrumb.Item>文档管理</Breadcrumb.Item>
      </Breadcrumb>
      <Card
        title={
          <>
            <Search
              size="small"
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
        extra={
          <>
            <a onClick={
              handleOpenModal(initialFormValue)
            }>
              <PlusOutlined />
              新建文档
            </a>
          </>
        }
      >
        <Table
          size="small" columns={columns} rowKey="_id"
          scroll={{ x: 700 }}
          {...tableProps}
        />
      </Card>
      <Modal
        title={
          <>
            <span>{currentItem && currentItem._id ? '编辑文档' : '新建文档'}</span>
          </>
        }
        visible={visible}
        onCancel={handleOpenClose}
        width={900}
        footer={[
          <Button
            type="primary"
            loading={updatingDueDoc || creatingDueDoc}
            onClick={() => form.submit()}
          >
            保存
          </Button>,
        ]}
      >
        <Spin spinning={updatingDueDoc || creatingDueDoc}>
          <Form
            hideRequiredMark
            form={form}
            layout="vertical"
            initialValues={initialFormValue}
            onFinish={handleClickSave}
          >
            {false &&
            <Form.Item
              label="名称"
              name="name"
              rules={[
                {
                  required: true,
                  message: '请输入名称',
                },
              ]}
            >
              <Input placeholder="名称"/>
            </Form.Item>}
            <Row gutter={8}>
              <Col span={6}>
                <Form.Item
                  label="文档属性"
                  name="docType"
                  rules={[
                    {
                      required: true,
                      message: '请选择文档属性',
                    },
                  ]}
                >
                  <Select
                  >
                    {docTypes.map(item => <Select.Option key={item}>{`${item}`}</Select.Option>)}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={9}>
                <Form.Item
                  label="调研对象"
                  name="researchObject"
                  rules={[
                    {
                      required: true,
                      message: '请输入调研对象',
                    },
                  ]}
                >
                  <Input placeholder="调研对象"/>
                </Form.Item>
              </Col>
              <Col span={9}>
                <Form.Item
                  label="所属公司"
                  name="affiliatedCompany"
                  rules={[
                    {
                      required: true,
                      message: '请输入所属公司',
                    },
                  ]}
                >
                  <Input placeholder="所属公司"/>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={8}>
              <Col span={6}>
                <Form.Item
                  label="资产类别"
                  name="assetCategory"
                  rules={[
                    {
                      required: true,
                      message: '请输入资产类别',
                    },
                  ]}
                >
                  <Select
                  >
                    {assetCategoryList.map(item => <Select.Option key={item}>{`${item}`}</Select.Option>)}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={18}>
                <Form.Item
                  label="策略简述"
                  name="strategyDescription"
                  rules={[
                    {
                      required: true,
                      message: '请输入策略简述',
                    },
                  ]}
                >
                  <Input placeholder="策略简述"/>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  label="报告人"
                  name="reporter"
                  rules={[
                    {
                      required: true,
                      message: '请输入报告人',
                    },
                  ]}
                >
                  <Input placeholder="报告人"/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="报告时间"
                  name="reportingTime"
                  rules={[
                    {
                      required: true,
                      message: '请输入报告时间',
                    },
                  ]}
                >
                  <DatePicker placeholder="报告时间"/>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={8}>
              <Col span={12}>
                <Form.Item
                  label="优先级评级"
                  name="priorityRating"
                  rules={[
                    {
                      required: true,
                      message: '请输入优先级评级',
                    },
                  ]}
                >
                  <Select
                  >
                    {priorityRatingList.map(item => <Select.Option key={item}>{`${item}`}</Select.Option>)}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="评级时间"
                  name="ratingTime"
                  rules={[
                    {
                      required: true,
                      message: '请输入评级时间',
                    },
                  ]}
                >
                  <DatePicker placeholder="评级时间"/>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              label="上传文档"
              name="fileInfo"
              getValueFromEvent={normFile}
              rules={[
                {
                  validator: (rule, values) => {
                    if (!values && !uploadStatus.uploading && !uploadStatus.hasError) {
                      return Promise.reject('请上传文档')
                    }
                    setUploadCount(uploadCount + 1)
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <Upload.Dragger
                action="/api/duedocs/upload"
                headers={{
                  authorization: getToken(),
                }}
                name="attachment"
                showUploadList={false}
                beforeUpload={beforeFileUpload}
                // defaultFileList={fileInfoValue && [fileInfoValue]}
                onChange = {({ file }) => {
                  const { status, error } = file
                  if (status === 'uploading') {
                    setUploadStatus({
                      uploading: true,
                      hasError: false,
                    })
                  } else if (status === 'done') {
                    setUploadStatus({
                      uploading: false,
                      hasError: false,
                    })
                  } else if (status === 'error') {
                    if (error && error.status === 413) {
                      notification.error({ message: `文档太大，上传失败，请确保上传文件不要超过100MB.` })
                    } else {
                      notification.error({ message: `${file.name} 上传失败.` })
                    }
                    setUploadStatus({
                      uploading: false,
                      hasError: true,
                    })
                  }
                }}
              >
                <p className="ant-upload-drag-icon">
                  { uploadStatus.uploading ? <Spin/> : <InboxOutlined />}
                </p>
                <p className="ant-upload-text">
                  {!uploadStatus.hasError && fileInfoValue ? <a>{`已上传: ${fileInfoValue.name}`}</a> : '点击或者拖放文件到此处进行上传'}
                </p>
                <p className="ant-upload-hint">支持上传的文件类型为: doc,docx,xls,xlsx,png,jpg,jpeg,zip,pdf</p>
                <p className="ant-upload-hint">上传附件请不要超过100MB</p>
              </Upload.Dragger>
            </Form.Item>
            <Form.Item
              label="可见用户组"
              name="userGroupIds"
            >
              <Select
                showSearch
                mode="multiple"
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {groups.map(item => <Select.Option key={item._id}>{`${item.name}`}</Select.Option>)}
              </Select>
            </Form.Item>
            <Form.Item
              name="refFunds"
            >
              <FundListFormItem
                defaultTabs={[
                  {
                    name: '实盘组合',
                    value: 'private',
                  },
                  {
                    name: '公募基金',
                    value: 'mutual',
                  },
                ]}
                title="关联基金"
                addTitle="添加基金"
                hideEmpty
              />
            </Form.Item>
            <Form.Item
              name="refManagers"
            >
              <ManagerListFormItem
                title="关联基金经理"
                addTitle="添加基金经理"
                hideEmpty
              />
            </Form.Item>
          </Form>
        </Spin>
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
