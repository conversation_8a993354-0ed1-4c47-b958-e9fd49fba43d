import { Effect } from 'dva'
import { Reducer } from 'redux'
import { loadList } from './service'
export interface ModelState {
  surveyList?: any
}

export interface ModelType {
  namespace: 'userSurvey';
  state: ModelState;
  effects: {
    fetchUserSurvey: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
  };
}

const UserSurveysModel: ModelType = {
  namespace: 'userSurvey',
  state: {
    surveyList: null
  },
  effects: {
    *fetchUserSurvey({ payload }, { call, put }) {
      const response = yield call(loadList, { source: 'invite' })
      yield put({
        type: 'save',
        payload: {
          surveyList: response,
        },
      })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
  },
}

export default UserSurveysModel
