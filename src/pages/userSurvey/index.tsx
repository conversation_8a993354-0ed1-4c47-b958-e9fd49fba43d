import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'dva'
import { Link } from 'umi'
import classnames from 'classnames'
import moment from 'moment'
import { Empty, Spin, Table } from 'antd'
import styles from './style.less'
import { Dispatch } from 'redux'
import { ModelState } from './model'
interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  surveyList?: any,
}
@connect(
  ({
    userSurvey,
    loading,
  }: {
    userSurvey: ModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    surveyList: userSurvey.surveyList,
    loading: loading.effects['userSurvey/fetchUserSurvey'],
  }),
)

class UserSurvey extends Component<ComponentProps, ComponentState> {

  componentDidMount() {
    const pathname = this.props.location.pathname
    if (pathname) {
      this.loadList()
    }
  }

  loadList = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'userSurvey/fetchUserSurvey'
    })
  }

  getColumns = () => {
    return [
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        render: (text, row) => {
          return <Link style={{ color: 'orange' }} to={`/duediligence/survey/${row._id}`} title={row.title}>
            {row.isSpecial && <i className="fa fa-diamond" />}
            {row.isSpecial && ' '}
            {row.title}
          </Link>
        }
      },
      {
        title: '发布者',
        dataIndex: 'company',
        key: 'company',
        render: (text, row) => {
          return <span>{row.publisher.company}</span>
        }
      },
      {
        title: '发布时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        render: (text, row) => {
          return <span>{moment(row.updated_at).from(Date.now())}</span>
        }
      },
      {
        title: '状态',
        dataIndex: 'answered',
        key: 'answered',
        render: (text, row) => {
          return <span>{row.answered ? '已提交' : '未提交'}</span>
        }
      },
      {
        title: '回收状态',
        dataIndex: 'status',
        key: 'status',
        render: (text, row) => {
          return <span>{row.status == 'on' ? '正在回收' : '暂停回收'}</span>
        }
      },
      {
        title: '提交时间',
        dataIndex: 'answered',
        key: 'answered',
        render: (text, row) => {
          return <span>{!row.answered ? '未提交' : moment(row.submitTime || row.updated_at).from(Date.now())}</span>
        }
      },

    ]
  }

  render() {
    const { surveyList } = this.props
    if (!surveyList) {
      return <div style={{ display: 'flex', justifyContent: 'center', alignContent: 'center' }}><Spin /></div>
    }
    if (surveyList && surveyList.length == 0) {
      return <div style={{ display: 'flex', justifyContent: 'center', alignContent: 'center' }}><Empty /></div>
    }
    const columns = this.getColumns()
    return (
      <div style={{ width: '90%', margin: 'auto', marginTop: 30 }} >
        {
          surveyList && surveyList.length &&
          <Table
            pagination={false}
            bordered
            dataSource={surveyList}
            columns={columns}
          />
        }
      </ div>
    )
  }
}


export default UserSurvey
