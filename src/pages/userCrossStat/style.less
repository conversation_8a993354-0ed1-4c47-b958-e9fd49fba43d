.crossStatePage {
  margin-top: 50px;
  .error {
    margin: 150px auto;
  }
  .main {
    padding-top: 20px;
    background-color: #242424;
    color: white;
    :global(.table-bordered > tbody > tr > td){
      border-bottom-width:0px;
      border:1px solid #6a6a6a
    }
    :global(.table-bordered > thead > tr > td){
      border-bottom-width:0px;
      border:1px solid #6a6a6a
    }
    .leftButton {
      float: left;
      margin-right: 10px;
    }
    .rightButton {
      float: right;
      margin-left: 10px;
      background-color: #424242;
      color:white;
      outline: none;
      :global(.btn.btn-default){
        color:#fffefe !important;
        background-color:#424242 !important;
        outline: none !important;
      }
      :global(.btn.btn-default:focus){
        background-color:rgb(189, 126, 9) !important;
      }
      :global(.btn.btn-default:visited){
        background-color:rgb(189, 126, 9) !important;
      }
      :global(.btn.btn-default:target){
        background-color:rgb(189, 126, 9) !important;
      }
      :global(.btn.btn-default.active){
        background-color:rgb(189, 126, 9) !important;
      }
    }
  }
  .chartContainer {
    margin-top: 50px;
    // display: flex;
    // justify-content :space-around;
    :global(.highcharts-container) {
      max-height: 650px;
    }
  }
  .chartSwitcher {
    margin-bottom: 10px;
  }
  .textHtml {
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 40px;
    overflow-y: scroll;
    img {
      display: block;
      margin: 0 auto;
      max-width: 100%;
      max-height: 100%;
    }
    p {
      margin-bottom: -1.2em;
    }
  }
}
