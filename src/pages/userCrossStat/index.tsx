import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'dva'
import classnames from 'classnames'
import { Table } from 'react-bootstrap'
import Chart from '@/components/Chart/Chart'
import AntTdModal from '@/components/AntTdModal'
import AntTable from '@/components/AntTable'
import ExportTableData from '@/components/ExportTableData'
import deduplication from '../../utils/deduplication'
import { Empty, Menu, Select } from 'antd'
import { parse } from 'qs'
import styles from './style.less'
import { CrossStatUserModelState } from './model'
import { router } from 'umi'
const { Option } = Select


interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  match: any;
  currentUser?: CurrentUser,
  surveys?: any
  categories?: any
}

interface ComponentState {
  type: string,
  question: Object,
  quotas: any,
  questions: any,
  questionMap: any,
  activeTab: string
}
@connect(
  ({
    crossStatUser,
    loading,
  }
    : {
      crossStatUser: CrossStatUserModelState;
      loading: {
        effects: {
          [key: string]: boolean;
        };
      };
    }
  ) => {
    return ({
      surveys: crossStatUser.surveys,
      loading: loading.effects['crossStat/fetchSurveyList'],
    })
  }
)

export default class UserCrossStat extends Component<ComponentProps, ComponentState> {
  static propTypes = {
    surveys: PropTypes.array.isRequired,
    resetSurveyState: PropTypes.func.isRequired,
    location: PropTypes.object.isRequired,
    categories: PropTypes.array.isRequired,
    loadList: PropTypes.func
  }

  constructor(props) {
    super(props)
    const questions = this.generateQuestions(props.surveys)
    const questionMap = this.generateQuestionMap(props.surveys)
    const quotas = this.generateQuotas(props.surveys)
    this.state = {
      type: 'line',
      question: questions[0],
      quotas,
      questions,
      questionMap,
      activeTab: 'user'
    }
  }

  async componentDidMount() {
    const { location } = this.props
    const queryObject = location.query || parse(location.search.substr(1))
    const category = queryObject && queryObject.category
    if (category) {
      await this.loadList({ category })
    }
  }

  loadList = (params: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'crossStatUser/fetchSurveyList',
      payload: { params },
    })
  }

  componentDidUpdate(prevProps) {
    const { surveys } = this.props
    if (!prevProps.surveys.length && surveys && surveys.length) {
      const questions = this.generateQuestions(surveys)
      const questionMap = this.generateQuestionMap(surveys)
      const quotas = this.generateQuotas(surveys)
      this.setState({ // eslint-disable-line
        question: questions[0],
        quotas,
        questions,
        questionMap
      })
    }
  }

  onQuestionChange = (value: any) => {
    this.setState({ question: value })
  }

  handleMenuChange = category => event => {
    const activeTab = event.key
    this.setState({ activeTab }, () => {
      if (activeTab === 'cross') {
        router.push(`/duediligence/stat/cross?category=${category}`)
      } else {
        router.push(`/duediligence/stat/crossUser?category=${category}`)
      }
    })
  }

  getQueryObject = location => {
    return location.query || parse(location.search.substr(1))
  }

  getMatrixData = question => {
    let [options, values] = [[], []]
    const title = []
    if (question.type === 'matrix_radio') {
      question.questions
        .map(item => item.value)
        .map(value => title.push(`${question.title}-${value}`))
    } else {
      // 筛选出numeric matrix 画图的字段
      if (this.isQuestionMatrix(question)) {
        values = question.questions
          .filter(item => item.isNumeric)
          .map(item => item.value)
        options = question.options.map(item => item.value)
      }
      if (this.isOptionMatrix(question)) {
        values = question.questions.map(item => item.value)
        options = question.options
          .filter(item => item.isNumeric)
          .map(item => item.value)
      }
      values.map(value => {
        options.map(option => {
          title.push(`${question.title}-${value}-${option}`)
        })
      })
    }
    return { values, options, title }
  }

  switchChartType = type => () => {
    this.setState({ type })
  }

  isNumericQuestion = question =>
    question && ((!!~['radio', 'select', 'matrix_radio', 'input'].indexOf(question.type) &&
      question.isNumeric) ||
      question.type === 'slider' ||
      question.matrixNumric)

  isTextQuestion = question =>
    question && (question.type === 'textarea' ||
      (question.type === 'input' && !question.isNumeric))

  isComplexQuestion = question =>
    question && !!~['table', 'matrix', 'matrix_checkbox'].indexOf(question.type)

  isSimpleQuestion = question =>
    this.isNumericQuestion(question) || this.isTextQuestion(question)

  isQuestionMatrix = question =>
    question && question.type === 'matrix' &&
    question.questions.find(item => item.isNumeric) // question numeric 矩阵

  isOptionMatrix = question =>
    question && question.type === 'matrix' &&
    question.options.find(option => option.isNumeric) // option numeric 矩阵

  isNumericMatrix = question =>
    this.isQuestionMatrix(question) || this.isOptionMatrix(question)

  isNumericSurveyQuestion = question => question && ['matrix', 'table'].includes(question.type) && (question.questions.find(item => item.isNumeric) || question.options.find(option => option.isNumeric))

  isAllNotNumeric = () => {
    const { surveys } = this.props
    return surveys.every(survey => survey.questions.every(question => {
      if (this.isNumericSurveyQuestion(question)) {
        return false
      }
      return !question.isNumeric
    }))
  }

  generateQuestionMap(surveys) {
    return surveys
      .reduce(
        (out, survey) =>
          out.concat(
            survey.questions
              .filter(this.questionFilter)
              .reduce(this.questionReducer, [])
          ),
        []
      )
      .reduce((out, question) => {
        const generateMatrixMap = item => {
          out[item.title] = item
          const { title } = this.getMatrixData(item)
          title.map(name => {
            out[name] = {
              title: name,
              matrixNumric: true
            }
          })
        }
        if (question.type === 'matrix_radio' || question.type === 'matrix') {
          generateMatrixMap(question) // 生成numeric matrix 的 questionMap
        } else {
          out[question.title] = question
        }
        return out
      }, {})
  }

  questionFilter = question => {
    return (
      (question.answers || question.answer) &&
      (this.isSimpleQuestion(question) || this.isComplexQuestion(question))
    )
  }

  questionReducer(out, question) {
    if (
      ~['radio', 'select', 'input', 'slider', 'textarea'].indexOf(question.type)
    ) {
      out.push(question)
    } else if (
      ~['matrix_radio', 'matrix_checkbox', 'table'].indexOf(question.type)
    ) {
      out.push(question)
    } else if (question.type === 'matrix') {
      out.push(question)
    }
    return out
  }

  generateQuestions(surveys) {
    const matrixNames = surveys
      .map(survey => survey.questions.filter(this.questionFilter))
      .reduce((prev, next) => {
        return prev.concat(next)
      }, [])
      .map(item => {
        return this.getMatrixData(item).title
      })
      .filter(item => Array.isArray(item))
      .reduce((prev, next) => {
        return prev.concat(next)
      }, [])
    return deduplication(
      surveys
        .map(survey =>
          survey.questions
            .filter(this.questionFilter)
            .reduce(this.questionReducer, [])
            .filter(item => item.type !== 'matrix_radio')
            .map(item => item.title)
        )
        .reduce((out, names) => out.concat(names), [])
        .concat(matrixNames)
    )
  }

  generateStatData() {
    const { question } = this.state
    const { surveys } = this.props
    const answerList = surveys.map(survey => {
      return survey.questions
        .filter(this.isSimpleQuestion)
        .reduce(this.questionReducer, [])
        .reduce((out, item) => {
          return item.answers.reduce((ret, answer) => {
            ret[answer.author._id] = out[answer.author._id] || {}
            if (answer.question) {
              // matrix_radio类型的renderData
              ret[answer.author._id][`${item.title}-${answer.question}`] =
                answer.answer
            } else {
              ret[answer.author._id][item.title] = answer.answer
            }
            return ret
          }, out)
        }, {})
    })
    // 数值矩阵的renderData
    surveys.forEach((survey, count) => {
      const authorIds = survey.answerAuthorList.map(item => item._id)
      const getAnswers = srcQuestion => {
        const { values, options } = this.getMatrixData(srcQuestion)
        values.forEach(value => {
          options.forEach(opt => {
            authorIds.forEach(author => {
              const titleName = `${srcQuestion.title}-${value}-${opt}`
              const userAnswer =
                srcQuestion.answers[value] &&
                srcQuestion.answers[value].find(
                  answer => answer.author._id === author
                )
              answerList[count][author] = answerList[count][author] || {}
              answerList[count][author][titleName] =
                userAnswer && userAnswer.answer && userAnswer.answer[opt]
            })
          })
        })
      }
      return survey.questions.reduce((out, item) => {
        getAnswers(item)
      }, {})
    })
    const userMap = surveys
      .map(survey =>
        survey.questions.filter(this.isSimpleQuestion).map(item => item.answers)
      )
      .reduce((out, answers) => out.concat(answers), [])
      .reduce((out, answers) => {
        return answers.reduce((ret, item) => {
          ret[item.author._id] = item.author.company
          return ret
        }, out)
      }, {})
    const userIds = deduplication(
      answerList
        .map(item => Object.keys(item))
        .reduce((out, keys) => out.concat(keys), [])
    )
    const data = userIds.map(userId => {
      const ret = { name: userMap[userId] }
      answerList.forEach((item, index) => {
        ret[`survey-${index}`] = item[userId] && item[userId][question]
      })
      return ret
    })
    return data
  }

  generateQuotas(surveys) {
    const quotas = [
      {
        name: '公司',
        value: 'name'
      }
    ]

    surveys.forEach((item, index) => {
      quotas.push({
        name: item.title,
        value: `survey-${index}`
      })
    })
    return quotas
  }

  generateComplexStatData() {
    const { question } = this.state
    const { surveys } = this.props
    return surveys
      .map(survey => {
        const currentQuestion =
          survey.questions.filter(item => item.title === question)[0] || {}
        currentQuestion.surveyName = survey.title
        return currentQuestion
      })
      .reduce(this.complexQuestionReducer, [])
  }

  generateComplexQuotas(question) {
    const { type, options, questions } = question
    const hasNumericQuestion = (questions || []).some(
      subQuestion => subQuestion.isNumeric
    )
    const getFormatter = quota => {
      const quotaValue = quota.value
      if (quota.isDate) {
        return item =>
          item[quotaValue] === 'now' ? '至今' : item[quotaValue]
      }
      return null
    }
    const quotas = [
      {
        name: '问卷',
        value: 'surveyName',
        format: 'text'
      },
      {
        name: '名称',
        value: '__name',
        format: 'text'
      },
      {
        name: '公司',
        value: '__company',
        format: 'text'
      }
    ]
    if (type === 'table') {
      (options || []).forEach(item => {
        quotas.push({
          name: item.value,
          value: item.value,
          formatter: getFormatter(item),
          isNumeric: item.isNumeric
        })
      })
    } else if (type === 'matrix') {
      quotas.push({
        name: '问题',
        format: 'text',
        value: '__question'
      })
        (options || []).forEach(item => {
          quotas.push({
            name: item.value,
            value: item.value,
            formatter: getFormatter(item),
            isNumeric: hasNumericQuestion || item.isNumeric
          })
        })
    } else if (type === 'matrix_radio' || type === 'matrix_checkbox') {
      quotas.push({
        name: '问题',
        format: 'text',
        value: '__question'
      })
      options.forEach(option => {
        quotas.push({
          name: option.value,
          value: option.value
        })
      })
    }
    return quotas
  }

  generateAntQuotas = quotas => {
    return quotas.map(quota => {
      const antQuota = {
        ...quota,
        hasSorter: true
      }
      if (
        ['surveyName', '__name', '__company', '__question'].includes(
          quota.value
        )
      ) {
        antQuota.fixed = 'left'
        antQuota.hasFilter = true
        antQuota.width = 114
      } else if (quota.formatter === null) {
        antQuota.format = 'text'
      }
      return antQuota
    })
  }

  complexQuestionReducer(retData, question) {
    const { type, options, answers, questions } = question
    let data = []
    if (type === 'table') {
      data = (answers || []).map(item => {
        return {
          author: item.author,
          ...(item.answer || {})
        }
      })
    } else if (type === 'matrix') {
      questions.forEach(subQuestion => {
        ; (answers[subQuestion.value] || []).forEach(item => {
          data.push({
            __question: subQuestion.value,
            author: item.author,
            ...item.answer
          })
        })
      })
    } else if (type === 'matrix_radio' || type === 'matrix_checkbox') {
      const answerByUser = answers.reduce((out, answer) => {
        const userId = answer.author._id
        out[userId] = out[userId] || []
        out[userId].push(answer)
        return out
      }, {})
      data = Object.keys(answerByUser).reduce((out, userId) => {
        const answerByQuestion = answerByUser[userId].reduce((ret, answer) => {
          const questionTitle = answer.question
          ret[questionTitle] = ret[questionTitle] || []
          ret[questionTitle].push(answer.answer)
          return ret
        }, {})
        const author = answerByUser[userId][0].author
        const list = Object.keys(answerByQuestion).map(key => {
          const ret = { author, __question: key }
          options.forEach(option => {
            if (~answerByQuestion[key].indexOf(option.value)) {
              ret[option.value] = <i className="fa fa-check-square-o" />
            }
          })
          return ret
        })
        return out.concat(list)
      }, [])
    }
    data = data.map(item => ({
      __name: item.author && item.author.nickname,
      __company: item.author && item.author.company,
      _id: item.author && item.author._id,
      surveyName: question.surveyName,
      ...item
    }))
    return retData.concat(data)
  }

  renderTextContent(item, quota) {
    const { question, questionMap } = this.state
    const currentQuestion = questionMap[question]
    const isTextarea = currentQuestion.type === 'textarea'
    const value = item[quota.value]
    if (!value) {
      return <span>-</span>
    }
    if (isTextarea) {
      return (
        <AntTdModal text={value} isHtml>
          <div
            className={styles.textHtml}
            dangerouslySetInnerHTML={{ __html: value }}
          />
        </AntTdModal>
      )
    }
    return <span>{value}</span>
  }

  renderComplexTable() {
    const { questionMap, question } = this.state
    const currentQuestion = questionMap[question]
    const quotas = this.generateComplexQuotas(currentQuestion)
    const data = this.generateComplexStatData()
    const antQuotas = this.generateAntQuotas(quotas)
    return <AntTable quotas={antQuotas} data={data} />
  }

  renderTable() {
    const { quotas } = this.state
    const data = this.generateStatData()
    return (
      <Table responsive bordered>
        <thead>
          <tr>
            {quotas.map(quota => (
              <td key={`table-head-${quota.value}`}>{quota.name}</td>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={`table-body-${index}`}>
              {quotas.map((quota, idx) => (
                <td key={`table-body-${index}-${quota.value}-${idx}`}>
                  {this.renderTextContent(item, quota)}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </Table>
    )
  }

  renderChart() {
    const { type, question, questionMap } = this.state
    if (!this.isNumericQuestion(questionMap[question])) {
      return
    }
    const { surveys } = this.props
    const categories = surveys.map(survey => survey.title)
    const remarkMap = {}
    surveys
      .map(survey =>
        survey.questions.filter(this.isNumericQuestion).map(item => {
          item.surveyTitle = survey.title
          return item
        })
      )
      .reduce((out, items) => out.concat(items), [])
      .forEach(item => {
        item.answers.forEach(
          answer =>
            (remarkMap[`${item.surveyTitle}-${item.title}-${answer.author.company}`] = answer.remark)
        )
      })
    const data = this.generateStatData()
    const series = data.map(item => {
      return {
        name: item.name,
        data: categories
          .map((category, index) => item[`survey-${index}`])
          .map(value =>
            value === undefined || Number.isNaN(Number(value))
              ? null
              : Number(value)
          )
      }
    })
    const config = {
      chart: {
        type: type,
        pinchType: '',
        height: 400
      },
      exporting: {
        enabled: false
      },
      title: {
        text: ''
      },
      credits: {
        enabled: false
      },
      xAxis: {
        categories
      },
      yAxis: {
        title: {
          text: '分数'
        }
      },
      colors: [
        '#7cb5ec',
        '#90ed7d',
        '#f7a35c',
        '#8085e9',
        '#f15c80',
        '#e4d354',
        '#2b908f',
        '#f45b5b',
        '#91e8e1'
      ],
      series: series,
      tooltip: {
        useHTML: true,
        pointFormatter() {
          let result = ''
          const remark =
            remarkMap[`${this.category}-${question}-${this.series.name}`] || ''
          result += `<span style="color: ${this.color}">\u25CF</span> ${this.series.name
            }: `
          result += `<span>${this.y}</span><br/>`
          result += remark
          return result
        }
      }
    }
    return <Chart options={config} />
  }

  render() {
    const { location } = this.props
    const query = this.getQueryObject(location)
    const category = query && query.category
    const { type, question, questions, questionMap, quotas, activeTab } = this.state
    const currentQuestion = questionMap[question]
    const isTextQuestion = this.isTextQuestion(currentQuestion)
    const data = this.generateStatData()
    const isAllNotNumeric = this.isAllNotNumeric()
    return (
      <div className={classnames(styles.crossStatePage)}>
        <Menu
          onClick={this.handleMenuChange(category)}
          mode="horizontal"
          selectedKeys={[activeTab]}
        >
          <Menu.Item key="cross">
            问卷交叉分析
          </Menu.Item>
          <Menu.Item key="user">
            用户交叉分析
          </Menu.Item>
        </Menu>
        {isAllNotNumeric && <div className={styles.error}>
          <Empty description='没有数值类型的问题，无法进行交叉分析。' />
        </div>}
        {!isAllNotNumeric && <div className={classnames('container', styles.main)}>
          <div className={styles.leftButton}>
            <Select value={question} placeholder='--请选择问题--' onChange={this.onQuestionChange}>
              {questions.map(item => {
                return <Option key={item} value={item}>{item}</Option>
              })}
            </Select>
          </div>
          <div className={styles.chartContainer}>
            {!isTextQuestion && !this.isComplexQuestion(currentQuestion) && (
              <div
                className={classnames(
                  'btn-group',
                  styles.rightButton,
                  styles.chartSwitcher
                )}
              >
                <button
                  className={classnames('btn btn-default', {
                    active: type === 'line'
                  })}
                  onClick={this.switchChartType('line')}
                >
                  <i className="fa fa-line-chart" />
                </button>
                <button
                  className={classnames('btn btn-default', {
                    active: type === 'column'
                  })}
                  onClick={this.switchChartType('column')}
                >
                  <i className="fa fa-bar-chart" />
                </button>
              </div>
            )}
            {!isTextQuestion && !this.isComplexQuestion(currentQuestion) && (
              <button
                className={classnames('btn btn-default', styles.rightButton)}
              >
                <ExportTableData
                  name={`${question}-交叉分析}`}
                  id="cross-stat"
                  data={data}
                  quotas={quotas}
                />
              </button>
            )}
          </div>
          {this.isNumericQuestion(currentQuestion) && this.renderChart()}
          {isTextQuestion && this.renderTable()}
          {this.isComplexQuestion(currentQuestion) &&
            !currentQuestion.matrixNumric &&
            this.renderComplexTable()}
        </div>}
      </div>
    )
  }
}
