import { Effect } from 'dva'
import { Reducer } from 'redux'
import { loadList } from './service'

export interface CrossStatUserModelState {
  surveys?: any
}

export interface ModelType {
  namespace: 'crossStatUser';
  state: CrossStatUserModelState;
  effects: {
    fetchSurveyList: Effect;
  };
  reducers: {
    save: Reducer<CrossStatUserModelState>;
  };
}

const StatModel: ModelType = {
  namespace: 'crossStatUser',
  state: {
    surveys: []
  },
  effects: {
    *fetchSurveyList({ payload: { params } }, { call, put }) {
      const response = yield call(loadList, params)
      yield put({
        type: 'save',
        payload: {
          surveys: response,
        },
      })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
  },
}

export default StatModel
