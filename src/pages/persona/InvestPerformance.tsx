import React from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import moment from 'moment'
import _ from 'lodash'
import createRange from 'lodash/range'
import times from 'lodash/times'
import { DownOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Card, Dropdown, Menu, Button, Spin, Row, Col, Tooltip, DatePicker, Select, Affix, Tabs } from 'antd';
import Chart from '@/components/Chart/Chart'
import * as calculator from '@/utils/calculator'
import getIntersectionByDate from '@/utils/getIntersectionByDate'
import StandardTable, { StandardTableColumnProps } from '@/components/StandardTable'
import changeFrequency from '@/utils/changeFrequency'
import countByRange from '@/utils/countByRange'
import math from '@/utils/math'
import fillEmptyValues from '@/utils/fillEmptyValues'
import ScenarioChart from './components/ScenarioChart'
import ReturnFrequencyChart from './components/ReturnFrequencyChart'
import { calculateFundQuotasByTimeRange } from '@/utils/calculateFundQuota'
import SearchSelect from '@/components/SearchSelect'
import ExportData from '@/components/ExportData'

const RangePicker = DatePicker.RangePicker
const Highcharts = Chart.Highcharts

const { TabPane } = Tabs
interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentFund?: any;
  currentBenchmark: any;
  match: any;
  location: any;
  benchmarkList: any;
  currentManager?: any;
  scenarios: any;
}

class InvestPerformance extends React.Component<ComponentProps> {
  constructor(props) {
    super(props)
    const state = {
      fundMonthlyNav: [],
      fundMonthlyReturns: [],
      benchmarkMonthlyNav: [],
      benchmarkMonthlyReturns: [],
      rollingPeriod: 12,
      rollingGap: 1,
      isManager: props.match && props.match.path.includes('manager'),
      activeTab: 'performance',
      refNavChart: null,
    }
    let dateRange = {}
    try {
      const currentFund = props.currentFund
      const cacheKey = `investperfDates:${props.currentFund._id}`
      const cachedDates = localStorage.getItem(cacheKey)
      if (cachedDates) {
        dateRange = JSON.parse(cachedDates)
      } else {
        dateRange = {
          startDate: currentFund && currentFund.navStartDate,
          endDate: currentFund && currentFund.navEndDate,
        }
      }
    } catch (error) {
    }
    if (dateRange.startDate && dateRange.endDate) {
      state.startDate = +moment(dateRange.startDate).startOf('date')
      state.endDate = +moment(dateRange.endDate).startOf('date')
    }
    this.state = state
  }

  componentDidMount() {
    const { currentFund, currentBenchmark } = this.props
    const { startDate, endDate } = this.state
    if (currentBenchmark && currentBenchmark._id === currentFund.benchmarkId) {
      this.buildIntersectionData(currentFund, currentBenchmark, startDate, endDate)
    } else {
      this.loadBenchmark(this.props.currentFund.benchmarkId || '000300.SH')
    }
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'fund/save',
      payload: {
        currentBenchmark: null,
      },
    })
  }

  componentWillReceiveProps(nextProps) {
    const { currentBenchmark, currentFund } = nextProps
    const { startDate, endDate } = this.state
    if (
      currentBenchmark &&
      ((!this.props.currentBenchmark && currentBenchmark) ||
        this.props.currentBenchmark._id !== currentBenchmark._id)
    ) {
      this.buildIntersectionData(currentFund, currentBenchmark, startDate, endDate)
    }
  }

  onSelectBenchmark = (benchmarkId) => {
    this.loadBenchmark(benchmarkId)
  };

  onDateRangeChange = (dates: any) => {
    const { currentFund, currentBenchmark } = this.props
    if (!dates || !dates.length) {
      return
    }
    const startDate = +dates[0].startOf('date')
    const endDate = +dates[1].startOf('date')
    this.setState({ startDate, endDate })
    const cacheKey = `investperfDates:${this.props.currentFund._id}`
    localStorage.setItem(cacheKey, JSON.stringify({
      startDate: dates[0].format('YYYY-MM-DD'),
      endDate: dates[1].format('YYYY-MM-DD'),
    }))
    if (currentBenchmark) {
      this.buildIntersectionData(
        currentFund,
        currentBenchmark,
        startDate,
        endDate,
      )
    }
  }

  handleRollingPeriodChange = (rollingPeriod: number) => {
    this.setState({ rollingPeriod })
  }

  getCalcStartDate(navList, date) {
    const index = _.findLastIndex(navList, item => item.date < date)
    if (index === -1) {
      return date
    } else {
      return navList[index].date
    }
  }

  buildIntersectionData(currentFund, currentBenchmark, startDate, endDate) {
    const realStartDate = this.getCalcStartDate(currentFund.nets || [], startDate)
    const filteredNets = (currentFund.nets || []).filter(item => {
      if (!startDate || !endDate) {
        return true
      }
      return item.date >= realStartDate && item.date <= endDate
    })
    let fundQuotas = currentFund
    let benchmarkQuotas = currentBenchmark
    if (startDate && endDate) {
      fundQuotas = calculateFundQuotasByTimeRange({
        name: currentFund.name,
        nets: currentFund.nets,
        isFixedReturn: currentFund.isFixedReturn,
        fixedReturn: currentFund.fixedReturn,
      }, startDate, endDate)
      if (currentBenchmark) {
        benchmarkQuotas = calculateFundQuotasByTimeRange({
          name: currentBenchmark.name,
          nets: currentBenchmark.nets,
          isFixedReturn: currentBenchmark.isFixedReturn,
          fixedReturn: currentBenchmark.fixedReturn,
        }, startDate, endDate)
      }
    }
    const filledNets = fillEmptyValues(filteredNets, endDate || currentFund.navEndDate, null, null)
    const dataDisplay = getIntersectionByDate(filledNets, currentBenchmark.nets)
    const fundNavDisplay = dataDisplay.map(item => ({ date: item[0], value: item[1] }))
    const benchmarkNavDisplay = dataDisplay.map(item => ({ date: item[0], value: item[2] }))
    const benchmarkReturnDisplay = calculator.calculateReturns(benchmarkNavDisplay)
    const data = getIntersectionByDate(filteredNets, currentBenchmark.nets)
    const fundNav = data.map(item => ({ date: item[0], value: item[1] }))
    const benchmarkNav = data.map(item => ({ date: item[0], value: item[2] }))
    const fundReturns = calculator.calculateReturns(fundNav)
    const benchmarkReturns = calculator.calculateReturns(benchmarkNav)
    const fundReturnMap = fundReturns.reduce((out, item) => {
      out[item.date] = item.value
      return out
    }, {})
    const excessNavData = dataDisplay.filter(item => item[1] && item[2])
    const initalData = excessNavData[0]
    let excessReturns = []
    if (initalData) {
      excessReturns = excessNavData.map(item => {
        const fRet = item[1] / initalData[1] - 1
        const bRet = item[2] / initalData[2] - 1
        return {
          date: item[0],
          value: (fRet - bRet) * 100,
          fundAccRet: fRet,
          bmkAccRet: bRet,
          excessRet: fRet - bRet,
        }
      })
    }
    const fundMonthlyNav = changeFrequency(fundNav, 'month').slice(0, -1)
    const fundMonthlyReturns = calculator.calculateReturns(fundMonthlyNav)
    const benchmarkMonthlyNav = changeFrequency(benchmarkNav, 'month').slice(0, -1)
    const benchmarkMonthlyReturns = calculator.calculateReturns(benchmarkMonthlyNav)
    let rollingPeriod = 12
    if (fundMonthlyReturns.length < 12) {
      rollingPeriod = 3
    } else if (fundMonthlyReturns.length < 18) {
      rollingPeriod = 6
    }
    this.setState({
      rollingPeriod,
      fundNav,
      benchmarkNav,
      fundReturns,
      benchmarkReturns,
      excessReturns,
      fundMonthlyNav,
      fundMonthlyReturns,
      benchmarkMonthlyNav,
      benchmarkMonthlyReturns,
      fundNavDisplay,
      benchmarkNavDisplay,
      fundQuotas,
      benchmarkQuotas,
    })
  }

  loadBenchmark(id) {
    const { dispatch, currentFund } = this.props
    dispatch({
      type: 'fund/fetchBenchmark',
      payload: {
        id,
        params: {
          navStartDate: currentFund && currentFund.navStartDate,
        },
      },
    })
  }

  calculateRollingData(dataOrig: any, valueFn: any, benchmarkDataOrig?: any) {
    const data = dataOrig.slice(1)
    const benchmarkData = benchmarkDataOrig && benchmarkDataOrig.slice(1)
    const { rollingPeriod, rollingGap } = this.state
    const result = []
    const getValues = (data, curIndex) => data.slice(curIndex - rollingPeriod, curIndex)
    for (let index = rollingPeriod; index <= data.length; index = index + rollingGap) {
      const values = getValues(data, index)
      let benchmarkValues = []
      if (benchmarkData) {
        benchmarkValues = getValues(benchmarkData, index)
      }
      // if (benchmarkData) {
      //   if (
      //     moment(data[index-1].date).format('YYYYMMDD') === '20180228' ||
      //     index === rollingPeriod ||
      //     index === data.length
      //   ) {
      //     values.forEach((item, index) => {
      //       console.log(moment(item.date).format('YYYYMMDD'), item.value, benchmarkValues[index].value)
      //     })
      //   }
      // }
      result.push({
        date: data[index-1].date,
        value: benchmarkData ? valueFn(values, benchmarkValues) : valueFn(values),
      })
    }
    return result
  }

  calculateRollingDailyData(data: any, valueFn: any, benchmarkData?: any) {
    const { rollingPeriod, rollingGap } = this.state
    const result = []
    if (!data || !data.length) {
      return []
    }
    let endDate = moment(data[data.length - 1].date).subtract(1, 'month').endOf('month')
    const startDate = moment(data[0].date).add(rollingPeriod, 'month').endOf('month')
    while (startDate <= endDate) {
      const endTs = +endDate
      const startTs = +endDate.clone().subtract(rollingPeriod, 'month').endOf('month')
      const values = data.filter(item => item.date > startTs && item.date <= endTs)
      const benchmarkValues = benchmarkData && benchmarkData.filter(item => item.date > startTs && item.date <= endTs)
      endDate = endDate.subtract(rollingGap, 'month').endOf('month')
      result.push({
        date: endTs,
        value: valueFn(values, benchmarkValues),
      })
    }
    return result.sort((fst, snd) => fst.date - snd.date)
  }

  calculateRollingReturn(data) {
    return this.calculateRollingDailyData(data, values =>
      calculator._cucmulativeReturn(values.map(item => item.value))
    )
  }

  calculateRollingVol(data) {
    return this.calculateRollingDailyData(data, values =>
      calculator.vol(values.map(item => item.value), 'day')
    )
  }

  getFundAssetType = () => {
    const { currentFund } = this.props
    if (currentFund.asset_type) {
      return currentFund.asset_type
    } else if (currentFund.fundNature === '债券型' || currentFund.name.indexOf('固收')) {
      return '2'
    }
    return '1'
  }

  calculateRollingCapture(data, benchmarkData) {
    const assetType = this.getFundAssetType()
    const hw = assetType === '1' ? 0.01 : 0
    const valueFn = (values, benchmarkValues) => {
      const valueMapper = item => item.value
      const vals1 = values.map(valueMapper)
      const vals2 = benchmarkValues.map(valueMapper)
      return {
        up: calculator.upsideCapture(vals1, vals2, hw),
        down: calculator.downsideCapture(vals1, vals2, -hw),
      }
    }
    return this.calculateRollingData(data, valueFn, benchmarkData)
  }

  calculateRollingVar(data) {
    return this.calculateRollingDailyData(data, values =>
      calculator.VaRHistory(values.map(item => item.value), 0.95)
    )
  }

  calculateRollingBeta(data, benchmarkData) {
    const valueFn = (values, benchmarkValues) => {
      const valueMapper = item => item.value
      const vals1 = values.map(valueMapper)
      const vals2 = benchmarkValues.map(valueMapper)
      if (vals1.length < 2) {
        return null
      }
      const samples = vals2.map((item, index) => [item, vals1[index]])
      const mb = math.linearRegression(samples)
      return mb.m
    }
    return this.calculateRollingDailyData(data, valueFn, benchmarkData)
  }

  buildZones(data) {
    const len = data.length
    const zones = []
    let i = -1
    let current
    let previous
    let dashStyle
    let value

    if (!len) {
      return []
    }

    while (data[++i][1] === null);
    zones.push({
      value: i,
    })

    while (++i < len) {
      previous = data[i - 1]
      current = data[i]
      dashStyle = ''

      if (previous[1] !== null && current[1] === null) {
        dashStyle = 'solid'
        value = previous[0]
      } else if (previous[1] === null && current[1] !== null) {
        dashStyle = 'dot'
        value = current[0]
      }

      if (dashStyle) {
        zones.push({
          dashStyle: dashStyle,
          value: value,
        })
      }
    }

    return zones
  }

  renderNavChart() {
    const { currentFund, currentBenchmark } = this.props
    const { fundNavDisplay, benchmarkNavDisplay, excessReturns, startDate, endDate } = this.state
    const buildSerieData = (name, list, withZone) => {
      const data = (list || []).map(item => [item.date, item.value])
      let ret = {
        name,
        type: 'line',
        data,
        yAxis: 0,
        tooltip: {
          pointFormat: '{series.name}: <b>{point.y:.2f}%</b><br/>',
        },
      }
      if (name !== '超额收益') {
        ret.compare = 'percent'
        ret.tooltip = {
          pointFormat: '{series.name}: <b>{point.y:.2f}({point.change:.2f}%)</b><br/>',
        }
      }
      if (withZone) {
        ret = {
          ...ret,
          zones: this.buildZones(data),
          zoneAxis: 'x',
          connectNulls: true,
        }
      }
      return ret
    }
    const series = [buildSerieData(currentFund.name, fundNavDisplay || currentFund.nets, true)]
    if (currentFund.scale && currentFund.scale.length) {
      series.push({
        name: '管理规模',
        type: 'area',
        data: currentFund.scale.filter(item => {
          if (!startDate || !endDate) {
            return true
          }
          return item.date >= startDate && item.date <= endDate
        }).map(item => [item.date, item.value / 10000]),
        step: true,
        yAxis: 1,
        fillColor: {
          linearGradient: {
            x1: 0,
            y1: 0,
            x2: 0,
            y2: 1,
          },
          stops: [
            [0, Chart.Highcharts.getOptions().colors[0]],
            [
              1,
              Chart.Highcharts.Color(Chart.Highcharts.getOptions().colors[0])
                .setOpacity(0)
                .get('rgba'),
            ],
          ],
        },
        tooltip: {
          pointFormat: '{series.name}: <b>{point.y:,.0f} 万</b><br/>',
        },
      })
    }
    if (benchmarkNavDisplay && benchmarkNavDisplay.length) {
      // const plus3Nav = benchmarkNavDisplay.map((item, index) => {
      //   return {
      //     date: item.date,
      //     value: index === 0 ? item.value : item.value * 1.03,
      //   }
      // })
      series.push(buildSerieData(currentBenchmark.name, benchmarkNavDisplay))
      // series.push(buildSerieData('+300bp', plus3Nav))
      series.push(buildSerieData('超额收益', excessReturns))
    }
    const chartConfig = {
      chart: {
        type: 'line',
        height: 300,
      },
      // navigator: {
      //   enabled: false,
      // },
      scrollbar: {
        enabled: false,
      },
      yAxis: [
        {
          title: {
            text: '累计收益',
          },
          labels: {
            format: '{value}%',
          },
        },
        {
          title: {
            text: '规模',
          },
          labels: {
            format: '{value}',
          },
          opposite: true,
        },
      ],
      series,
    }
    return <Chart afterChartCreated={(refChart) => { this.setState({ refNavChart: refChart }) }} options={chartConfig} constructorType="stockChart" />
  }

  renderChartWithTable(columns, data) {
    const chartConfig = {
      chart: {
        type: 'column',
        height: 250,
      },
      xAxis: {
        categories: columns.slice(1).map(item => item.title),
      },
      series: data.map(item => {
        return {
          name: item.name,
          data: columns.slice(1).map(col => (item[col.dataIndex] || 0) * 100),
        }
      }),
    }
    return (
      <>
        <Chart options={chartConfig} />
        <StandardTable
          disableRowSlection
          size="small"
          columns={columns}
          data={{ list: data, pagination: false }}
          scroll={{ x: 700 }}
        />
      </>
    )
  }

  renderReturnStats() {
    // const { currentFund, currentBenchmark } = this.props
    const { fundQuotas, benchmarkQuotas } = this.state
    const data = [fundQuotas, benchmarkQuotas].filter(Boolean)
    const yearColumns: StandardTableColumnProps[] = [
      {
        dataIndex: 'name',
        width: 100,
        fixed: 'left',
      },
    ]
    const currentYear = moment().year()
    let startYear = currentYear - 6
    const quotaWidth = 70
    while (startYear < currentYear) {
      yearColumns.push({
        title: `${startYear}年`,
        dataIndex: `accReturn${startYear}`,
        format: 'percentage',
        width: quotaWidth,
      })
      startYear = startYear + 1
    }
    yearColumns.push({
      title: 'YTD',
      dataIndex: 'ytdAccReturn',
      format: 'percentage',
      width: quotaWidth,
    })

    yearColumns.push({
      title: '区间收益',
      dataIndex: 'accReturn',
      format: 'percentage',
    })

    const monthColumns: StandardTableColumnProps[] = [
      {
        dataIndex: 'name',
        width: 100,
        fixed: 'left',
      },
      {
        title: '1月',
        dataIndex: 'last1MAccReturn',
        format: 'percentage',
        width: quotaWidth,
      },
      {
        title: '6月',
        dataIndex: 'last6MAccReturn',
        format: 'percentage',
        width: quotaWidth,
      },
      {
        title: 'YTD',
        dataIndex: 'ytdAccReturn',
        format: 'percentage',
        width: quotaWidth,
      },
      {
        title: '1年',
        dataIndex: 'last1YAccReturn',
        format: 'percentage',
        width: quotaWidth,
      },
      {
        title: '年化3年',
        dataIndex: 'last3YYearReturn',
        format: 'percentage',
        width: quotaWidth,
      },
      {
        title: '年化5年',
        dataIndex: 'last5YYearReturn',
        format: 'percentage',
        width: quotaWidth,
      },
      {
        title: '年化收益',
        dataIndex: 'yearReturn',
        format: 'percentage',
      },
    ]
    return (
      <Row gutter={16}>
        <Col lg={12} md={24}>
          {this.renderChartWithTable(yearColumns, data)}
        </Col>
        <Col lg={12} md={24}>
          {this.renderChartWithTable(monthColumns, data)}
        </Col>
      </Row>
    )
  }

  calculateRiskReturnSeries(name, returnData) {
    const returns = this.calculateRollingReturn(returnData)
    const risks = this.calculateRollingVol(returnData)
    return {
      name,
      visible: true,
      data: risks
        .map((item, index) => {
          return [item.value * 100, returns[index].value * 100, item.date]
        })
        .filter(item => item[0] && item[1]),
    }
  }

  calculateDrawdownSeries(returns) {
    const returnsData = returns.map(item => ({ ...item }))
    let peak = 0
    return returnsData
      .map((item, index) => [returnsData[index - 1], item])
      .map(([pre, nxt]) => {
        if (!pre) {
          nxt.compound = nxt.value
          if (nxt.compound > 0) {
            nxt.drawdown = 0
            peak = nxt.compound
          } else {
            nxt.drawdown = nxt.compound
          }
        } else {
          nxt.compound = (pre.compound + 1) * (nxt.value + 1) - 1
          if (nxt.compound > peak) {
            nxt.drawdown = 0
            peak = nxt.compound
          } else {
            nxt.drawdown = (1 + nxt.compound) / (1 + peak) - 1
          }
        }
        return nxt
      }).map(item => {
        return {
          date: item.date,
          value: item.drawdown,
        }
      })
  }

  calculateMaxDrawdownSeries(nets) {
    const results = nets.map((item, index) => {
      const navValues = nets.slice(0, index + 1).map(item => item.value)
      return {
        date: item.date,
        value: calculator.maxDrawdown(navValues)
      }
    })
    return results
  }

  renderRiskChart() {
    const { currentFund, currentBenchmark } = this.props
    const { fundReturns, benchmarkReturns } = this.state
    const series = [this.calculateRiskReturnSeries(currentFund.name, fundReturns)]
    if (currentBenchmark) {
      series.push(this.calculateRiskReturnSeries(currentBenchmark.name, benchmarkReturns))
    }
    const chartConfig = {
      chart: {
        type: 'bubble',
        zoomType: 'xy',
      },
      yAxis: {
        title: {
          text: '收益率',
        },
        labels: {
          format: '{value}%',
        },
      },
      xAxis: {
        title: {
          text: '波动率',
          align: 'high',
          offset: -10,
        },
        labels: {
          format: '{value}%',
        },
      },
      tooltip: {
        pointFormat:
          '{point.z:%Y-%m-%e}<br/>收益率: <b>{point.y:,.2f}%</b>波动率: <b>{point.x:,.2f}%</b>',
      },
      series,
    }

    return <Chart options={chartConfig} />
  }

  calculateNormalValue(x, mean, std, size) {
    const translation = x - mean
    return (Math.exp(-(translation ** 2) / (2 * (std ** 2))) / (std * Math.sqrt(2 * Math.PI))) * size;
  }

  getRangeData = (returns) => {
    const returnData = returns.map(item => item.value * 100)
    const meanReturn = Math.round(math.mean(returnData))
    const ranges = createRange(-15 + meanReturn, 16 + meanReturn)
    const rangeData = countByRange(returnData, ranges)
    let frequencyValues = []
    Object.keys(rangeData).forEach(key => {
      const values = times(rangeData[key], () => +key)
      frequencyValues = frequencyValues.concat(values)
    })
    frequencyValues = frequencyValues.filter(item => !Number.isNaN(item))
    const meanValue = math.mean(frequencyValues)
    const stdValue = math.std(frequencyValues)
    return ranges.map(range => {
      return {
        range,
        value: rangeData[range] || 0,
        nValue: this.calculateNormalValue(range, meanValue, stdValue, frequencyValues.length),
      }
    })
  }

  renderFrequencyChart() {
    const { currentFund, currentBenchmark } = this.props
    const { fundMonthlyReturns, benchmarkMonthlyReturns } = this.state
    const returns = fundMonthlyReturns
    if (returns.length < 2) {
      return
    }
    const fundRangeData = this.getRangeData(fundMonthlyReturns)
    const benchmarkRangeData = this.getRangeData(benchmarkMonthlyReturns)
    const series = [
      {
        name: currentFund.name,
        data: fundRangeData.map(range => range.value),
      },
    ]
    if (currentBenchmark) {
      series.push({
        name: currentBenchmark.name,
        data: benchmarkRangeData.map(range => range.value),
      })
    }
    series.push(
      {
        name: 'Normal Curve',
        type: 'spline',
        color: '#F5A623',
        marker: {
          enabled: false,
        },
        tooltip: {
          enabled: false,
        },
        data: fundRangeData.map(range => range.nValue),
      },
    )
    const categories = fundRangeData.map(item => `${item.range}%`)
    const config = {
      chart: {
        type: 'column',
      },
      xAxis: {
        categories: categories,
      },
      yAxis: {
        min: 0,
        title: {
          text: 'Frequency',
        },
        labels: {
          format: '{value}',
        },
      },
      plotOptions: {
        series: {
          animation: false,
        },
        spline: {
          enableMouseTracking: false,
        },
      },
      tooltip: {
        shared: true,
        pointFormat: '{series.name}: <b>{point.y:.0f}</b><br/>',
      },
      series: series,
    }
    return <Chart options={config} />
  }

  renderCaptureChart() {
    const { fundMonthlyReturns, benchmarkMonthlyReturns } = this.state
    const captureData = this.calculateRollingCapture(fundMonthlyReturns, benchmarkMonthlyReturns)
    const chartConfig = {
      chart: {
        type: 'line',
      },
      navigator: {
        enabled: false,
      },
      scrollbar: {
        enabled: false,
      },
      yAxis: [
        {
          labels: {
            format: '{value}%',
          },
        },
      ],
      series: [
        {
          name: '上行捕获',
          data: captureData.map(item => [+moment(item.date).startOf('date'), item.value.up * 100]),
          fillColor: {
            linearGradient: {
              x1: 0,
              y1: 0,
              x2: 0,
              y2: 1,
            },
            stops: [
              [0, new Highcharts.Color(Highcharts.getOptions().colors[0]).setOpacity(0).get('rgba')],
              [1, new Highcharts.getOptions().colors[0]],
            ],
          },
        },
        {
          name: '下行捕获',
          data: captureData.map(item => [
            +moment(item.date).startOf('date'),
            item.value.down * 100,
          ]),
          fillColor: {
            linearGradient: {
              x1: 0,
              y1: 0,
              x2: 0,
              y2: 1,
            },
            stops: [
              [0, new Highcharts.Color(Highcharts.getOptions().colors[1]).setOpacity(0).get('rgba')],
              [1, new Highcharts.getOptions().colors[1]],
            ],
          },
        },
      ],
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  renderAreaSeriesChart(fundData, benchmarkData) {
    const { currentFund, currentBenchmark } = this.props
    const series = [
      [currentFund, fundData],
      [currentBenchmark, benchmarkData],
    ].map((itemData, index) => {
      return {
        name: itemData[0] && itemData[0].name,
        data: itemData[1].map(item => [+moment(item.date).startOf('date'), item.value * 100]),
        fillColor: {
          linearGradient: {
            x1: 0,
            y1: 0,
            x2: 0,
            y2: 1,
          },
          stops: [
            [0, new Highcharts.Color(Highcharts.getOptions().colors[index]).setOpacity(0).get('rgba')],
            [1, new Highcharts.getOptions().colors[index]],
          ],
        },
      }
    })
    const chartConfig = {
      chart: {
        type: 'area',
      },
      navigator: {
        enabled: false,
      },
      scrollbar: {
        enabled: false,
      },
      yAxis: [
        {
          labels: {
            format: '{value}%',
          },
        },
      ],
      plotOptions: {
        area: {
          stacking: null,
        },
      },
      series: series,
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  calculateDailyMonthReturn = (returnData) => {
    const startDate = +moment(returnData[0] && returnData[0].date).add(1, 'month')
    return returnData.map((item, index) => {
      if (item.date < startDate) {
        return null
      }
      const startTs = +moment(item.date).subtract(1, 'month')
      const values = returnData
        .filter(rd => rd.date >= startTs && rd.date <= item.date)
        .map(item => item.value)
      return {
        date: item.date,
        value: calculator._cucmulativeReturn(values),
      }
    }).filter(Boolean)
  }

  renderVarChart() {
    const { fundReturns, benchmarkReturns } = this.state
    const dailyMonthReturns = this.calculateDailyMonthReturn(fundReturns || [])
    const dailyMonthReturnsB = this.calculateDailyMonthReturn(benchmarkReturns || [])
    const fundData = this.calculateRollingVar(dailyMonthReturns)
    const benchmarkData = this.calculateRollingVar(dailyMonthReturnsB)
    return this.renderAreaSeriesChart(fundData, benchmarkData)
  }

  renderBetaChart() {
    const { fundReturns, benchmarkReturns } = this.state
    const fundData = this.calculateRollingBeta(fundReturns, benchmarkReturns)
    const chartConfig = {
      chart: {
        type: 'line',
      },
      navigator: {
        enabled: false,
      },
      scrollbar: {
        enabled: false,
      },
      yAxis: [
        {
          labels: {
            format: '{value}',
          },
        },
      ],
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y:.2f}</b><br/>',
      },
      series: [
        {
          name: 'Beta',
          data: fundData.map(item => [+moment(item.date).startOf('date'), item.value]),
        },
      ],
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  renderVolChart() {
    const { currentFund, currentBenchmark } = this.props
    const { fundReturns, benchmarkReturns } = this.state
    const fundData = this.calculateRollingVol(fundReturns)
    const benchmarkData = this.calculateRollingVol(benchmarkReturns)
    const chartConfig = {
      chart: {
        type: 'line',
      },
      navigator: {
        enabled: false,
      },
      scrollbar: {
        enabled: false,
      },
      yAxis: [
        {
          labels: {
            format: '{value}%',
          },
        },
      ],
      series: [
        {
          name: currentFund.name,
          data: fundData.map(item => [+moment(item.date).startOf('date'), item.value * 100]),
        },
        {
          name: currentBenchmark && currentBenchmark.name,
          data: benchmarkData.map(item => [+moment(item.date).startOf('date'), item.value * 100]),
        },
      ],
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  renderDrawdownChart() {
    const { fundReturns, benchmarkReturns } = this.state
    const fundData = this.calculateDrawdownSeries(fundReturns || [])
    const benchmarkData = this.calculateDrawdownSeries(benchmarkReturns || [])
    return this.renderAreaSeriesChart(changeFrequency(fundData, 'month', true), changeFrequency(benchmarkData, 'month', true))
  }

  renderMaxDrawdownChart() {
    const { fundNav, benchmarkNav } = this.state
    const fundData = this.calculateMaxDrawdownSeries(fundNav || [])
    const benchmarkData = this.calculateMaxDrawdownSeries(benchmarkNav || [])
    return this.renderAreaSeriesChart(changeFrequency(fundData, 'month', true), changeFrequency(benchmarkData, 'month', true))
  }

  handleTabChange = (activeTab) => {
    this.setState({ activeTab })
  }

  getNavDownloadData = () => {
    const { currentFund } = this.props
    const { fundNavDisplay, benchmarkNavDisplay, excessReturns } = this.state
    const buildData = (rawData, dataIndex, valueKey) => {
      return rawData.map(item => {
        return {
          date: moment(item.date).format('YYYYMMDD'),
          [dataIndex]: item[valueKey || 'value'],
        }
      })
    }
    const chartData = [
      ...buildData(currentFund.scale || [], 'latestScale'),
      ...buildData(fundNavDisplay || [], 'fundNav'),
      ...buildData(benchmarkNavDisplay || [], 'bmkNav'),
      ...buildData(excessReturns || [], 'excessRet', 'excessRet'),
      ...buildData(excessReturns || [], 'fundAccRet', 'fundAccRet'),
      ...buildData(excessReturns || [], 'bmkAccRet', 'bmkAccRet'),
    ]
    const data = _.map(_.groupBy(chartData, 'date'), (values) => {
      return _.assign.apply(null, values)
    }).sort((fst, snd) => snd.date - fst.date)
    return data
  }

  render() {
    const { currentFund, currentBenchmark } = this.props
    const defaultBenchmark = {
      name: currentFund.benchmark,
      _id: currentFund.benchmarkId,
    }
    const benchmarkList = this.props.benchmarkList.map(item => {
      return {
        ...item
      }
    })
    if (defaultBenchmark._id) {
      if (!benchmarkList.some(item => item._id === defaultBenchmark._id || item._qutkeId === defaultBenchmark._id)) {
        benchmarkList.unshift(defaultBenchmark)
      }
    }
    const {
      fundNav, benchmarkNav, activeTab,
      fundMonthlyReturns, benchmarkMonthlyReturns,
      isManager, fundNavDisplay,
      benchmarkNavDisplay, excessReturns, startDate, endDate,
      fundReturns, benchmarkReturns, rollingPeriod, rollingGap,
      fundQuotas, benchmarkQuotas
    } = this.state
    const initialStartDate = moment(new Date(this.state.startDate || currentFund.navStartDate))
    const initialEndDate = moment(new Date(this.state.endDate || currentFund.navEndDate))
    const benchmarkIds = benchmarkList.map(item => item._id)
    const currentBenchmarkId = currentBenchmark && currentBenchmark._id
    const currentBenchmarkName = currentBenchmark && currentBenchmark.name
    const navDownloadCols = [{
      title: '日期',
      dataIndex: 'date'
    }, {
      title: '基金净值',
      dataIndex: 'fundNav'
    }, {
      title: '基准净值',
      dataIndex: 'bmkNav'
    }, {
      title: '基金累计收益',
      dataIndex: 'fundAccRet'
    }, {
      title: '基准累计收益',
      dataIndex: 'bmkAccRet'
    }, {
      title: '超额收益',
      dataIndex: 'excessRet'
    }, {
      title: '规模',
      dataIndex: 'latestScale'
    }]
    
    return (
      <div>
        <Affix offsetTop={44}>
          <Tabs
            onChange={this.handleTabChange}
            activeKey={activeTab}
            style={{
              marginBottom: 15,
              background: 'rgb(24, 31, 41)',
            }}
            tabBarExtraContent={
              <>
                <span style={{ marginRight: 15 }}>
                  日期范围：<RangePicker
                    style={{ width: 200 }}
                    size="small"
                    ranges={{
                      本周以来: [initialEndDate.clone().startOf('week'), initialEndDate],
                      本月以来: [initialEndDate.clone().startOf('month'), initialEndDate],
                      本年以来: [initialEndDate.clone().startOf('year'), initialEndDate],
                    }}
                    onChange={this.onDateRangeChange}
                    disabledDate={(current) => {
                      const currentTs = +current.startOf('date')
                      return currentTs < currentFund.navStartDate || currentTs > currentFund.navEndDate
                    }}
                    defaultValue={[
                      initialStartDate, initialEndDate,
                    ]}
                  />
                </span>
                <span style={{ marginRight: 15 }}>
                  滚动窗口：<Select
                    value={rollingPeriod}
                    onChange={this.handleRollingPeriodChange}
                    style={{ width: 80 }}
                    size="small"
                  >
                    <Option value={3}>3个月</Option>
                    <Option value={6}>6个月</Option>
                    <Option value={12}>12个月</Option>
                    <Option value={24}>2年</Option>
                    <Option value={36}>3年</Option>
                  </Select>
                </span>
                <SearchSelect
                  placeholder="请选择基准"
                  value={benchmarkIds.includes(currentBenchmarkId) ? currentBenchmarkId : currentBenchmarkName}
                  options={benchmarkList.map(item => {
                    return {
                      title: item.name,
                      dataIndex: item._id,
                    }
                  })}
                  onChange={this.onSelectBenchmark}
                  width="150px"
                />
                {false &&
                <Dropdown
                  overlay={
                    <Menu onClick={this.onSelectBenchmark}>
                      {benchmarkList.map(item => (
                        <Menu.Item key={item._id}>{item.name}</Menu.Item>
                      ))}
                    </Menu>
                  }
                >
                  <Button ghost size="small">
                    {`基准：${currentBenchmark ? currentBenchmark.name : ''}`} <DownOutlined />
                  </Button>
                </Dropdown>}
              </>
            }
          >
            <TabPane tab="收益表现" key="performance">
            </TabPane>
            <TabPane tab="风险分析" key="risk">
            </TabPane>
            <TabPane tab="情景分析" key="scenario">
            </TabPane>
          </Tabs>
        </Affix>
        <Spin spinning={this.props.loading}>
          {activeTab === 'performance' &&
          <Card>
            <Row gutter={16}>
              <Col lg={12} md={24}>
                <span>收益走势</span>
                <span style={{ float: 'right' }}>
                  <ExportData title="下载数据" filename={`${currentFund.name}-收益走势`} getData={this.getNavDownloadData} columns={navDownloadCols}/>
                </span>

                {this.state.isManager &&
                <Tooltip title="虚线[---]代表基金经理在时间段没有管理产品" placement="right">
                  <span style={{ marginLeft: '5px' }}>
                    <QuestionCircleOutlined />
                  </span>
                </Tooltip>}
                {this.renderNavChart()}
              </Col>
              <Col lg={12} md={24}>
                <span>收益风险分布</span>
                <Tooltip title="滚动计算收益率和波动率，气泡越大代表日期越近" placement="right">
                  <span style={{ marginLeft: '5px' }}>
                    <QuestionCircleOutlined />
                  </span>
                </Tooltip>
                {this.renderRiskChart()}
              </Col>
            </Row>
          </Card>}
          {activeTab === 'performance' &&
          <Card title="业绩表现">{this.renderReturnStats()}</Card>}
          {activeTab === 'risk' &&
          <Card>
            <Row gutter={16}>
              <Col lg={12} md={24}>
                <span>滚动VaR值</span>
                {this.renderVarChart()}
              </Col>
              <div style={{ marginTop: 15 }}/>
              <Col lg={12} md={24}>
                <span>滚动BETA</span>
                {this.renderBetaChart()}
              </Col>
            </Row>
            <Row gutter={16}>
              <Col lg={12} md={24}>
                <span>滚动波动率</span>
                {this.renderVolChart()}
              </Col>
              <Col lg={12} md={24}>
                <span>历史回撤</span>
                {this.renderMaxDrawdownChart()}
              </Col>
            </Row>
          </Card>}
          {activeTab === 'scenario' &&
          <Card>
            <Row gutter={16}>
              <Col lg={12} md={24}>
                <ReturnFrequencyChart
                  {...{
                    currentBenchmark,
                    currentFund,
                    fundMonthlyReturns,
                    benchmarkMonthlyReturns,
                    fundNav,
                    benchmarkNav,
                  }}
                />
              </Col>
              <Col lg={12} md={24}>
                <span>涨跌市场捕获率</span>
                {this.renderCaptureChart()}
              </Col>
            </Row>
            <div style={{ marginTop: 15 }}/>
            <ScenarioChart
              {...{
                currentFund,
                currentBenchmark,
                fundNav,
                benchmarkNav,
              }}
            />
          </Card>}
        </Spin>
      </div>
    );
  }
}

const Wrapper: React.FC<WrapperProps> = props => {
  const {
    currentFund,
    currentManager,
    location: { pathname },
    ...restProps
  } = props
  const isFund = /fund|activefund|portfolios/.test(pathname)
  return <InvestPerformance currentFund={isFund ? currentFund : currentManager} {...restProps} />
}

export default connect(
  ({
    manager,
    loading,
    fund,
  }: {
    manager: any;
    fund: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    currentManager: fund.currentManager,
    benchmarkList: manager.benchmarkList,
    currentBenchmark: fund.currentBenchmark,
    loading: loading.models.fund,
    scenarios: fund.scenarios,
  }),
)(Wrapper)
