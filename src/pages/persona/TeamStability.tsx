import React from 'react'
import { connect } from 'dva'
import moment from 'moment'
import { Dispatch } from 'redux'
import { Card, Table } from 'antd'
import Chart from '@/components/Chart/Chart'
import IndividualRating from './components/IndividualRating'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentManager: any;
  match: any;
  location: any;
}

const TeamStability: React.FC<ComponentProps> = props => {
  const { currentManager } = props
  const {
    companyDesc,
    companyTeamInfo,
    companyTeamStabilityList,
    companyDataqList,
  } = currentManager
  const teamListColumns = [
    {
      title: '人员',
      dataIndex: 'manager_name',
    },
    {
      title: '职位',
      dataIndex: 'post',
    },
    {
      title: '入司时间',
      dataIndex: 'start_date',
    },
    {
      title: '离司时间',
      dataIndex: 'end_date',
    },
    {
      title: '机构',
      dataIndex: 'company_name',
    },
    {
      title: '部门',
      dataIndex: 'department',
    },
  ]
  const teamStabilityColumns = [
    {
      title: '新入职',
      dataIndex: 'new_num',
      color: '#3dbf9c',
    },
    {
      title: '留任',
      dataIndex: 'duty_num',
      color: '#205351',
    },
    {
      title: '离职',
      dataIndex: 'dimission_num',
      color: '#2b8474',
    },
  ]
  const teamStabilityChartConfig = {
    chart: {
      type: 'column',
    },
    navigator: {
      enabled: false,
    },
    scrollbar: {
      enabled: false,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y}</b><br/>',
    },
    yAxis: {
      labels: {
        format: '{value}',
      },
    },
    series: teamStabilityColumns.map(col => {
      let placementInfo = {
        stacking: '',
      }
      if (col.dataIndex !== 'dimission_num') {
        placementInfo = {
          stacking: 'stacking',
        }
      }
      return {
        name: col.title,
        color: col.color,
        data: companyTeamStabilityList.map(item => [
          +moment(item.the_date).startOf('date'),
          col.dataIndex !== 'dimission_num' ? item[col.dataIndex] : -item[col.dataIndex],
        ]),
        ...placementInfo,
      }
    }),
  }
  const performanceChartConfig = {
    chart: {
      type: 'column',
    },
    navigator: {
      enabled: false,
    },
    scrollbar: {
      enabled: false,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.2f}</b><br/>',
    },
    yAxis: {
      labels: {
        format: '{value}',
      },
    },
    series: [
      {
        name: '主动股',
        color: '#e85655',
        data: companyDataqList.map(item => [
          +moment(item.the_date).startOf('date'),
          item.act_stk_manage_ability,
        ]),
      },
      {
        name: '主动债',
        color: '#e49831',
        data: companyDataqList.map(item => [
          +moment(item.the_date).startOf('date'),
          item.act_bnd_manage_ability,
        ]),
      },
    ],
  }
  const managementChartConfig = {
    chart: {
      type: 'column',
    },
    navigator: {
      enabled: false,
    },
    scrollbar: {
      enabled: false,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.2f}</b><br/>',
    },
    yAxis: [
      {
        labels: {
          format: '{value}',
        },
      },
      {
        labels: {
          format: '{value}',
        },
        opposite: true,
      },
    ],
    series: [
      {
        name: '非货基金经理人数',
        color: '#e85655',
        yAxis: 0,
        data: companyDataqList.map(item => [
          +moment(item.the_date).startOf('date'),
          item.nch_fund_num,
        ]),
      },
      {
        name: '人均管理规模(万)',
        color: '#e49831',
        yAxis: 1,
        data: companyDataqList.map(item => [
          +moment(item.the_date).startOf('date'),
          item.nch_asset_scale / 10000,
        ]),
      },
    ],
  }

  return (
    <div>
      <IndividualRating rating={currentManager.team_stability} />
      <Card>
        <h4>人员稳定性评价</h4>
        <div>{companyDesc.team_stability_evaluation}</div>
      </Card>
      <Card>
        <h4>团队人员配置</h4>
        <div>{companyDesc.team_allocation}</div>
      </Card>
      <Card>
        <h4>团队成员</h4>
        <Table columns={teamListColumns} dataSource={companyTeamInfo} size="small" />
      </Card>
      <Card>
        <h4>团队稳定性</h4>
        <Chart options={teamStabilityChartConfig} constructorType="stockChart" />
      </Card>
      <Card>
        <h4>团队整体投资能力</h4>
        <Chart options={performanceChartConfig} constructorType="stockChart" />
      </Card>
      <Card>
        <h4>团队产能</h4>
        <Chart options={managementChartConfig} constructorType="stockChart" />
      </Card>
    </div>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentManager: fund.currentManager,
    loading: loading.models.fund,
  }),
)(TeamStability)
