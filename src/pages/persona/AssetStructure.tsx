import React from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { Card, Tabs, Spin } from 'antd'
import AreaPieChart from '@/components/AreaPieChart'
import FundStyleHeatMap from './components/FundStyleHeatMap'
import ExportData from '@/components/ExportData'

const { TabPane } = Tabs

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  match: any;
  location: any;
  fundPositionData: any;
  fundList?: any;
}

class AssetStructure extends React.Component<ComponentProps> {
  constructor(props: ComponentProps) {
    super(props)
    const {
      fundList,
      location: { pathname },
      match: {
        params: { id },
      },
      currentFund,
    } = props
    const isSingleFund = /fund|activefund|portfolios|manager/.test(pathname)
    this.state = {
      isSingleFund,
      activeFundId: isSingleFund ? currentFund._id : fundList[0] && fundList[0]._id,
    }
  }

  componentDidMount() {
    const { activeFundId } = this.state
    if (activeFundId) {
      this.loadFundPositionData(activeFundId)
    }
  }

  handleFundTabChange = (fundId: string) => {
    this.setState({ activeFundId: fundId }, () => {
      this.loadFundPositionData(fundId)
    })
  };

  loadFundPositionData(fundId: string) {
    const { dispatch, currentFund } = this.props
    const params = {
      dataFilters: 'assetStructure',
    }
    if (currentFund.isManager) {
      if (currentFund.ref_fund_start_date) {
        params.startDate = currentFund.ref_fund_start_date
      }
      if (currentFund.ref_fund_end_date) {
        params.endDate = currentFund.ref_fund_end_date
      }
    }
    dispatch({
      type: 'fund/fetchAssetData',
      payload: {
        id: fundId,
        params,
      },
    })
  }

  render() {
    const { fundList, loading, fundPositionData, currentFund } = this.props
    const { isSingleFund } = this.state
    const exportColumns = [{
      title: '日期',
      dataIndex: 'BIZ_DATE',
    }, {
      title: '资产类型',
      dataIndex: 'ASSET_TYPE',
    }, {
      title: '市值',
      dataIndex: 'BALANCE',
      format: 'number',
    }, {
      title: '市值占比',
      dataIndex: 'RATIO',
      format: 'percentage',
    }]
    return (
      <div style={{ marginTop: 15, marginBottom: 30 }}>
        {!isSingleFund && (
          <Card title="在管基金">
            <Tabs activeKey={this.state.activeFundId} onTabClick={this.handleFundTabChange}>
              {fundList.map((item, index) => (
                <TabPane tab={item.name} key={item._id} />
              ))}
            </Tabs>
          </Card>
        )}
        <Spin spinning={loading}>
          <Card
            title="资产配置历史变化"
            extra={
              <ExportData addDateSuffix columns={exportColumns} dataSource={fundPositionData.assetScales} filename={`${currentFund.name}-资产配置历史变化`}/>
            }
          >
            <AreaPieChart
              disablePie
              showNavigator
              rows={fundPositionData.assetScales || []}
              nameKey="ASSET_TYPE"
              valueKey="BALANCE"
              height={420}
            />
          </Card>
          {currentFund._syncType === 'mutual' && fundPositionData.fundStyleHistory && fundPositionData.fundStyleHistory.length !== 0 &&
          <FundStyleHeatMap data={fundPositionData.fundStyleHistory} currentFund={currentFund}/>}
        </Spin>
      </div>
    )
  }
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    fundPositionData: fund.fundAssetData,
    currentFund: fund.currentFund,
    loading: loading.effects['fund/fetchAssetData'],
  }),
)(AssetStructure)
