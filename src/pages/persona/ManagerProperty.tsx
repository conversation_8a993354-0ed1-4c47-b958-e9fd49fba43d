import React from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import moment from 'moment'
import { Card, Table, Col, Row } from 'antd'
import Chart from '@/components/Chart/Chart'
import StandardTable from '@/components/StandardTable'
import IndividualRating from './components/IndividualRating'
import sortQuotaFn from '@/utils/sortQuotaFn'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentManager: any;
  match: any;
  location: any;
}

interface ManagerSeriesDataChartProps {
  valueFormat?: string;
  title: string;
  data: [
    {
      date: string;
      value: number;
    },
  ];
}
const ManagerSeriesDataChart: React.FC<ManagerSeriesDataChartProps> = props => {
  const { title, data, valueFormat } = props
  const chartConfig = {
    chart: {
      type: 'column',
      height: 300,
    },
    legend: {
      enabled: false,
    },
    navigator: {
      enabled: false,
    },
    scrollbar: {
      enabled: false,
    },
    series: [
      {
        name: title,
        data: data.map(item => [+moment(item.date).startOf('date'), item.value]),
      },
    ],
  }
  if (valueFormat === 'number') {
    chartConfig.yAxis = {
      labels: {
        format: '{value}',
      },
    }
    chartConfig.tooltip = {
      pointFormat: '{series.name}: <b>{point.y:,.2f}</b><br/>',
    }
  }
  return (
    <div>
      <h4>{title}</h4>
      <Chart options={chartConfig} constructorType="stockChart" />
    </div>
  )
}

const ManagerProperty: React.FC<ComponentProps> = props => {
  const { currentManager } = props
  const getDateStr = (date, emptyTip) => {
    if (!date || date === 'null') {
      return emptyTip || '未知'
    }
    const dateObj = new Date(date)
    if (dateObj.toString() === 'Invalid Date') {
      return date
    }
    return moment(dateObj).format('YYYY/MM/DD')
  }
  const renderDateRange = (emptyStr) => (val: string, record: any, index: number) => {
    return `${getDateStr(record.start_date, emptyStr)} - ${getDateStr(record.end_date, emptyStr)}`
  }
  const educationColumns = [
    {
      title: '时间',
      dataIndex: 'start_date',
      render: renderDateRange(),
    },
    {
      title: '院校',
      dataIndex: 'school',
    },
    {
      title: '专业',
      dataIndex: 'major',
    },
    {
      title: '学历',
      dataIndex: 'edu_level',
    },
  ]
  const resumeColumns = [
    {
      title: '时间',
      dataIndex: 'start_date',
      render: renderDateRange('至今'),
    },
    {
      title: '机构',
      dataIndex: 'company_abbr_name',
    },
    {
      title: '部门',
      dataIndex: 'department',
    },
    {
      title: '职位',
      dataIndex: 'post',
    },
  ]
  const fundListColumns = [
    {
      title: '基金名称',
      dataIndex: 'name',
    },
    {
      title: '基金代码',
      dataIndex: '_qutkeId',
      render: (val: string) => {
        return val.split('.')[0]
      },
    },
    {
      title: '任职期',
      dataIndex: 'managers',
      render: (val: string, record: any, index: number) => {
        const manager = record.managers.filter(item => item.id === currentManager._id)[0]
        const { startDate, endDate, endToNow } = manager
        return `${moment(startDate).format('YYYY/MM/DD')} - ${
          endToNow ? '至今' : moment(endDate).format('YYYY/MM/DD')
        }`
      },
    },
    {
      title: '基金类型',
      dataIndex: 'fundNature',
    },
    {
      title: '年化收益',
      dataIndex: 'yearReturn',
      sorter: sortQuotaFn({ dataIndex: 'yearReturn', format: 'percentage' }, 'asc'),
      align: 'right',
      format: 'percentage',
    },
    {
      title: '最大回撤',
      dataIndex: 'maxDrawdown',
      align: 'right',
      format: 'valPercentage',
      sorter: sortQuotaFn({ dataIndex: 'maxDrawdown', format: 'valPercentage' }, 'asc'),
    },
    {
      title: '波动率',
      dataIndex: 'vol',
      align: 'right',
      format: 'valPercentage',
      sorter: sortQuotaFn({ dataIndex: 'vol', format: 'valPercentage' }, 'asc'),
    },
  ]
  const { managerDataqList } = currentManager
  const managerFundNumData = managerDataqList.map(item => {
    return {
      date: item.the_date,
      value: item.fund_num,
    }
  })
  const managerFundScaleData = managerDataqList.map(item => {
    return {
      date: item.the_date,
      value: item.manage_scale,
    }
  })
  const managerFundReturnData = managerDataqList.map(item => {
    return {
      date: item.the_date,
      value: item.performance_rank,
    }
  })
  return (
    <div>
      <IndividualRating rating={currentManager.manager_property} />
      <Card title="教育背景">
        <Table
          pagination={false}
          columns={educationColumns}
          dataSource={currentManager.educationList || []}
          size="small"
        />
      </Card>
      <Card title="工作履历">
        <Table
          pagination={false}
          columns={resumeColumns}
          dataSource={currentManager.resumeList || []}
          size="small"
        />
      </Card>
      <Card title="历史管理基金">
        <StandardTable
          disableRowSlection
          columns={fundListColumns}
          data={{ list: currentManager.fundList || [], pagination: { pageSize: 8 } }}
          size="small"
        />
      </Card>
      <Card title="历史基金情况">
        <Row gutter={16}>
          <Col lg={8} md={24}>
            <ManagerSeriesDataChart
              valueFormat="number"
              title="管理基金数"
              data={managerFundNumData}
            />
          </Col>
          <Col lg={8} md={24}>
            <ManagerSeriesDataChart
              valueFormat="number"
              title="管理规模（万）"
              data={managerFundScaleData}
            />
          </Col>
          <Col lg={8} md={24}>
            <ManagerSeriesDataChart title="业绩排名百分比（%）" data={managerFundReturnData} />
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentManager: fund.currentManager,
    loading: loading.models.fund,
  }),
)(ManagerProperty)
