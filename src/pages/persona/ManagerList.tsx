import React from 'react'
import { connect } from 'dva'
import ManagerGanttChart from '@/pages/managerpersona/components/ManagerGanttChart'
const ManagerList = ({ currentFund }) => {
  const managerIds = (currentFund.managerList || []).map(item => item.managerId)
  const managerFundData = (currentFund.managerList || []).map((item) => {
    return [
      managerIds.indexOf(item.managerId), +new Date(item.startDate),
      item.endToNow ? Date.now() : +new Date(item.endDate),
      item.name,
      item.managerId,
    ]
  })
  return (
    <div style={{ marginTop: 15 }}>
      <ManagerGanttChart managerFundData={managerFundData} />
    </div>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    fundPositionData: fund.fundPositionData,
    currentFund: fund.currentFund,
    loading: loading.effects['fund/fetchPositionData'],
  }),
)(ManagerList)
