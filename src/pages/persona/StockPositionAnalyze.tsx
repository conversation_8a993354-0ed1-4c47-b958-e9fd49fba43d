import React, { useState } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { useRequest } from '@umijs/hooks'
import uniq from 'lodash/uniq'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Card, Col, Row, Tabs, Spin, Radio, Select, Tooltip, Affix, Space, Empty } from 'antd'
import Chart from '@/components/Chart/Chart'
import moment from 'moment'
import styles from './style.less'
import StandardTable from '@/components/StandardTable'
import SearchSelect from '@/components/SearchSelect'
import getSeriesChartData from '@/utils/getSeriesChartData'
import sortQuotaFn from '@/utils/sortQuotaFn'
import { getPositionData, getStockFactorSeries, getSizeStyleHistory } from '@/services/fund'
import StockMktValSeriesDistribution from './components/StockMktValSeriesDistribution'
import TurnoverTable from './components/TurnoverTable'
import IndustryPreference from './components/IndustryPreference'
import IndustryDiff from './components/IndustryDiff'
import StockIndustryChart from './components/StockIndustry'
import IndustryBoxChart from './components/IndustryBoxChart'
import IndustryHeatMap from './components/IndustryHeatMap'
import PositionCharacterFactor from './components/PositionCharacterFactor'
import SizeStyleTravelChart from './components/SizeStyleTravelChart'
import StockNineSquareChart from './components/StockNineSquareChart'
import BalanceDistribution from './components/BalanceDistribution'
import DailyIndustryPrefer from './components/DailyIndustryPrefer'
import BarraStyleRadar from './components/BarraStyleRadar'
import IndustryHeatMapWrapper from './components/IndustryHeatMapWrapper'
import BarraStylePrefer from './components/BarraStylePrefer'

const { TabPane } = Tabs
const { Option } = Select
const Highcharts = Chart.Highcharts

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  match: any;
  location: any;
  fundPositionData: any;
  fundList?: any;
}

const halfYearFilter = item =>
  (item.F_CODE && item.F_CODE.length === 24) || /(0630|1231)$/.test(item.BIZ_DATE)

const buildPositionStyleSeriesConfig = (data, height) => {
  const series = getSeriesChartData(data.filter(halfYearFilter), 'STYLE_TYPE', 'RATIO')
  const seriesChartConfig = {
    chart: {
      type: 'column',
      height: height || 300,
    },
    yAxis: {
      max: 100,
    },
    navigator: {
      enabled: false,
    },
    scrollbar: {
      enabled: false,
    },
    plotOptions: {
      area: {
        stacking: 'percent',
        lineWidth: 1,
        marker: {
          lineWidth: 2,
          lineColor: '#ffffff',
        },
      },
      column: {
        stacking: 'normal',
      },
    },
    tooltip: {
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.percentage:.2f}%</b><br/>',
    },
    series,
  }
  return seriesChartConfig
}

interface PositionCharacterProps {
  stockPositions: any;
  bondPositions: any;
  assetScales: any;
}

class PositionCharacter extends React.Component<PositionCharacterProps> {
  constructor(props: PositionCharacterProps) {
    super(props)
    const { stockPositions } = props
    this.state = {
      viewType: stockPositions.length !== 0 ? 'stock' : 'bond',
    }
  }

  handleViewTypeChange = event => {
    this.setState({ viewType: event.target.value })
  };

  getChartSeriesData(rows, valueKey, isPercentage) {
    return rows
      .sort((fst, snd) => fst.BIZ_DATE - snd.BIZ_DATE)
      .map(item => [
        +moment(item.BIZ_DATE).startOf('date'),
        isPercentage ? item[valueKey] * 100 : item[valueKey],
      ])
  }

  getAssetRatioData() {
    const { viewType } = this.state
    const { assetScales } = this.props
    if (viewType === 'stock') {
      return assetScales.filter(item => item.ASSET_TYPE === '股票')
    } else {
      return assetScales.filter(item => item.ASSET_TYPE === '债券')
    }
  }

  renderPositionCountChart() {
    const { viewType } = this.state
    const { stockPositions, bondPositions, isMutual } = this.props
    const assetName = viewType === 'stock' ? '股票' : '债券'
    let currentPositions = viewType === 'stock' ? stockPositions : bondPositions
    currentPositions = currentPositions.filter(halfYearFilter)
    const assetData = this.getAssetRatioData().filter(halfYearFilter)
    const chartConfig = {
      navigator: {
        enabled: true,
      },
      scrollbar: {
        enabled: false,
      },
      tooltip: {
        pointFormat:
          '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}<br/>',
      },
      yAxis: [
        {
          labels: {
            format: '{value}',
          },
        },
        {
          labels: {
            format: '{value}%',
          },
          opposite: true,
        },
      ],
      series: [
        {
          yAxis: 0,
          type: 'area',
          name: `${assetName}数量`,
          data: this.getChartSeriesData(currentPositions, 'COUNT', false),
        },
        {
          yAxis: 1,
          type: 'line',
          name: `${assetName}持仓占比${isMutual ? '(半年)' : ''}`,
          data: this.getChartSeriesData(assetData, 'RATIO', true),
          tooltip: {
            pointFormat:
              '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%<br/>',
          },
        },
      ],
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  renderTopTenPostionRatioChart() {
    const { viewType } = this.state
    const { stockPositions, bondPositions, isMutual } = this.props
    const assetName = viewType === 'stock' ? '股票' : '债券'
    const currentPositions = viewType === 'stock' ? stockPositions : bondPositions
    const assetData = this.getAssetRatioData()
    const chartConfig = {
      navigator: {
        enabled: true,
      },
      scrollbar: {
        enabled: false,
      },
      yAxis: [
        {
          labels: {
            format: '{value}',
          },
        },
        {
          labels: {
            format: '{value}%',
          },
          opposite: true,
        },
      ],
      series: [
        {
          yAxis: 0,
          type: 'area',
          name: `${assetName}集中度`,
          data: this.getChartSeriesData(currentPositions, 'RATIO', true),
        },
        {
          yAxis: 1,
          type: 'line',
          name: `${assetName}持仓占比${isMutual ? '(季度)' : ''}`,
          data: this.getChartSeriesData(assetData, 'RATIO', true),
        },
      ],
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  render() {
    const { viewType } = this.state
    return (
      <Card>
        <h4>
          <span>股票集中度</span>
          <Tooltip title="股票集中度为前十大股票持仓占净资产的平方和" placement="right">
            <span style={{ marginLeft: '5px' }}>
              <QuestionCircleOutlined />
            </span>
          </Tooltip>
        </h4>
        {false &&
        <Radio.Group
          value={viewType}
          size="small"
          className={styles.rightAction}
          onChange={this.handleViewTypeChange}
        >
          <Radio.Button value="stock">股票持仓</Radio.Button>
          <Radio.Button value="bond">债券持仓</Radio.Button>
        </Radio.Group>}
        <Row gutter={16}>
          <Col lg={12} md={24}>
            {this.renderPositionCountChart()}
          </Col>
          <Col lg={12} md={24}>
            {this.renderTopTenPostionRatioChart()}
          </Col>
        </Row>
      </Card>
    )
  }
}

interface PositionTableProps {
  dailyStockPosition: any;
  stockPositions: [];
  fundId: string;
  dispatch: Dispatch;
}

@connect(({ fund, loading }: { fund: any; loading: { models: { [key: string]: boolean } } }) => ({
  dailyStockPosition: fund.dailyStockPosition,
  loading: loading.models.fund,
}))
class PositionTable extends React.Component<PositionTableProps> {
  constructor(props: PositionTableProps) {
    super(props)
    const dates = uniq(props.stockPositions.map(item => item.BIZ_DATE)).sort(
      (fst, snd) => snd - fst,
    )
    this.state = {
      viewType: 'stock',
      positionScope: 'top10',
      currentDate: dates[0],
      prevDate: dates[1],
      dates,
    }
  }

  componentDidMount() {
    this.loadStockPosition()
  }

  loadStockPosition() {
    const { dispatch, fundId } = this.props
    const { currentDate, prevDate } = this.state
    dispatch({
      type: 'fund/fetchDailyStockPosition',
      payload: {
        id: fundId,
        params: {
          date: currentDate,
          prevDate: prevDate,
        },
      },
    })
  }

  handleViewTypeChange = (event: any) => {
    this.setState({ viewType: event.target.value })
  };

  handleDateChange = (date: string) => {
    const dates = this.state.dates
    const index = dates.indexOf(date)
    this.setState({ currentDate: date, prevDate: dates[index + 1] }, this.loadStockPosition)
  };

  handlePositionScopeChange = (positionScope: string) => {
    this.setState({ positionScope })
  }

  render() {
    const positionColumns = [
      {
        title: '代码',
        dataIndex: 'STOCK_CODE',
      },
      {
        title: '股票名称',
        dataIndex: 'STOCK_NAME',
      },
      {
        title: '行业',
        dataIndex: 'INDUSTRY',
      },
      {
        title: '权重',
        dataIndex: 'BALANCE_RATIO',
        format: 'valPercentage',
        align: 'right',
        sorter: sortQuotaFn({ dataIndex: 'BALANCE_RATIO', format: 'percentage' }, 'asc'),
      },
      {
        title: '持仓变化',
        dataIndex: 'BALANCE_GROWTH_RATE',
        format: 'valPercentage',
        align: 'right',
        sorter: sortQuotaFn({ dataIndex: 'BALANCE_GROWTH_RATE', format: 'percentage' }, 'asc'),
      },
      {
        title: '股价变化',
        dataIndex: 'PRICE_GROWTH_RATE',
        format: 'valPercentage',
        align: 'right',
        sorter: sortQuotaFn({ dataIndex: 'BALANCE_GROWTH_RATE', format: 'percentage' }, 'asc'),
      },
    ]
    const { dailyStockPosition, loading } = this.props
    const { currentDate, dates, positionScope } = this.state
    const data = dailyStockPosition
      .filter(item => positionScope === 'top10' ? item.RANK <= 10 : true)
    return (
      <Card
        title="核心股票持仓"
        extra={
          <>
            <Select
              value={positionScope}
              onChange={this.handlePositionScopeChange}
              style={{ width: 120 }}
            >
              <Option value="top10">前十大持仓</Option>
              <Option value="all">半年报+季报</Option>
            </Select>
            <Select
              showSearch
              value={currentDate}
              // className={styles.rightAction}
              style={{ width: 150, marginLeft: 15 }}
              placeholder="选择日期"
              optionFilterProp="children"
              onChange={this.handleDateChange}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {dates.map(item => (
                <Option value={item}>{item}</Option>
              ))}
            </Select>
          </>
        }
      >
        <StandardTable
          disableRowSlection
          loading={loading}
          size="small"
          columns={positionColumns}
          data={{
            list: data,
          }}
          scroll={{ y: 450 }}
        />
      </Card>
    )
  }
}

const StockPositionAnalyze = ({ currentFund, dispatch }) => {
  const [activeTab, setActiveTab] = useState('industryAllocation')
  const fundId = currentFund && currentFund._id
  const isMutual = currentFund && currentFund._syncType === 'mutual'
  const defaultFundData = {
    assetScales: [],
    bondPositions: [],
    stockIndustry: [],
    stockPositions: [],
    bondPosition: [],
    stockStyleData: [],
    stockSizeData: [],
  }
  const defaultIndustryData = {
    stockIndustry: [],
  }
  const dateParams = {}
  if (currentFund.isManager) {
    if (currentFund.ref_fund_start_date) {
      dateParams.startDate = currentFund.ref_fund_start_date
    }
    if (currentFund.ref_fund_end_date) {
      dateParams.endDate = currentFund.ref_fund_end_date
    }
  }

  // const { data: fundIndustryData = defaultIndustryData, loading: loadingIndustryData } = useRequest(() => {
  //   const params = {
  //     dataFilters: 'stockIndustry',
  //     ...dateParams,
  //   }
  //   return getPositionData(fundId, params)
  // })

  const { data: fundPositionData = defaultFundData, loading } = useRequest(() => {
    const params = {
      dataFilters: 'stockPosition',
      ...dateParams,
    }
    return getPositionData(fundId, params)
  })

  const { data: stockFactorSeries = [], loading: loadingFactorData } = useRequest(() => {
    return getStockFactorSeries(fundId, dateParams)
  })

  const { data: sizeStyleHistory = { benchmarkScoreData: [], fundScoreData: [] }, loading: loadingSizeStyleHistory } = useRequest(() => {
    return getSizeStyleHistory(fundId, dateParams)
  })

  // const industryData = (fundIndustryData.stockIndustry || [])
  const industryData = []
  const latestIndustryData = fundPositionData.latestIndustryData || []
  const getDatesFromAssetData = (assetData) => {
    return uniq(assetData.map(item => item.BIZ_DATE)).sort(
      (fst, snd) => snd - fst,
    )
  }
  const analyzeDates = getDatesFromAssetData(fundPositionData.stockPositions)
  const dates = [...currentFund.dates]
    .reverse()
    .filter(date => {
      if (!isMutual) {
        return true
      }
      return /(06-30|12-31)$/.test(date)
    })
  const dateList = dates.map(item => {
    return {
      title: item,
      dataIndex: item,
    }
  })
  const [bizDate, setBizDate] = useState(dates[0])
  const indexTypes = [
    { benchmarkName: '沪深300', benchmarkId: '沪深300' },
    { benchmarkName: '中证500', benchmarkId: '中证500' },
    { benchmarkName: '中证800', benchmarkId: '中证800' },
    { benchmarkName: '中证红利', benchmarkId: '中证红利' },
    { benchmarkName: '国证成长', benchmarkId: 'CN2370' },
  ]
  const [indexType, setIndexType] = useState('沪深300')
  return (
    <div>
      <Spin spinning={false} style={{ minHeight: 400 }}>
        <Affix offsetTop={44}>
          <Tabs
            onChange={setActiveTab}
            activeKey={activeTab}
            style={{
              marginBottom: 15,
              background: 'rgb(24, 31, 41)',
            }}
            tabBarExtraContent={
              activeTab === 'industryPrefer' &&
              <Space>
                <SearchSelect
                  value={bizDate}
                  options={dateList}
                  onChange={setBizDate}
                  width="150px"
                />
                <Select
                  placeholder="选择基准"
                  style={{
                    width: '120px',
                  }}
                  value={indexType}
                  onChange={setIndexType}
                >
                  {indexTypes.map(item => {
                    return (
                      <Option value={item.benchmarkId}>
                        {item.benchmarkName}
                      </Option>
                    )
                  })}
                </Select>
              </Space>
            }
          >
            <TabPane tab="行业配置" key="industryAllocation">
            </TabPane>
            <TabPane tab="行业偏好" key="industryPrefer">
            </TabPane>
            <TabPane tab="风格偏好" key="stylePrefer">
            </TabPane>
            <TabPane tab="持仓特征" key="positionCharacter">
            </TabPane>
            <TabPane tab="换手分析" key="turnover">
            </TabPane>
          </Tabs>
        </Affix>
        {['stylePrefer', 'positionCharacter'].includes(activeTab) && loading && <Spin spinning>
            <div style={{ height: 300 }}></div>
        </Spin>
        }
        {['stylePrefer', 'positionCharacter', 'turnover'].includes(activeTab) && !loading &&
        fundPositionData.stockPositions.length === 0 &&
        <Empty style={{ marginTop: 100 }}/>
        }
        {industryData.length !== 0 && false && <StockIndustryChart industryData={industryData} />}
        {activeTab === 'industryAllocation' && false && industryData.length !== 0 && <IndustryHeatMap dataType="industry" product={currentFund} data={industryData} />}
        {activeTab === 'industryAllocation' && false  && industryData.length !== 0 && <IndustryHeatMap dataType="industryBoard" product={currentFund} data={industryData} />}
        {activeTab === 'industryAllocation' &&
        <IndustryHeatMapWrapper currentFund={currentFund} dataType="industry" dateParams={dateParams}/>}
        {activeTab === 'industryAllocation' &&
        <IndustryHeatMapWrapper currentFund={currentFund} dataType="industryBoard" dateParams={dateParams}/>}
        {activeTab === 'industryPrefer' &&
        <DailyIndustryPrefer bizDate={bizDate} fundId={fundId} indexType={indexType}/>}
        {activeTab === 'stylePrefer' && fundPositionData.styleSeriesData && fundPositionData.styleSeriesData.length !== 0 && (
          <Card>
            {currentFund._syncType === 'activeFund' &&
            <Row gutter={16}>
              <Col lg={24} md={24}>
                <BarraStylePrefer currentFund={currentFund} />
              </Col>
            </Row>}
            <Row gutter={16}>
              <Col lg={16} md={24}>
                <BalanceDistribution
                  seriesNames={['价值型', '成长型', '均衡型', '其他']}
                  title="持仓风格漂移"
                  nameKey="STYLE_TYPE"
                  data={fundPositionData.styleSeriesData || []}
                />
              </Col>
              <Col lg={8} md={24}>
                <StockNineSquareChart fundPositionData={fundPositionData}/>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col lg={8} md={24}>
                <StockMktValSeriesDistribution currentFund={currentFund} />
              </Col>
              <Col lg={8} md={24}>
                <BarraStyleRadar currentFund={currentFund} />
              </Col>
              <Col lg={8} md={24}>
                <Spin spinning={loadingSizeStyleHistory}>
                  <SizeStyleTravelChart sizeStyleHistory={sizeStyleHistory} />
                </Spin>
              </Col>
            </Row>
          </Card>
        )}
        {activeTab === 'positionCharacter' && fundPositionData.stockPositions.length !== 0 && (
          <PositionCharacter {...fundPositionData} isMutual={isMutual}/>
        )}
        {activeTab === 'positionCharacter' && fundPositionData.stockPositions.length !== 0 && (
          <PositionCharacterFactor data={stockFactorSeries.map(item => {
            item.date = +new Date(item.factor_date)
            return item
          }).sort((fst, snd) => fst.date - snd.date)} loading={loadingFactorData} />
        )}
        {fundPositionData.stockPositions.length !== 0 && false && (
          <PositionTable
            fundId={fundId}
            stockPositions={fundPositionData.stockPositions}
            dispatch={dispatch}
          />
        )}
        {activeTab === 'turnover' && fundPositionData.stockPositions.length !== 0 && (
          <TurnoverTable dates={analyzeDates} fundId={fundId} />
        )}
      </Spin>
    </div>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    fundPositionData: fund.fundPositionData,
    currentFund: fund.currentFund,
    loading: loading.effects['fund/fetchPositionData'],
  }),
)(StockPositionAnalyze)
