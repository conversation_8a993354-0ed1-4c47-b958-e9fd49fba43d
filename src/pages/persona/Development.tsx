import React from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import moment from 'moment'
import { Collapse, Card, Row, Col } from 'antd'
import StandardTable from '@/components/StandardTable'
import Chart from '@/components/Chart/Chart'
import IndividualRating from './components/IndividualRating'

const { Panel } = Collapse

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentManager: any;
  match: any;
  location: any;
}

const buildTableData = (companyData, marketCompanyData) => {
  const keys = ['fund_num', 'asset_scale', 'nch_fund_num', 'nch_asset_scale']
  const companyTypes = [
    {
      name: '行业最高',
      index: 'h',
    },
    {
      name: '行业前1/4',
      index: '1',
    },
    {
      name: '行业中位数',
      index: '2',
    },
    {
      name: '行业前3/4',
      index: '3',
    },
    {
      name: '行业最低',
      index: 'l',
    },
  ]
  const data = companyTypes.reduce(
    (out, type) => {
      const ret = {
        company_name: type.name,
      }
      keys.forEach(item => {
        const key = `${item}_${type.index}`
        ret[item] = marketCompanyData[key]
      })
      out.push(ret)
      return out
    },
    [companyData],
  )
  return data
}

const Development: React.FC<ComponentProps> = props => {
  const { currentManager } = props
  const { companyDesc, companyDataqList, marketCompanyDataq } = currentManager
  const companyDataColumns = [
    {
      title: '基金公司',
      dataIndex: 'company_name',
    },
    {
      title: '基金数量',
      dataIndex: 'fund_num',
      align: 'right',
    },
    {
      title: '资产合计(亿元)',
      dataIndex: 'asset_scale',
      align: 'right',
      format: 'tenThousandToBillion',
    },
    {
      title: '非货基数量',
      dataIndex: 'nch_fund_num',
      align: 'right',
    },
    {
      title: '非货基资产合计(亿元)',
      dataIndex: 'nch_asset_scale',
      align: 'right',
      format: 'tenThousandToBillion',
    },
  ]

  const companyData = companyDataqList[companyDataqList.length - 1] || {
    company_abbr_name: currentManager.company_abbr_name,
  }
  const typeQuotas = [
    { value: 'stk', name: '股票型' },
    { value: 'bnd', name: '债券型' },
    { value: 'ch', name: '货币型' },
    { value: 'mix', name: '混合型' },
  ]
  const pieChartConfig = {
    chart: {
      type: 'pie',
      height: 260,
    },
    legend: {
      enabled: false,
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.y:,.2f}</b><br/>',
    },
    series: [
      {
        name: '管理规模(亿)',
        size: '100%',
        innerSize: '80%',
        data: typeQuotas.map(quota => {
          return {
            name: quota.name,
            y: companyData[`${quota.value}_asset_scale`] / 10000,
          }
        }),
      },
      {
        name: '管理数量',
        size: '60%',
        innerSize: '60%',
        data: typeQuotas.map(quota => {
          return {
            name: quota.name,
            y: companyData[`${quota.value}_fund_num`],
          }
        }),
      },
    ],
  }
  const series = typeQuotas.map(quota => {
    return {
      name: quota.name,
      data: companyDataqList.map(item => [
        +moment(item.the_date).startOf('date'),
        item[`${quota.value}_asset_scale`] / 10000,
      ]),
    }
  })
  const chartConfig = {
    chart: {
      type: 'area',
    },
    navigator: {
      enabled: false,
    },
    scrollbar: {
      enabled: false,
    },
    plotOptions: {
      area: {
        stacking: 'percent',
        lineWidth: 1,
        marker: {
          lineWidth: 2,
          lineColor: '#ffffff',
        },
      },
      column: {
        stacking: 'normal',
      },
    },
    yAxis: {
      labels: {
        format: '{value}',
      },
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.y:,.2f}亿</b><br/>',
    },
    series,
  }
  return (
    <div>
      <IndividualRating rating={currentManager.development} />
      <Card title="定性分析">
        <Collapse bordered={false} defaultActiveKey={['1']} expandIconPosition="right">
          <Panel header="公司描述" key="1">
            {companyDesc.company_profile}
          </Panel>
          <Panel header="合规风控" key="2">
            {companyDesc.compliance_risk_control}
          </Panel>
          <Panel header="运营交易" key="3">
            {companyDesc.operational_transaction}
          </Panel>
          <Panel header="公司文化" key="4">
            {companyDesc.business_culture}
          </Panel>
        </Collapse>
      </Card>
      <Card title="管理规模">
        <StandardTable
          disableRowSlection
          columns={companyDataColumns}
          data={{ list: buildTableData(companyData, marketCompanyDataq) }}
          size="small"
        />
      </Card>
      <Card>
        <Row gutter={16}>
          <Col lg={16} md={24}>
            <span>产品结构</span>
            <Chart options={chartConfig} constructorType="stockChart" />
          </Col>
          <Col lg={8} md={24}>
            <span>
              <span>外圈(管理规模)</span>
              <span style={{ marginLeft: 15 }}>内圈(管理数量)</span>
            </span>
            <Chart options={pieChartConfig} />
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentManager: fund.currentManager,
    loading: loading.models.fund,
  }),
)(Development)
