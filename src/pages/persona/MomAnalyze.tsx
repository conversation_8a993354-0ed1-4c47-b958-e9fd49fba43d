import React, { useState } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import _ from 'lodash'
import StandardTable from '@/components/StandardTable'
import { FundModelState } from '@/models/fund'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Row, Col, Spin, Card, Select, Radio, Space, Tooltip, Affix, Tabs } from 'antd'
import Chart from '@/components/Chart/Chart'
import chartTheme from '@/components/Chart/theme'
import AreaPieChart from '@/components/AreaPieChart'
import moment from 'moment'
import getIndustryBoardData from '@/utils/getIndustryBoardData'
import getSeriesChartData from '@/utils/getSeriesChartData'
import buildTableColumn from '@/utils/buildTableColumn'
import ChildFundCorrelation from './components/ChildFundCorrelation'
import ChildFundPeriodReturn from './components/ChildFundPeriodReturn'
import IndustryHeatMap from './components/IndustryHeatMap'
import ChildFundPerformanceNew from './components/ChildFundPerformance'
import ChildFundFactorScore from './components/ChildFundFactorScore'

const { Option } = Select
const { TabPane } = Tabs

interface ChildFundPerformanceProps {
  dispatch: Dispatch;
  childFundList: any;
  loadingFundList?: boolean;
}

interface ChildFundPerformanceState {
  current: any;
}

class ChildFundPerformance extends React.Component<
  ChildFundPerformanceProps,
  ChildFundPerformanceState
> {
  render() {
    const { childFundList, loadingFundList } = this.props
    const columns = [
      {
        title: '基金',
        dataIndex: 'name',
        width: 150,
        fixed: 'left',
      },
      {
        title: '买入日期',
        dataIndex: 'startDate',
        format: 'date',
      },
      {
        title: '年化收益',
        dataIndex: 'yearReturn',
        format: 'percentage',
      },
      {
        title: (
          <Space>
            <span>年化超额收益率</span>
            <Tooltip title="不满一年不进行年化处理" placement="right">
              <QuestionCircleOutlined />
            </Tooltip>
          </Space>
        ),
        width: 140,
        dataIndex: 'accessReturn',
        format: 'percentage',
      },
      {
        title: '投资收益率',
        dataIndex: 'capitalReturn',
        format: 'percentage',
      },
      {
        title: '波动率',
        dataIndex: 'vol',
        format: 'percentage',
      },
      {
        title: '跟踪误差',
        dataIndex: 'deviationTracking',
        format: 'percentage',
      },
      {
        title: '夏普比',
        dataIndex: 'sharpeRatio',
        format: 'percentage',
      },
      {
        title: '信息比',
        dataIndex: 'informationRatio',
        format: 'percentage',
      },
      {
        title: '上涨月份占比',
        dataIndex: 'perPositive',
        format: 'percentage',
      },
      {
        title: '正超额月份占比',
        dataIndex: 'monthlyReturnWinningRate',
        format: 'percentage',
      },
    ]
    return (
      <StandardTable
        tableLayout="fixed"
        loading={loadingFundList}
        disableRowSlection
        size="small"
        columns={columns}
        scroll={{ x: 1300 }}
        data={{ list: childFundList, pagination: false }}
      />
    )
  }
}

interface SharedStockAnalyzeProps {
  dispatch: Dispatch;
  sharedStocks: any;
  childFundStockData: any;
  loadingStockData?: boolean;
  currentFund:? any;
}

interface SharedStockAnalyzeState {
  current: any;
}

class SharedStockAnalyze extends React.Component<SharedStockAnalyzeProps, SharedStockAnalyzeState> {
  constructor(props: SharedStockAnalyzeProps) {
    super(props)
    const { sharedStocks } = props
    this.state = {
      current: sharedStocks[0],
    }
  }

  componentDidMount() {
    this.loadChildFundStockData()
  }

  handleStockChange = (stockCode: string) => {
    const current = this.props.sharedStocks.find(item => item.stockCode === stockCode)
    this.setState({ current }, this.loadChildFundStockData)
  };

  loadChildFundStockData = () => {
    const { current } = this.state
    const { dispatch, currentFund } = this.props
    const fundId = currentFund._id
    dispatch({
      type: 'fund/fetchChildFundStockData',
      payload: {
        fundId,
        params: {
          stockCode: current.stockCode,
          ids: current.managerData.map(item => item.fundId).join(','),
        },
      },
    })
  };

  renderChart() {
    const { current } = this.state
    const { childFundStockData } = this.props
    const stockData = _.mapValues(_.groupBy(childFundStockData, 0), values => {
      return values
        .map(item => [+moment(item[1]).startOf('date'), item[6], item[5]])
        .sort((fst, snd) => fst[0] - snd[0])
        .map(item => ({
          x: item[0],
          y: item[1] * 100,
        }))
    })
    const stockPriceData = _.map(_.groupBy(childFundStockData, 1), (values, date) => {
      return [+moment(date).startOf('date'), values[0][4]]
    })
    const series = current.managerData.map(item => {
      return {
        name: item.name,
        type: 'column',
        data: stockData[item.fundId],
        yAxis: 0,
        tooltip: {
          pointFormat: '{series.name}: <b>{point.y:,.2f}%</b><br/>',
        },
      }
    })
    series.push({
      name: '股价',
      type: 'line',
      data: stockPriceData,
      yAxis: 1,
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y:,.2f}</b><br/>',
      },
    })
    const chartConfig = {
      chart: {
        height: 350,
      },
      navigator: {
        enabled: false,
      },
      scrollbar: {
        enabled: false,
      },
      yAxis: [
        {
          labels: {
            format: '{value}%',
          },
        },
        {
          labels: {
            format: '{value}',
          },
          opposite: true,
        },
      ],
      plotOptions: {
        column: {
          stacking: 'normal',
        },
      },
      series,
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  render() {
    const { sharedStocks, loadingStockData } = this.props
    const { current } = this.state
    return (
      <Spin spinning={loadingStockData}>
        <Card
          title="共同持仓分析"
          extra={
            <div>
              <span style={{ marginRight: '10px' }}>选择股票:</span>
              <Select
                showSearch
                style={{ width: 150 }}
                placeholder="请选择股票"
                onChange={this.handleStockChange}
                value={current.stockCode}
              >
                {sharedStocks.map((item: any) => (
                  <Option value={item.stockCode} key={item.stockCode}>
                    {item.stockName}
                  </Option>
                ))}
              </Select>
            </div>
          }
        >
          {this.renderChart()}
        </Card>
      </Spin>
    )
  }
}

interface StockIndustryChartProps {
  industryData: any;
  fundNameMap: any;
  stockSizeData: any;
  stockStyleData: any;
}

interface IndustryItem {
  F_CODE: string;
  BIZ_DATE: string;
  INDUSTRY: string;
  BALANCE: number;
}

interface StockIndustryChartState {
  industryData: IndustryItem[];
  industryBoardData: IndustryItem[];
  fundList: any;
  industryList: any;
  industryBoardList: any;
  currentFilter: any;
  currentFilterList: any;
  viewType: string;
  filterType: string;
  stockSizeList: any,
  stockStyleList: any,
  stockStyleData: IndustryItem[];
  stockSizeData: IndustryItem[];
}

class StockIndustryChart extends React.Component<StockIndustryChartProps, StockIndustryChartState> {
  constructor(props: StockIndustryChartProps) {
    super(props)
    const halfYearFilter = item =>
      (item.F_CODE && item.F_CODE.length === 24) || /(0630|1231)$/.test(item.BIZ_DATE)
    const dataMapper = item => {
      return {
        F_CODE: item[0],
        BIZ_DATE: item[1],
        INDUSTRY: item[2],
        BALANCE: item[3],
      }
    }
    const industryData = props.industryData
      .map(dataMapper)
      .filter(halfYearFilter)
    const industryBoardData = getIndustryBoardData(industryData)
    const stockSizeData = props.stockSizeData
      .map(dataMapper)
      .filter(halfYearFilter)
    const stockStyleData = props.stockStyleData
      .map(dataMapper)
      .filter(halfYearFilter)
    const { fundNameMap } = props
    const fundList = _.uniq(industryBoardData.map(item => item.F_CODE)).map(fundId => ({
      value: fundId,
      name: fundNameMap[fundId],
    }))
    const industryList = _.uniq(industryData.map(item => item.INDUSTRY)).map(industry => ({
      value: industry,
      name: industry,
    }))
    const industryBoardList = _.uniq(industryBoardData.map(item => item.INDUSTRY)).map(
      industry => ({
        value: industry,
        name: industry,
      }),
    )
    const stockSizeList = _.uniq(stockSizeData.map(item => item.INDUSTRY)).map(industry => ({
      value: industry,
      name: industry,
    }))
    const stockStyleList = _.uniq(stockStyleData.map(item => item.INDUSTRY)).map(industry => ({
      value: industry,
      name: industry,
    }))
    this.state = {
      industryData,
      industryBoardData,
      stockSizeData,
      stockStyleData,
      fundList,
      industryList,
      industryBoardList,
      stockSizeList,
      stockStyleList,
      currentFilter: industryList[0] && industryList[0].value,
      currentFilterList: industryList,
      viewType: 'industry',
      filterType: 'industry',
    }
  }

  handleFilterChange = filter => {
    this.setState({ currentFilter: filter })
  };

  getFilterData = (viewType, filterType) => {
    const { industryList, fundList, industryBoardList, stockStyleList, stockSizeList } = this.state
    let currentFilterList
    if (filterType === 'industry') {
      currentFilterList = viewType === 'industry'
        ? industryList : viewType === 'industryBoard'
        ? industryBoardList : viewType === 'style'
        ? stockStyleList : stockSizeList
    } else {
      currentFilterList = fundList
    }
    const currentFilter = currentFilterList[0] && currentFilterList[0].value
    return {
      currentFilterList,
      currentFilter,
    }
  };

  handleViewTypeChange = viewType => {
    const { filterType } = this.state
    this.setState({
      viewType,
      ...this.getFilterData(viewType, filterType),
    })
  };

  handleFilterTypeChange = event => {
    const { viewType } = this.state
    const filterType = event.target.value
    this.setState({
      filterType,
      ...this.getFilterData(viewType, filterType),
    })
  };

  renderIndustryPreference() {
    const { fundNameMap } = this.props
    const { viewType, filterType, currentFilter, industryData, industryBoardData, stockStyleData, stockSizeData } = this.state
    let data = viewType === 'industry'
        ? industryData : viewType === 'industryBoard'
        ? industryBoardData : viewType === 'style'
        ? stockStyleData : stockSizeData
    data = data.filter(item => {
      if (filterType === 'industry') {
        return item.INDUSTRY === currentFilter
      }
      return item.F_CODE === currentFilter
    })
    const series = getSeriesChartData(
      data,
      filterType === 'industry' ? 'F_CODE' : 'INDUSTRY',
      'BALANCE',
      fundNameMap,
    )
    let heatMapData = series.map((item, index) => {
      const data = item.data
      const value = data[data.length - 1].yValue
      return {
        name: item.name,
        value: value,
        color: chartTheme.colors[index % chartTheme.colors.length],
      }
    })
    const maxDate = _.max(data.map(item => item.BIZ_DATE))
    const balanceSum = _.sumBy(heatMapData, 'value')
    heatMapData = heatMapData.map(item => {
      return {
        ...item,
        value: (item.value / balanceSum) * 100,
      }
    })
    const chartConfig = {
      chart: {
        type: 'column',
      },
      yAxis: {
        max: 100,
      },
      navigator: {
        enabled: false,
      },
      scrollbar: {
        enabled: false,
      },
      plotOptions: {
        area: {
          stacking: 'normal',
        },
        column: {
          stacking: 'normal',
        },
      },
      tooltip: {
        pointFormat:
          '<span style="color:{series.color}">{series.name}</span>: <b>{point.percentage:.2f}%<br/>',
      },
      series,
    }
    const config = {
      chart: {
        height: 280,
      },
      tooltip: {
        pointFormat: '<span>{point.name}</span>: <b>{point.value:.2f}%<br/>',
      },
      series: [
        {
          type: 'treemap',
          layoutAlgorithm: 'squarified',
          data: heatMapData,
        },
      ],
    }
    return (
      <Row gutter={16}>
        <Col lg={18} md={24}>
          <h4>{fundNameMap[currentFilter] || currentFilter}</h4>
          <Chart options={chartConfig} constructorType="stockChart" />
        </Col>
        <Col lg={6} md={24}>
          <h4>{moment(maxDate).format('YYYY-MM-DD')}</h4>
          <Chart options={config} />
        </Col>
      </Row>
    )
  }

  render() {
    const { viewType, filterType, currentFilter, currentFilterList } = this.state
    const viewTypeList = [{
      name: '行业',
      value: 'industry',
    }, {
      name: '行业板块',
      value: 'industryBoard',
    }, {
      name: '风格',
      value: 'style',
    }, {
      name: '市值',
      value: 'size',
    }]
    const currentViewType = viewTypeList.find(item => item.value === viewType)
    return (
      <Card
        title="持仓行业分布"
        extra={
          <>
            <Select
              size="small"
              style={{ width: 100, marginLeft: 15 }}
              placeholder="请选择"
              onChange={this.handleViewTypeChange}
              value={viewType}
            >
              {viewTypeList.map((item: any) => (
                <Option value={item.value} key={item.value}>
                  {item.name}
                </Option>
              ))}
            </Select>
            <Radio.Group
              style={{ marginLeft: 15 }}
              value={filterType}
              size="small"
              onChange={this.handleFilterTypeChange}
            >
              <Radio.Button value="industry">{currentViewType.name}</Radio.Button>
              <Radio.Button value="manager">基金经理</Radio.Button>
            </Radio.Group>
            <Select
              showSearch
              size="small"
              style={{ width: 150, marginLeft: 15 }}
              placeholder="请选择"
              onChange={this.handleFilterChange}
              value={currentFilter}
            >
              {currentFilterList.map((item: any) => (
                <Option value={item.value} key={item.value}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </>
        }
      >
        {this.renderIndustryPreference()}
      </Card>
    )
  }
}

const HisInvestChart = ({ assetRows, fundNameMap, loadingFundPosition }) => {
  const [viewType, setViewType] = useState('percentage')
  const handleViewTypeChange = event => {
    setViewType(event.target.value)
  }
  return (
    <Card title="历史投资比例"
      extra={
        <Radio.Group
          value={viewType}
          size="small"
          onChange={handleViewTypeChange}
        >
          <Radio.Button value="percentage">按比例</Radio.Button>
          <Radio.Button value="balance">按份额</Radio.Button>
        </Radio.Group>
      }
    >
      <Spin spinning={loadingFundPosition}>
        <AreaPieChart
          key={viewType}
          disablePie
          height={350}
          stackingValue={viewType === 'balance'}
          pctValueKey={viewType === 'balance' ? 'NET_ASSET' : undefined}
          rows={assetRows}
          nameKey="F_CODE"
          valueKey="NET_ASSET"
          nameMap={fundNameMap}
        />
      </Spin>
    </Card>
  )
}

interface ComponentProps {
  dispatch: Dispatch<any>;
  loadingFundList?: boolean;
  loadingFundPosition?: boolean;
  loadingStockData?: boolean;
  currentFund: any;
  childFundList: any;
  childFundPosition?: any;
  childFundStockData: any;
  correlationData: any;
}

class MomAnalyze extends React.Component<ComponentProps> {
  constructor(props) {
    super(props)
    const dates = props.currentFund.dates || []
    this.state = {
      activeTab: 'performance',
      currentDate: dates[dates.length - 1],
    }
  }

  componentDidMount() {
    this.loadChildFundList()
    this.loadChildFundPosition()
    this.loadChildFundIndustry()
  }

  loadChildFundList() {
    const { dispatch, currentFund } = this.props
    dispatch({
      type: 'fund/fetchChildFundList',
      payload: {
        id: currentFund._id,
      },
    })
  }

  loadChildFundPosition() {
    const { dispatch, currentFund } = this.props
    dispatch({
      type: 'fund/fetchChildFundPosition',
      payload: {
        id: currentFund._id,
      },
    })
  }

  loadChildFundIndustry() {
    const { currentDate } = this.state
    const { dispatch, currentFund } = this.props
    dispatch({
      type: 'fund/fetchChindFundIndustry',
      payload: {
        id: currentFund._id,
        params: { bizDate: currentDate && currentDate.replace(/-/g, '')},
      },
    })
  }

  renderSharedPositionStat() {
    const {
      childFundPosition: { sharedStocks },
      loadingFundPosition,
    } = this.props
    const columns = [
      {
        title: '股票代码',
        width: 80,
        dataIndex: 'stockCode',
      },
      {
        title: '股票名称',
        width: 80,
        dataIndex: 'stockName',
      },
      {
        title: '行业',
        width: 80,
        dataIndex: 'industry',
        hasSorter: true,
      },
      {
        title: '基金经理数量',
        dataIndex: 'managerCount',
        hasSorter: true,
      },
      {
        title: '占母基金净资产比',
        width: 140,
        dataIndex: 'managerRatio',
        format: 'valPercentage',
        hasSorter: true,
      },
    ].map(buildTableColumn)
    const managerData = sharedStocks.reduce((out, item) => {
      return out.concat(item.managerData)
    }, [])
    const amountData = _.mapValues(_.groupBy(managerData, 'name'), values => {
      return values.reduce((out, item) => {
        out[item.stockCode] = item.ratio * 100
        return out
      }, {})
    })
    const series = _.keys(amountData).map(name => {
      return {
        name,
        yAxis: 0,
        type: 'column',
        data: sharedStocks.map(stock => {
          return amountData[name][stock.stockCode] || null
        }),
        tooltip: {
          pointFormat: '{series.name}: <b>{point.y:,.2f}%</b><br/>',
        },
      }
    })
    series.push({
      name: '基金经理数量',
      yAxis: 1,
      type: 'line',
      data: sharedStocks.map(stock => stock.managerCount),
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y:,.0f}</b><br/>',
      },
    })
    const chartConfig = {
      chart: {
        height: 350,
      },
      xAxis: {
        categories: sharedStocks.map(item => item.stockName),
      },
      plotOptions: {
        column: {
          stacking: 'normal',
        },
      },
      yAxis: [
        {
          title: {
            text: '占母基金净资产比',
          },
          labels: {
            format: '{value}%',
          },
        },
        {
          title: {
            text: '基金经理数量',
          },
          labels: {
            format: '{value}',
          },
          opposite: true,
        },
      ],
      series,
    }
    return (
      <Spin spinning={loadingFundPosition}>
        <Row gutter={16}>
          <Col lg={14} md={24}>
            <Chart options={chartConfig} />
          </Col>
          <Col lg={10} md={24}>
            <StandardTable
              disableRowSlection
              size="small"
              columns={columns}
              data={{ list: sharedStocks, pagination: false }}
              scroll={{
                y: 300,
              }}
            />
          </Col>
        </Row>
      </Spin>
    )
  }

  handleTabChange = (activeTab) => {
    this.setState({ activeTab })
  }

  handleDateChange = (date: string) => {
    this.setState({ currentDate: date }, this.loadChildFundIndustry)
  }

  render() {
    const { activeTab, currentDate } = this.state
    const {
      childFundPosition: {
        sharedStocks, stockIndustry, fundNameMap, assetData,
        stockSizeData, stockStyleData,
      },
      dispatch,
      childFundStockData,
      loadingStockData,
      loadingFundPosition,
      childFundList,
      loadingFundList,
      currentFund,
      childFundIndustry,
      loadingFundIndustry,
    } = this.props
    const assetRows = assetData.map(item => {
      return {
        F_CODE: item[0],
        BIZ_DATE: item[1],
        NET_ASSET: item[2],
      }
    })
    const dates = [...currentFund.dates].reverse()
    return (
      <div>
        <Affix offsetTop={44}>
          <Tabs
            onChange={this.handleTabChange}
            activeKey={activeTab}
            style={{
              marginBottom: 15,
              background: 'rgb(24, 31, 41)',
            }}
            tabBarExtraContent={
              activeTab === 'industryAllocation' &&
              <Select
                showSearch
                value={currentDate}
                // className={styles.rightAction}
                style={{ width: 150, marginLeft: 15 }}
                placeholder="选择日期"
                optionFilterProp="children"
                onChange={this.handleDateChange}
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {dates.map(item => (
                  <Option value={item}>{item}</Option>
                ))}
              </Select>
            }
          >
            <TabPane tab="业绩指标" key="performance">
            </TabPane>
            <TabPane tab="配置情况" key="allocation">
            </TabPane>
            <TabPane tab="共同持仓" key="sharestock">
            </TabPane>
            <TabPane tab="行业配置" key="industryAllocation">
            </TabPane>
            <TabPane tab="因子评价" key="factorEval">
            </TabPane>
          </Tabs>
        </Affix>
        {activeTab === 'performance' &&
        <>
          <Row gutter={0} style={{ marginBottom: 15 }}>
            <Col md={24} lg={16} xl={16}>
              <ChildFundPeriodReturn currentFund={currentFund} childFundList={childFundList} />
            </Col>
            <Col md={24} lg={8} xl={8}>
              {childFundList.length !== 0 && <ChildFundCorrelation childFundList={childFundList} />}
            </Col>
          </Row>
          {childFundList.length !== 0 &&
          <ChildFundPerformanceNew currentFund={currentFund} childFundList={childFundList}/>}
        </>}
        {activeTab === 'allocation' &&
        <>
          <HisInvestChart
            assetRows={assetRows}
            fundNameMap={fundNameMap}
            loadingFundPosition={loadingFundPosition}
          />
          {stockIndustry.length !== 0 && (
            <StockIndustryChart stockSizeData={stockSizeData} stockStyleData={stockStyleData} industryData={stockIndustry} fundNameMap={fundNameMap} />
          )}
        </>}
        {activeTab === 'sharestock' &&
        <>
          <Card title="共同持仓统计">{this.renderSharedPositionStat()}</Card>
          {sharedStocks.length !== 0 && (
            <SharedStockAnalyze
              currentFund={currentFund}
              sharedStocks={sharedStocks}
              dispatch={dispatch}
              childFundStockData={childFundStockData}
              loadingStockData={loadingStockData}
            />
          )}
        </>}
        {activeTab === 'industryAllocation' && <IndustryHeatMap isSectionData loading={loadingFundIndustry} dataType="industry" product={currentFund} data={childFundIndustry} />}
        {activeTab === 'industryAllocation' && <IndustryHeatMap isSectionData loading={loadingFundIndustry} dataType="industryBoard" product={currentFund} data={childFundIndustry} />}
        {activeTab === 'factorEval' && <ChildFundFactorScore product={currentFund}/>}
      </div>
    )
  }
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: FundModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    childFundList: fund.childFundList,
    childFundPosition: fund.childFundPosition,
    currentFund: fund.currentFund,
    childFundStockData: fund.childFundStockData,
    correlationData: fund.correlationData,
    childFundIndustry: fund.childFundIndustry,
    loadingFundList: loading.effects['fund/fetchChildFundList'],
    loadingFundPosition: loading.effects['fund/fetchChildFundPosition'],
    loadingStockData: loading.effects['fund/fetchChildFundStockData'],
    loadingFundIndustry: loading.effects['fund/fetchChindFundIndustry'],
  }),
)(MomAnalyze)
