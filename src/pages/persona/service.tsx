import request from '@/utils/request'

export async function queryCompany(code: string) {
  return request(`/api/persona/company/${code}`)
}

export async function queryCompanyFunds(name: string) {
  return request(`/api/persona/company/${name}/funds`)
}

export async function queryHisTotalScore(fundId: string, params) {
  return request(`/api/products/${fundId}/histotalscore`, {
    params,
  })
}

export async function queryManagerHisTotalScore(fundId: string, params) {
  return request(`/api/managers/${fundId}/histotalscore`, {
    params,
  })
}

export async function queryHisTotalScoreAsset(fundId: string, params) {
  return request(`/api/products/${fundId}/histotalscoreandasset`, {
    params,
  })
}

export async function queryManagerHisTotalScoreAsset(fundId: string, params) {
  return request(`/api/managers/${fundId}/histotalscoreandasset`, {
    params,
  })
}

export async function queryFundTagData(fundId: string, params) {
  return request(`/api/products/${fundId}/fundtags`, {
    params,
  })
}

export async function queryFundTagDateList(fundId: string, params) {
  return request(`/api/products/${fundId}/fundtagdatelist`, {
    params,
  })
}
