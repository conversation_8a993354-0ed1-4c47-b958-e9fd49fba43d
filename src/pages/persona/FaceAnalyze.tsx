import React from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { Card, Table, Col, Row } from 'antd'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentManager: any;
  match: any;
  location: any;
}

const FaceAnalyze: React.FC<ComponentProps> = props => {
  const { currentManager } = props
  const columns = [
    {
      title: '面相分析',
      dataIndex: 'face_analysis',
    },
    {
      title: '绝对值',
      dataIndex: 'abs_value',
    },
    {
      title: '相对分析',
      dataIndex: 'rel_analysis',
    },
  ]
  const {
    faceInfo: { details, summary },
  } = currentManager
  return (
    <div>
      <Card>
        <Row gutter={16}>
          <Col lg={6} md={24}>
            <div style={{ textAlign: 'center' }}>
              <img
                src={currentManager.avatar}
                style={{
                  width: '220px',
                  background: 'none',
                  verticalAlign: 'middle',
                }}
              />
            </div>
          </Col>
          <Col lg={18} md={24}>
            <Table size="small" columns={columns} dataSource={details} pagination={false} />
          </Col>
        </Row>
      </Card>
      <Card title="分析结果">
        <p>{summary}</p>
      </Card>
    </div>
  )
}

export default connect(
  ({
    manager,
    loading,
  }: {
    manager: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentManager: manager.currentManager,
    loading: loading.models.manager,
  }),
)(FaceAnalyze)
