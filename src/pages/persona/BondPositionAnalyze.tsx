import React, { useState } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import uniq from 'lodash/uniq'
import { formatMessage } from 'umi-plugin-react/locale'
import { Tabs, Spin, Affix, Row, Col, Empty } from 'antd'
import { useRequest } from '@umijs/hooks'
import BondStatistics from '@/pages/analysis/position/components/BondStatistics'
import BondTermDistribution from '@/pages/analysis/position/components/BondTermDistribution'
import PositionModel from '@/pages/analysis/position/PositionModel'
import {
  getConvtBondIndustry, getPositionData,
  getBondDistributionData,
  getConvtBondDistirubtion,
} from '@/services/fund'
import ConvtBondWeight from './components/ConvtBondWeight'
import ConvtBondDetail from './components/ConvtBondDetail'
import StockIndustryChart from './components/StockIndustry'
import PureBondAnalyze from './components/PureBondAnalyze'
import IndustryChange from './components/IndustryChange'
import IndustryHeatMap from './components/IndustryHeatMap'
import BalanceDistribution from './components/BalanceDistribution'

const t = (id: string) => formatMessage({ id })
const { TabPane } = Tabs

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  match: any;
  location: any;
  fundPositionData: any;
  fundList?: any;
}

const ConvtBondIndustry = ({
  fundId,
}: {
  fundId: string,
}) => {
  const { data, loading } = useRequest(() => {
    return getConvtBondIndustry(fundId)
  })
  return (
    <Spin spinning={loading}>
      <StockIndustryChart industryData={data || []} title="可转债正股行业配置"/>
    </Spin>
  )
}

const BondPositionAnalyze = ({ currentFund }) => {
  const [activeTab, setActiveTab] = useState('purebond')
  const fundId = currentFund && currentFund._id
  const isMutual = currentFund && currentFund._syncType === 'mutual'
  const defaultFundData = {
    assetScales: [],
    bondPositions: [],
    stockIndustry: [],
    stockPositions: [],
    bondPosition: [],
    stockStyleData: [],
    stockSizeData: [],
  }
  const dateParams = {}
  if (currentFund.isManager) {
    if (currentFund.ref_fund_start_date) {
      dateParams.startDate = currentFund.ref_fund_start_date
    }
    if (currentFund.ref_fund_end_date) {
      dateParams.endDate = currentFund.ref_fund_end_date
    }
  }
  const { data: fundPositionData = defaultFundData, loading } = useRequest(() => {
    return getPositionData(fundId, {
      dataFilters: 'bondPosition',
      ...dateParams,
    })
  })
  const defaultBondData = {
    classData: [],
    creditTermData: [],
    noneCreditTermData: [],
    ratingData: [],
  }
  const { data: bondData = defaultBondData, loading: loadingBond } = useRequest(() => {
    return getBondDistributionData(fundId, dateParams)
  })
  // const { data: bondSizeData = [], loading: loadingBondSizeSeries } = useRequest(() => {
  //   return getConvtBondSizeSeries(fundId)
  // })
  // const { data: industryData = [], loading: loadingIndustryData } = useRequest(() => {
  //   return getConvtBondIndustry(fundId)
  // })
  const { data: distributionData = {
    industryData: [],
    sizeData: [],
    assetData: [],
  }, loading: loadingDistribution } = useRequest(() => {
    return getConvtBondDistirubtion(fundId, dateParams)
  })

  const positionModel = new PositionModel({
    t,
    analysisResult: { bondPosition: fundPositionData.bondPosition || [] },
  })
  const getDatesFromAssetData = (assetData) => {
    return uniq(assetData.map(item => item.BIZ_DATE)).sort(
      (fst, snd) => snd - fst,
    )
  }
  const hasConvtBond = fundPositionData.bondPosition &&
    fundPositionData.hasConvtBond
  const analyzeDates = getDatesFromAssetData(fundPositionData.bondPositions)
  return (
    <div>
      <Spin spinning={loading && false} style={{ minHeight: 400 }}>
        <Affix offsetTop={44}>
          <Tabs
            onChange={setActiveTab}
            activeKey={activeTab}
            style={{
              marginBottom: 15,
              background: 'rgb(24, 31, 41)',
            }}
          >
            <TabPane tab="纯债分析" key="purebond">
            </TabPane>
            <TabPane tab="可转分析" key="convtbond">
            </TabPane>
            <TabPane tab="指标统计" key="quotas">
            </TabPane>
          </Tabs>
        </Affix>
        {activeTab === 'purebond' &&
        <Spin spinning={loadingBond}>
          <PureBondAnalyze isMutual={isMutual} bondData={bondData} />
        </Spin>}
        {activeTab === 'convtbond' && hasConvtBond &&
        <Row gutter={8}>
          <Col xs={24} md={12} xl={12} xxl={12}>
            <ConvtBondWeight fundId={fundId} />
          </Col>
          <Col xs={24} md={12} xl={12} xxl={12}>
            <BalanceDistribution title="可转债类型分布" data={distributionData.assetData} balanceRatioName="占可转债市值"/>
          </Col>
        </Row>}
        {activeTab === 'convtbond' && !hasConvtBond && <Empty style={{ marginTop: 60 }} description="本基金没有可转债持仓"/>}
        {activeTab === 'convtbond' && hasConvtBond && <IndustryHeatMap dataType="industry" product={currentFund} data={distributionData.industryData} balanceRatioName="占可转债市值" />}
        {activeTab === 'convtbond' && hasConvtBond && <IndustryHeatMap dataType="industryBoard" product={currentFund} data={distributionData.industryData} balanceRatioName="占可转债市值" />}
        {activeTab === 'convtbond' && hasConvtBond &&
        <Row gutter={8}>
          <Col xs={24} md={12} xl={12} xxl={12}>
            <Spin spinning={loadingDistribution}>
              <BalanceDistribution title="可转债正股市值分布" nameKey="sizeType" data={distributionData.sizeData} balanceRatioName="占可转债市值"/>
            </Spin>
          </Col>
          <Col xs={24} md={12} xl={12} xxl={12}>
            {distributionData.industryData.length &&
            <IndustryChange title="可转债正股行业走势" industryData={distributionData.industryData}/>}
          </Col>
        </Row>}
        {activeTab === 'convtbond' && hasConvtBond && <ConvtBondDetail dates={analyzeDates} fundId={fundId} />}
        {activeTab === 'quotas' && fundPositionData.bondPosition.length !== 0 && (
          <BondTermDistribution {...positionModel.buildBondTermDistributionConfig()} />
        )}
        {activeTab === 'quotas' && fundPositionData.bondPosition.length !== 0 && (
          <BondStatistics {...positionModel.buildBondStatisticsConfig()} />
        )}
      </Spin>
    </div>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    fundPositionData: fund.fundPositionData,
    currentFund: fund.currentFund,
    loading: loading.effects['fund/fetchPositionData'],
  }),
)(BondPositionAnalyze)
