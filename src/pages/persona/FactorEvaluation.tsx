import React, { useState } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import _ from 'lodash'
import moment from 'moment'
import router from 'umi/router'
import { Tabs, Table, Col, Row, Select, Spin, Space, Tag, Steps, Typography, Avatar, Divider } from 'antd'
import { useRequest } from '@umijs/hooks'
import { assetClassMap, styleTypeMap } from '@/utils/kymDefMapping'
import {
  queryFactorSchemas, getFactorResults,
  calculateFundFactorScore, calculateManagerFactorScore,
} from '@/pages/factorschema/service'
import ExportData from '@/components/ExportData'
import styles from './style.less'
import ScoringPolarChart from './components/ScoringPolarChart'
import FactorBarChart from './components/FactorBarChart'
import HisTotalScoreModal from './components/HisTotalScoreModal'
import HisFactorScoreAsset from './components/HisFactorScoreAsset'
const { TabPane } = Tabs
const { Title } = Typography

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentFund: any;
  match: any;
  location: any;
}

const getDefaultSchema = (fund, factorSchemaList) => {
  const schema = factorSchemaList.find(item => {
    if (item.isDefault) {
      if (fund.asset_type === '2') {
        return item.schemaType === 'bond'
      } else if (fund.asset_type === '3') {
        return item.schemaType === 'allocate'
      } else {
        return item.schemaType === 'stock'
      }
    } else {
      if (fund.asset_type === '2') {
        return item.name === '默认因子方案-纯债'
      } else if (fund.asset_type === '3') {
        return item.name === '默认因子方案-配置'
      } else {
        return item.name === '默认因子方案-纯股'
      }
    }
  })
  return schema || factorSchemaList[0]
}

const factorTierKeyMap = [{
  name: '收益类',
  value: 'incomeFactorScore',
}, {
  name: '风险类',
  value: 'riskFactorScore',
}, {
  name: '归因类',
  value: 'attributionFactorScore',
}, {
  name: '策略类',
  value: 'strategyFactorScore',
}, {
  name: '基金公司类',
  value: 'companyFactorScore',
}, {
  name: '基金经理类',
  value: 'managerFactorScore',
}, {
  name: '持仓类',
  value: 'positionFactorScore',
}, {
  name: 'total',
  value: 'totalFactorScore',
}].reduce((out, item) => {
  out[item.name] = item.value
  return out
}, {})

const FactorEvaluation = ({
  currentFund,
  factorSchemaList,
  isFund,
}) => {
  const deafultFactorTier = {
    factorTier: '历史得分',
    value: 'historyScore',
  }
  const defaultSchema = getDefaultSchema(currentFund, factorSchemaList)
  const [currentSchem, setCurrentSchema] = useState(defaultSchema)
  const [factorData, setFactorData] = useState(currentSchem.factorData)
  const [currentFactorTier, setCurrentFactorTier] = useState(deafultFactorTier)
  const { loading, data: results = [], run: doGetFactorResults } = useRequest((schemaId) => {
    const type = isFund ? 'fund' : 'manager'
    return getFactorResults(schemaId || currentSchem._id, type, currentFund._id, {
      isDefault: schemaId ? 'N' : 'Y',
    })
  })
  const { loading: loadingFactorScore, data: factorScoreData = {}, run: doGetFactorScore } = useRequest((schemaId) => {
    const requestData = { schemaId: schemaId || currentSchem._id }
    return isFund ? calculateFundFactorScore(currentFund._id, requestData, {
      isDefault: schemaId ? 'N' : 'Y',
    })
      : calculateManagerFactorScore(currentFund._id, requestData, {
        isDefault: schemaId ? 'N' : 'Y',
      })
  })
  // const factorTierWeight = factorData.reduce((out, item) => {
  //   out[item.factorTier] = (item.weight || 0) / 100
  //   return out
  // }, {})
  // const scoreSummary = _.mapValues(_.groupBy(results, 'level5_tier'), (values, factorTier) => {
  //   const factorTierData = factorData.find(item => item.factorTier === factorTier) || {}
  //   const factors = factorTierData.factors || []
  //   const valueMap = values.reduce((out, item) => {
  //     out[item.factor_code] = item.vpct
  //     return out
  //   }, {})
  //   const weightSum = _.sum(factors.filter(item => valueMap[item.code] !== undefined).map(item => item.weight)) || 100
  //   return _.sum(factors.map(item => {
  //     let score = valueMap[item.code]
  //     if (score) {
  //       score = item.scoringOrder === 'desc' ? (1 - score) : score
  //     } else {
  //       score = 0
  //     }
  //     return score * (item.weight || 0) / weightSum
  //   }))
  // })
  const factorResultMap = _.mapValues(_.groupBy(results, 'factor_code'), values => values[0])
  // const scores =  _.map(scoreSummary, (score, factorTier) => { return score * factorTierWeight[factorTier] })
  // const totalScore = _.sum(scores) * 100
  const totalScore = factorScoreData.totalFactorScore
  const reportDate = factorScoreData.factorEvalDate
    ? moment(factorScoreData.factorEvalDate).format('YYYY-MM-DD')
    : '-'
  let totalScoreDiv = totalScore
    ? <Space><span>{_.round(totalScore, 2)}</span></Space>
    : '-'
  totalScoreDiv = (loadingFactorScore ? <Spin size="small" /> : totalScoreDiv)
  const chartQuotas = factorData.map(item => {
    return {
      name: item.factorTier,
      value: factorTierKeyMap[item.factorTier],
    }
  })
  const handleSchemaChange = schemeId => {
    const factorSchema = factorSchemaList.find(item => item._id === schemeId) || {}
    const { factorData } = factorSchema
    setCurrentSchema(factorSchema)
    setFactorData(factorData)
    setCurrentFactorTier(deafultFactorTier)
    doGetFactorResults(factorSchema._id)
    doGetFactorScore(factorSchema._id)
  }
  const handleFactorTierChange = factorTier => {
    setCurrentFactorTier(factorTier)
  }
  let data = []
  if (isFund) {
    const netValue = currentFund.net_value || currentFund.latestScale
    data = [
      {
        value: currentFund.name,
        name: '',
      },
      {
        name: '基金代码',
        value: currentFund._qutkeId,
      },
      {
        name: '成立日期',
        value: currentFund.startDate ? moment(currentFund.startDate).format('YYYY-MM-DD') : '-',
      },
      {
        name: '到期日期',
        value: !currentFund.isRunning && currentFund.endDate ? moment(currentFund.endDate).format('YYYY-MM-DD') : '-',
      },
      {
        name: '评价日期',
        value: (loadingFactorScore ? <Spin size="small" /> : reportDate),
      },
      {
        name: '基金规模',
        value: !netValue ? '-' : _.round(netValue / *********, 2) + '亿',
      },
      {
        name: '资产类别',
        value: assetClassMap[currentFund.asset_type],
      },
      {
        name: '风格类别',
        value: styleTypeMap[currentFund.style_type],
      },
      {
        name: '基金经理',
        value: currentFund.manager_name,
      },
      {
        name: '基金公司',
        value: currentFund.company,
      },
      {
        name: <Space><span>综合得分</span><HisTotalScoreModal fundId={currentFund._id} schemaId={currentSchem._id}/></Space>,
        value: totalScoreDiv,
      },
      // {
      //   name: '标签属性',
      //   value: !currentFund.labelInfos ? '' : currentFund.labelInfos.map(item => <Tag>{item}</Tag>),
      // },
    ]
  } else {
    data = [
      {
        value: currentFund.name,
        name: '',
      },
      {
        name: '评价日期',
        value: (loadingFactorScore ? <Spin size="small" /> : reportDate),
      },
      {
        name: '代表产品',
        value: currentFund.ref_fund_name,
      },
      {
        name: '代表产品代码',
        value: currentFund.ref_fund_id,
      },
      {
        name: '资产类别',
        value: assetClassMap[currentFund.asset_type],
      },
      {
        name: '风格类别',
        value: styleTypeMap[currentFund.style_type],
      },
      {
        name: '基金公司',
        value: currentFund.company,
      },
      {
        name: '管理规模',
        value: _.round(currentFund.asset_scale / *********, 2) + '亿',
      },
      {
        name: '管理产品数',
        value: currentFund.fund_num,
      },
      {
        name: <Space><span>综合得分</span><HisTotalScoreModal isManager fundId={currentFund._id} schemaId={currentSchem._id}/></Space>,
        value: totalScoreDiv,
      },
    ]
  }
  const columns = [
    {
      dataIndex: 'name',
    },
    {
      dataIndex: 'value',
      align: 'right',
    },
  ]
  const refFundList = (currentFund.refFundList || [])
  const getClickItem = (text, fundId) => {
    return <div onClick={() => {
      window.open(`/fund/${fundId}/factor_evaluation`)
    }}>{text}</div>
  }
  let refFundItems = refFundList.map((item, index) => {
    let text = item.startDate
    if (index === refFundList.length - 1) {
      text = item.endToNow ? `${item.startDate} ~ 至今` : `${item.startDate} ~ ${item.endDate}`
    }
    return {
      title: getClickItem(item.name, item.fundId),
      description: getClickItem(text, item.fundId),
    }
  })
  if (refFundList.length === 1) {
    const fundItem = refFundList[0]
    refFundItems = [{
      title: getClickItem(fundItem.name, fundItem.fundId),
      description: getClickItem(fundItem.startDate, fundItem.fundId),
    }, {
      title: getClickItem(fundItem.name, fundItem.fundId),
      description: getClickItem(fundItem.endToNow ? '至今' : fundItem.endDate, fundItem.fundId),
    }]
  }
  const onRefFundChange = value => console.log(value)
  const exportColumns = [{
    title: '维度类别',
    dataIndex: 'factorTier',
  }, {
    title: '因子名称',
    dataIndex: 'name',
  }, {
    title: '权重',
    dataIndex: 'weight',
  }, {
    title: '方向',
    dataIndex: 'scoringOrder',
  }, {
    title: '数据日期',
    dataIndex: 'factorDate',
  }, {
    title: '因子值',
    dataIndex: 'value',
  }, {
    title: '历史平均值',
    dataIndex: 'avg',
  }, {
    title: '历史最低值',
    dataIndex: 'min',
  }, {
    title: '历史最高值',
    dataIndex: 'max',
  }, {
    title: '近3年数据点',
    dataIndex: 'valueCount',
  }]
  const getExportData = () => {
    const results = factorData.reduce((out, item) => {
      const factors = item.factors.map(factor => {
        const factorValue = factorResultMap[factor.code] || {}
        const ret = {
          factorTier: item.factorTier,
          name: factor.name,
          scoringOrder: factor.scoringOrder === 'desc' ? '逆' : '正',
          weight: `${_.round(factor.weight, 2)}%`,
        }
        const getValue = value => value ? value * 100 : value
        const min = getValue(factorValue.hpct_min_3y)
        const max = getValue(factorValue.hpct_max_3y)
        const value = getValue(factorValue.vpct)
        const avg = getValue(factorValue.hpct_mean_3y)
        if (factorValue) {
          if (factorValue.factor_date) {
            ret.factorDate = moment(factorValue.factor_date).format('YYYY-MM-DD')
          }
          ret.min = min
          ret.max = max
          ret.value = value
          ret.avg = avg
          ret.valueCount = factorValue.total_num || 0
        }
        return ret
      })
      return out.concat(factors)
    }, [])
    return results
  }

  return (
    <Row gutter={16}>
      <Col lg={6} md={24}>
        <Table
          showHeader={false}
          pagination={false}
          columns={columns}
          dataSource={data}
          size="small"
          style={{ marginBottom: 30 }}
        />
        <Spin spinning={loadingFactorScore}>
          <ScoringPolarChart currentFund={{ ...factorScoreData, name: '因子得分' }} chartQuotas={chartQuotas} height={230} isPct />
        </Spin>
      </Col>
      <Col lg={18} md={24}>
        {refFundItems.length !== 0 &&
        <div>
          <Title level={4}>历史代表产品</Title>
          <Steps
            progressDot
            style={{
              padding: '10px 0',
            }}
            size="small"
            current={refFundItems.length}
            items={refFundItems}
            onChange={onRefFundChange}
          />
        </div>}
        <Tabs
          type="card"
          activeKey={currentFactorTier.factorTier}
          onChange={handleFactorTierChange}
          tabBarExtraContent={
            <Space>
              <Select
                showSearch
                placeholder="点击切换方案"
                style={{
                  // marginTop: '5px',
                  width: '160px',
                }}
                value={currentSchem._id}
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                onChange={handleSchemaChange}
              >
                {factorSchemaList.map(item => {
                  return (
                    <Option value={item._id}>
                      {item.name}
                    </Option>
                  )
                })}
              </Select>
              <ExportData title="下载因子明细数据" columns={exportColumns} getData={getExportData} filename={`${currentFund.name}-${currentSchem.name}因子明细-${moment().format('YYYYMMDD')}`}/>
            </Space>
          }
        >
          {[deafultFactorTier, ...factorData].map(item =>  {
            if (item.value === 'historyScore') {
              return (
                <TabPane tab={item.factorTier} key={item.factorTier}>
                  <HisFactorScoreAsset isManager={!isFund} fundId={currentFund._id} schemaId={currentSchem._id}/>
                </TabPane>
              )
            }
            return (
              <TabPane tab={`${item.factorTier} (${_.round(item.weight, 2)}%)`} key={item.factorTier}>
                <Spin spinning={loading}>
                  <div style={{ textAlign: 'center', marginTop: 15 }}>
                    <Space size="middle">
                      <span style={{ marginLeft: 15 }}>
                        <span className={styles.circel} style={{ background: '#e75d02' }}/>
                        <span className={styles.circelLine} style={{ background: '#e75d02' }}></span>
                        <span>因子值</span>
                      </span>
                      <span style={{ marginLeft: 15 }}>
                        <span className={styles.circel} style={{ background: '#fff' }}/>
                        <span className={styles.circelLine}></span>
                        <span>历史平均</span>
                      </span>
                      <span><span className={styles.circel} style={{ background: '#81431d', width: 30 }}/>3年历史范围</span>
                    </Space>
                  </div>
                  <Row>
                    {item.factors.map(factor => {
                      const factorValue = factorResultMap[factor.code] || {}
                      return (
                        <Col md={4} sm={12} xs={12}>
                          <FactorBarChart show3YDataPoint height={250} factorData={[{
                            name: factor.name,
                            ...factorValue,
                          }]}/>
                          <div style={{ textAlign: 'center', paddingLeft: 30 }}>{(factor.name || '').replace(/^(基金经理|基金公司)/, '').replace(/^(基金)/, '')}</div>
                          <div style={{ textAlign: 'center', paddingLeft: 30 }}>
                            <Space direction="horizontal" size="small">
                              <span>权重: {_.round(factor.weight, 2)}%</span>
                              <Avatar size={20} style={{ backgroundColor: '#3e3b3a', color: '#e85d02' }}>{factor.scoringOrder === 'desc' ? '逆' : '正'}</Avatar>
                            </Space>
                          </div>
                        </Col>
                      )
                    })}
                  </Row>
                </Spin>
              </TabPane>
            )
          })}
        </Tabs>
        <Divider orientation="left" plain>
          因子得分说明
        </Divider>
        {(currentFactorTier.value === 'historyScore' || currentFactorTier === '历史得分')
        ? <div>
          <p>1. 得分越高代表排名越靠前</p>
        </div>
        : <div>
          <p>1. 正：表示因子值越大，维度得分越高</p>
          <p>2. 逆：表示因子值越小，维度得分越高</p>
          <p>3. 因子值是按实际公告日计算的因子原始分位数值，不叠加正逆方向，维度得分时才叠加正逆计算</p>
        </div>
        }
      </Col>
    </Row>
  )
}

const FactorEvaluationWrapper: React.FC<ComponentProps> = ({ currentFund, currentManager, currentUser, location: { pathname } }) => {
  let pageMenusIds = currentUser && currentUser.menus.map(item => item.menuId)
  if (!(pageMenusIds.includes('d')) && !pathname.includes('/manager/')) {
    const nextPath = pathname.replace('factor_evaluation', 'invest_performance')
    router.push(nextPath)
    return null
  }
  if (!(pageMenusIds.includes('e')) && pathname.includes('/manager/')) {
    const nextPath = pathname.replace('factor_evaluation', 'persona')
    router.push(nextPath)
    return null
  }
  const { loading, data: factorSchemaList = { list: [] } } = useRequest(() => {
    return queryFactorSchemas({ pageSize: 100 })
  })
  const isFund = /fund|activefund|portfolios/.test(pathname)
  const currentData = isFund ? currentFund : currentManager
  return (
    <Spin spinning={loading}>
      <div style={{ minHeight: 300 }}>
        {!!factorSchemaList.list.length && <FactorEvaluation isFund={isFund} currentFund={currentData} factorSchemaList={factorSchemaList.list} />}
      </div>
    </Spin>
  )
}

export default connect(
  ({
    fund,
    loading,
    user,
  }: {
    fund: any;
    manager: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
    user: any;
  }) => ({
    currentFund: fund.currentFund,
    currentManager: fund.currentManager,
    loading: loading.models.fund,
    currentUser: user.currentUser,
  }),
)(FactorEvaluationWrapper)
