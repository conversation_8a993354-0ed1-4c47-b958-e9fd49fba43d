import React from 'react'
import {
  Affix,
  Tabs,
} from 'antd'
import router from 'umi/router'

const { TabPane } = Tabs

const PositionDetection = ({ location, tabBarExtraContent }) => {
  const { pathname } = location
  const tabs = [{
    name: '业绩概况',
    tab: 'detection_performance',
  }, {
    name: '资产配置',
    tab: 'detection_asset_allocation',
  }, {
    name: '行业配置',
    tab: 'detection_ind_allocation',
  }, {
    name: '行业偏好',
    tab: 'detection_ind_prefer',
  }, {
    name: '持仓明细',
    tab: 'detection_position_detail',
  }, {
    name: '权益归因',
    tab: 'detection_stock_attr',
  }]
  const splits = pathname.split('/')
  let activeTab = splits.pop()
  const pathPrefix = splits.join('/')
  const handleTabChange = currentTab => {
    router.push(`${pathPrefix}/${currentTab}`)
  }
  return (
    <Affix offsetTop={44}>
      <Tabs
        animated={false}
        activeKey={activeTab}
        onChange={handleTabChange}
        tabBarExtraContent={tabBarExtraContent}
        style={{
          marginBottom: 15,
          background: 'rgb(24, 31, 41)',
        }}
      >
        {tabs.map(item => <TabPane tab={item.name} key={item.tab}/>)}
      </Tabs>
    </Affix>
  )
}

export default PositionDetection
