import React from 'react'
import {
  Spin,
  Card,
} from 'antd'
import { useRequest } from '@umijs/hooks'
import _ from 'lodash'
import { connect } from 'dva'
import AreaPieChart from '@/components/AreaPieChart'
import NavTabs from './NavTabs'
import { getDetectAssetData } from '@/services/fund'

const AssetAllocationWrapper = ({ location, currentFund }) => {
  const { loading, data = [] } = useRequest(() => {
    return getDetectAssetData(currentFund._id)
  })
  return (
    <div>
      <NavTabs
        location={location}
      />
      <Spin spinning={loading}>
        <Card title="">
          <AreaPieChart
            disablePie
            showNavigator
            hideBalance
            rows={data}
            nameKey="ASSET_TYPE"
            pctValueKey="RATIO"
            height={420}
          />
        </Card>
      </Spin>
    </div>
  )
}

export default connect(
  ({
    fund,
  }: {
    fund: any;
  }) => ({
    currentFund: fund.currentFund,
  }),
)(AssetAllocationWrapper)
