import React, { useState } from 'react'
import {
  Spin,
} from 'antd'
import _ from 'lodash'
import { connect } from 'dva'
import DailyIndustryPrefer from '../components/DailyIndustryPrefer'
import NavTabs from './NavTabs'
import SearchSelect from '@/components/SearchSelect'
import moment from 'moment'
import { useRequest } from '@umijs/hooks'
import { getDetectNav } from '@/services/fund'

const IndustryPrefer = ({ location, currentFund, decNav }) => {
  const latestIncomeDate = moment(currentFund.latestIncomeDate).format('YYYY-MM-DD')
  const halfDates = [...currentFund.dates]
    .reverse()
    .filter(date => {
      return /(06-30|12-31)$/.test(date) && date <= latestIncomeDate
    })
  const dates = (decNav || []).map(item => {
    return moment(item.date).format('YYYY-MM-DD')
  }).filter(item => {
    return item >= '2010-01-01'
  }).reverse()
  const dateList = dates.map(item => {
    return {
      title: item,
      dataIndex: item,
    }
  })
  const [bizDate, setBizDate] = useState(dates[0])
  const fundId = currentFund._id
  return (
    <div>
      <NavTabs
        location={location}
        tabBarExtraContent={
          <SearchSelect
            value={bizDate}
            options={dateList}
            onChange={setBizDate}
            width="150px"
          />
        }
      />
      <DailyIndustryPrefer isDetect bizDate={bizDate} fundId={fundId} halfDates={halfDates}/>
    </div>
  )
}

const IndustryPreferWrapper = ({ location, currentFund }) => {
  const { data: navData = [], loading: loadingNav } = useRequest(() => {
    return getDetectNav(currentFund._id)
  })
  if (loadingNav) {
    return (
      <Spin spinning>
        <div style={{ height: 400 }}></div>
      </Spin>
    )
  }
  return <IndustryPrefer location={location} currentFund={currentFund} decNav={navData}/>
}

export default connect(
  ({
    fund,
  }: {
    fund: any;
  }) => ({
    currentFund: fund.currentFund,
  }),
)(IndustryPreferWrapper)
