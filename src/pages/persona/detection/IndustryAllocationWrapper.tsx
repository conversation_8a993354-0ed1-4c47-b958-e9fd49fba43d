import React from 'react'
import {
  Spin,
} from 'antd'
import { useRequest } from '@umijs/hooks'
import _ from 'lodash'
import { connect } from 'dva'
import NavTabs from './NavTabs'
import { getDetectIndAllocateData } from '@/services/fund'
import IndustryHeatMap from '../components/IndustryHeatMap'

const IndustryAllocationWrapper = ({ location, currentFund }) => {
  const { loading, data = [] } = useRequest(() => {
    return getDetectIndAllocateData(currentFund._id)
  })
  return (
    <div>
      <NavTabs
        location={location}
      />
      <Spin spinning={loading}>
        {data.length !== 0 &&
        <IndustryHeatMap isDetect dataType="industry" product={currentFund} data={data} />}
        {data.length !== 0 &&
        <IndustryHeatMap isDetect dataType="industryBoard" product={currentFund} data={data} />}
      </Spin>
    </div>
  )
}

export default connect(
  ({
    fund,
  }: {
    fund: any;
  }) => ({
    currentFund: fund.currentFund,
  }),
)(IndustryAllocationWrapper)
