import React, { useEffect, useMemo, useState } from 'react'
import {
  Space,
  DatePicker,
  Select,
  Spin,
} from 'antd'
import { useRequest } from '@umijs/hooks'
import moment from 'moment'
import _ from 'lodash'
import { connect } from 'dva'
import NavTabs from './NavTabs'
import * as calculator from '@/utils/calculator'
import getIntersectionByDate from '@/utils/getIntersectionByDate'
import changeFrequency from '@/utils/changeFrequency'
import fillEmptyValues from '@/utils/fillEmptyValues'
import { calculateFundQuotasByTimeRange } from '@/utils/calculateFundQuota'
import { deleteFund, getDetectNav } from '@/services/fund'
import SearchSelect from '@/components/SearchSelect'
import RevenuePerf from '../components/RevenuePerf'

const RangePicker = DatePicker.RangePicker
const Option = Select.Option

const getCalcStartDate = (navList, date) => {
  const index = _.findLastIndex(navList, item => item.date < date)
  if (index === -1) {
    return date
  } else {
    return navList[index].date
  }
}

const buildIntersectionData = (currentFund, currentBenchmark, startDate, endDate) => {
  const realStartDate = getCalcStartDate(currentFund.nets || [], startDate)
  const filteredNets = (currentFund.nets || []).filter(item => {
    if (!startDate || !endDate) {
      return true
    }
    return item.date >= realStartDate && item.date <= endDate
  })
  let fundQuotas = currentFund
  let benchmarkQuotas = currentBenchmark
  if (startDate && endDate) {
    fundQuotas = calculateFundQuotasByTimeRange({
      name: currentFund.name,
      nets: currentFund.nets,
    }, startDate, endDate)
    if (currentBenchmark) {
      benchmarkQuotas = calculateFundQuotasByTimeRange({
        name: currentBenchmark.name,
        nets: currentBenchmark.nets,
      }, startDate, endDate)
    }
  }
  const filledNets = fillEmptyValues(filteredNets, endDate || currentFund.navEndDate, null, null)
  const dataDisplay = getIntersectionByDate(filledNets, currentBenchmark ? currentBenchmark.nets : [])
  const fundNavDisplay = dataDisplay.map(item => ({ date: item[0], value: item[1] }))
  const benchmarkNavDisplay = dataDisplay.map(item => ({ date: item[0], value: item[2] }))
  const benchmarkReturnDisplay = calculator.calculateReturns(benchmarkNavDisplay)
  const data = getIntersectionByDate(filteredNets, currentBenchmark ? currentBenchmark.nets : [])
  const fundNav = data.map(item => ({ date: item[0], value: item[1] }))
  const benchmarkNav = data.map(item => ({ date: item[0], value: item[2] }))
  const fundReturns = calculator.calculateReturns(fundNav)
  const benchmarkReturns = calculator.calculateReturns(benchmarkNav)
  const fundReturnMap = fundReturns.reduce((out, item) => {
    out[item.date] = item.value
    return out
  }, {})
  const initalData = dataDisplay[0]
  let excessReturns = []
  if (initalData) {
    excessReturns = dataDisplay.filter(item => item[1] && item[2]).map(item => {
      const fRet = item[1] / initalData[1] - 1
      const bRet = item[2] / initalData[2] - 1
      return {
        date: item[0],
        value: (fRet - bRet) * 100,
      }
    })
  }
  const fundMonthlyNav = changeFrequency(fundNav, 'month').slice(0, -1)
  const fundMonthlyReturns = calculator.calculateReturns(fundMonthlyNav)
  const benchmarkMonthlyNav = changeFrequency(benchmarkNav, 'month').slice(0, -1)
  const benchmarkMonthlyReturns = calculator.calculateReturns(benchmarkMonthlyNav)
  let rollingPeriod = 12
  if (fundMonthlyReturns.length < 12) {
    rollingPeriod = 3
  } else if (fundMonthlyReturns.length < 18) {
    rollingPeriod = 6
  }
  return {
    fundNav,
    benchmarkNav,
    fundReturns,
    benchmarkReturns,
    excessReturns,
    fundMonthlyNav,
    fundMonthlyReturns,
    benchmarkMonthlyNav,
    benchmarkMonthlyReturns,
    fundNavDisplay,
    benchmarkNavDisplay,
    fundQuotas,
    benchmarkQuotas,
  }
}

const getDefaultRollingPeriod = (currentFund, startDate, endDate) => {
  const fundNav = (currentFund.nets || []).filter(item => {
    item.date >= startDate && item.date <= endDate
  })
  const fundMonthlyNav = changeFrequency(fundNav, 'month').slice(0, -1)
  let rollingPeriod = 12
  if (fundMonthlyNav.length < 12) {
    rollingPeriod = 3
  } else if (fundMonthlyNav.length < 18) {
    rollingPeriod = 6
  }
  return rollingPeriod
}

const RevenuePerfComp = ({ dispatch, location, currentFund, currentBenchmark, benchmarkList, loading }) => {
  const onSelectBenchmark = (id) => {
    dispatch({
      type: 'fund/fetchBenchmarkDet',
      payload: {
        id,
        params: {
          navStartDate: currentFund && currentFund.navStartDate,
        },
      },
    })
  }
  // useEffect(() => {
  //   onSelectBenchmark(currentFund.benchmarkId || '000300.SH')
  // }, [])
  let initialDateRange = {}
  try {
    const cacheKey = `investperfDates:${currentFund._id}`
    const cachedDates = localStorage.getItem(cacheKey)
    if (cachedDates) {
      initialDateRange = JSON.parse(cachedDates)
    } else {
      initialDateRange = {
        startDate: currentFund && +moment(currentFund.navEndDate).subtract(3, 'year').startOf('date'),
        endDate: currentFund && currentFund.navEndDate,
      }
    }
  } catch (error) {
  }

  const initialStartDate = moment(new Date(initialDateRange.startDate || currentFund.navStartDate))
  const initialEndDate = moment(new Date(initialDateRange.endDate || currentFund.navEndDate || new Date()))
  const [dateRange, setDateRange] = useState({
    startDate: +initialStartDate.startOf('date'),
    endDate: +initialEndDate.startOf('date'),
  })
  const state = useMemo(() => {
    return buildIntersectionData(currentFund, currentBenchmark, dateRange.startDate, dateRange.endDate)
  }, [currentBenchmark && currentBenchmark._id, dateRange.startDate, dateRange.endDate])
  const defaultRollingPeriod = getDefaultRollingPeriod(currentFund, dateRange.startDate, dateRange.endDate)
  const [rollingPeriod, setRollingPeriod] = useState(defaultRollingPeriod)
  const rollingGap = 1
  const onDateRangeChange = (dates) => {
    if (!dates || !dates.length) {
      return
    }
    const startDate = +dates[0].startOf('date')
    const endDate = +dates[1].startOf('date')
    setDateRange({
      startDate, endDate,
    })
  }
  const defaultBenchmark = {
    name: currentFund.benchmark,
    _id: currentFund.benchmarkId,
  }
  const benchmarkListNew = benchmarkList.map(item => {
    return {
      ...item
    }
  })
  if (defaultBenchmark._id) {
    if (!benchmarkListNew.some(item => item._id === defaultBenchmark._id || item._qutkeId === defaultBenchmark._id)) {
      benchmarkListNew.unshift(defaultBenchmark)
    }
  }
  const benchmarkIds = benchmarkListNew.map(item => item._id)
  const currentBenchmarkId = currentBenchmark && currentBenchmark._id
  const currentBenchmarkName = currentBenchmark && currentBenchmark.name
  return (
    <div>
      <NavTabs
        location={location}
        tabBarExtraContent={
          <Space>
            <span style={{ marginRight: 15 }}>
              日期范围：<RangePicker
                style={{ width: 200 }}
                size="small"
                ranges={{
                  本周以来: [initialEndDate.clone().startOf('week'), initialEndDate],
                  本月以来: [initialEndDate.clone().startOf('month'), initialEndDate],
                  本年以来: [initialEndDate.clone().startOf('year'), initialEndDate],
                }}
                onChange={onDateRangeChange}
                disabledDate={(current) => {
                  const currentTs = +current.startOf('date')
                  return currentTs < currentFund.navStartDate || currentTs > currentFund.navEndDate
                }}
                defaultValue={[
                  initialStartDate, initialEndDate,
                ]}
              />
            </span>
            <span style={{ marginRight: 15 }}>
              滚动窗口：<Select
                value={rollingPeriod}
                onChange={setRollingPeriod}
                style={{ width: 80 }}
                size="small"
              >
                <Option value={3}>3个月</Option>
                <Option value={6}>6个月</Option>
                <Option value={12}>12个月</Option>
                <Option value={24}>2年</Option>
                <Option value={36}>3年</Option>
              </Select>
            </span>
            <SearchSelect
              placeholder="请选择基准"
              value={benchmarkIds.includes(currentBenchmarkId) ? currentBenchmarkId : currentBenchmarkName}
              options={benchmarkListNew.map(item => {
                return {
                  title: item.name,
                  dataIndex: item._id,
                }
              })}
              onChange={onSelectBenchmark}
              width="150px"
            />
          </Space>
        }
      />
      <Spin spinning={loading}>
        <RevenuePerf
          isManager={false}
          rollingPeriod={rollingPeriod}
          rollingGap={rollingGap}
          startDate={dateRange.startDate}
          endDate={dateRange.endDate}
          currentFund={currentFund}
          currentBenchmark={currentBenchmark}
          {...state}
        />
      </Spin>
    </div>
  )
}

const RevenuePerfWrapper = ({
  dispatch, location, currentFund, currentBenchmark, benchmarkList, loading
}) => {
  const { data: navData = [], loading: loadingNav } = useRequest(() => {
    return getDetectNav(currentFund._id)
  })
  if (loadingNav) {
    return (
      <Spin spinning>
        <div style={{ height: 400 }}></div>
      </Spin>
    )
  }
  const detectFund = _.pick(currentFund, ['_id', '_qutkeId'])
  detectFund.nets = navData
  detectFund.name = `${currentFund.name}-持仓探测`
  detectFund.benchmark = currentFund.name
  detectFund.benchmarkId = currentFund._id
  if (navData.length) {
    detectFund.navStartDate = navData[0].date
    detectFund.navEndDate = navData[navData.length - 1].date
  }
  return <RevenuePerfComp
    {...{
      dispatch, location,
      currentBenchmark: currentBenchmark || currentFund,
      benchmarkList, loading,
      currentFund: detectFund,
    }}
  />
}

export default connect(
  ({
    loading,
    fund,
    manager,
  }: {
    manager: any;
    fund: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    benchmarkList: manager.benchmarkList,
    currentBenchmark: fund.currentBenchmarkDet,
    loading: loading.models.fund,
  }),
)(RevenuePerfWrapper)
