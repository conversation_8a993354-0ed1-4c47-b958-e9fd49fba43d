import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Select, Card, Spin,
} from 'antd'
import { getDailyStockIndicator, getDailyConvtBondStockIndicator } from '@/services/fund'
import Chart from '@/components/Chart/Chart'
import _ from 'lodash'

const { Option } = Select

export default ({
  dates,
  fundId,
  isConvtBond,
}: {
  dates: any,
  fundId: string,
  isConvtBond?: boolean,
}) => {
  const [currentDate, setCurrentDate] = useState(dates[0])
  const { data, loading } = useRequest(() => {
    return isConvtBond ? getDailyConvtBondStockIndicator(fundId, { date: currentDate })
      : getDailyStockIndicator(fundId, { date: currentDate })
  }, {
    refreshDeps: [currentDate],
  })
  const mktValGrouper = row => {
    const mktVal = row.MKT_VAL / 10000
    if (mktVal < 50) {
      return 0
    } else if (mktVal < 100) {
      return 50
    } else if (mktVal < 200) {
      return 100
    } else if (mktVal < 500) {
      return 200
    } else if (mktVal < 1000) {
      return 500
    } else {
      return 1000
    }
  }
  const nameMap = {
    0: '50以下',
    50: '50-100',
    100: '100-200',
    200: '200-500',
    500: '500-1000',
    1000: '1000以上',
  }
  const sumBalance = _.sumBy(data, 'BALANCE')
  const mktValData = _.map(_.groupBy(data, mktValGrouper), (rows, key) => {
    return {
      name: nameMap[key],
      value: _.sumBy(rows, 'BALANCE') / sumBalance,
      mktVal: Number(key),
    }
  }).sort((fst, snd) => fst.mktVal - snd.mktVal)
  const categories = mktValData.map(item => item.name)
  const config = {
    chart: {
      type: 'column',
    },
    xAxis: {
      categories: categories,
    },
    yAxis: {
      labels: {
        format: '{value}%',
      },
      max: 100,
    },
    tooltip: {
      shared: true,
      pointFormat: '占比: <b>{point.y:.2f}%</b><br/>',
    },
    legend: {
      enabled: false,
    },
    series: [{
      data: mktValData.map(item => item.value * 100),
    }],
  }
  return (
    <Card
      title={isConvtBond ? '可转债对应正股市值分布(亿)' : '股票市值分布(亿)'}
      bordered={false}
      extra={
        <>
          <Select
            showSearch
            value={currentDate}
            // className={styles.rightAction}
            style={{ width: 150, marginLeft: 15 }}
            placeholder="选择日期"
            optionFilterProp="children"
            onChange={setCurrentDate}
            filterOption={(input, option) =>
              option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {dates.map(item => (
              <Option value={item}>{item}</Option>
            ))}
          </Select>
        </>
      }
    >
      <Spin spinning={loading}>
        <Chart options={config} />
      </Spin>
    </Card>
  )
}
