import React from 'react'
import _ from 'lodash'
import {
  Card, Row, Col, Tooltip, Space, Button,
} from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import Echart from '@/components/Chart/Echarts'
import { exportNArrayDataAsExcel } from '@/utils/exportAsExcel'
import ExportData from '@/components/ExportData'

const getChartOptions = (data, industryData, isAvg) => {
  const currentData = industryData
    .sort((fst, snd) => snd.BALANCE_RATIO - fst.BALANCE_RATIO)
    .slice(0, 5)
    .map(item => item.BALANCE_RATIO * 100)
  const option = {
    grid: {
      left: '8%',
      right: '8%',
    },
    xAxis: {
      type: 'category',
      data: data.axisData,
      boundaryGap: true,
      nameGap: 30,
      splitArea: {
        show: false,
      },
      axisLabel: {
        formatter: '{value}',
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [{
      type: 'value',
      // name: '收益率',
      // splitArea: {
      //   show: true
      // },
      // min: 'dataMin',
    }],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const param = params[0]
        return [
          param.name + ': ',
          '当前值: ' + _.round(params[1].data, 2) + '%',
          (isAvg ? '95%分位数: ' : '最大值: ') + _.round(param.data[5], 2) + '%',
          '3/4分位数: ' + _.round(param.data[4], 2) + '%',
          '中位数: ' + _.round(param.data[3], 2) + '%',
          '1/4分位数: ' + _.round(param.data[2], 2) + '%',
          (isAvg ? '5%分位数: ' : '最小值: ') + _.round(param.data[1], 2) + '%',
        ].join('<br/>')
      },
    },
    series: [
      {
        name: 'boxplot',
        type: 'boxplot',
        data: data.boxData,
        itemStyle: {
          color: '#7d6440',
          borderColor: '#E49831',
        },
      },
      // {
      //   name: 'outlier',
      //   type: 'scatter',
      //   data: data.outliers,
      // },
      {
        name: '当前值',
        type: 'scatter',
        data: currentData,
        symbolSize: 15,
        smooth: false,
        showSymbol: false,
        lineStyle: {
          width: 1,
          color: '#0EBF9C',
        },
      },
    ],
  }

  return option
}

export default ({
  data,
  avgData,
  industryData,
}: {
  data: any,
  avgData: any,
  industryData: any,
}) => {
  const options = getChartOptions(data, industryData)
  const avgOptions = getChartOptions(avgData, industryData, true)
  const handleExportData = (title, rawData) => () => {
    const currentData = industryData.reduce((out, item) => {
      out[item.INDUSTRY] = item.BALANCE_RATIO
      return out
    }, {})
    const exportData = rawData.axisData.map((industry, index) => {
      const boxData = rawData.boxData[index]
      return [
        industry,
        ...boxData,
        _.round(currentData[industry] * 100, 2),
      ]
    })
    exportData.unshift([
      '行业', '最小值', '1/4分位数', '中位数', '3/4分位数', '最大值', '当前值',
    ])
    exportNArrayDataAsExcel(title, exportData)
  }
  return (
    <Row gutter={8}>
      <Col xs={24} md={12} xl={12} xxl={12}>
        <Card
          title={
            <>
              <span>前五大行业历史</span>
              <Tooltip title="最新一期持仓行业前五大" placement="right">
                <span style={{ marginLeft: '5px' }}>
                  <QuestionCircleOutlined />
                </span>
              </Tooltip>
            </>
          }
          extra={
            <Space>
              <ExportData onClick={handleExportData('前五大行业历史', data)}/>
            </Space>
          }
        >
          <Echart style={{ height: '350px' }} options={options} />
        </Card>
      </Col>
      <Col xs={24} md={12} xl={12} xxl={12}>
        <Card
          title="前五大行业同类分布"
          extra={
            <Space>
              <ExportData onClick={handleExportData('前五大行业同类分布', avgData)}/>
            </Space>
          }
        >
          <Echart style={{ height: '350px' }} options={avgOptions} />
        </Card>
      </Col>
    </Row>
  )
}
