import React from 'react'
import Echart from '@/components/Chart/Echarts'
import _ from 'lodash'
import normalizeFixedRange from '@/utils/normalize-fixed-range'

const getChartOptions = (data) => {
  const allData = data.fundScoreData.concat(data.benchmarkScoreData)
  const styleValues = allData.map(item => item.styleScore)
  const sizeValues = allData.map(item => item.sizeScore)
  const maxStyle = _.max(styleValues)
  const maxSize = _.max(sizeValues)
  const minStyle = _.min(styleValues)
  const minSize = _.min(sizeValues)
  
  const mapper = item => [
    normalizeFixedRange(item.styleScore, minStyle, maxStyle, 0, 1) * 100,
    normalizeFixedRange(item.sizeScore, minSize, maxSize, 0, 1) * 100,
    item.date
  ]
  const data1 = data.fundScoreData.map(mapper)
  const data2 = data.benchmarkScoreData.map(mapper)
  const markPoint1 = [{
    value: '开始',
    coord: data1[0],
  }, {
    value: '结束',
    coord: data1[data1.length - 1],
  }]
  const markPoint2 = [{
    value: '开始',
    coord: data2[0],
  }, {
    value: '结束',
    coord: data2[data2.length - 1],
  }]

  const option = {
    legend: {
      show: true,
    },
    grid: {
      left: '8%',
      bottom: '8%',
    },
    tooltip: {
      // trigger: 'item',
      formatter: ({ data }) => {
        return data[2]
      },
    },
    xAxis: {
      min: 0, max: 120,
      axisTick: {
        show: false,
      },
      axisLabel: {
        formatter: value => {
          if (value === 0) {
            return '成长'
          }
          if (value === 120) {
            return '价值'
          }
          // if (value === 60) {
          //   return '均衡'
          // }
        },
      },
    },
    yAxis: {
      min: 0, max: 120,
      axisTick: {
        show: false,
      },
      axisLabel: {
        formatter: value => {
          if (value === 0) {
            return '小盘'
          }
          if (value === 120) {
            return '大盘'
          }
          // if (value === 60) {
          //   return '中盘'
          // }
        },
      },
    },
    series: [{
      name: '组合',
      showSymbol: true,
      symbolSize: 5,
      data: data1,
      type: 'line',
      lineStyle: {
        width: 5,
      },
      smooth: false,
      markPoint: {
        data: markPoint1,
        tooltip: {
          show: false,
        },
      },
    }, {
      name: '沪深300',
      showSymbol: true,
      symbolSize: 5,
      data: data2,
      type: 'line',
      lineStyle: {
        width: 5,
      },
      smooth: false,
      markPoint: {
        data: markPoint2,
        tooltip: {
          show: false,
        },
      },
    }],
  }
  return option
}

export default ({
  sizeStyleHistory,
}: {
  sizeStyleHistory: any,
}) => {
  const options = getChartOptions(sizeStyleHistory)
  return (
    <Echart style={{ height: '300px' }} options={options} />
  )
}
