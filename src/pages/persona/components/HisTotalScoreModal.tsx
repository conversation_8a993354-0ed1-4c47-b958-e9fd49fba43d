import React, { useState } from 'react'
import { LineChartOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { Modal, Tooltip, Spin, Space } from 'antd'
import moment from 'moment'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import { queryHisTotalScore, queryManagerHisTotalScore } from '../service'
import Echart from '@/components/Chart/Echarts'

const getChartOptions = (data, chartSeries) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      // axisPointer: {
      //   type: 'shadow',
      // },
    },
    legend: {
      data: chartSeries.map(item => item.title),
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: data.map(item => moment(item.factor_date).format('YYYY-MM-DD')),
      },
    ],
    yAxis: [{
      type: 'value',
      name: '综合得分(%)',
    }],
    series: chartSeries.map(serie => {
      return {
        name: serie.title,
        type: serie.type || 'line',
        yAxisIndex: serie.yAxisIndex,
        data: data.map(item => _.round(item[serie.dataIndex] || 0, 2)),
        smooth: false,
      }
    }),
  }
  return option
}

const HisTotalScoreModal: React.FC = ({ fundId, schemaId, isManager }) => {
  const [open, setOpen] = useState(false)
  const { loading, data = [] } = useRequest(() => {
    return isManager ? queryManagerHisTotalScore(fundId, { schemaId }) : queryHisTotalScore(fundId, { schemaId })
  }, {
    cacheKey: `queryHisTotalScore${fundId}`,
    refreshDeps: [schemaId],
  })
  const chartSeries = [{
    title: '综合得分',
    dataIndex: 'value_quantile',
  }]
  const options = getChartOptions(data, chartSeries)
  const tooltipIcon = <Tooltip
    title="综合得分越高代表排名越靠前"
  >
    <QuestionCircleOutlined />
  </Tooltip>
  return (
    <>
      <Tooltip
        title="点击查看综合得分历史"
      >
        <LineChartOutlined color='#e85d02' twoToneColor="#e85d02" onClick={() => setOpen(true) }/>
      </Tooltip>
      <Modal
        title={
          <Space><span>综合得分历史</span>{tooltipIcon}</Space>
        }
        centered
        visible={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        width={1000}
      >
        <Spin spinning={loading}>
          <Echart style={{ height: '500px' }} options={options} />
        </Spin>
      </Modal>
    </>
  )
}

export default HisTotalScoreModal
