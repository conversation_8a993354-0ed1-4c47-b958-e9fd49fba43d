import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Table,
  Card,
  Select,
} from 'antd'
import { getDailyPositionDetail } from '@/services/fund'
import buildTableColumn from '@/utils/buildTableColumn'
import sortQuotaFn from '@/utils/sortQuotaFn'
import _ from 'lodash'

const { Option } = Select

export default ({
  dates,
  fundId,
}: {
  dates: any,
  fundId: string,
}) => {
  const [currentDate, setCurrentDate] = useState(dates[0])
  const { data, loading } = useRequest(() => {
    return getDailyPositionDetail(fundId, { date: currentDate })
  }, {
    refreshDeps: [currentDate],
  })
  const assetFilters = _.uniq((data || []).map(item => item.ASSET_TYPE)).map(asset => {
    return {
      text: asset,
      value: asset,
    }
  })
  const columns = [{
    title: '资产类型',
    dataIndex: 'ASSET_TYPE',
    filters: assetFilters,
    onFilter: (value, record) => record.ASSET_TYPE === value,
  }, {
    title: '证券名称',
    dataIndex: 'STOCK_NAME',
    sorter: sortQuotaFn({ dataIndex: 'STOCK_NAME', format: 'text' }, 'asc'),
  }, {
    title: '证券代码',
    dataIndex: 'STOCK_CODE',
    sorter: sortQuotaFn({ dataIndex: 'STOCK_CODE', format: 'text' }, 'asc'),
  }, {
    title: '持仓数量',
    dataIndex: 'AMOUNT',
    format: 'commaNumber',
    sorter: sortQuotaFn({ dataIndex: 'AMOUNT', format: 'commaNumber' }, 'asc'),
  }, {
    title: '持仓市值',
    dataIndex: 'BALANCE',
    format: 'commaNumber',
    sorter: sortQuotaFn({ dataIndex: 'BALANCE', format: 'commaNumber' }, 'asc'),
  }].map(buildTableColumn)
  return (
    <div>
      <Card
        title="持仓明细"
        bordered={false}
        extra={
          <>
            <Select
              showSearch
              value={currentDate}
              // className={styles.rightAction}
              style={{ width: 150, marginLeft: 15 }}
              placeholder="选择日期"
              optionFilterProp="children"
              onChange={setCurrentDate}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {dates.map(item => (
                <Option value={item}>{item}</Option>
              ))}
            </Select>
          </>
        }
      >
        <Table
          size="small"
          columns={columns}
          loading={loading}
          dataSource={data}
          pagination={false}
          scroll={{ y: 450 }}
        />
      </Card>
    </div>
  )
}
