import React from 'react'
import { Card } from 'antd'
import Echart from '@/components/Chart/Echarts'
import ExportData from '@/components/ExportData'

const styleList = [
  '成长型',
  '均衡型',
  '价值型',
  '消极配置型',
  '积极配置型',
  '信用型',
  '平衡型',
  '久期型',
].reverse()

export default ({
  data,
  currentFund,
}: {
  data: any,
  currentFund: any,
}) => {
  const heatmapData = []
  for (let i = 0; i < data.length; i++) {
    for (let j = 0; j < styleList.length; j++) {
      const styleType = data[i].styleType
      const value = styleType === styleList[j] ? 1 : null
      heatmapData.push([i, j, value, data[i].date])
    }
  }
  const options = {
    grid: {
      // height: '100%',
      left: 90,
      right: 10,
    },
    tooltip: {
      position: 'top',
      formatter: function (params) {
        const data = params.data || []
        const date = data[3]
        const styleType = styleList[data[1]]
        return `${date}: ${styleType}`
      },
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date),
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: 'category',
      data: styleList,
      splitArea: {
        show: true,
      },
    },
    series: [{
      name: '基金风格',
      type: 'heatmap',
      data: heatmapData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    }],
  }
  const exportColumns = [{
    title: '日期',
    dataIndex: 'date',
  }, {
    title: '风格类型',
    dataIndex: 'styleType',
  }]
  return (
    <Card
      title="基金风格历史变化"
      size="small"
      extra={
        <ExportData addDateSuffix columns={exportColumns} dataSource={data} filename={`${currentFund.name}-基金风格历史变化`}/>
      }
    >
      <Echart style={{ height: '350px' }} options={options} />
    </Card>
  )
}
