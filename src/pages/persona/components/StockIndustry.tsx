import React from 'react'
import round from 'lodash/round'
import { Card, Col, Row, Radio } from 'antd'
import Chart from '@/components/Chart/Chart'
import moment from 'moment'
import getSeriesChartData from '@/utils/getSeriesChartData'
import getIndustryBoardData from '@/utils/getIndustryBoardData'
import IndustryChange from './IndustryChange'
import styles from '../style.less'

interface StockIndustryChartProps {
  industryData: any;
  title?: string;
}

class StockIndustryChart extends React.Component<StockIndustryChartProps> {
  constructor(props: StockIndustryChartProps) {
    super(props)
    this.state = {
      viewType: 'industry',
    }
  }

  handleIndustryChange = industry => {
    this.setState({ currentIndustry: industry })
  };

  handleViewTypeChange = event => {
    this.setState({ viewType: event.target.value })
  };

  renderIndustryPreference() {
    const { viewType } = this.state
    let data = this.props.industryData
    if (viewType === 'industryBoard') {
      data = getIndustryBoardData(data)
    }
    const chartConfig = {
      chart: {
        type: 'column',
        height: 400,
      },
      yAxis: {
        max: 100,
      },
      navigator: {
        enabled: true,
      },
      scrollbar: {
        enabled: false,
      },
      plotOptions: {
        area: {
          stacking: 'normal',
        },
        column: {
          stacking: 'normal',
          pointPadding: 0,
        },
      },
      tooltip: {
        useHTML: true,
        formatter: function() {
          let tmp = ''
          const length = this.points.length
          const rowCount = 10
          const lineCount = parseInt(length / rowCount) + 1
          tmp += '<table>'
          tmp += `<tr><td>${moment(new Date(this.x)).format('YYYY-MM-DD')}</td></tr>`
          for (let i = 0; i < rowCount; i++) {
            tmp += '<tr>'
            for (let j = 0; j < lineCount && j * rowCount + i  < length; j++) {
              const it = this.points[j * rowCount + i]
              tmp += `
                <td style='padding-right: 20px'>
                  <span style='color:${it.color}'>● </span>${it.series.name}:<b>${round(it.y, 2)}%</b>
                </td>
              `
            }
            tmp += '</tr>'
          }
          tmp += '</table>'
          return tmp
        },
        // pointFormat:
        //   '<span style="color:{series.color}">{series.name}</span>: <b>{point.percentage:.2f}%</b> ({point.y:,.0f})<br/>',
      },
      series: getSeriesChartData(data, 'INDUSTRY', 'BALANCE'),
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }


  render() {
    const { industryData, title } = this.props
    const { viewType } = this.state
    return (
      <Card>
        <Row gutter={16}>
          <Col lg={24} md={24}>
            <h4>{title || '行业配置'}</h4>
            <Radio.Group
              value={viewType}
              size="small"
              className={styles.rightAction}
              onChange={this.handleViewTypeChange}
            >
              <Radio.Button value="industry">行业偏好</Radio.Button>
              <Radio.Button value="industryBoard">板块偏好</Radio.Button>
            </Radio.Group>
            {this.renderIndustryPreference()}
          </Col>
          <Col lg={24} md={24}>
            {industryData && industryData.length &&
            <IndustryChange title="申万一级行业走势" industryData={industryData}/>}
          </Col>
        </Row>
      </Card>
    )
  }
}

export default StockIndustryChart
