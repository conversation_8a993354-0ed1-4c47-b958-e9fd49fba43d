import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Select, Card, Spin,
} from 'antd'
import { getConvtBondWeight, getAIndexPrices } from '@/services/fund'
import Chart from '@/components/Chart/Chart'
import _ from 'lodash'
import moment from 'moment'

const { Option } = Select

export default ({
  fundId,
}: {
  fundId: string,
}) => {
  const benchmarks = [{
    name: '上证转债',
    code: '000139.SH',
  }, {
    name: '中证转债',
    code: '000832.CSI',
  }, {
    name: '沪深300',
    code: '000300.SH',
  }, {
    name: '沪深300全收益',
    code: 'h00300.CSI',
  }, {
    name: '中证500',
    code: '000905.SH',
  }, {
    name: '中证800',
    code: '000905.SH',
  }, {
    name: '上证50',
    code: '000016.SH',
  }, {
    name: '上证指数',
    code: '000001.SH',
  }]
  const [benchmark, setBenchmark] = useState('000300.SH')
  const { data, loading } = useRequest(() => {
    return getConvtBondWeight(fundId)
  })
  const { data: benchmarkData, loading: loadingBenchmark } = useRequest(() => {
    return getAIndexPrices({ code: benchmark })
  }, {
    refreshDeps: [benchmark],
  })
  const weightData = (data || []).map(item => {
    return [
      +moment(item[0]).startOf('date'),
      item[1] * 100,
    ]
  })
  const startDate = _.min(weightData.map(item => item[0]))
  const endDate = _.max(weightData.map(item => item[0]))
  const benchmarkDataSeries = (benchmarkData || []).map(item => {
    return [
      +moment(item[0]).startOf('date'),
      item[1],
    ]
  }).filter(item => item[0] >= startDate && item[0] <= endDate)
  const benchmarkName = (benchmarks.find(item => item.code === benchmark) || {}).name
  const chartConfig = {
    navigator: {
      enabled: true,
    },
    scrollbar: {
      enabled: false,
    },
    yAxis: [
      {
        labels: {
          format: '{value}%',
        },
      },
      {
        labels: {
          format: '{value}%',
        },
        opposite: true,
      },
    ],
    series: [
      {
        yAxis: 0,
        type: 'area',
        name: '可转债仓位',
        data: weightData,
        tooltip: {
          pointFormat:
            '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%<br/>',
        },
      },
      {
        yAxis: 1,
        type: 'line',
        name: benchmarkName,
        data: benchmarkDataSeries,
        compare: 'percent',
        tooltip: {
          pointFormat: '{series.name}: <b>{point.y:.2f}({point.change:.2f}%)</b><br/>',
        },
      },
    ],
  }
  return (
    <Card
      title="可转债仓位变化"
      bordered={false}
      extra={
        <>
          <Select
            showSearch
            value={benchmark}
            style={{ width: 150, marginLeft: 15 }}
            size="small"
            placeholder="选择基准"
            optionFilterProp="children"
            onChange={setBenchmark}
            filterOption={(input, option) =>
              option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {benchmarks.map(item => (
              <Option value={item.code}>{item.name}</Option>
            ))}
          </Select>
        </>
      }
    >
      <Spin spinning={loading || loadingBenchmark}>
        <Chart options={chartConfig} constructorType="stockChart" />
      </Spin>
    </Card>
  )
}
