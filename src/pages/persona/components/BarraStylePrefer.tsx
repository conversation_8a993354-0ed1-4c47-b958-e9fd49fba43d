import React, { useState } from 'react'
import {
  Spin,
  Card,
  Space,
  Row,
  Col,
} from 'antd'
import { useRequest } from '@umijs/hooks'
import Chart from '@/components/Chart/Chart'
import _ from 'lodash'
import SearchSelect from '@/components/SearchSelect'
import ExportData from '@/components/ExportData'
import { getFundBarraStylePrefer, getFundPositionDatesRp } from '@/services/fund'

const StylePreferComp = ({ styleData, benchmark, bizDate }) => {
  const categories = styleData.map(item => item.factor)
  const height = categories.length * 25
  const chartConfig = {
    chart: {
      type: 'bar',
      height,
    },
    xAxis: {
      categories,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}<br/>',
    },
    series: [{
      name: '风格偏好',
      data: styleData.map(item => item.factor_value),
    }, {
      name: '基准风格偏好',
      data: styleData.map(item => item.factor_value_benchmark),
      type: 'line',
      lineWidth: 0,
      states: {
        hover: {
          lineWidthPlus: 0,
        },
      },
      color: '#E49831',
      marker: {
        enabled: true,
        lineWidth: 4,
        symbol: 'circle',
        lineColor: '#E49831',
      },
    }]
  }
  const exportCols = [{
    title: '风格因子',
    dataIndex: 'factor',
  }, {
    title: '风格偏好',
    dataIndex: 'factor_value',
  }, {
    title: '基准风格偏好',
    dataIndex: 'factor_value_benchmark',
  }]
  return (
    <Card
      size="small"
      bordered={false}
      className="zero-padding-card"
      title="风格偏好"
      extra={
        <Space>
          <ExportData columns={exportCols} dataSource={styleData} filename={`风格偏好-${bizDate}-${benchmark}`}/>
        </Space>
      }
    >
      <Chart options={chartConfig}/>
    </Card>
  )
}

const StyleDiffComp = ({ styleData, benchmark, bizDate }) => {
  const categories = styleData.map(item => item.factor)
  const height = categories.length * 25
  const chartConfig = {
    chart: {
      type: 'bar',
      height,
    },
    xAxis: {
      categories,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}<br/>',
    },
    series: [{ name: '风格偏差', value: 'excess_value' }].map(quota => {
      return {
        name: quota.name,
        data: styleData.map(item => item[quota.value]),
      }
    }),
  }
  const exportCols = [{
    title: '风格因子',
    dataIndex: 'factor',
  }, {
    title: '风格偏差',
    dataIndex: 'excess_value',
  }]
  return (
    <Card
      size="small"
      bordered={false}
      className="zero-padding-card"
      title="风格偏差"
      extra={
        <Space>
          <ExportData columns={exportCols} dataSource={styleData} filename={`风格偏差-${bizDate}-${benchmark}`}/>
        </Space>
      }
    >
      <Chart options={chartConfig}/>
    </Card>
  )
}

const BarraStylePrefer = ({ currentFund, dateList }) => {
  const benchmarkList = [{
    name: '沪深300', value: '000300.SH',
  },
  {
    name: '中证500', value: '000905.SH',
  },
  {
    name: '中证800', value: '000906.SH',
  },
  ]
  const [bizDate, setBizDate] = useState(dateList[0])
  const [benchmark, setBenchmark] = useState(benchmarkList[0].value)
  const { loading, data = [] } = useRequest(() => {
    return getFundBarraStylePrefer(currentFund._id, {
      bizDate, benchmark,
    })
  }, {
    refreshDeps: [bizDate, benchmark],
  })
  return (
    <Card
      size="small"
      bordered={false}
      className="zero-padding-card"
      extra={
        <Space>
          <SearchSelect
            placeholder="清选择日期"
            value={bizDate}
            options={dateList.map(date => {
              return {
                title: date,
                dataIndex: date,
              }
            })}
            onChange={setBizDate}
            width="150px"
          />
          <SearchSelect
            placeholder="清选择基准"
            value={benchmark}
            options={benchmarkList.map(item => {
              return {
                title: item.name,
                dataIndex: item.value,
              }
            })}
            onChange={setBenchmark}
            width="150px"
          />
        </Space>
      }
    >
      <Spin spinning={loading}>
        <Row gutter={16}>
          <Col lg={12} md={24}>
            <StylePreferComp styleData={_.orderBy(data, 'factor_value', 'desc')} bizDate={bizDate} benchmark={benchmark} />
          </Col>
          <Col lg={12} md={24}>
            <StyleDiffComp styleData={data} bizDate={bizDate} benchmark={benchmark} />
          </Col>
        </Row>
      </Spin>
    </Card>
  )
}

const BarraStylePreferWrapper = ({ currentFund }) => {
  const { loading, data = [] } = useRequest(() => {
    return getFundPositionDatesRp(currentFund._id)
  })
  return (
    <div>
      <Spin spinning={loading} style={{ minHeight: 400 }}>
        {!loading && <BarraStylePrefer currentFund={currentFund} dateList={data}/>}
      </Spin>
    </div>
  )
}

export default BarraStylePreferWrapper
