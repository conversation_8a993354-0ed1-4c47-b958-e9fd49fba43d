import React, { useState } from 'react'
import _ from 'lodash'
import { Card, Col, Row, Spin, Select, Space } from 'antd'
import Chart from '@/components/Chart/Chart'

const { Option } = Select

const FactorSeriesChart = ({ factor, data }) => {
  const { title } = factor
  const [ currentAlg, setCurrentAlg ] = useState('scaleWeighted')
  const defaultFactors = factor.factors.filter(item => {
    return item.alg === currentAlg
  })
  const [ factors, setFactors ] = useState(defaultFactors)
  const [ currentFactor, setCurrentFactor ] = useState(factors[0])
  const factorData = data.filter(item => item.factor_code === currentFactor.dataIndex)
  const handleFactorChange = factorValue => {
    const item = factors.find(item => item.dataIndex === factorValue)
    setCurrentFactor(item)
  }
  const handleAlgChange = algValue => {
    const factors = factor.factors.filter(item => {
      return item.alg === algValue
    })
    setCurrentAlg(algValue)
    setFactors(factors)
    setCurrentFactor(factors[0])
  }
  const algList = [{
    name: '市值加权法',
    value: 'scaleWeighted',
  }, {
    name: '整体平均法',
    value: 'avg',
  }]
  const algTypes = _.uniq(factor.factors.map(item => item.alg))
  const series = [{
    dataIndex: 'factor_value',
    type: 'column',
  }, {
    title: '同类Q1',
    dataIndex: 'q1',
  }, {
    title: '同类Q2',
    dataIndex: 'q2',
  }, {
    title: '同类Q3',
    dataIndex: 'q3',
  }].map(item => {
    return {
      name: item.title || currentFactor.title,
      type: item.type || 'line',
      data: factorData.map(fd => {
        console.log(fd)
        const value = fd[item.dataIndex]
        if (!value || currentFactor.format !== 'hundredMillion') {
          return [fd.date, value]
        }
        return [fd.date, value / 100000000]
      }),
    }
  })

  const chartConfig = {
    navigator: {
      enabled: true,
    },
    scrollbar: {
      enabled: false,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}<br/>',
    },
    yAxis: [
      {
        labels: {
          format: '{value}',
        },
      },
    ],
    lang: {
      noData: `该基金没有分类信息，${title}因子数据为空`,
    },
    series,
  }
  return (
    <Card
      title={title}
      extra={
        <Space>
          {algTypes.length > 1 &&
          <Select size="small" defaultValue={currentAlg} style={{ width: 110 }} onChange={handleAlgChange}>
            {algList.map(item => <Option value={item.value}>{item.name}</Option>)}
          </Select>}
          {factors.length > 1 &&
          <Select size="small" value={currentFactor.dataIndex} style={{ width: 120 }} onChange={handleFactorChange}>
            {factors.map(item => {
              return <Option value={item.dataIndex}>{item.title}</Option>
            })}
          </Select>}
        </Space>
      }
    >
      <Chart options={chartConfig} constructorType="stockChart" />
    </Card>
  )
}

export default ({ data, loading }) => {
  const factorList = [{
    title: '估值',
    factors: [{
      title: 'PE TTM',
      alg: 'scaleWeighted',
      dataIndex: 'FdPE_TTMPosMcapWt_H_12M_E',
    }, {
      title: 'PB',
      alg: 'scaleWeighted',
      dataIndex: 'FdPBPosMcapWt_H_12M_E',
    }, {
      title: 'PS TTM',
      alg: 'scaleWeighted',
      dataIndex: 'FdPS_TTMPosMcapWt_H_12M_E',
    }, {
      title: 'PE TTM',
      alg: 'avg',
      dataIndex: 'FdPE_TTMTotalAvg_H_12M_E',
    }, {
      title: 'PB',
      alg: 'avg',
      dataIndex: 'FdPBTotalAvg_H_12M_E',
    }, {
      title: 'PS TTM',
      alg: 'avg',
      dataIndex: 'FdPS_TTMTotalAvg_H_12M_E',
    }],
  }, {
    title: '成长',
    factors: [{
      title: '营业收入TTM',
      alg: 'scaleWeighted',
      dataIndex: 'FdSalesGYOYPosMcapWt_H_12M_E',
    }, {
      title: '净利润TTM',
      alg: 'scaleWeighted',
      dataIndex: 'FdNPrfGYOYPosMcapWt_H_12M_E',
    }, {
      title: '营业收入TTM',
      alg: 'avg',
      dataIndex: 'FdSalesGYOYTotalAvg_H_12M_E',
    }, {
      title: '净利润TTM',
      alg: 'avg',
      dataIndex: 'FdNPrfGYOYTotalAvg_H_12M_E',
    }],
  }, {
    title: '盈利',
    factors: [{
      title: 'ROE TTM',
      alg: 'scaleWeighted',
      dataIndex: 'FdROEPosMcapWt_H_12M_E',
    }, {
      title: 'ROE TTM',
      alg: 'avg',
      dataIndex: 'FdROETotalAvg_H_12M_E',
    }],
  }, {
    title: '市值(亿)',
    factors: [{
      title: '持仓市值',
      alg: 'scaleWeighted',
      dataIndex: 'FdSizePosMcapWt_H_6M_E',
      format: 'hundredMillion',
    }],
  }]
  return (
      <Spin spinning={loading} >
        <Row gutter={16}>
          {factorList.map(factor => {
            return (
              <Col lg={12} md={24}>
                <FactorSeriesChart factor={factor} data={data} />
              </Col>
            )
          })}
        </Row>
    </Spin>
  )
}
