import React from 'react'
import { Card, Rate } from 'antd'
import round from 'lodash/round'

interface ComponentProps {
  rating: number;
  title?: string;
  useInCard?: boolean;
}

const IndividualRating: React.FC<ComponentProps> = props => {
  const { rating, title, useInCard } = props
  let realValue
  if (rating) {
    const n = Math.floor(rating)
    const diff = rating - n >= 0.5 ? 0.5 : 0
    realValue = n + diff
  }
  const content = (
    <span>
      <span>{title || '单项评分：'}</span>
      <Rate disabled allowHalf defaultValue={realValue} />
      {rating && <span className="ant-rate-text">{round(rating, 1)}</span>}
    </span>
  )
  if (useInCard) {
    return content
  }
  return (
    <Card>{content}</Card>
  )
}

export default IndividualRating
