import React from 'react'
import { Spin } from 'antd'
import moment from 'moment'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import { queryHisTotalScoreAsset, queryManagerHisTotalScoreAsset } from '../service'
import Echart from '@/components/Chart/Echarts'

const getChartOptions = ({ factorScoreData, assetData }) => {
  const series = factorScoreData.map(item => {
    const ret = {
      name: item.name,
      type: 'line',
      yAxisIndex: 0,
      data: item.data.map(item => [item.factor_date, _.round(item.value_quantile, 2)]),
      smooth: false,
    }
    if (item.name === '总得分') {
      ret.lineStyle = {
        // color: '#5470C6',
        type: 'dashed',
        width: 5
      }
    }
    return ret
  })
  series.push({
    name: '净资产(亿)',
    type: 'bar',
    yAxisIndex: 1,
    data: assetData.map(item => [item.bizDate, _.round(item.netAsset, 2)]),
  })
  const option = {
    tooltip: {
      trigger: 'axis',
      // axisPointer: {
      //   type: 'shadow',
      // },
    },
    legend: {
      data: factorScoreData.map(item => item.name),
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: factorScoreData[0] && factorScoreData[0].data.map(item => item.factor_date),
      },
    ],
    yAxis: [{
      type: 'value',
      name: '得分(%)',
    }, {
      type: 'value',
      name: '净资产(亿)',
    }],
    series,
  }
  return option
}

const HisFactorScoreAsset: React.FC = ({ fundId, schemaId, isManager }) => {
  const { loading, data: hisData = { factorScoreData: [], assetData: [] } } = useRequest(() => {
    return isManager ? queryManagerHisTotalScoreAsset(fundId, { schemaId }) : queryHisTotalScoreAsset(fundId, { schemaId })
  }, {
    cacheKey: `queryHisTotalScore${schemaId}`,
    refreshDeps: [schemaId],
  })
  const options = getChartOptions(hisData)
  return (
    <Spin spinning={loading}>
      <div>
        <Echart style={{ height: '650px', marginTop: 20 }} options={options} key={`${schemaId}-${loading}`}/>
      </div>
    </Spin>
  )
}

export default HisFactorScoreAsset
