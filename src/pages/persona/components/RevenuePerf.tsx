import React from 'react'
import moment from 'moment'
import { QuestionCircleOutlined } from '@ant-design/icons'
import {
  Card,
  Row,
  Col,
  Tooltip,
} from 'antd'
import Chart from '@/components/Chart/Chart'
import StandardTable, { StandardTableColumnProps } from '@/components/StandardTable'
import navPerfHelper from '@/utils/navPerfHelper'

const renderChartWithTable = (columns, data) => {
  const chartConfig = {
    chart: {
      type: 'column',
      height: 250,
    },
    xAxis: {
      categories: columns.slice(1).map(item => item.title),
    },
    series: data.map(item => {
      return {
        name: item.name,
        data: columns.slice(1).map(col => (item[col.dataIndex] || 0) * 100),
      }
    }),
  }
  return (
    <>
      <Chart options={chartConfig} />
      <StandardTable
        disableRowSlection
        size="small"
        columns={columns}
        data={{ list: data, pagination: false }}
        scroll={{ x: 700 }}
      />
    </>
  )
}

const buildZones = (data) => {
  const len = data.length
  const zones = []
  let i = -1
  let current
  let previous
  let dashStyle
  let value

  if (!len) {
    return []
  }

  while (data[++i][1] === null);
  zones.push({
    value: i,
  })

  while (++i < len) {
    previous = data[i - 1]
    current = data[i]
    dashStyle = ''

    if (previous[1] !== null && current[1] === null) {
      dashStyle = 'solid'
      value = previous[0]
    } else if (previous[1] === null && current[1] !== null) {
      dashStyle = 'dot'
      value = current[0]
    }

    if (dashStyle) {
      zones.push({
        dashStyle: dashStyle,
        value: value,
      })
    }
  }

  return zones
}

const NavChart = ({
  currentFund, currentBenchmark,
  fundNavDisplay, benchmarkNavDisplay, excessReturns, startDate, endDate,
}) => {
  const buildSerieData = (name, list, withZone) => {
    const data = (list || []).map(item => [item.date, item.value])
    let ret = {
      name,
      type: 'line',
      data,
      yAxis: 0,
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y:.2f}%</b><br/>',
      },
    }
    if (name !== '超额收益') {
      ret.compare = 'percent'
      ret.tooltip = {
        pointFormat: '{series.name}: <b>{point.y:.2f}({point.change:.2f}%)</b><br/>',
      }
    }
    if (withZone) {
      ret = {
        ...ret,
        zones: buildZones(data),
        zoneAxis: 'x',
        connectNulls: true,
      }
    }
    return ret
  }
  const series = [buildSerieData(currentFund.name, fundNavDisplay || currentFund.nets, true)]
  if (currentFund.scale && currentFund.scale.length) {
    series.push({
      name: '管理规模',
      type: 'area',
      data: currentFund.scale.filter(item => {
        if (!startDate || !endDate) {
          return true
        }
        return item.date >= startDate && item.date <= endDate
      }).map(item => [item.date, item.value / 10000]),
      step: true,
      yAxis: 1,
      fillColor: {
        linearGradient: {
          x1: 0,
          y1: 0,
          x2: 0,
          y2: 1,
        },
        stops: [
          [0, Chart.Highcharts.getOptions().colors[0]],
          [
            1,
            Chart.Highcharts.Color(Chart.Highcharts.getOptions().colors[0])
              .setOpacity(0)
              .get('rgba'),
          ],
        ],
      },
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y:,.0f} 万</b><br/>',
      },
    })
  }
  if (benchmarkNavDisplay && benchmarkNavDisplay.length) {
    // const plus3Nav = benchmarkNavDisplay.map((item, index) => {
    //   return {
    //     date: item.date,
    //     value: index === 0 ? item.value : item.value * 1.03,
    //   }
    // })
    series.push(buildSerieData(currentBenchmark.name, benchmarkNavDisplay))
    // series.push(buildSerieData('+300bp', plus3Nav))
    series.push(buildSerieData('超额收益', excessReturns))
  }
  const chartConfig = {
    chart: {
      type: 'line',
      height: 300,
    },
    // navigator: {
    //   enabled: false,
    // },
    scrollbar: {
      enabled: false,
    },
    yAxis: [
      {
        title: {
          text: '累计收益',
        },
        labels: {
          format: '{value}%',
        },
      },
      {
        title: {
          text: '规模',
        },
        labels: {
          format: '{value}',
        },
        opposite: true,
      },
    ],
    series,
  }
  return <Chart options={chartConfig} constructorType="stockChart" />
}

const getRiskReturnSeries = (name, returnData, rollingPeriod, rollingGap) => {
  const rollingReturnData = navPerfHelper.calculateRollingReturn(returnData, rollingPeriod, rollingGap)
  const rollingVolData = navPerfHelper.calculateRollingVol(returnData, rollingPeriod, rollingGap)
  return {
    name,
    visible: true,
    data: rollingVolData
      .map((item, index) => {
        return [item.value * 100, rollingReturnData[index].value * 100, item.date]
      })
      .filter(item => item[0] && item[1]),
  }
}

const RiskChart = ({
  currentFund, currentBenchmark,
  fundReturns, benchmarkReturns,
  rollingPeriod, rollingGap,
}) => {
  const series = [getRiskReturnSeries(currentFund.name, fundReturns, rollingPeriod, rollingGap,)]
  if (currentBenchmark) {
    series.push(getRiskReturnSeries(currentBenchmark.name, benchmarkReturns, rollingPeriod, rollingGap,))
  }
  const chartConfig = {
    chart: {
      type: 'bubble',
      zoomType: 'xy',
    },
    yAxis: {
      title: {
        text: '收益率',
      },
      labels: {
        format: '{value}%',
      },
    },
    xAxis: {
      title: {
        text: '波动率',
        align: 'high',
        offset: -10,
      },
      labels: {
        format: '{value}%',
      },
    },
    tooltip: {
      pointFormat:
        '{point.z:%Y-%m-%e}<br/>收益率: <b>{point.y:,.2f}%</b>波动率: <b>{point.x:,.2f}%</b>',
    },
    series,
  }
  return <Chart options={chartConfig} />
}

const ReturnStats = ({ fundQuotas, benchmarkQuotas }) => {
  const data = [fundQuotas, benchmarkQuotas].filter(Boolean)
  const yearColumns: StandardTableColumnProps[] = [
    {
      dataIndex: 'name',
      width: 100,
      fixed: 'left',
    },
  ]
  const currentYear = moment().year()
  let startYear = currentYear - 6
  const quotaWidth = 70
  while (startYear < currentYear) {
    yearColumns.push({
      title: `${startYear}年`,
      dataIndex: `accReturn${startYear}`,
      format: 'percentage',
      width: quotaWidth,
    })
    startYear = startYear + 1
  }
  yearColumns.push({
    title: 'YTD',
    dataIndex: 'ytdAccReturn',
    format: 'percentage',
  })
  const monthColumns: StandardTableColumnProps[] = [
    {
      dataIndex: 'name',
      width: 100,
      fixed: 'left',
    },
    {
      title: '1月',
      dataIndex: 'last1MAccReturn',
      format: 'percentage',
      width: quotaWidth,
    },
    {
      title: '6月',
      dataIndex: 'last6MAccReturn',
      format: 'percentage',
      width: quotaWidth,
    },
    {
      title: 'YTD',
      dataIndex: 'ytdAccReturn',
      format: 'percentage',
      width: quotaWidth,
    },
    {
      title: '1年',
      dataIndex: 'last1YAccReturn',
      format: 'percentage',
      width: quotaWidth,
    },
    {
      title: '年化3年',
      dataIndex: 'last3YYearReturn',
      format: 'percentage',
      width: quotaWidth,
    },
    {
      title: '年化5年',
      dataIndex: 'last5YYearReturn',
      format: 'percentage',
      width: quotaWidth,
    },
    {
      title: '年化收益',
      dataIndex: 'yearReturn',
      format: 'percentage',
    },
  ]
  return (
    <Row gutter={16}>
      <Col lg={12} md={24}>
        {renderChartWithTable(yearColumns, data)}
      </Col>
      <Col lg={12} md={24}>
        {renderChartWithTable(monthColumns, data)}
      </Col>
    </Row>
  )
}

const RevenuePerf = ({
  isManager,
  ...otherProps
}) => {
  return (
    <div>
      <Card>
        <Row gutter={16}>
          <Col lg={12} md={24}>
            <span>收益走势</span>
            {isManager &&
            <Tooltip title="虚线[---]代表基金经理在时间段没有管理产品" placement="right">
              <span style={{ marginLeft: '5px' }}>
                <QuestionCircleOutlined />
              </span>
            </Tooltip>}
            <NavChart {...otherProps}/>
          </Col>
          <Col lg={12} md={24}>
            <span>收益风险分布</span>
            <Tooltip title="滚动计算收益率和波动率，气泡越大代表日期越近" placement="right">
              <span style={{ marginLeft: '5px' }}>
                <QuestionCircleOutlined />
              </span>
            </Tooltip>
            <RiskChart {...otherProps}/>
          </Col>
        </Row>
      </Card>
      <Card title="业绩表现">
        <ReturnStats {...otherProps}/>
      </Card>
    </div>
  )
}

export default RevenuePerf
