import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Radio, Card, Spin,
} from 'antd'
import CorrelationTable from '@/components/CorrelationTable'
import { computeCorrelationData } from '@/services/fund'

export default ({ childFundList }) => {
  const ids = childFundList.map(item => item._id).join(',')
  const [retType, setRetType] = useState('excessReturn')
  const handleRetTypeChange = (event) => {
    setRetType(event.target.value)
  }
  const { data: correlationData = {}, loading } = useRequest(() => {
    return computeCorrelationData({
      ids, retType,
    })
  }, {
    refreshDeps: [retType],
  })
  return (
    <Spin spinning={loading}>
      <Card
        title="子基金相关性"
        extra={
          <Radio.Group
            defaultValue={retType}
            size="small"
            onChange={handleRetTypeChange}
          >
            <Radio.Button value="excessReturn">超额收益率</Radio.Button>
            <Radio.Button value="accReturn">收益率</Radio.Button>
          </Radio.Group>
        }
      >
        <CorrelationTable useIdAsKey funds={childFundList} cor={correlationData} />
      </Card>
    </Spin>
  )
}
