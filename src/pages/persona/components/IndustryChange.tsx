import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Select, Card, Spin,
} from 'antd'
import { getSwIndustryPrices } from '@/services/fund'
import Chart from '@/components/Chart/Chart'
import _ from 'lodash'
import moment from 'moment'

const { Option } = Select

export default ({
  title,
  industryData,
}: {
  title: string,
  industryData: any,
}) => {
  const industries = _.uniq(industryData.map(item => item.INDUSTRY))
  const [industry, setIndustry] = useState(industries[0])
  const { data: benchmarkData = [], loading } = useRequest(() => {
    return getSwIndustryPrices({ industry })
  }, {
    refreshDeps: [industry],
  })
  const data = industryData
    .filter(item => item.INDUSTRY === industry)
    .map(item => [
      +moment(item.BIZ_DATE).startOf('date'),
      item.BALANCE_RATIO * 100,
    ])
  const startDate = _.min(industryData.map(item => +moment(item.BIZ_DATE).startOf('date')))
  const endDate = _.max(data.map(item => item[0]))
  const benchmarkDataSeries = (benchmarkData || []).map(item => {
    return [
      +moment(item[0]).startOf('date'),
      item[1],
    ]
  }).filter(item => item[0] >= startDate && item[0] <= endDate)
  const chartConfig = {
    chart: {
      type: 'column',
      height: 350,
    },
    navigator: {
      enabled: true,
    },
    scrollbar: {
      enabled: false,
    },
    yAxis: [
      {
        labels: {
          format: '{value}%',
        },
        min: 0,
        max: 100,
      },
      {
        labels: {
          format: '{value}%',
        },
        opposite: true,
      },
    ],
    series: [
      {
        yAxis: 0,
        type: 'column',
        name: '行业占比',
        data: data,
      },
      {
        yAxis: 1,
        type: 'line',
        name: '行业指数',
        data: benchmarkDataSeries,
        compare: 'percent',
        tooltip: {
          pointFormat: '{series.name}: <b>{point.y:.2f}({point.change:.2f}%)</b><br/>',
        },
      },
    ],
  }
  return (
    <Card
      title={title}
      bordered={false}
      extra={
        <>
          <Select
            showSearch
            value={industry}
            style={{ width: 150, marginLeft: 15 }}
            placeholder="选择行业"
            optionFilterProp="children"
            onChange={setIndustry}
            filterOption={(input, option) =>
              option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {industries.map(item => (
              <Option value={item}>{item}</Option>
            ))}
          </Select>
        </>
      }
    >
      <Spin spinning={loading}>
        <Chart options={chartConfig} constructorType="stockChart" />
      </Spin>
    </Card>
  )
}
