import React from 'react'
import {
  Select,
  Space,
  DatePicker,
} from 'antd'
import ExportData from '@/components/ExportData'
import moment from 'moment'

const { Option } = Select
const { RangePicker } = DatePicker

const PositionSeriesActionBlock = ({
  currentFund, childFundList, assetTypeList, setfundType, setDateRange,
  viewType, setViewType, dataType, setDataType, handleDateRangeChange,
  dateRange, frequency, setFrequency, dateOrder, setDateOrder,
  fundId, isDetect, exportedData, dataRange, setDataRange,
  isPortfolio, isOrigin, handleIsOriginChange, isActiveFund,
}) => {
  return (
    <Space>
      {[currentFund, ...childFundList].length > 1 &&
        <span>
          选择组合：<Select
            onChange={(id) => {
              const fund = [currentFund, ...childFundList].find(item => item._id === id)
              setDateRange([
                moment(new Date(fund.navStartDate)),
                moment(new Date(fund.navEndDate)),
              ])
            }}
            style={{ width: 130 }}
            size="small"
            defaultValue={fundId}
          >
            {[currentFund, ...childFundList].map(item => <Option value={item._id}>{item.name}</Option>)}
          </Select>
        </span>}
      {isPortfolio && <span>
        <Select
          onChange={(isOrigin) => {
            handleIsOriginChange(isOrigin)
            if (isOrigin) {
              setViewType('detail')
              setDataType('ratioOfNetAsset')
            } else {
              setViewType('category')
            }
          }}
          style={{ minWidth: 70 }}
          size="small"
          defaultValue={isOrigin}
        >
          <Option value={false}>穿透后</Option>
          <Option value={true}>穿透前</Option>
        </Select>
      </span>}
      {viewType === `detail` && !isOrigin && <span>
        资产类型：<Select
          mode="multiple"
          onChange={(fundType) => { setfundType(fundType) }}
          style={{ minWidth: 70 }}
          size="small"
          defaultValue={assetTypeList}
        >
          {assetTypeList.map(item =>
            <Option value={item}>{item}</Option>,
          )}
        </Select>
      </span>}
      {!isOrigin &&
      <span>
        显示模式：<Select
          onChange={(viewType) => { setViewType(viewType) }}
          style={{ width: 80 }}
          size="small"
          defaultValue={viewType}
        >
          <Option value="category">按分类</Option>
          <Option value="detail">按明细</Option>
          {isActiveFund &&
          <Option value="000922_component">按红利</Option>}
          {isActiveFund &&
          <Option value="000300_component">按300</Option>}
        </Select>
      </span>}
      {!isDetect && !isOrigin &&
      <span>
        显示字段：<Select
          onChange={(dataType) => { setDataType(dataType) }}
          style={{ width: 100 }}
          size="small"
          defaultValue={dataType}
        >
          <Option value="amount">持仓数量</Option>
          <Option value="balance">持仓市值</Option>
          <Option value="ratioOfNetAsset">占净资产比</Option>
        </Select>
      </span>}
      {!isDetect && !isOrigin &&
      <span>
        数据范围：<Select
          onChange={(dataRange) => { setDataRange(dataRange) }}
          style={{ width: 80 }}
          size="small"
          defaultValue={dataRange}
        >
          <Option value="all">全部</Option>
          <Option value="top10">前十大</Option>
        </Select>
      </span>}
      <span>
        日期范围：<RangePicker
          style={{ width: 200 }}
          size="small"
          onChange={handleDateRangeChange}
          disabledDate={(current) => {
            const currentTs = +current.startOf('date')
            return currentTs < +(currentFund.ref_fund_start_date_ts || currentFund.navStartDate || defaultStartDate)
              ||
              currentTs > +(currentFund.ref_fund_end_date_ts || currentFund.navEndDate || defaultEndDate)
          }}
          value={dateRange}
        />
      </span>
      <span>
        数据频率：<Select
          onChange={(frequency) => { setFrequency(frequency) }}
          style={{ width: 70 }}
          size="small"
          defaultValue={frequency}
        >
          <Option value="daily">日频</Option>
          <Option value="monthly">月频</Option>
          <Option value="quarterly">季频</Option>
        </Select>
      </span>
      <span>
        日期排序：<Select
          onChange={(dateOrder) => { setDateOrder(dateOrder) }}
          style={{ width: 70 }}
          size="small"
          defaultValue={dateOrder}
        >
          <Option value="desc">降序</Option>
          <Option value="asc">升序</Option>
        </Select>
      </span>
      <ExportData title="导出数据" columns={exportedData.columns} dataSource={exportedData.dataSource} filename={`${currentFund.name}_持仓明细_${dateRange[0].format('YYYYMMDD')}-${dateRange[1].format('YYYYMMDD')}`}/>
    </Space>
  )
}

export default PositionSeriesActionBlock
