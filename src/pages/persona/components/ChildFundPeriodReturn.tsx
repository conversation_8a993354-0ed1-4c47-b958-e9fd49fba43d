import React, { useState } from 'react'
import {
  Radio, Card, Space,
} from 'antd'
import _ from 'lodash'
import ProgressBarTable from '@/components/ProgressBarTable'
import ExportData from '@/components/ExportData'

export default ({ currentFund, childFundList }) => {
  const [retType, setRetType] = useState('accReturn')
  const handleRetTypeChange = (event) => {
    setRetType(event.target.value)
  }
  const retQuotas = [{
    name: '1月',
    value: 'last1M'
  }, {
    name: '6月',
    value: 'last6M'
  }, {
    name: 'YTD',
    value: 'ytd'
  }, {
    name: '1年',
    value: 'last1Y'
  }, {
    name: '3年',
    value: 'last3Y'
  }, {
    name: '成立以来',
    value: ''
  }].map(item => {
    const keySuffix = item.value ? _.upperFirst(retType) : retType
    const key = `${item.value}${keySuffix}`
    return {
      title: item.name,
      width: 100,
      dataIndex: key,
      align: 'right',
      format: 'percentage',
      isProgressBar: true,
    }
  })
  const columns = [
    {
      title: '基金',
      dataIndex: 'name',
      width: 100,
      ellipsis: true,
      fixed: 'left',
    },
    ...retQuotas,
  ]
  return (
    <Card
      title="阶段收益比较"
      extra={
        <Space>
          <Radio.Group
            defaultValue={retType}
            size="small"
            onChange={handleRetTypeChange}
          >
            <Radio.Button value="accReturn">收益率</Radio.Button>
            <Radio.Button value="excessReturn">超额收益率</Radio.Button>
          </Radio.Group>
          <ExportData columns={columns} dataSource={childFundList} filename={`${currentFund.name}-阶段收益比较-${retType === 'accReturn' ? '收益率' : '超额收益率'}`}/>
        </Space>
      }
    >
      <ProgressBarTable tableData={childFundList} tableColumns={columns}/>
    </Card>
  )
}
