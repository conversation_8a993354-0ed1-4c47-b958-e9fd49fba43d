
import React from 'react'
import Chart from '@/components/Chart/Chart'

export default ({ currentFund, chartQuotas, height, isPct }) => {
  const getRatingData = item => {
    return chartQuotas.reduce((out, quota) => {
      const key = quota.value
      out[key] = (item[key] || 0) * (isPct ? 1 : 100)
      return out
    }, {})
  }
  const chartData = [
    {
      name: currentFund.name,
      ...getRatingData(currentFund),
    },
  ]
  const series = chartData.map(item => {
    return {
      pointPlacement: 'on',
      name: item.name,
      data: chartQuotas.map(quota => item[quota.value] || 0).filter(Boolean),
    }
  })
  const categories = chartQuotas.filter(item => chartData[0][item.value]).map(item => item.name)
  const chartConfig = {
    chart: {
      polar: true,
      type: 'line',
      height: height || 280,
    },
    pane: {
      size: '80%',
    },
    tooltip: {
      shared: true,
      pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.2f}</b><br/>',
    },
    legend: {
      enabled: false,
    },
    xAxis: {
      categories,
      tickmarkPlacement: 'on',
      lineWidth: 0,
      labels: {
        useHTML: true,
        formatter: function formatter() {
          const labelValue = this.value + ''
          const start = labelValue.slice(0, 4)
          const end = labelValue.slice(4)
          if (!end) {
            return `<span>${start}</span>`
          }
          return `<span>${start}</span><br/><span>${end}</span>`
        },
      },
    },
    yAxis: {
      gridLineInterpolation: 'polygon',
      min: 0,
      max: 100,
      labels: {
        enabled: false,
        format: '{value}',
      },
    },
    series,
  }
  return (
    <Chart options={chartConfig} />
  )
}
