import React, { useState } from 'react'
import {
  Select, Card, Space,
} from 'antd'
import _ from 'lodash'
import Chart from '@/components/Chart/Chart'
import ExportData from '@/components/ExportData'

const { Option } = Select

const renderChart = (industryData, viewType, isDetect, indexType) => {
  let data
  if (viewType === 'top10') {
    data = industryData.slice(0, 10)
  } else {
    data = industryData
  }
  const categories = data.map(item => item.INDUSTRY)
  const height = categories.length * 25
  const chartConfig = {
    chart: {
      type: 'bar',
      height: height > 400 ? height : 400,
    },
    xAxis: {
      categories,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%<br/>',
    },
    series: [{
      name: '行业占比',
      data: data.map(item => item.BALANCE_RATIO * 100),
    }, {
      name: isDetect ? '本基金' : indexType,
      data: data.map(item => item.BASE_RATIO * 100),
      type: 'line',
      lineWidth: 0,
      states: {
        hover: {
          lineWidthPlus: 0,
        },
      },
      color: '#E49831',
      marker: {
        enabled: true,
        lineWidth: 4,
        symbol: 'circle',
        lineColor: '#E49831',
      },
    }],
  }
  return <Chart options={chartConfig}/>
}

export default ({ industryData, isDetect, indexType }) => {
  const [viewType, setViewType] = useState('top10')
  const handleViewTypeChange = newType => {
    setViewType(newType)
    try {
      setTimeout(() => {
        const event = new Event('resize')
        window.dispatchEvent(event)
      }, 200)
    } catch (error) {}
  }
  const latestDate = _.max(industryData.map(item => item.BIZ_DATE))
  const latestData = industryData
    .filter(item => item.BIZ_DATE === latestDate)
    .sort((fst, snd) => snd.BALANCE_RATIO - fst.BALANCE_RATIO)
  const exportCols = [{
    title: '行业',
    dataIndex: 'INDUSTRY',
  }, {
    title: '行业占比',
    dataIndex: 'BALANCE_RATIO',
  }, {
    title: isDetect ? '本基金' : indexType,
    dataIndex: 'BASE_RATIO',
  }]
  return (
    <Card
      key={`${latestDate}-${viewType}`}
      title={`行业偏好(${latestDate})`}
      extra={
        <Space>
          <Select
            defaultValue={viewType}
            onChange={handleViewTypeChange}
            style={{ width: 120 }}
            size="small"
          >
            <Option value="top10">前十大行业</Option>
            <Option value="all">全部行业</Option>
          </Select>
          <ExportData columns={exportCols} dataSource={latestData} filename={`行业偏好(${latestDate})`}/>
        </Space>
      }
    >
      {renderChart(latestData, viewType, isDetect, indexType)}
    </Card>
  )
}
