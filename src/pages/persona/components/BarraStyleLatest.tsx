
import React from 'react'
import Chart from '@/components/Chart/Chart'
import { useRequest } from '@umijs/hooks'
import { getLatestBarraStyle } from '@/services/fund'
import { Spin, Card } from 'antd'

export default ({ currentFund }) => {
  const { loading, data = [] } = useRequest(() => {
    return getLatestBarraStyle(currentFund._id)
  }, {
    cacheKey: 'getLatestBarraStyle',
  })
  const chartData = [
    {
      name: currentFund.name,
    },
  ]
  const series = chartData.map(item => {
    return {
      pointPlacement: 'on',
      name: item.name,
      data: data.map(item => item.style_exposure_vpct),
    }
  })
  const categories = data.map(item => item.style)
  const chartConfig = {
    chart: {
      polar: true,
      type: 'line',
      height: 240,
    },
    pane: {
      size: '80%',
    },
    tooltip: {
      shared: true,
      pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.2f}</b><br/>',
    },
    legend: {
      enabled: false,
    },
    xAxis: {
      categories,
      tickmarkPlacement: 'on',
      lineWidth: 0,
      labels: {
        useHTML: true,
        formatter: function formatter() {
          const labelValue = this.value + ''
          const start = labelValue.slice(0, 25)
          const end = labelValue.slice(25)
          if (!end) {
            return `<span>${start}</span>`
          }
          return `<span>${start}</span><br/><span>${end}</span>`
        },
      },
    },
    yAxis: {
      gridLineInterpolation: 'polygon',
      min: 0,
      max: 1,
      labels: {
        enabled: false,
        format: '{value}',
      },
    },
    series,
  }
  return (
    <Card
      title="Archimedes风格因子"
    >
      <Spin spinning={loading}>
        <Chart options={chartConfig} />
      </Spin>
    </Card>
  )
}
