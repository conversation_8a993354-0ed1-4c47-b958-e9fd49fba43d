import React, { useState } from 'react'
import round from 'lodash/round'
import { Card, Col, Row, Radio } from 'antd'
import Chart from '@/components/Chart/Chart'
import moment from 'moment'
import getSeriesChartData from '@/utils/getSeriesChartData'

const renderChart = (series) => {
  const chartConfig = {
    chart: {
      type: 'column',
      height: 350,
    },
    yAxis: {
      max: 100,
    },
    navigator: {
      enabled: true,
    },
    scrollbar: {
      enabled: false,
    },
    plotOptions: {
      area: {
        stacking: 'normal',
      },
      column: {
        stacking: 'normal',
        pointPadding: 0,
      },
    },
    tooltip: {
      useHTML: true,
      formatter: function() {
        let tmp = ''
        const length = this.points.length
        const rowCount = 10
        const lineCount = parseInt(length / rowCount) + 1
        tmp += '<table>'
        tmp += `<tr><td>${moment(new Date(this.x)).format('YYYY-MM-DD')}</td></tr>`
        for (let i = 0; i < rowCount; i++) {
          tmp += '<tr>'
          for (let j = 0; j < lineCount && j * rowCount + i  < length; j++) {
            const it = this.points[j * rowCount + i]
            tmp += `
              <td style='padding-right: 20px'>
                <span style='color:${it.color}'>● </span>${it.series.name}:<b>${round(it.y, 2)}%</b>
              </td>
            `
          }
          tmp += '</tr>'
        }
        tmp += '</table>'
        return tmp
      },
      // pointFormat:
      //   '<span style="color:{series.color}">{series.name}</span>: <b>{point.percentage:.2f}%</b> ({point.y:,.0f})<br/>',
    },
    series,
  }
  return <Chart options={chartConfig} constructorType="stockChart" />
}

const ConvtBondRefStock = ({ industryData, sizeData }) => {
  const [viewType, setViewType] = useState('size')
  const handleViewTypeChange = event => {
    setViewType(event.target.value)
  }
  let series = []
  if (viewType === 'industry') {
    series = getSeriesChartData(industryData, 'INDUSTRY', 'BALANCE')
  } else {
    series = getSeriesChartData(sizeData, 'sizeType', 'BALANCE')
  }
  return (
    <Card
      title="可转债正股市值分布"
      extra={
        false && <Radio.Group
          value={viewType}
          size="small"
          onChange={handleViewTypeChange}
        >
          <Radio.Button value="industry">行业偏好</Radio.Button>
          {/* <Radio.Button value="industryBoard">板块偏好</Radio.Button> */}
          <Radio.Button value="size">市值偏好</Radio.Button>
        </Radio.Group>
      }
    >
      <Row gutter={16}>
        <Col lg={24} md={24}>
          {renderChart(series)}
        </Col>
      </Row>
    </Card>
  )
}

export default ConvtBondRefStock
