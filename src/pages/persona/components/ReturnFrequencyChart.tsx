import React, { useState } from 'react'
import {
  Radio,
} from 'antd'
import moment from 'moment'
import createRange from 'lodash/range'
import times from 'lodash/times'
import countByRange from '@/utils/countByRange'
import math from '@/utils/math'
import calculateFundRollingQuotas from '@/utils/calculateRollingQuota'
import Chart from '@/components/Chart/Chart'

const calculateNormalValue = (x, mean, std, size) => {
  const translation = x - mean
  return (Math.exp(-(translation ** 2) / (2 * (std ** 2))) / (std * Math.sqrt(2 * Math.PI))) * size;
}

const getRangeData = (returns) => {
  if (returns.length < 2) {
    return []
  }
  const returnData = returns.map(item => item.value * 100)
  const meanReturn = Math.round(math.mean(returnData))
  const ranges = createRange(-15 + meanReturn, 16 + meanReturn)
  const rangeData = countByRange(returnData, ranges)
  let frequencyValues = []
  Object.keys(rangeData).forEach(key => {
    const values = times(rangeData[key], () => +key)
    frequencyValues = frequencyValues.concat(values)
  })
  frequencyValues = frequencyValues.filter(item => !Number.isNaN(item))
  if (frequencyValues.length < 2) {
    return []
  }
  const meanValue = math.mean(frequencyValues)
  const stdValue = math.std(frequencyValues)
  return ranges.map(range => {
    return {
      range,
      value: rangeData[range] || 0,
      nValue: calculateNormalValue(range, meanValue, stdValue, frequencyValues.length),
    }
  })
}

const ReturnFrequencyChart = ({
  currentFund, currentBenchmark,
  // fundMonthlyReturns, benchmarkMonthlyReturns,
  fundNav, benchmarkNav,
}) => {
  const [viewType, setViewType] = useState('month')
  // const returns = fundMonthlyReturns
  if (fundNav && fundNav.length < 2) {
    return null
  }
  const navMapper = item => {
    return {
      date: moment(new Date(item.date)).format('YYYYMMDD'),
      value: item.value,
    }
  }
  const windowMap = {
    month: 1,
    quarter: 3,
    year: 12,
    '3year': 36,
  }
  const fundMonthlyReturns = calculateFundRollingQuotas((fundNav || []).map(navMapper), windowMap[viewType])
  const benchmarkMonthlyReturns = calculateFundRollingQuotas(benchmarkNav.map(navMapper), windowMap[viewType])
  const fundRangeData = getRangeData(fundMonthlyReturns)
  const benchmarkRangeData = getRangeData(benchmarkMonthlyReturns)
  const series = [
    {
      name: currentFund.name,
      data: fundRangeData.map(range => range.value),
    },
  ]
  if (currentBenchmark) {
    series.push({
      name: currentBenchmark.name,
      data: benchmarkRangeData.map(range => range.value),
    })
  }
  series.push(
    {
      name: 'Normal Curve',
      type: 'spline',
      color: '#F5A623',
      marker: {
        enabled: false,
      },
      tooltip: {
        enabled: false,
      },
      data: fundRangeData.map(range => range.nValue),
    },
  )
  const categories = fundRangeData.map(item => `${item.range}%`)
  const config = {
    chart: {
      type: 'column',
    },
    xAxis: {
      categories: categories,
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Frequency',
      },
      labels: {
        format: '{value}',
      },
    },
    plotOptions: {
      series: {
        animation: false,
      },
      spline: {
        enableMouseTracking: false,
      },
    },
    tooltip: {
      shared: true,
      pointFormat: '{series.name}: <b>{point.y:.0f}</b><br/>',
    },
    series: series,
  }
  return (
    <div>
      <div>
        <span>收益频次统计</span>
        <Radio.Group style={{ float: 'right' }} defaultValue="month" size="small" onChange={(event) => setViewType(event.target.value)}>
          <Radio.Button value="month">月度</Radio.Button>
          <Radio.Button value="quarter">季度</Radio.Button>
          <Radio.Button value="year">年度</Radio.Button>
          <Radio.Button value="3year">3年</Radio.Button>
        </Radio.Group>
      </div>
      <Chart options={config} />
    </div>
  )
}

export default ReturnFrequencyChart
