
import React, { useMemo, useState } from 'react'
import _ from 'lodash'
import Chart from '@/components/Chart/Chart'
import { useRequest } from '@umijs/hooks'
import { getLatestBarraStyleSeries } from '@/services/fund'
import { Spin, Card } from 'antd'
import SelectDate from '@/components/SelectDate'

export default ({ currentFund }) => {
  const { loading, data: dataSeries = [] } = useRequest(() => {
    return getLatestBarraStyleSeries(currentFund._id)
  }, {
    cacheKey: 'getLatestBarraStyleSeries',
  })
  const dates = _.uniq(dataSeries.map(item => item.the_date)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const [date, setDate] = useState(dates[0])
  const initialDate = useMemo(() => {
    return dates[0]
  }, [dates.length])
  const targetDate = date || initialDate
  const data = dataSeries.filter(item => item.the_date === targetDate)
  const chartData = [
    {
      name: currentFund.name,
    },
  ]
  const series = chartData.map(item => {
    return {
      pointPlacement: 'on',
      name: item.name,
      data: data.map(item => item.style_exposure_vpct),
    }
  })
  const categories = data.map(item => item.style)
  const chartConfig = {
    chart: {
      polar: true,
      type: 'line',
      height: 240,
    },
    pane: {
      size: '80%',
    },
    tooltip: {
      shared: true,
      pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.2f}</b><br/>',
    },
    legend: {
      enabled: false,
    },
    xAxis: {
      categories,
      tickmarkPlacement: 'on',
      lineWidth: 0,
      labels: {
        useHTML: true,
        formatter: function formatter() {
          const labelValue = this.value + ''
          const start = labelValue.slice(0, 25)
          const end = labelValue.slice(25)
          if (!end) {
            return `<span>${start}</span>`
          }
          return `<span>${start}</span><br/><span>${end}</span>`
        },
      },
    },
    yAxis: {
      gridLineInterpolation: 'polygon',
      min: 0,
      max: 1,
      labels: {
        enabled: false,
        format: '{value}',
      },
    },
    series,
  }
  return (
    <Card
      title="Archimedes风格因子"
      extra={
        <SelectDate
          date={targetDate}
          dates={dates}
          onChange={setDate}
        />
      }
    >
      <Spin spinning={loading}>
        <Chart options={chartConfig} />
      </Spin>
    </Card>
  )
}
