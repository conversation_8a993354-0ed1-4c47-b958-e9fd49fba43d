import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Table,
  Card,
  Select,
} from 'antd'
import { getDailyConvtBondIndicator } from '@/services/fund'
import buildTableColumn from '@/utils/buildTableColumn'
import sortQuotaFn from '@/utils/sortQuotaFn'

const { Option } = Select

export default ({
  dates,
  fundId,
}: {
  dates: any,
  fundId: string,
}) => {
  const [currentDate, setCurrentDate] = useState(dates[0])
  const { data, loading } = useRequest(() => {
    return getDailyConvtBondIndicator(fundId, { date: currentDate })
  }, {
    refreshDeps: [currentDate],
  })
  const columns = [{
    title: '证券名称',
    dataIndex: 'STOCK_NAME',
    fixed: 'left',
    hasSorter: true,
  }, {
    title: '证券代码',
    dataIndex: 'STOCK_CODE',
    hasSorter: true,
  }, {
    title: '持仓数量',
    dataIndex: 'AMOUNT',
    format: 'commaNumber',
    hasSorter: true,
  }, {
    title: '持仓市值',
    dataIndex: 'BALANCE',
    format: 'commaNumber',
    hasSorter: true,
  }, {
    title: '上市日期',
    dataIndex: 'LIST_DATE',
    format: 'date',
    hasSorter: true,
  }, {
    title: '行业',
    dataIndex: 'INDUSTRY',
    hasSorter: true,
  }, {
    title: '转股溢价率',
    dataIndex: 'CONVPREMIUMRATIO',
    format: 'percentageSuffix',
    hasSorter: true,
  }, {
    title: '纯债溢价率',
    dataIndex: 'STRBPREMIUMRATIO',
    format: 'percentageSuffix',
    hasSorter: true,
  }, {
    title: '正股PE',
    dataIndex: 'PE_TTM',
    format: 'number',
    hasSorter: true,
  }, {
    title: '债券余额(亿)',
    dataIndex: 'OUTSTANDINGBALANCE',
    format: 'hundredMillion',
    hasSorter: true,
  }, {
    title: '近1月成交额(亿)',
    width: 150,
    dataIndex: 'AMOUNT_1M',
    format: 'hundredMillion',
    hasSorter: true,
  }].map(buildTableColumn)
  return (
    <div>
      <Card
        title="可转债持仓明细"
        bordered={false}
        extra={
          <>
            <Select
              showSearch
              value={currentDate}
              // className={styles.rightAction}
              style={{ width: 150, marginLeft: 15 }}
              placeholder="选择日期"
              optionFilterProp="children"
              onChange={setCurrentDate}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {dates.map(item => (
                <Option value={item}>{item}</Option>
              ))}
            </Select>
          </>
        }
      >
        <Table
          size="small"
          columns={columns}
          loading={loading}
          dataSource={data}
          pagination={false}
          scroll={{ y: 450, x: 1300 }}
        />
      </Card>
    </div>
  )
}
