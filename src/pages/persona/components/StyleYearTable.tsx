import React from 'react'
import {
  Table,
  Card,
} from 'antd'
import buildTableColumn from '@/utils/buildTableColumn'

export default ({
  styleYearData,
}: {
  styleYearData: any,
}) => {
  const columns = [{
    title: '资产类型',
    dataIndex: 'assetType',
    fixed: 'left',
    width: 80,
    render: (val, record) => {
      return {
        children: val,
        props: { rowSpan: record.rowSpan || 0 },
      }
    },
  }, {
    title: '风格类型',
    dataIndex: 'styleType',
    fixed: 'left',
    width: 100,
  },
  ...styleYearData.years.map(year => ({ title: year, dataIndex: year })),
  ].map(buildTableColumn)
  return (
    <div>
      <Card
        title="基金风格历史变化"
        size="small"
      >
        <Table
          size="small"
          columns={columns}
          dataSource={styleYearData.data}
          pagination={false}
          scroll={{ x: 1300 }}
        />
      </Card>
    </div>
  )
}
