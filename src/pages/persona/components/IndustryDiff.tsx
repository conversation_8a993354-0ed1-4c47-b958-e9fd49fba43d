import React, { useState } from 'react'
import {
  Select, Card, Space,
} from 'antd'
import _ from 'lodash'
import Chart from '@/components/Chart/Chart'
import ExportData from '@/components/ExportData'

const { Option } = Select

const renderChart = (industryData, viewType) => {
  let data
  const total = industryData.length
  if (viewType === 'headTail5') {
    data = industryData
      .filter((item, index) => {
        return index < 5 || index >= total - 5
      })
  } else {
    data = industryData
  }
  const categories = data.map(item => item.INDUSTRY)
  const height = categories.length * 25
  const chartConfig = {
    chart: {
      type: 'bar',
      height: height > 400 ? height : 400,
    },
    xAxis: {
      categories,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%<br/>',
    },
    series: [{ name: '占比偏差', value: 'DIFF_RATIO' }].map(quota => {
      return {
        name: quota.name,
        data: data.map(item => item[quota.value] * 100),
      }
    }),
  }
  return <Chart options={chartConfig}/>
}

export default ({ industryData, isDetect, indexType }) => {
  const [viewType, setViewType] = useState('headTail5')
  const handleViewTypeChange = newType => {
    setViewType(newType)
    try {
      setTimeout(() => {
        const event = new Event('resize')
        window.dispatchEvent(event)
      }, 200)
    } catch (error) {}
  }
  const latestDate = _.max(industryData.map(item => item.BIZ_DATE))
  const latestData = industryData
    .filter(item => item.BIZ_DATE === latestDate)
    .map(item => {
      return {
        ...item,
        DIFF_RATIO: item.BALANCE_RATIO - item.BASE_RATIO,
      }
    }).sort((fst, snd) => snd.DIFF_RATIO - fst.DIFF_RATIO)
    const exportCols = [{
      title: '行业',
      dataIndex: 'INDUSTRY',
    }, {
      title: '行业占比',
      dataIndex: 'BALANCE_RATIO',
    }, {
      title: isDetect ? '本基金' : indexType,
      dataIndex: 'BASE_RATIO',
    }, {
      title: '占比偏差',
      dataIndex: 'DIFF_RATIO',
    }]
  return (
    <Card
      title={`行业占比偏差(${latestDate})`}
      key={`${latestDate}-${viewType}`}
      extra={
        <Space>
          <Select
            defaultValue={viewType}
            onChange={handleViewTypeChange}
            style={{ width: 160 }}
            size="small"
          >
            <Option value="headTail5">超配前五/低配前五行业</Option>
            <Option value="all">全部行业</Option>
          </Select>
          <ExportData columns={exportCols} dataSource={latestData} filename={`行业占比偏差(${latestDate})`}/>
      </Space>
      }
    >
      {renderChart(latestData, viewType)}
    </Card>
  )
}
