import React, { useState } from 'react'
import {
  Empty,
  Spin,
} from 'antd'
import { useRequest } from '@umijs/hooks'
import _ from 'lodash'
import { getStockIndAllocData } from '@/services/fund'
import IndustryHeatMap from '../components/IndustryHeatMap'

const IndustryAllocationWrapper = ({ currentFund, dataType, dateParams }) => {
  const [positionRange, setPositionRange] = useState('all')
  const { loading, data = [] } = useRequest(() => {
    return getStockIndAllocData(currentFund._id, {
      positionRange,
      ...dateParams,
    })
  }, {
    refreshDeps: [positionRange],
  })
  if (!loading && !data.length && dataType === 'industry') {
    return <Empty style={{ marginTop: 100 }}/>
  }
  return (
    <div>
      <Spin spinning={loading} style={{ minHeight: 400 }}>
        {data.length !== 0 &&
        <IndustryHeatMap dataType={dataType} product={currentFund} data={data} onPositionRangeChange={setPositionRange}/>}
      </Spin>
    </div>
  )
}

export default IndustryAllocationWrapper
