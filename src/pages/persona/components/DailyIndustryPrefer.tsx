import React from 'react'
import { useRequest } from '@umijs/hooks'
import _ from 'lodash'
import {
  Row,
  Col,
  Empty,
  Spin,
} from 'antd'
import { getDailyIndustryPrefer, getDetectIndPreferData } from '@/services/fund'
import IndustryBoxChart from './IndustryBoxChart'
import IndustryPreference from './IndustryPreference'
import IndustryDiff from './IndustryDiff'

export default ({
  bizDate,
  fundId,
  isDetect,
  halfDates,
  indexType,
}: {
  bizDate: any,
  indexType: any,
  fundId: string,
  isDetect?: boolean,
  halfDates?: any,
}) => {
  const { data = { latestIndustryData: [] }, loading } = useRequest(() => {
    if (isDetect) {
      let halfYearDate = _.find(halfDates || [], item => item <= bizDate)
      if (halfYearDate) {
        halfYearDate = halfYearDate.split('-').join('')
      }
      return getDetectIndPreferData(fundId, { bizDate: bizDate.split('-').join(''), halfYearDate })
    } else {
      return getDailyIndustryPrefer(fundId, { bizDate: bizDate.split('-').join(''), indexType })
    }
  }, {
    refreshDeps: [bizDate, indexType],
    cacheKey: `DailyIndustryPref_${bizDate}_${indexType}`,
  })

  if (!loading && data.latestIndustryData.length === 0) {
    return <Empty/>
  }

  return (
    <Spin spinning={loading}>
      <Row gutter={8}>
        <Col xs={24} md={12} xl={12} xxl={12}>
          <IndustryPreference isDetect={isDetect} industryData={data.latestIndustryData} indexType={indexType} />
        </Col>
        <Col xs={24} md={12} xl={12} xxl={12}>
          <IndustryDiff isDetect={isDetect} industryData={data.latestIndustryData} indexType={indexType} />
        </Col>
      </Row>
      {data.latestIndustryData.length &&
      <IndustryBoxChart industryData={data.latestIndustryData} data={data.industryBoxPlotData} avgData={data.avgIndustryBoxPlotData}/>}
    </Spin>
  )
}
