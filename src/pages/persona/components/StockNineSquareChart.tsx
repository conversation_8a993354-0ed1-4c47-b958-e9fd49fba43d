import React, { useState } from 'react'
import { Radio } from 'antd'
import Chart from '@/components/Chart/Chart'

const getHeatMapConfig = data => {
  const heatmapConfig = {
    chart: {
      type: 'heatmap',
      height: 300,
    },
    xAxis: {
      categories: ['价值', '成长', '均衡'],
    },
    yAxis: {
      categories: ['大盘', '中盘', '小盘'],
      labels: {
        format: '{value}',
      },
      title: null,
    },
    colorAxis: {
      min: 0,
      minColor: '#3a404c',
      maxColor: '#4d6cac',
    },
    legend: {
      enabled: false,
    },
    tooltip: {
      enabled: false,
    },
    series: [
      {
        borderWidth: 1,
        data: data || [],
        color: '#3a404c',
        dataLabels: {
          enabled: true,
          color: '#000000',
        },
      },
    ],
  }
  return heatmapConfig
}

export default function ({ fundPositionData }) {
  const [viewType, setViewType] = useState('top10')
  const data = viewType === 'top10' ? fundPositionData.styleTop10HeatMapData : fundPositionData.styleHeatMapData
  const date = viewType === 'top10' ? fundPositionData.latestTop10PositionDate : fundPositionData.latestPositionDate
  return (
    <div>
      <span>{date}</span>
      <Radio.Group
        value={viewType}
        size="small"
        style={{
          float: 'right',
        }}
        onChange={event => setViewType(event.target.value)}
      >
        <Radio.Button value="top10">前十大</Radio.Button>
        <Radio.Button value="all">全部持仓</Radio.Button>
      </Radio.Group>
      <Chart options={getHeatMapConfig(data || [])} />
    </div>
  )
}

