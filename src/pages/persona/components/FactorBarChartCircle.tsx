
import React from 'react'
import _ from 'lodash'
import moment from 'moment'
import Echart from '@/components/Chart/Echarts'

const renderAverageItem = (bandWidth) => (param, api) => {
  // const bandWidth = api.size([0, 0])[0] * 0.4
  const point = api.coord([api.value(0), api.value(1)])
  return {
    type: 'line',
    shape: {
      x1: point[0] - bandWidth / 2,
      x2: point[0] + bandWidth / 2,
      y1: point[1],
      y2: point[1],
    },
    style: api.style({
      fill: null,
      stroke: api.visual('color'),
      lineWidth: 2,
    }),
  }
}

function buildFactorData(origFactorData) {
  return origFactorData.map(factorValue => {
    // factorValue.hpct_min_3y = 93/100
    // factorValue.hpct_max_3y = 100/100
    // factorValue.vpct = 100/100
    // factorValue.hpct_mean_3y = 97/100
    const getValue = value => value ? value * 100 : value
    const min = getValue(factorValue.hpct_min_3y)
    const max = getValue(factorValue.hpct_max_3y)
    const value = getValue(factorValue.vpct)
    const avg = getValue(factorValue.hpct_mean_3y)
    let vMin = min * 0.9
    let vMax = max * 0.9
    let vValue = value * 0.9
    let vAvg = avg * 0.9
    const diff = max - min
    // if (value >= 90) {
    //   vValue = value - 10
    // } else if ((max - value) < 10) {
    //   vValue = max - 14
    // }
    const vMean = (vMin + vMax) / 2
    // F1
    // if (diff < 30) {
    //   if (vMean > vValue) {
    //     vMin = vMin - (vValue - vMin + 15)
    //   } else {
    //     vMax = vMax + (vMax - vValue + 15)
    //   }
    // }
    if (value <= 10) {
      vValue = value / 10 + 10
    }
    // F2
    if ((vMax - vValue) < 20) {
      vMax = vMax + (vMax - vValue + 10)
    }
    if ((vValue - vMin) < 20) {
      vMin = vMin - (vValue - vMin + 10)
    }
    // else if ((value - min) < 10) {
    //   vValue = min + 14
    // }
    // if (avg >= 90) {
    //   vAvg = avg - 10
    // } else if ((max - avg) < 10) {
    //   vAvg = max - 10
    // }
    // if (avg <= 10) {
    //   vAvg = avg + 10
    // } else if ((avg - min) < 10) {
    //   vAvg = min + 10
    // }
    // if (diff <= 25) {
    //   vMin = _.max([vValue - 14, 0])
    //   vMax = _.min([vValue + 14, 100])
    //   vAvg = avg + (vValue - value) / 2
    // }
    return {
      name: factorValue.name,
      date: moment(factorValue.factor_date).format('YYYY-MM-DD'),
      avg,
      min,
      max,
      value,
      vMin,
      vMax,
      vValue,
      vAvg,
    }
  })
}

export default ({ factorData, width, height, showLabel }) => {
  const barWidth = width || 60
  const factors = buildFactorData(factorData)
  const options = {
    // backgroundColor: '#20252E',
    grid: {
      top: '2%',
      left: '0%',
      right: '0%',
      bottom: '2%',
      containLabel: true,
    },
    color: ['#15477B', '#3FF5E6', '#FFCC54'],
    tooltip: {
      trigger: 'axis',
      confine: true,
      axisPointer: {
        type: 'line',// 默认为直线，可选为：'line' | 'shadow'
      },
      formatter:function(p){
        if (p[0].data.value === undefined) {
          return p[0].name+'<br>'
        }
        var text = p[0].name+'<br>'+'数据日期：'+p[0].data.date+'<br>'+p[0].seriesName+'：'+_.round(p[0].data.tValue, 2)+'&nbsp;&nbsp;&nbsp;'+'<br>'+p[1].seriesName+'：'+_.round(p[1].value[2], 2)+'&nbsp;&nbsp;&nbsp;'+'<br>'+p[3].seriesName+'：'+_.round(p[3].value[2], 2)+'&nbsp;&nbsp;&nbsp;'+'<br>'+'历史最高值'+'：'+_.round(parseFloat(p[4].value[2]), 2)+'&nbsp;&nbsp;&nbsp;'+'<br>';
        return text;
      },
    },
    series: [
      {
        // 该系列为背景深蓝色半胶囊
        name: 'factor name',
        data: factors.map(() => 100),
        type: 'bar',
        xAxisIndex: 0,
        silent: true,
        itemStyle: {
          borderColor: '#502d20',
          barBorderRadius: 100,
          borderWidth: 2,
          color: 'rgba(0,0,0,0)',
        },
        barWidth: barWidth,
        tooltip: {
          show: false,
        },
      },
      {
        name: '历史最低值',
        barWidth: barWidth - 4,
        type: 'bar',
        stack: '浮动',
        xAxisIndex: 1,
        itemStyle: {
          normal: {
            barBorderColor: 'rgba(0,0,0,0)',
            color: 'rgba(0,0,0,0)',
          },
          emphasis: {
            barBorderColor: 'rgba(0,0,0,0)',
            color: 'rgba(0,0,0,0)',
          },
        },
        data: factors.map(item => [0, item.vMin, item.min]),
      },
      {
        name: '最高最低区间',
        data: factors.map(item => [0, item.vMax - item.vMin, item.max]),
        type: 'bar',
        xAxisIndex: 1,
        z: 2,
        stack: '浮动',
        itemStyle: {
          barBorderRadius: 35,
          color: '#81431d',
          // color: {
          //   type: 'linear',
          //   x: 0,
          //   y: 0,
          //   x2: 0,
          //   y2: 1,
          //   colorStops: [{
          //     offset: 0,
          //     color: '#E4F634', // 0% 处的颜色
          //   }, {
          //     offset: 0.25,
          //     color: '#EEFC5D',
          //   }, {
          //     offset: 0.5,
          //     color: '#F8974E',
          //   }, {
          //     offset: 0.75,
          //     color: '#FFBC88',
          //   }, {
          //     offset: 1,
          //     color: '#FD9B50', // 100% 处的颜色
          //   }],
          // },
        },
        barWidth: barWidth - 4,
      },
      {
        name: '因子值',
        type: 'scatter',
        hoverAnimation: false,
        // xAxisIndex: 2,
        symbolOffset: [0, 0], //相对于原本位置的偏移量
        symbolSize: barWidth - 4,
        z: 2,
        label: {
          show: false,
          position: 'top',
          fontSize: 16,
        },
        itemStyle: {
          color: '#e75d02',
          borderColor: 'rgba(215,100,41,1)',
        },
        data: factors.map(item => {
          return {
            date: item.date,
            name: item.name,
            tValue: item.value,
            value: item.vValue,
          }
        }),
      },
      // {
      //   name: '历史平均值',
      //   type: 'pictorialBar',
      //   symbolSize: [56, 6],
      //   symbolOffset: [0, -5],
      //   z: 12,
      //   // itemStyle: {
      //   //     normal: {
      //   //         color: function(params) {
      //   //             return barTopColor[params.dataIndex];
      //   //         }
      //   //     }
      //   // },
      //   itemStyle: {
      //     color: '#FFF',
      //     borderColor: '#FFF',
      //     barBorderRadius: 30,
      //     borderWidth: 1,
      //   },
      //   label: {
      //     show: false,
      //     position: 'top',
      //     fontSize: 16,
      //   },
      //   symbolPosition: 'end',
      //   data: factors.map(item => item.avg || 0.00001),
      // },
      {
        type: 'custom',
        name: '历史平均值',
        renderItem: renderAverageItem(barWidth),
        encode: {
          x: 0,
          y: 1,
        },
        z: 12,
        itemStyle: {
          color: '#fff',
        },
        data: factors.map(item => [0, item.vAvg, item.avg]),
      },
      {
        name: '历史平均值',
        type: 'scatter',
        hoverAnimation: false,
        // xAxisIndex: 2,
        symbolOffset: [0, 0],
        symbolSize: 15,
        z: 12,
        label: {
          show: false,
          position: 'top',
          fontSize: 14,
        },
        itemStyle: {
          color: '#fff',
        },
        data: factors.map(item => [0, item.vAvg, item.avg]),
      },
    ],
    dataZoom: {
      type: 'inside',
      xAxisIndex: [0, 1],
    },
    legend: {
      show: false,
      itemWidth: 10,
      top: 15,
      right: 25,
      textStyle: {
        color: '#fff',
      },
    },
    xAxis: [{
      // show: false,
      data: factors.map(item => item.name),
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: !!showLabel,
      },
      axisTick: {
        show: false,
      },
      position: 'bottom',
      type: 'category',
    },
    {
      show: false,
      data: factors.map(item => item.value),
      axisLine: {
        lineStyle: {
          color: '#00D2FF',
        },
      },
      axisLabel: {
        color: '#fff',
      },
      axisTick: {
        show: false,
      },
      position: 'bottom',
    },
    ],
    yAxis: {
      show: false,
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#00D2FF',
          opacity: 0.1,
        },
      },
    },
  }
  return <Echart style={{ height: height || 350 }} options={options} />
}
