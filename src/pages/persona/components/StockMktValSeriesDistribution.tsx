import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Card, Spin,
} from 'antd'
import getSeriesChartData from '@/utils/getSeriesChartData'
import { getStockSizeSeries, getConvtBondSizeSeries } from '@/services/fund'
import Chart from '@/components/Chart/Chart'
import _ from 'lodash'
import moment from 'moment'
import BalanceDistribution from './BalanceDistribution'

const buildAreaSeriesConfig = (data) => {
  const series = getSeriesChartData(data, 'name', 'ratio')
  const seriesChartConfig = {
    chart: {
      type: 'column',
      height: 300,
    },
    yAxis: {
      max: 100,
    },
    navigator: {
      enabled: false,
    },
    scrollbar: {
      enabled: false,
    },
    plotOptions: {
      area: {
        stacking: 'normal',
        lineWidth: 1,
        marker: {
          lineWidth: 2,
          lineColor: '#ffffff',
        },
      },
      column: {
        stacking: 'normal',
      },
    },
    tooltip: {
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%</b><br/>',
    },
    series,
  }
  return seriesChartConfig
}

export default ({
  currentFund,
  isConvtBond,
}: {
  currentFund: any,
  isConvtBond?: boolean,
}) => {
  let filters = []
  if (!isConvtBond) {
    filters = [{
      name: '市值',
      value: 'mkt_size',
    }, {
      name: '市值排名',
      value: 'size_rank',
    }]
  }
  const [type, setType] = useState('mkt_size')
  const fundId = currentFund._id
  const dateParams = { type }
  if (currentFund.isManager) {
    if (currentFund.ref_fund_start_date) {
      dateParams.startDate = currentFund.ref_fund_start_date
    }
    if (currentFund.ref_fund_end_date) {
      dateParams.endDate = currentFund.ref_fund_end_date
    }
  } else if (currentFund._syncType === 'activeFund') {
    dateParams.endDate = moment(currentFund.navEndDate).format('YYYYMMDD')
    dateParams.startDate = moment(currentFund.navEndDate).subtract(6, 'month').format('YYYYMMDD')
  }
  const { data = [], loading } = useRequest(() => {
    return isConvtBond ? getConvtBondSizeSeries(fundId, dateParams) : getStockSizeSeries(fundId, dateParams)
  }, {
    refreshDeps: [type]
  })
  return (
    <Spin spinning={loading}>
      <BalanceDistribution
        title="股票市值分布"
        nameKey="name"
        data={data}
        hideBalance={true}
        filters={filters}
        seriesNames={['大盘', '中盘', '小盘']}
        onFilterChange={setType}
      />
    </Spin>
  )
}
