import React, { useState } from 'react'
import {
  Select, Card, Space, Radio,
} from 'antd'
import AreaPieChart from '@/components/AreaPieChart'

const { Option } = Select

export default ({
  title,
  data,
  nameKey,
  filters,
  getData,
  hideBalance,
  balanceRatioName,
  seriesNames,
  onFilterChange,
}: {
  title: string,
  data: any,
  nameKey?: string,
  filters?: any,
  onFilterChange?: any,
  getData?: any,
  hideBalance?: boolean,
  balanceRatioName?: string,
  seriesNames?: any,
}) => {
  const [ratioKey, setRatioKey] = useState('BALANCE_RATIO')
  const defaultFilterValue = filters ? filters[0].value : ''
  const [dataFiler, setDataFilter] = useState(defaultFilterValue)
  const rows = typeof getData === 'function' ? getData(data, dataFiler) : data
  let pctValueKey = ''
  if (hideBalance || ratioKey === 'BALANCETONAV') {
    pctValueKey = ratioKey
  }
  console.log(rows)
  return (
    <Card
      title={title}
      extra={
        <Space>
          {filters && filters.length > 1 &&
          <Radio.Group defaultValue={defaultFilterValue} size="small" onChange={(event) => {
            setDataFilter(event.target.value)
            onFilterChange && onFilterChange(event.target.value)
          }}>
            {filters.map(item => <Radio.Button value={item.value}>{item.name}</Radio.Button>)}
          </Radio.Group>}
          <Select size="small" defaultValue="BALANCE_RATIO" style={{ width: 110 }} onChange={setRatioKey}>
            <Option value="BALANCE_RATIO">{balanceRatioName || '占股票市值'}</Option>
            <Option value="BALANCETONAV">占净资产</Option>
          </Select>
        </Space>
      }
    >
      <AreaPieChart
        disablePie
        showNavigator
        autoMaxYHeight={ratioKey === 'BALANCETONAV'}
        key={ratioKey}
        height={300}
        rows={rows}
        nameKey={nameKey || 'assetType'}
        valueKey="BALANCE"
        hideBalance={hideBalance}
        pctValueKey={pctValueKey}
        seriesNames={seriesNames}
      />
    </Card>
  )
}
