import React from 'react'
import moment from 'moment'
import _ from 'lodash'
import {
  Row, Col,
} from 'antd'
import BalanceDistribution from './BalanceDistribution'

const getQuarterList = (startDate, endDate) => {
  let end = endDate
  let temp = moment(startDate).endOf('quarter').format('YYYYMMDD')
  const results = []
  while (temp <= end) {
    results.push(temp)
    temp = moment(temp).add(1, 'quarter').endOf('quarter').format('YYYYMMDD')
  }
  return results.filter(item => item >= startDate && item <= endDate)
}

const fillGapQuarter = (data, nameFiled) => {
  let results = data
  const dates = data.map(item => item.BIZ_DATE)
  const startDate = _.min(dates)
  const endDate = _.max(dates)
  const names = _.uniq(data.map(item => item[nameFiled]))
  const allDates = getQuarterList(startDate, endDate)
  if (dates.length !== allDates.length) {
    allDates.forEach(date => {
      if (!dates.includes(date)) {
        const fillData = names.map(name => {
          return {
            BALANCE: 0,
            BALANCETONAV: 0,
            BALANCE_RATIO: 0,
            BIZ_DATE: date,
            [nameFiled]: name,
            chartPctValue: 0,
          }
        })
        results = results.concat(fillData)
      }
    })
  }
  return results.sort((fst, snd) => {
    return fst.BIZ_DATE - snd.BIZ_DATE
  })
}

const fillBondData = (bondData) => {
  bondData.classData = fillGapQuarter(bondData.classData || [], 'bondClass')
  bondData.creditTermData = fillGapQuarter(bondData.creditTermData || [], 'termType')
  bondData.noneCreditTermData = fillGapQuarter(bondData.noneCreditTermData || [], 'termType')
  bondData.ratingData = fillGapQuarter(bondData.ratingData || [], 'ratingType')
  bondData.ratingDataCB = fillGapQuarter(bondData.ratingDataCB || [], 'ratingType')
  return bondData
}

const PureBondAnalyze = ({ bondData: bondDataOrig, isMutual }) => {
  const bondData = fillBondData(bondDataOrig)
  const getClassData = (bondData, classType) => {
    if (classType === 'all') {
      return bondData.classData
    }
    return bondData.classData.filter(item => {
      if (classType === 'credit') {
        return item.creditType === '信用债'
      } else {
        return item.creditType !== '信用债'
      }
    })
  }
  return (
    <>
      <BalanceDistribution
        isMutual={isMutual}
        title="券种分布"
        nameKey="bondClass"
        data={bondData}
        filters={!isMutual && [
          {name: '大类', value: 'all'},
          {name: '信用债', value: 'credit'},
          {name: '利率债', value: 'interestRate'},
        ]}
        getData={getClassData}
        balanceRatioName="占债券市值"
      />
      <Row gutter={16}>
        <Col lg={12} md={24}>
          <BalanceDistribution
            isMutual={isMutual}
            title="期限分布"
            nameKey="termType"
            data={bondData}
            filters={[
              {name: '信用债', value: 'credit'},
              {name: '利率债', value: 'interestRate'},
            ]}
            getData={(bondData, creditType) => creditType === 'credit' ? bondData.creditTermData : bondData.noneCreditTermData}
            balanceRatioName="占债券市值"
          />
        </Col>
        <Col lg={12} md={24}>
          <BalanceDistribution
            isMutual={isMutual}
            title="信用债信用评级分布"
            filters={[
              {name: '全部', value: 'all'},
              {name: '中债', value: 'CB'},
            ]}
            data={bondData}
            getData={(bondData, creditType) => creditType === 'all' ? bondData.ratingData : bondData.ratingDataCB}
            nameKey="ratingType"
            balanceRatioName="占债券市值"
          />
        </Col>
      </Row>
    </>
  )
}

export default PureBondAnalyze
