import React, { useState } from 'react'
import { useBoolean, useRequest } from '@umijs/hooks'
import { Tag } from 'antd'
import Chart from '@/components/Chart/Chart'
import * as calculator from '@/utils/calculator'
import guessFrequency from '@/utils/guessFrequency'
import moment from 'moment'
import StandardTable from '@/components/StandardTable'
import { getScenarios } from '@/services/fund'

function calculateScenariosData(scenarios, fundList) {
  const data = scenarios.reduce((out, scenario) => {
    const { startDate, endDate } = scenario
    const scenarioFilter = item => item.date >= startDate && item.date <= endDate
    const valueMapper = item => item.value
    const dataByScenario = fundList.map(item => {
      const nets = !item.nets.length ? [] : item.nets.filter(scenarioFilter)
      const ret = {
        name: scenario.name,
        fundName: item.name,
      }
      if (nets.length < 3) {
        return ret
      }
      const frequency = guessFrequency(nets)
      const returns = calculator.calculateReturns(nets)
      ret.cucmulativeValue = nets[nets.length - 1].value / nets[0].value - 1
      ret.vol = calculator.vol(returns.map(valueMapper), frequency)
      ret.maxDrawdown = calculator.maxDrawdown(nets.map(valueMapper))
      return ret
    })
    return out.concat(dataByScenario)
  }, [])
  return data
}

function getSelectedScenarios(scenarios, fundList) {
  const scenarioData = scenarios.map(item => {
    const ret = {
      ...item,
      startDate: +moment(new Date(item.startDate)).startOf('date'),
    }
    if (item.endToNow) {
      ret.endDate = +moment()
    } else {
      ret.endDate = +moment(new Date(item.endDate)).startOf('date')
    }
    return ret
  })
  const startDate = Math.min.apply(
    null,
    fundList.map(item => !!item.nets.length && item.nets[0].date),
  )
  const endDate = Math.max.apply(
    null,
    fundList.map(item => !!item.nets.length && item.nets[item.nets.length - 1].date),
  )
  return scenarioData
    .filter(item => item.startDate >= startDate && item.startDate < endDate)
}

const CheckableTag = ({
  tag,
  onChange,
}: {
  tag: any,
  onChange: any,
}) => {
  const { state: checked, toggle } = useBoolean(true)
  return (
    <Tag onClick={() => {
      toggle()
      onChange(!checked)
    }} color={checked ? tag.color : undefined} key={tag.name}>
      {tag.name}
    </Tag>
  )
}

export default ({
  currentFund,
  currentBenchmark,
  fundNav,
  benchmarkNav,
}: {
  currentFund: any,
  currentBenchmark: any,
  scenarios: any,
  fundNav: any,
  benchmarkNav: any,
}) => {
  const fundList = [currentFund]
  const fundDataList = [
    {
      name: currentFund.name,
      nets: fundNav || [],
    },
  ]
  if (currentBenchmark) {
    fundList.push(currentBenchmark)
    fundDataList.push({
      name: currentBenchmark.name,
      nets: benchmarkNav || [],
    })
  }
  const [checkedMap, setCheckedMap] = useState({})
  const { data: scenarios } = useRequest(getScenarios,  {
    onSuccess: (result) => {
      const checkedMapData = result.list.reduce((out, item) => {
        out[item.name] = true
        return out
      }, {})
      setCheckedMap(checkedMapData)
    },
  })
  const handleTagClick = (item: any) => (checked: boolean) => {
    setCheckedMap({
      ...checkedMap,
      [item.name]: checked,
    })
  }
  const selectedScenarios = getSelectedScenarios(scenarios && scenarios.list || [], fundDataList)
  const scenarioData = calculateScenariosData(selectedScenarios, fundList)
  const plotBands = selectedScenarios.filter(item => checkedMap[item.name]).map(scenario => {
    const { startDate, endDate, color } = scenario
    return {
      color,
      from: startDate,
      to: endDate,
    }
  })
  const chartConfig = {
    chart: {
      type: 'line',
    },
    series: fundDataList.map(fund => {
      return {
        name: fund.name,
        data: !fund.nets.length ? [] : fund.nets.map(item => [item.date, item.value]),
      }
    }),
    yAxis: [
      {
        title: {
          text: '累计收益',
        },
        labels: {
          format: '{value}%',
        },
      },
    ],
    plotOptions: {
      series: {
        compare: 'percent',
      },
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.y:.2f}({point.change:.2f}%)</b><br/>',
    },
    xAxis: {
      plotBands,
    },
  }
  const tableColumns = [
    {
      title: '',
      dataIndex: 'fundName',
    },
    {
      title: '情景',
      dataIndex: 'name',
    },
    {
      title: '累计收益',
      dataIndex: 'cucmulativeValue',
      format: 'percentage',
      align: 'right',
    },
    {
      title: '波动性',
      dataIndex: 'vol',
      format: 'percentage',
      align: 'right',
    },
    {
      title: '最大回撤',
      dataIndex: 'maxDrawdown',
      format: 'percentage',
      align: 'right',
    },
  ]

  return (
    <div>
      <div style={{ margin: '15px 0', textAlign: 'right' }}>
        {selectedScenarios.map(scenario => (
          <CheckableTag tag={scenario} onChange={handleTagClick(scenario)} />
        ))}
      </div>
      <Chart options={chartConfig} constructorType="stockChart" />
      <StandardTable
        columns={tableColumns}
        data={{ list: scenarioData }}
        size="small"
        disableRowSlection
        scroll={{ y: 400 }}
      />
    </div>
  )
}
