import React, { useState, useMemo } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import _ from 'lodash'
import moment from 'moment'
import {
  Radio, Card, Modal, Button, Spin, Select, Space, Tooltip,
} from 'antd'
import Chart from '@/components/Chart/Chart'
import { getSwIndustryExReturns } from '@/services/fund'
import Echart from '@/components/Chart/Echarts'
import math from '@/utils/math'
import getIndustryBoardData from '@/utils/getIndustryBoardData'
import { exportNArrayDataAsExcel } from '@/utils/exportAsExcel'
import ExportData from '@/components/ExportData'

const { Option } = Select
const industryList = [
  { _qutkeId: '801770', name: '通信' },
  { _qutkeId: '801750', name: '计算机' },
  { _qutkeId: '801760', name: '传媒' },
  { _qutkeId: '801080', name: '电子' },
  { _qutkeId: '801780', name: '银行' },
  { _qutkeId: '801180', name: '房地产' },
  { _qutkeId: '801790', name: '非银金融' },
  { _qutkeId: '801150', name: '医药生物' },
  { _qutkeId: '801200', name: '商贸零售' },
  { _qutkeId: '801140', name: '轻工制造' },
  { _qutkeId: '801010', name: '农林牧渔' },
  { _qutkeId: '801210', name: '社会服务' },
  { _qutkeId: '801120', name: '食品饮料' },
  { _qutkeId: '801130', name: '纺织服饰' },
  { _qutkeId: '801110', name: '家用电器' },
  { _qutkeId: '801880', name: '汽车' },
  { _qutkeId: '801980', name: '美容护理' },
  { _qutkeId: '801720', name: '建筑装饰' },
  { _qutkeId: '801160', name: '公用事业' },
  { _qutkeId: '801170', name: '交通运输' },
  { _qutkeId: '801970', name: '环保' },
  { _qutkeId: '801230', name: '综合' },
  { _qutkeId: '801710', name: '建筑材料' },
  { _qutkeId: '801030', name: '基础化工' },
  { _qutkeId: '801040', name: '钢铁' },
  { _qutkeId: '801050', name: '有色金属' },
  { _qutkeId: '801950', name: '煤炭' },
  { _qutkeId: '801960', name: '石油石化' },
  { _qutkeId: '801730', name: '电力设备' },
  { _qutkeId: '801890', name: '机械设备' },
  { _qutkeId: '801740', name: '国防军工' },
]

const industryBoardList = [{
  name: 'TMT',
}, {
  name: '金融',
}, {
  name: '医药生物',
}, {
  name: '消费',
}, {
  name: '公共产业',
}, {
  name: '周期中游',
}, {
  name: '周期上游',
}, {
  name: '周期下游',
}]

const getModalChartConfig = (indsutryData, exReturnData) => {
  const chartConfig = {
    chart: {
      type: 'column',
      height: 350,
    },
    navigator: {
      enabled: true,
    },
    scrollbar: {
      enabled: false,
    },
    yAxis: [
      {
        labels: {
          format: '{value}%',
        },
      },
      {
        labels: {
          format: '{value}%',
        },
        opposite: true,
      },
    ],
    series: [
      {
        yAxis: 0,
        type: 'column',
        name: '行业占比',
        data: indsutryData,
      },
      {
        yAxis: 1,
        type: 'line',
        name: '行业指数超额',
        data: exReturnData,
        tooltip: {
          pointFormat: '{series.name}: <b>{point.y:.2f}%<br/>',
        },
      },
    ],
  }
  return chartConfig
}

export default ({
  data: rawData,
  product,
  dataType,
  balanceRatioName,
  isSectionData,
  loading,
  isDetect,
  onPositionRangeChange,
}: {
  data: any,
  product: any,
  dataType: any,
  balanceRatioName?: string,
  isSectionData?: boolean,
  loading?: boolean,
  isDetect?: boolean,
  onPositionRangeChange?: any,
}) => {
  const isMutual = product && product._syncType === 'mutual' && !isDetect
  const data = dataType === 'industry' ? rawData : getIndustryBoardData(rawData)
  const [ratioKey, setRatioKey] = useState('BALANCE_RATIO')
  const defaultFrequency = isSectionData ? 'daily' : 'monthly'
  const [frequency, setFrequency] = useState(defaultFrequency)
  const [dataRange, setDataRange] = useState('halfYear')
  const hasTop10Select = !isSectionData && !isDetect
  let dates = _.uniq(data.map(item => item.BIZ_DATE))
    .sort((fst, snd) => fst - snd)
    .map(dateStr => {
      const date = moment(dateStr)
      return {
        dateStr,
        month: date.format('YYYYMM'),
        quarter: date.endOf('quarter').format('YYYYMMDD'),
      }
    })
  if (isMutual && dataRange === 'halfYear') {
    dates = dates.filter(item => /(0630|1231)$/.test(item.dateStr))
  }
  if (frequency === 'monthly') {
    dates = _.map(_.groupBy(dates, 'month'), values => {
      return values[values.length - 1].dateStr
    })
  } else if (frequency === 'quarterly') {
    dates = _.map(_.groupBy(dates, 'quarter'), values => {
      return values[values.length - 1].dateStr
    })
  } else {
    dates = dates.map(item => item.dateStr)
  }
  const chartHeight = dataType === 'industry' ? 900 : 350
  const clientWidth = document.body.clientWidth || 1500
  const labelCount = clientWidth / 40
  const labelPct = labelCount / (dates.length || 1) * 100
  const currentPctRange = 20
  const defaultZoomRange = { start: 80, end: 100 }
  if (labelPct > 100) {
    defaultZoomRange.start = 0
  }
  const [showLabel, setShowLabel] = useState(currentPctRange <= labelPct)
  const [zoomRange, setZoomRange] = useState(defaultZoomRange)
  useMemo(() => {
    setShowLabel(currentPctRange <= labelPct)
    if (labelPct > 100) {
      setZoomRange({
        ...defaultZoomRange,
        start: 0,
      })
    } else {
      setZoomRange({
        ...defaultZoomRange,
        start: 80,
      })
    }
  }, [frequency, dataRange])
  const industries = (dataType === 'industry' ? industryList : industryBoardList).map(item => item.name).reverse()
  const handleFreqChange = (event) => {
    const freq = event.target.value
    setFrequency(freq)
  }
  // const [dataMap] = useState(() => {
  //   return data.reduce((out, item) => {
  //     out[`${item.INDUSTRY}-${item.BIZ_DATE}-BALANCE_RATIO`] = item.BALANCE_RATIO
  //     out[`${item.INDUSTRY}-${item.BIZ_DATE}-BALANCETONAV`] = item.BALANCETONAV
  //     return out
  //   }, {})
  // })
  const dataMap = data.reduce((out, item) => {
    out[`${item.INDUSTRY}-${item.BIZ_DATE}-BALANCE_RATIO`] = item.BALANCE_RATIO
    out[`${item.INDUSTRY}-${item.BIZ_DATE}-BALANCETONAV`] = item.BALANCETONAV
    return out
  }, {})
  const [currentIndustry, setCurrentIndustry] = useState('')
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const {
    data: industryExReturns = [],
    run: runGetSwIndustryExReturns,
    loading: loadingExReturns,
  } = useRequest((industry) => {
    return getSwIndustryExReturns({ industry })
  }, {
    manual: true,
  })
  const handleNameClick = (industry) => {
    setVisibleTrue()
    runGetSwIndustryExReturns(industry)
    setCurrentIndustry(industry)
  }
  const indsutryData = rawData
    .filter(item => item.INDUSTRY === currentIndustry)
    .map(item => [
      +moment(item.BIZ_DATE).startOf('date'),
      item[ratioKey] * 100,
    ])
  const exReturnData = industryExReturns.map(item => {
    return [
      item.date, item.value * 100,
    ]
  })
  const values = data.map(item => item[ratioKey] * 100)
  const std = values.length && math.standardDeviation(values)
  const heatmapData = []
  for (let i = 0; i < dates.length; i++) {
    for (let j = 0; j < industries.length; j++) {
      const key = `${industries[j]}-${dates[i]}`
      let value = dataMap[`${key}-${ratioKey}`] || 0
      if (value) {
        value = _.round(value * 100, 2)
      }
      heatmapData.push([i, j, value])
    }
  }
  const options = {
    tooltip: {
      position: 'top',
      formatter: function (params) {
        const data = params.data || []
        const date = dates[data[0]]
        const industry = industries[data[1]]
        if (data[2] === 0) {
          return
        }
        return `${industry}<br/>${date}: ${data[2]}%`
      },
    },
    // animation: false,
    grid: {
      // height: '100%',
      top: 30,
      right: 10,
      bottom: 30,
    },
    xAxis: {
      position: 'top',
      type: 'category',
      data: dates,
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: 'category',
      data: industries,
      splitArea: {
        show: true,
      },
    },
    visualMap: {
      min: 0,
      max: Math.ceil(std * 3),
      calculable: true,
      orient: 'horizontal',
      top: '0',
      right: '10',
      inRange: {
        // color: ['#0EBF9C', 'rgba(3,4,5,0.4)', '#E85655'],
        color: ['#0EBF9C', '#e49732', '#e85654'],
      },
      show: false,
    },
    dataZoom: [
      // {
      //   type: 'inside',
      //   start: 0,
      //   end: 20,
      // },
      {
        show: true,
        height: 20,
        type: 'slider',
        bottom: '0',
        xAxisIndex: [0],
        ...zoomRange,
      },
    ],
    series: [{
      name: '行业配置',
      type: 'heatmap',
      data: heatmapData,
      label: {
        show: showLabel,
        formatter: (params) => {
          const data = params.data || []
          if (data[2] === 0) {
            return ''
          }
          return data[2]
        }
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    }],
  }
  const onEvents = {
    datazoom: (action) => {
      const currentRange = action.end - action.start
      const show = currentRange <= labelPct
      if (showLabel !== show) {
        setShowLabel(show)
        setZoomRange({
          start: action.start,
          end: action.end,
        })
      }
    },
    click: (action) => {
      const value = action.value
      if (dataType === 'industry' && !isSectionData) {
        handleNameClick(industries[value[1]])
      }
    },
  }
  const freqs = [{
    title: '季频',
    dataIndex: 'quarterly',
  }, {
    title: '月频',
    dataIndex: 'monthly',
  }, {
    title: '日频',
    dataIndex: 'daily',
  }]
  const title = dataType === 'industry' ? '行业配置' : '行业板块配置'
  const handleExportData = () => {
    const exportData = [
      ['', ...dates],
    ]
    for (let i = 0; i < industries.length; i++) {
      const row = [industries[i]]
      for (let j = 0; j < dates.length; j++) {
        const key = `${industries[i]}-${dates[j]}`
        row.push(dataMap[`${key}-${ratioKey}`])
      }
      exportData.push(row)
    }
    exportNArrayDataAsExcel(title, exportData)
  }
  return (
    <Spin spinning={!!loading}>
      <div style={{ marginBottom: 15, minHeight: 200 }}>
        <Card
          className="nav-tab-wrapper"
          title={
            title
          }
          extra={
            <Space>
              {!isMutual && !isSectionData &&
              <Radio.Group
                defaultValue={defaultFrequency}
                size="small"
                onChange={handleFreqChange}
              >
                {freqs.map(item => {
                  return <Radio.Button style={{ zIndex: 1 }} value={item.dataIndex}>{item.title}</Radio.Button>
                })}
              </Radio.Group>}
              {isMutual &&
              <Select size="small" defaultValue="halfYear" style={{ width: 120 }} onChange={setDataRange}>
                <Option value="halfYear">半年报</Option>
                <Option value="all">半年报+季报</Option>
              </Select>}
              <Select size="small" defaultValue="BALANCE_RATIO" style={{ width: 120 }} onChange={setRatioKey}>
                <Option value="BALANCE_RATIO">{balanceRatioName || '占股票市值'}</Option>
                <Option value="BALANCETONAV">占净资产</Option>
              </Select>
              {hasTop10Select &&
              <Select size="small" defaultValue="all" style={{ width: 120 }} onChange={(value) => onPositionRangeChange && onPositionRangeChange(value)}>
                <Option value="all">全部持仓</Option>
                <Option value="top10">前十大持仓</Option>
              </Select>}
              <Tooltip title="点击导出数据">
                <ExportData onClick={handleExportData}/>
              </Tooltip>
            </Space>
          }
        >
        </Card>
        <Echart style={{ height: `${chartHeight}px` }} options={options} onEvents={onEvents} />
        <Modal
          title={
            <>
              <span>{currentIndustry}</span>
            </>
          }
          visible={visible}
          onCancel={setVisibleFalse}
          width={1000}
          footer={[
            <Button
              type="primary"
              onClick={setVisibleFalse}
            >
              关闭
            </Button>,
          ]}
        >
          <Spin spinning={loadingExReturns}>
            <Chart options={getModalChartConfig(indsutryData, exReturnData)} constructorType="stockChart" />
          </Spin>
        </Modal>
      </div>
    </Spin>
  )
}
