import React, { useState } from 'react'
import {
  Card,
  Spin,
  Table,
  Space,
} from 'antd'
import { useRequest } from '@umijs/hooks'
import { queryChildFactorSchemas, queryChildFactorData } from '@/services/fund'
import SearchSelect from '@/components/SearchSelect'
import buildTableColumn from '@/utils/buildTableColumn'

const ChildFundFactorScore = ({ product, schemaList }) => {
  const [schema, setSchema] = useState(schemaList[0])
  const [factorDate, setFactorDate] = useState(schema.dateList[0])
  const { data, loading } = useRequest(() => {
    return queryChildFactorData(product._id, {
      factorDate,
      schemaId: schema._id,
    })
  }, {
    refreshDeps: [factorDate, schema._id],
  })
  const columns = [{
    title: '名称',
    dataIndex: 'name',
    fixed: 'left',
    width: 120,
  }, {
    title: '收益类',
    dataIndex: 'incomeFactorScore',
    format: 'zeroNotNumber',
    width: 80,
    hasSorter: true,
  }, {
    title: '风险类',
    dataIndex: 'riskFactorScore',
    format: 'zeroNotNumber',
    width: 80,
    hasSorter: true,
  }, {
    title: '归因类',
    dataIndex: 'attributionFactorScore',
    format: 'zeroNotNumber',
    width: 80,
    hasSorter: true,
  }, {
    title: '策略类',
    dataIndex: 'strategyFactorScore',
    format: 'zeroNotNumber',
    width: 80,
    hasSorter: true,
  }, {
    title: '基金公司类',
    dataIndex: 'companyFactorScore',
    format: 'zeroNotNumber',
    width: 100,
    hasSorter: true,
  }, {
    title: '基金经理类',
    dataIndex: 'managerFactorScore',
    format: 'zeroNotNumber',
    width: 100,
    hasSorter: true,
  }, {
    title: '持仓类',
    dataIndex: 'positionFactorScore',
    format: 'zeroNotNumber',
    width: 80,
    hasSorter: true,
  }, {
    title: '综合得分',
    dataIndex: 'totalFactorScore',
    format: 'zeroNotNumber',
    width: 90,
    hasSorter: true,
  }, {
    title: '综合得分排名',
    dataIndex: 'totalFactorScoreRank',
    format: 'zeroNotNumber',
    width: 120,
    hasSorter: true,
  }].map(buildTableColumn)
  return (
    <Card
      className="nav-tab-wrapper"
      size="small"
      bordered={false}
      extra={
        <Space>
          <SearchSelect
            placeholder="清选择方案"
            value={schema._id}
            options={schemaList.map(item => {
              return {
                title: item.name,
                dataIndex: item._id,
              }
            })}
            onChange={(schemaId) => {
              const newSchema = schemaList.find(item => item._id === schemaId)
              setSchema(newSchema)
            }}
            width="150px"
          />
          <SearchSelect
            placeholder="清选择日期"
            value={factorDate}
            options={schema.dateList.map(date => {
              return {
                title: date,
                dataIndex: date,
              }
            })}
            onChange={setFactorDate}
            width="150px"
          />
        </Space>
      }
    >
      <Table
        size="small"
        rowKey="_id"
        columns={columns}
        loading={loading}
        dataSource={data}
        pagination={false}
        scroll={{ y: 600 }}
      />
    </Card>
  )
}

export default function ChildFundFactorScoreWrapper({ product }) {
  const { data = [], loading } = useRequest(() => {
    return queryChildFactorSchemas(product._id)
  })
  return (
    <div>
      <Spin spinning={loading}>
        {data.length && <ChildFundFactorScore product={product} schemaList={data} />}
      </Spin>
    </div>
  )
}
