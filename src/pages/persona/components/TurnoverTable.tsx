import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Table,
  Card,
  DatePicker,
  Descriptions,
  Spin,
  Row,
  Col,
} from 'antd'
import { getTurnoverData } from '@/services/fund'
import buildTableColumn from '@/utils/buildTableColumn'
import renderFundQuota from '@/utils/renderFundQuota'
import buildTreeDataWithSummary from '@/utils/buildTreeDataWithSummary'
import Echart from '@/components/Chart/Echarts'
import _ from 'lodash'
import moment from 'moment'

const { RangePicker } = DatePicker

const getTurnoverSummaryData = (data, key) => {
  const groupKey = key || (() => '组合换手率')
  return _.map(_.groupBy(data, groupKey), (values, name) => {
    const tradeVolumeSum = _.sumBy(values, 'tradeVolumeSum')
    const avgBalance = _.sumBy(values, 'avgBalance')
    const turnoverRatio = avgBalance === 0 ? 0 : tradeVolumeSum / avgBalance
    return {
      name,
      turnoverRatio,
    }
  }).sort((fst, snd) => snd.turnoverRatio - fst.turnoverRatio)
}

const renderTreeChart = (rawData) => {
  const data = buildTreeDataWithSummary(rawData, [{
    dataIndex: 'tradeVolumeSum',
  }, {
    dataIndex: 'avgBalance',
  }, {
    dataIndex: 'tRatio',
  }, {
    dataIndex: 'bRatio',
  }, {
    dataIndex: 'value',
    getValue: item => {
      const value = item.avgBalance === 0 ? 0 : item.tradeVolumeSum / item.avgBalance * 100
      return [value, value]
    },
  }, {
    dataIndex: 'name',
    getValue: item => item.STOCK_NAME,
  }, {
    dataIndex: 'turnoverRatio',
    getValue: item => {
      const value = item.avgBalance === 0 ? 0 : item.tradeVolumeSum / item.avgBalance * 100
      return value
    }
  }], ['ASSET_TYPE', 'industryBoard', 'industry'], 'STOCK_NAME')
  const formatUtil = Echart.echarts.format
  const levelOption = [
    {
      itemStyle: {
        normal: {
          borderColor: '#777',
          borderWidth: 0,
          gapWidth: 1,
        },
      },
      upperLabel: {
        normal: {
          show: false,
        },
      },
      colorMappingBy: 'value',
      visualDimension: 1,
      color: ['#0EBF9C', '#e49732', '#e85654'],
    },
    {
      itemStyle: {
        normal: {
          borderColor: '#252b35',
          borderWidth: 3,
          gapWidth: 1,
        },
        emphasis: {
          borderColor: '#ddd',
        },
      },
      colorMappingBy: 'value',
      visualDimension: 1,
      // color: ['#4caf50', '#fff', '#f44336'],
      color: ['#0EBF9C', '#e49732', '#e85654'],
    },
    {
      itemStyle: {
        normal: {
          borderColor: '#252b35',
          borderWidth: 3,
          gapWidth: 1,
        },
        emphasis: {
          borderColor: '#ddd',
        },
      },
      colorMappingBy: 'value',
      visualDimension: 1,
      color: ['#0EBF9C', '#e49732', '#e85654'],
    },
    {
      itemStyle: {
        normal: {
          borderColor: '#252b35',
          borderWidth: 0,
          gapWidth: 0,
        },
        emphasis: {
          borderColor: '#ddd',
        },
      },
    },
  ]
  const options = {
    backgroundColor: '#252b35',
    grid: {
      backgroundColor: '#252b35',
    },
    tooltip: {
      formatter: function (info) {
        const value = info.value[1] || 0
        const treePathInfo = info.treePathInfo
        const treePath = []
        for (let i = 1; i < treePathInfo.length; i++) {
          treePath.push(treePathInfo[i].name)
        }
        return [
          '<div class="tooltip-title">' + formatUtil.encodeHTML(treePath.join('/')) + '</div>',
          '换手率: ' + value.toFixed(2) + '%',
        ].join('')
      },
    },
    series: [{
      name: '组合换手率',
      type: 'treemap',
      width: '100%',
      height: '100%',
      visibleMin: 300,
      // visualMin: -2,
      // visualMax: 2,
      leafDepth: 2,
      colorMappingBy: 'value',
      visualDimension: 1,
      color: ['#0EBF9C', '#e49732', '#e85654'],
      label: {
        normal: {
          show: true,
          color: '#252b35',
          formatter: function (info) {
            const { value } = info
            return [info.name, `${_.round(value[1], 2)}%`].filter(Boolean).join('\n\n')
          },
        },
      },
      upperLabel: {
        show: true,
        height: 30,
        formatter: param => `${param.name}: ${_.round(param.value[1], 2)}%`,
      },
      itemStyle: {
        normal: {
          borderColor: '#fff',
          backgroundColor: '#252b35',
        },
        borderColor: '#252b35',
      },
      levels: levelOption,
      data: data,
    }],
  }
  const columns = [{
    title: '资产',
    dataIndex: 'name',
  }, {
    title: '换手率',
    dataIndex: 'turnoverRatio',
    format: 'percentageSuffix',
    width: 70,
  }, {
    title: '市值占比',
    dataIndex: 'bRatio',
    format: 'valPercentage',
    width: 70,
  }, {
    title: '换手贡献',
    dataIndex: 'tRatio',
    format: 'valPercentage',
    width: 70,
  }].map(buildTableColumn)
  const portfolioTurnoverData = getTurnoverSummaryData(data || [])[0] || {}
  return (
    <>
      <Row gutter={16}>
        <Col lg={16} md={24}>
          <Echart options={options} style={{ height: 500 }} />
        </Col>
        <Col lg={8} md={24}>
          <Descriptions
            bordered
            size="small"
            column={{ xl: 3, lg: 3, md: 3, sm: 3, xs: 1 }}
          >
            <Descriptions.Item
              label="组合换手率"
            >
              {renderFundQuota({ dataIndex: 'turnoverRatio', format: 'valPercentage' }, portfolioTurnoverData)}
            </Descriptions.Item>
          </Descriptions>
          <div style={{ margin: 10 }}></div>
          <Table
            columns={columns}
            dataSource={data}
            size="small"
            rowKey="id"
            scroll={{ y: 450 }}
            pagination={false}
          />
        </Col>
      </Row>
    </>
  )
}

export default ({
  dates,
  fundId,
}: {
  dates: any,
  fundId: string,
}) => {
  const initialDates = [
    moment(dates[dates.length - 1]),
    moment(dates[0]),
  ]
  const [dateRange, setDateRange] = useState(initialDates)
  const handleDateRangeChange = (dates: any) => {
    if (!dates || !dates.length) {
      return
    }
    setDateRange(dates)
  }
  const { data, loading } = useRequest(() => {
    return getTurnoverData(fundId, {
      startDate: dateRange[0] && dateRange[0].format('YYYYMMDD'),
      endDate: dateRange[1] && dateRange[1].format('YYYYMMDD'),
    })
  }, {
    refreshDeps: [dateRange],
  })
  return (
    <div>
      <Card
        title="换手率查询"
        bordered={false}
        extra={
          <>
            <span style={{ marginRight: 15 }}>
              日期范围：<RangePicker
                style={{ width: 220 }}
                size="small"
                onChange={handleDateRangeChange}
                disabledDate={(current) => {
                  const currentTs = +current.startOf('date')
                  return currentTs < initialDates[0] || currentTs > initialDates[1]
                }}
                value={dateRange}
              />
            </span>
          </>
        }
      >
        <Spin spinning={loading}>
          {renderTreeChart(data)}
        </Spin>
      </Card>
    </div>
  )
}
