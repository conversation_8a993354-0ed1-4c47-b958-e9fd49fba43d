import React from 'react'
import _ from 'lodash'
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Row, Col, Card, Statistic, Select, Tag, Tooltip } from 'antd';
import Chart from '@/components/Chart/Chart'
import renderFundQuota from '@/utils/renderFundQuota'
import { styleTypeMap } from '@/utils/kymDefMapping'

const ManagerCard = ({
  manager,
  managerPortraitInfoAvg,
  gotoPrev,
  gotoNext,
}: {
  manager: any;
  managerPortraitInfoAvg: any;
  gotoPrev: any;
  gotoNext: any;
}) => {
  const chartQuotas = [{
    name: '质量',
    value: 'incomeFactorScore',
    format: 'number',
  }, {
    name: '价值',
    value: 'riskFactorScore',
    format: 'number',
  }, {
    name: '成长',
    value: 'attributionFactorScore',
    format: 'number',
  }, {
    name: '动量',
    value: 'strategyFactorScore',
    format: 'number',
  }, {
    name: '波动性',
    value: 'companyFactorScore',
    format: 'number',
  }, {
    name: '股息率',
    value: 'managerFactorScore',
    format: 'number',
  }, {
    name: '市值',
    value: 'positionFactorScore',
    format: 'number',
  }]
  const categories = chartQuotas.map(item => item.name)
  const chartConfig = {
    chart: {
      polar: true,
      type: 'line',
      height: 210,
    },
    pane: {
      size: '83%',
    },
    tooltip: {
      shared: true,
      pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.2f}</b><br/>',
    },
    legend: {
      enabled: false,
    },
    xAxis: {
      categories,
      tickmarkPlacement: 'on',
      lineWidth: 0,
      labels: {
        useHTML: true,
        style: {
          fontSize: '10px',
        },
        formatter: function formatter() {
          const labelValue = this.value + ''
          const start = labelValue.slice(0, 4)
          const end = labelValue.slice(4)
          if (!end) {
            return `<span>${start}</span>`
          }
          return `<span>${start}</span><br/><span>${end}</span>`
        },
      },
    },
    yAxis: {
      gridLineInterpolation: 'polygon',
      min: 0,
      max: 100,
      labels: {
        enabled: false,
        format: '{value}',
      },
    },
    series: [{
      pointPlacement: 'on',
      name: '雷达图',
      data: chartQuotas.map(quota => _.random(100)),
    },
    // {
    //   pointPlacement: 'on',
    //   name: '同业平均',
    //   data: chartQuotas.map(quota => avgPortraitInfo[quota.value] || 0),
    // }
    ],
  }
  const quotas = [
    {
      name: '收益率(1day)',
      value: 'last1DAccReturn',
      format: 'percentage',
    }, {
      name: '收益率(1Mo)',
      value: 'last1MAccReturn',
      format: 'percentage',
    }, {
      name: '收益率(YTD)',
      value: 'ytdAccReturn',
      format: 'percentage',
    },
  ]
  return (
    <Card
      title="股票风格因子"
      extra={
        <>
          <Select placeholder="选择日期">
          </Select>
        </>
      }
    >
      <Chart options={chartConfig} />
    </Card>
  );
}

export default ManagerCard
