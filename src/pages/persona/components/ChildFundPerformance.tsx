import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import moment from 'moment'
import {
  DatePicker, Card, Spin, Table, Space, Tooltip,
} from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { queryFundQuotas } from '@/services/fund'
import buildTableColumn from '@/utils/buildTableColumn'
import ExportData from '@/components/ExportData'

const { RangePicker } = DatePicker

export default ({ childFundList, currentFund }) => {
  const dates = currentFund.dates || []
  const initialDates = [
    moment(dates[0]),
    moment(dates[dates.length - 1]),
  ]
  const [dateRange, setDateRange] = useState(initialDates)
  const handleDateRangeChange = (dates: any) => {
    if (!dates || !dates.length) {
      return
    }
    setDateRange(dates)
  }
  const ids = childFundList.map(item => item._id).join(',')
  const { data, loading } = useRequest(() => {
    return queryFundQuotas({
      ids,
      startDate: dateRange[0] && dateRange[0].format('YYYY-MM-DD'),
      endDate: dateRange[1] && dateRange[1].format('YYYY-MM-DD'),
    })
  }, {
    refreshDeps: [dateRange],
  })
  const columns = [
    {
      title: '基金',
      dataIndex: 'name',
      width: 150,
      fixed: 'left',
    },
    {
      title: '买入日期',
      dataIndex: 'startDate',
      format: 'date',
    },
    {
      title: '年化收益',
      dataIndex: 'yearReturn',
      format: 'percentage',
    },
    {
      title: (
        <Space>
          <span>年化超额收益率</span>
          <Tooltip title="不满一年不进行年化处理" placement="right">
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
      ),
      titleText: '年化超额收益率',
      width: 140,
      dataIndex: 'accessReturn',
      format: 'percentage',
    },
    {
      title: '投资收益率',
      dataIndex: 'capitalReturn',
      format: 'percentage',
    },
    {
      title: '波动率',
      dataIndex: 'vol',
      format: 'percentage',
    },
    {
      title: '跟踪误差',
      dataIndex: 'deviationTracking',
      format: 'percentage',
    },
    {
      title: '夏普比',
      dataIndex: 'sharpeRatio',
      format: 'percentage',
    },
    {
      title: '信息比',
      dataIndex: 'informationRatio',
      format: 'percentage',
    },
    {
      title: '上涨月份占比',
      dataIndex: 'perPositive',
      format: 'percentage',
    },
    {
      title: '正超额月份占比',
      dataIndex: 'monthlyReturnWinningRate',
      format: 'percentage',
    },
  ].map(buildTableColumn)
  return (
    <Spin spinning={loading}>
      <Card
        title="业绩指标汇总"
        extra={
          <>
            <span style={{ marginRight: 15 }}>
              日期范围：<RangePicker
                style={{ width: 220 }}
                size="small"
                onChange={handleDateRangeChange}
                disabledDate={(current) => {
                  const currentTs = +current.startOf('date')
                  return currentTs < initialDates[0] || currentTs > initialDates[1]
                }}
                value={dateRange}
              />
            </span>
            <ExportData columns={columns} dataSource={data} filename={`${currentFund.name}-业绩指标汇总`}/>
          </>
        }
      >
        <Table columns={columns} dataSource={data} pagination={false} size="small" bordered={false} scroll={{ y: 800 }} />
      </Card>
    </Spin>
  )
}
