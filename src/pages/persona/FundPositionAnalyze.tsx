import React from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import groupBy from 'lodash/groupBy'
import map from 'lodash/map'
import uniq from 'lodash/uniq'
import round from 'lodash/round'
import { formatMessage } from 'umi-plugin-react/locale'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Card, Col, Row, Tabs, Spin, Radio, Select, Tooltip } from 'antd'
import { useRequest } from '@umijs/hooks'
import Chart from '@/components/Chart/Chart'
import moment from 'moment'
import styles from './style.less'
import StandardTable from '@/components/StandardTable'
import AreaPieChart from '@/components/AreaPieChart'
import getIndustryBoardData from '@/utils/getIndustryBoardData'
import getSeriesChartData from '@/utils/getSeriesChartData'
import BondStatistics from '@/pages/analysis/position/components/BondStatistics'
import BondTermDistribution from '@/pages/analysis/position/components/BondTermDistribution'
import PositionModel from '@/pages/analysis/position/PositionModel'
import sortQuotaFn from '@/utils/sortQuotaFn'
import thousandFormatter from '@/utils/thousandFormatter'
import { getConvtBondIndustry } from '@/services/fund'
import DailyPositionDetail from './components/DailyPositionDetail'
import StockMktValDistribution from './components/StockMktValDistribution'
import TurnoverTable from './components/TurnoverTable'
import ConvtBondWeight from './components/ConvtBondWeight'
import ConvtBondDetail from './components/ConvtBondDetail'

const t = (id: string) => formatMessage({ id })
const { TabPane } = Tabs
const { Option } = Select
const Highcharts = Chart.Highcharts

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  match: any;
  location: any;
  fundPositionData: any;
  fundList?: any;
}

const halfYearFilter = item =>
  (item.F_CODE && item.F_CODE.length === 24) || /(0630|1231)$/.test(item.BIZ_DATE)

const buildAreaSeriesConfig = (data, height) => {
  const series = getSeriesChartData(data.filter(halfYearFilter), 'STYLE_TYPE', 'RATIO')
  const seriesChartConfig = {
    chart: {
      type: 'area',
      height: height || 300,
    },
    navigator: {
      enabled: false,
    },
    scrollbar: {
      enabled: false,
    },
    plotOptions: {
      area: {
        stacking: 'percent',
        lineWidth: 1,
        marker: {
          lineWidth: 2,
          lineColor: '#ffffff',
        },
      },
      column: {
        stacking: 'normal',
      },
    },
    tooltip: {
      pointFormat:
        '<span style="color:{series.color}">{series.name}</span>: <b>{point.percentage:.2f}%</b><br/>',
    },
    series,
  }
  return seriesChartConfig
}

interface StockIndustryChartProps {
  industryData: any;
  title?: string;
}

class StockIndustryChart extends React.Component<StockIndustryChartProps> {
  constructor(props: StockIndustryChartProps) {
    super(props)
    this.state = {
      viewType: 'industry',
    }
  }

  handleIndustryChange = industry => {
    this.setState({ currentIndustry: industry })
  };

  handleViewTypeChange = event => {
    this.setState({ viewType: event.target.value })
  };

  renderIndustryPreference() {
    const { viewType } = this.state
    let data = this.props.industryData
    if (viewType === 'industryBoard') {
      data = getIndustryBoardData(data)
    }
    const chartConfig = {
      chart: {
        type: 'area',
        height: 350,
      },
      navigator: {
        enabled: true,
      },
      scrollbar: {
        enabled: false,
      },
      plotOptions: {
        area: {
          stacking: 'percent',
        },
      },
      tooltip: {
        useHTML: true,
        formatter: function() {
          let tmp = ''
          const length = this.points.length
          const rowCount = 10
          const lineCount = parseInt(length / rowCount) + 1
          tmp += '<table>'
          tmp += `<tr><td>${moment(new Date(this.x)).format('YYYY-MM-DD')}</td></tr>`
          for (let i = 0; i < rowCount; i++) {
            tmp += '<tr>'
            for (let j = 0; j < lineCount && j * rowCount + i  < length; j++) {
              const it = this.points[j * rowCount + i]
              tmp += `
                <td style='padding-right: 20px'>
                  <span style='color:${it.color}'>● </span>${it.series.name}:<b>${round(it.y, 2)}%</b>
                </td>
              `
            }
            tmp += '</tr>'
          }
          tmp += '</table>'
          return tmp
        },
        // pointFormat:
        //   '<span style="color:{series.color}">{series.name}</span>: <b>{point.percentage:.2f}%</b> ({point.y:,.0f})<br/>',
      },
      series: getSeriesChartData(data, 'INDUSTRY', 'BALANCE'),
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  renderIndustryChangeChart() {
    const { industryData } = this.props
    const tempIndustry = industryData.length ? industryData[0].INDUSTRY : ''
    const currentIndustry = this.state.currentIndustry || tempIndustry
    const data = industryData.filter(item => item.INDUSTRY === currentIndustry)
    const chartConfig = {
      chart: {
        type: 'area',
        height: 350,
      },
      navigator: {
        enabled: true,
      },
      scrollbar: {
        enabled: false,
      },
      tooltip: {
        pointFormat:
          '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%<br/>',
      },
      plotOptions: {
        area: {
          stacking: undefined,
        },
      },
      series: [
        {
          name: '行业占比',
          data: data.map(item => [
            +moment(item.BIZ_DATE).startOf('date'),
            item.BALANCE_RATIO * 100,
          ]),
          fillColor: {
            linearGradient: {
              x1: 0,
              y1: 0,
              x2: 0,
              y2: 1,
            },
            stops: [
              [0, new Highcharts.Color(Highcharts.getOptions().colors[0]).setOpacity(0).get('rgba')],
              [1, new Highcharts.getOptions().colors[0]],
            ],
          },
        },
        {
          name: '沪深300',
          data: data.map(item => [+moment(item.BIZ_DATE).startOf('date'), item.BASE_RATIO * 100]),
          fillColor: {
            linearGradient: {
              x1: 0,
              y1: 0,
              x2: 0,
              y2: 1,
            },
            stops: [
              [0, new Highcharts.Color(Highcharts.getOptions().colors[1]).setOpacity(0).get('rgba')],
              [1, new Highcharts.getOptions().colors[1]],
            ],
          },
        },
      ],
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  render() {
    const { industryData, title } = this.props
    const { viewType, currentIndustry } = this.state
    const industries = uniq(industryData.map(item => item.INDUSTRY))
    const tempIndustry = industryData.length ? industryData[0].INDUSTRY : ''
    return (
      <Card>
        <Row gutter={16}>
          <Col lg={24} md={24}>
            <h4>{title || '申万一级行业偏好'}</h4>
            <Radio.Group
              value={viewType}
              size="small"
              className={styles.rightAction}
              onChange={this.handleViewTypeChange}
            >
              <Radio.Button value="industry">行业偏好</Radio.Button>
              <Radio.Button value="industryBoard">板块偏好</Radio.Button>
            </Radio.Group>
            {this.renderIndustryPreference()}
          </Col>
          <Col lg={24} md={24}>
            <h4>行业走势</h4>
            <Select
              showSearch
              value={currentIndustry || tempIndustry}
              className={styles.rightAction}
              size="small"
              style={{ width: 150 }}
              placeholder="选择行业"
              optionFilterProp="children"
              onChange={this.handleIndustryChange}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {industries.map(item => (
                <Option value={item}>{item}</Option>
              ))}
            </Select>
            {this.renderIndustryChangeChart()}
          </Col>
        </Row>
      </Card>
    )
  }
}

const ConvtBondIndustry = ({
  fundId,
}: {
  fundId: string,
}) => {
  const { data, loading } = useRequest(() => {
    return getConvtBondIndustry(fundId)
  })
  return (
    <Spin spinning={loading}>
      <StockIndustryChart industryData={data || []} title="可转债对应正股申万一级行业分布"/>
    </Spin>
  )
}

interface PositionCharacterProps {
  stockPositions: any;
  bondPositions: any;
  assetScales: any;
}

class PositionCharacter extends React.Component<PositionCharacterProps> {
  constructor(props: PositionCharacterProps) {
    super(props)
    const { stockPositions } = props
    this.state = {
      viewType: stockPositions.length !== 0 ? 'stock' : 'bond',
    }
  }

  handleViewTypeChange = event => {
    this.setState({ viewType: event.target.value })
  };

  getChartSeriesData(rows, valueKey, isPercentage) {
    return rows
      .sort((fst, snd) => fst.BIZ_DATE - snd.BIZ_DATE)
      .map(item => [
        +moment(item.BIZ_DATE).startOf('date'),
        isPercentage ? item[valueKey] * 100 : item[valueKey],
      ])
  }

  getAssetRatioData() {
    const { viewType } = this.state
    const { assetScales } = this.props
    if (viewType === 'stock') {
      return assetScales.filter(item => item.ASSET_TYPE === '股票')
    } else {
      return assetScales.filter(item => item.ASSET_TYPE === '债券')
    }
  }

  renderPositionCountChart() {
    const { viewType } = this.state
    const { stockPositions, bondPositions } = this.props
    const assetName = viewType === 'stock' ? '股票' : '债券'
    let currentPositions = viewType === 'stock' ? stockPositions : bondPositions
    currentPositions = currentPositions.filter(halfYearFilter)
    const assetData = this.getAssetRatioData().filter(halfYearFilter)
    const chartConfig = {
      navigator: {
        enabled: false,
      },
      scrollbar: {
        enabled: false,
      },
      tooltip: {
        pointFormat:
          '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}<br/>',
      },
      yAxis: [
        {
          labels: {
            format: '{value}',
          },
        },
        {
          labels: {
            format: '{value}',
          },
          opposite: true,
        },
      ],
      series: [
        {
          yAxis: 0,
          type: 'area',
          name: `${assetName}数量`,
          data: this.getChartSeriesData(currentPositions, 'COUNT', false),
        },
        {
          yAxis: 1,
          type: 'line',
          name: `${assetName}持仓占比`,
          data: this.getChartSeriesData(assetData, 'RATIO', true),
        },
      ],
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  renderTopTenPostionRatioChart() {
    const { viewType } = this.state
    const { stockPositions, bondPositions } = this.props
    const assetName = viewType === 'stock' ? '股票' : '债券'
    const currentPositions = viewType === 'stock' ? stockPositions : bondPositions
    const assetData = this.getAssetRatioData()
    const chartConfig = {
      navigator: {
        enabled: false,
      },
      scrollbar: {
        enabled: false,
      },
      tooltip: {
        pointFormat:
          '<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}%<br/>',
      },
      yAxis: [
        {
          labels: {
            format: '{value}',
          },
        },
        {
          labels: {
            format: '{value}',
          },
          opposite: true,
        },
      ],
      series: [
        {
          yAxis: 0,
          type: 'area',
          name: `${assetName}集中度`,
          data: this.getChartSeriesData(currentPositions, 'RATIO', true),
        },
        {
          yAxis: 1,
          type: 'line',
          name: `${assetName}持仓占比`,
          data: this.getChartSeriesData(assetData, 'RATIO', true),
        },
      ],
    }
    return <Chart options={chartConfig} constructorType="stockChart" />
  }

  render() {
    const { viewType } = this.state
    return (
      <Card>
        <h4>
          <span>持仓特征</span>
          <Tooltip title="股票(债券)集中度为前十大股票(债券)持仓占股票(债券)类资产权重" placement="right">
            <span style={{ marginLeft: '5px' }}>
              <QuestionCircleOutlined />
            </span>
          </Tooltip>
        </h4>
        <Radio.Group
          value={viewType}
          size="small"
          className={styles.rightAction}
          onChange={this.handleViewTypeChange}
        >
          <Radio.Button value="stock">股票持仓</Radio.Button>
          <Radio.Button value="bond">债券持仓</Radio.Button>
        </Radio.Group>
        <Row gutter={16}>
          <Col lg={12} md={24}>
            {this.renderPositionCountChart()}
          </Col>
          <Col lg={12} md={24}>
            {this.renderTopTenPostionRatioChart()}
          </Col>
        </Row>
      </Card>
    )
  }
}

interface PositionTableProps {
  dailyStockPosition: any;
  stockPositions: [];
  fundId: string;
  dispatch: Dispatch;
}

@connect(({ fund, loading }: { fund: any; loading: { models: { [key: string]: boolean } } }) => ({
  dailyStockPosition: fund.dailyStockPosition,
  loading: loading.models.fund,
}))
class PositionTable extends React.Component<PositionTableProps> {
  constructor(props: PositionTableProps) {
    super(props)
    const dates = uniq(props.stockPositions.map(item => item.BIZ_DATE)).sort(
      (fst, snd) => snd - fst,
    )
    this.state = {
      viewType: 'stock',
      positionScope: 'top10',
      currentDate: dates[0],
      prevDate: dates[1],
      dates,
    }
  }

  componentDidMount() {
    this.loadStockPosition()
  }

  loadStockPosition() {
    const { dispatch, fundId } = this.props
    const { currentDate, prevDate } = this.state
    dispatch({
      type: 'fund/fetchDailyStockPosition',
      payload: {
        id: fundId,
        params: {
          date: currentDate,
          prevDate: prevDate,
        },
      },
    })
  }

  handleViewTypeChange = (event: any) => {
    this.setState({ viewType: event.target.value })
  };

  handleDateChange = (date: string) => {
    const dates = this.state.dates
    const index = dates.indexOf(date)
    this.setState({ currentDate: date, prevDate: dates[index + 1] }, this.loadStockPosition)
  };

  handlePositionScopeChange = (positionScope: string) => {
    this.setState({ positionScope })
  }

  render() {
    const positionColumns = [
      {
        title: '代码',
        dataIndex: 'STOCK_CODE',
      },
      {
        title: '股票名称',
        dataIndex: 'STOCK_NAME',
      },
      {
        title: '行业',
        dataIndex: 'INDUSTRY',
      },
      {
        title: '权重',
        dataIndex: 'BALANCE_RATIO',
        format: 'valPercentage',
        align: 'right',
        sorter: sortQuotaFn({ dataIndex: 'BALANCE_RATIO', format: 'percentage' }, 'asc'),
      },
      {
        title: '持仓变化',
        dataIndex: 'BALANCE_GROWTH_RATE',
        format: 'valPercentage',
        align: 'right',
        sorter: sortQuotaFn({ dataIndex: 'BALANCE_GROWTH_RATE', format: 'percentage' }, 'asc'),
      },
      {
        title: '股价变化',
        dataIndex: 'PRICE_GROWTH_RATE',
        format: 'valPercentage',
        align: 'right',
        sorter: sortQuotaFn({ dataIndex: 'BALANCE_GROWTH_RATE', format: 'percentage' }, 'asc'),
      },
    ]
    const { dailyStockPosition, loading } = this.props
    const { currentDate, dates, positionScope } = this.state
    const data = dailyStockPosition
      .filter(item => positionScope === 'top10' ? item.RANK <= 10 : true)
    return (
      <Card
        title="核心股票持仓"
        extra={
          <>
            <Select
              value={positionScope}
              onChange={this.handlePositionScopeChange}
              style={{ width: 120 }}
            >
              <Option value="top10">前十大持仓</Option>
              <Option value="all">全部报告</Option>
            </Select>
            <Select
              showSearch
              value={currentDate}
              // className={styles.rightAction}
              style={{ width: 150, marginLeft: 15 }}
              placeholder="选择日期"
              optionFilterProp="children"
              onChange={this.handleDateChange}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {dates.map(item => (
                <Option value={item}>{item}</Option>
              ))}
            </Select>
          </>
        }
      >
        <StandardTable
          disableRowSlection
          loading={loading}
          size="small"
          columns={positionColumns}
          data={{
            list: data,
          }}
          scroll={{ y: 450 }}
        />
      </Card>
    )
  }
}

class FundPositionAnalyze extends React.Component<ComponentProps> {
  constructor(props: ComponentProps) {
    super(props)
    const {
      fundList,
      location: { pathname },
      match: {
        params: { id },
      },
    } = props
    const isSingleFund = /fund|activefund|portfolios/.test(pathname)
    this.state = {
      isSingleFund,
      activeFundId: isSingleFund ? id : fundList[0] && fundList[0]._id,
    }
  }

  componentDidMount() {
    const { activeFundId } = this.state
    if (activeFundId) {
      this.loadFundPositionData(activeFundId)
    }
  }

  handleFundTabChange = (fundId: string) => {
    this.setState({ activeFundId: fundId }, () => {
      this.loadFundPositionData(fundId)
    })
  };

  loadFundPositionData(fundId: string) {
    const { dispatch } = this.props
    dispatch({
      type: 'fund/fetchPositionData',
      payload: {
        id: fundId,
      },
    })
  }

  render() {
    const { isSingleFund, activeFundId } = this.state
    const { fundList, loading, fundPositionData } = this.props
    const industryData = (fundPositionData.stockIndustry || []).filter(halfYearFilter)
    const getHeatMapConfig = data => {
      const heatmapConfig = {
        chart: {
          type: 'heatmap',
          height: 250,
        },
        xAxis: {
          categories: ['价值', '成长', '均衡'],
        },
        yAxis: {
          categories: ['大盘', '中盘', '小盘'],
          labels: {
            format: '{value}',
          },
          title: null,
        },
        colorAxis: {
          min: 0,
          minColor: '#3a404c',
          maxColor: '#4d6cac',
        },
        legend: {
          enabled: false,
        },
        tooltip: {
          enabled: false,
        },
        series: [
          {
            borderWidth: 1,
            data: data || [],
            color: '#3a404c',
            dataLabels: {
              enabled: true,
              color: '#000000',
            },
          },
        ],
      }
      return heatmapConfig
    }
    const positionModel = new PositionModel({
      t,
      analysisResult: { bondPosition: fundPositionData.bondPosition || [] },
    })
    const getDatesFromAssetData = (assetData) => {
      return uniq(assetData.map(item => item.BIZ_DATE)).sort(
        (fst, snd) => snd - fst,
      )
    }
    const hasConvtBond = fundPositionData.bondPosition &&
      fundPositionData.bondPosition.some(item => ['可转债', '可交换债', '可分离转债存债'].includes(item.CLASS1))
    const analyzeDates = getDatesFromAssetData(fundPositionData.assetScales)
    return (
      <div>
        {!isSingleFund && (
          <Card title="在管基金">
            <Tabs activeKey={this.state.activeFundId} onTabClick={this.handleFundTabChange}>
              {fundList.map((item, index) => (
                <TabPane tab={item.name} key={item._id} />
              ))}
            </Tabs>
          </Card>
        )}
        <Spin spinning={loading}>
          <Card title="资产类型结构">
            <AreaPieChart
              disablePie
              rows={fundPositionData.assetScales || []}
              nameKey="ASSET_TYPE"
              valueKey="BALANCE"
            />
          </Card>
          {industryData.length !== 0 && <StockIndustryChart industryData={industryData} />}
          {fundPositionData.styleSeriesData && fundPositionData.styleSeriesData.length !== 0 && (
            <Card title="持仓风格">
              <Row gutter={16}>
                <Col lg={6} md={24}>
                  <h4>前十大</h4>
                  <Chart options={getHeatMapConfig(fundPositionData.styleTop10HeatMapData)} />
                </Col>
                <Col lg={6} md={24}>
                  <h4>半年报+季报</h4>
                  <Chart options={getHeatMapConfig(fundPositionData.styleHeatMapData)} />
                </Col>
                <Col lg={12} md={24}>
                  <h4>持仓风格漂移</h4>
                  <Chart
                    options={buildAreaSeriesConfig(fundPositionData.styleSeriesData || [], 250)}
                    constructorType="stockChart"
                  />
                </Col>
              </Row>
            </Card>
          )}
          {(fundPositionData.stockPositions.length !== 0 ||
            fundPositionData.bondPositions.length !== 0) && (
            <PositionCharacter {...fundPositionData} />
          )}
          {fundPositionData.stockPositions.length !== 0 && (
            <StockMktValDistribution dates={analyzeDates} fundId={activeFundId} />
          )}
          {fundPositionData.stockPositions.length !== 0 && false && (
            <PositionTable
              fundId={activeFundId}
              stockPositions={fundPositionData.stockPositions}
              dispatch={this.props.dispatch}
            />
          )}
          {hasConvtBond && <ConvtBondWeight fundId={activeFundId} />}
          {hasConvtBond && <ConvtBondDetail dates={analyzeDates} fundId={activeFundId} />}
          {hasConvtBond && <ConvtBondIndustry fundId={activeFundId} />}
          {hasConvtBond && <StockMktValDistribution isConvtBond dates={analyzeDates} fundId={activeFundId} />}
          {fundPositionData.bondPosition.length !== 0 && (
            <BondTermDistribution {...positionModel.buildBondTermDistributionConfig()} />
          )}
          {fundPositionData.bondPosition.length !== 0 && (
            <BondStatistics {...positionModel.buildBondStatisticsConfig()} />
          )}
          {fundPositionData.assetScales.length !== 0 && false && (
            <DailyPositionDetail dates={analyzeDates} fundId={activeFundId} />
          )}
          {fundPositionData.assetScales.length !== 0 && (
            <TurnoverTable dates={analyzeDates} fundId={activeFundId} />
          )}
        </Spin>
      </div>
    )
  }
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    fundPositionData: fund.fundPositionData,
    loading: loading.effects['fund/fetchPositionData'],
  }),
)(FundPositionAnalyze)
