import React, { useState, useEffect, useCallback } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import _, { uniq } from 'lodash'
import moment from 'moment'
import { connect } from 'dva'
import router from 'umi/router'
import {
  Card,
  Select,
  Spin,
  Modal,
  Button,
  DatePicker,
  Tooltip,
  Radio,
  Switch,
  Divider,
} from 'antd'
import { RightOutlined } from '@ant-design/icons'
import { getPositionSeries, getPositionSeriesWithBmkComponent, getPositionPriceSeries, queryChildFunds, getSwIndustryPrices, getDetectPositionDetail } from '@/services/fund'
import buildTableColumn from '@/utils/buildTableColumn'
import sortQuotaFn from '@/utils/sortQuotaFn'
import Chart from '@/components/Chart/Chart'
import buildTreeData from '@/utils/buildTreeDataWithSummary'
import { CrossTreeTable } from 'ali-react-table/pivot'
import styled from 'styled-components'
import { applyTransforms, makeBuildTreeTransform, makeSortTransform, makeTreeModeTransform, SortItem } from 'ali-react-table'
import PositionSeriesActionBlock from './components/PositionSeriesActionBlock'
import NavTabs from './detection/NavTabs'

const DarkSupportBaseTable: any = styled(CrossTreeTable)`
  &.dark {
    --color: #dadde1;

    --bgcolor: #181F29;
    --hover-bgcolor: #252b35;

    --header-color: #dadde1;
    --header-bgcolor: #252b35;
    --header-hover-bgcolor:#252b35;
    --header-highlight-bgcolor: #191a1b;

    --border-color: #3c4045;
    --highlight-bgcolor: #191a1b;
    --lock-shadow: rgb(37 37 37 / 0.5) 0 0 6px 2px;

    --row-height: 35px;
  }
`
const { Option } = Select
const { RangePicker } = DatePicker

async function getOriginFundPositionSeries(portfolio, { startDate, endDate }) {
  const { segments } = portfolio
  const funds = _.uniqBy(segments.reduce((out, item) => out.concat(item.funds), []), '_id')
  const fundMap = funds.reduce((out, item) => {
    out[item._id] = item.name
    return out
  }, {})
  const weightData = (portfolio.weightData || []).reduce((out, item) => {
    const date = item.date
    const ret = _.map(item.weights, (weight, fundId) => {
      return [
        portfolio._id, '基金', date, fundMap[fundId] || '', fundId,
        '', '', fundId, '', '', '', weight, ''
      ]
    })
    return out.concat(ret)
  }, []).filter(item => {
    return item[2] >= startDate && item[2] <= endDate
  })
  return {
    positionSeriesData: weightData,
    mutualAssetData: [],
  }
}

const getTableProps = (originData, dateRange, frequency, handleNameClick, dataType, viewType, dateOrder, assetSummaryData, selectAll, setselectAll, dataRange, isOrigin) => {
  if (!originData) return {
    columns: [],
    dataSource: [],
    dates: [],
  }
  const filterFun = (data, key) => {
    return data.filter(item => {
      const startDate = dateRange[0].format('YYYYMMDD')
      const endDate = dateRange[1].format('YYYYMMDD')
      return item[key] >= startDate && item[key] <= endDate
    })
  }

  let dates = filterFun(originData.dates, 'dateStr')
  const positions = filterFun(originData.positions, 'BIZ_DATE')
    .filter(item => {
      return dataRange === 'all' ? true : item.RANK <= 10
    })
  const assetFilters = _.uniq(positions.map(item => item.ASSET_TYPE)).map(asset => {
    return {
      text: asset,
      value: asset,
    }
  })
  if (frequency === 'monthly') {
    dates = _.map(_.groupBy(dates, 'month'), values => {
      return values[0].dateStr
    })
  } else if (frequency === 'quarterly') {
    dates = _.map(_.groupBy(dates, 'quarter'), values => {
      return values[0].dateStr
    })
  } else {
    dates = dates.map(item => item.dateStr)
  }
  dates = dates.sort((fst, snd) => dateOrder === 'desc' ? snd - fst : fst - snd)
  let valuePrefix = 'BALANCE_RATIO'
  if (dataType === 'amount') {
    valuePrefix = 'AMOUNT'
  } else if (dataType === 'balance') {
    valuePrefix = 'BALANCE'
  }
  const headColumns = [{
    title: <span>证券名称
      {viewType === 'category' &&
      <Switch
        style={{ marginLeft: 12, float: 'right' }}
        size='small'
        checked={selectAll}
        onChange={setselectAll}
        checkedChildren="全部展开"
        unCheckedChildren="全部收起"
      />}
    </span>,
    dataIndex: 'STOCK_NAME',
    fixed: 'left',
    width: 200,
    render: (text, record) => {
      if (isOrigin) {
        return text
      }
      if (!record.REF_CODE) {
        return text
      }
      return (
        <Tooltip title="点击查看资产价格变动">
          <a onClick={() => handleNameClick(record)}>{text} <RightOutlined /></a>
        </Tooltip>
      )
    },
  }]
  if (viewType === 'detail') {
    headColumns.unshift({
      title: '资产类型',
      dataIndex: 'ASSET_TYPE',
      filters: assetFilters,
      onFilter: (value, record) => record.ASSET_TYPE === value,
      fixed: 'left',
      width: 100,
    })
  }
  const columns = dates.reduce((out, date, index) => {
    out.push({
      title: moment(date).format('YYYY-MM-DD'),
      dataIndex: `${valuePrefix}_${date}`,
      format: dataType === 'ratioOfNetAsset' ? 'valPercentage' : 'commaNumber',
      width: 110,
      hasSorter: true,
    })
    return out
  }, headColumns).map(buildTableColumn)
  const defaultSortIndex = columns[2] && columns[2].dataIndex
  let dataSource = positions.sort(sortQuotaFn({
    dataIndex: defaultSortIndex,
    format: 'valPercentage',
  }, 'desc'))
  if (viewType === 'category') {
    dataSource = buildTreeData(dataSource, columns.slice(1), ['ASSET_TYPE', 'CLASS1', 'CLASS2'], 'STOCK_NAME')
    dataSource = dataSource.map(item => {
      return {
        ...item,
        ...assetSummaryData[item.STOCK_NAME],
      }
    })
  } else if (['000922_component', '000300_component'].includes(viewType)) {
    dataSource = buildTreeData(dataSource, columns.slice(1), ['bmkComponentDesc'], 'STOCK_NAME')
  }

  return {
    columns,
    dataSource,
    dates,
  }
}

const renderPriceChart = (positionItem, positionData, priceData, dataType, startDate, dates, syncType, valueType, industryPrices) => {
  const [refChart, setRefChart] = useState(null)
  const data = positionData
    .filter(item => item[7] === positionItem.REF_CODE)
  // const dates = data.map(item => item[2])
  const positionPriceData = priceData
    // .filter(item => item >= '20100101')
    .map(item => [
      +moment(item[0]).startOf('date'), valueType === 'price' ? item[1] : item[2],
    ])
    .sort((fst, snd) => fst[0] - snd[0])
  const industryPriceData = industryPrices
    .map(item => [
      +moment(item[0]).startOf('date'), valueType === 'price' ? item[1] : item[2],
    ])
    .sort((fst, snd) => fst[0] - snd[0])
  let valueIndex = 11
  let name = '占净资产比'
  if (dataType === 'amount') {
    valueIndex = 6
    name = '持仓数量'
  } else if (dataType === 'balance') {
    valueIndex = 5
    name = '持仓市值'
  }
  const positionDataMap = data.reduce((out, item) => {
    out[item[2]] = item
    return out
  }, {})
  const positionBalanceData = dates.map(date => {
    const item = positionDataMap[date] || []
    let value = item[valueIndex] || 0
    if (dataType === 'ratioOfNetAsset') {
      value = value * 100
    }
    return [
      +moment(date).startOf('date'), value,
    ]
  }).sort((fst, snd) => fst[0] - snd[0])
  const xdates = positionBalanceData.map(item => item[0])
  const afterChartCreated = (refChart) => {
    setRefChart(refChart)
  }
  if (refChart) {
    setTimeout(() => refChart.xAxis[0].setExtremes(+startDate, +new Date()), 1000)
  }
  const isYtm = positionItem.ASSET_TYPE === '债券' && valueType === 'ytm'
  const isCompare = ['股票', '基金'].includes(positionItem.ASSET_TYPE)
  const isConvtBond = positionItem.ASSET_TYPE === '可转债'
  const series = [
    {
      yAxis: 0,
      type: syncType === 'mutual' ? 'column' : 'area',
      name: name,
      data: positionBalanceData,
    },
    {
      yAxis: 1,
      type: 'line',
      name: isYtm ? '到期收益率' : '价格',
      data: positionPriceData,
      compare: isCompare ? 'percent' : null,
      tooltip: {
        pointFormat: !isCompare ? `{series.name}: <b>{point.y:.2f}${isYtm ? '%' : ''}</b><br />` : '{series.name}: <b>{point.y:.2f}({point.change:.2f}%)</b><br />',
      },
    },
  ]
  if (positionItem.ASSET_TYPE === '股票') {
    series.push({
      yAxis: 1,
      type: 'line',
      name: `${positionItem.INDUSTRY}行业指数`,
      data: industryPriceData,
      compare: 'percent',
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y:.2f}({point.change:.2f}%)</b><br/>',
      },
    })
  }
  const chartConfig = {
    chart: {
      height: 420,
    },
    rangeSelector: {
      enabled: true,
      inputEnabled: true,
    },
    navigator: {
      enabled: true,
    },
    scrollbar: {
      enabled: false,
    },
    tooltip: {
      pointFormat:
        `<span style="color:{series.color}">{series.name}</span>: <b>{point.y:.2f}${dataType === 'ratioOfNetAsset' ? '%' : ''}<br />`,
    },
    yAxis: [
      {
        labels: {
          format: `{value}${dataType === 'ratioOfNetAsset' ? '%' : ''}`,
        },
      },
      {
        labels: {
          format: `{value}${isCompare || isYtm ? '%' : ''}`,
        },
        opposite: true,
      },
    ],
    series,
  }
  return <Chart options={chartConfig} constructorType="stockChart" afterChartCreated={afterChartCreated} />
}

const FundPositionSeriesQuery = ({
  currentFund,
  match: {
    params: { id },
  },
  location,
}: {
  currentFund: any,
  match: any,
  location: any,
}) => {
  const isDetect = location.pathname.includes('detection_position_detail')
  let positionQueryDates = {}
  try {
    const cachedDates = localStorage.getItem('positionQueryDates')
    if (cachedDates) {
      positionQueryDates = JSON.parse(cachedDates)
    }
  } catch (error) {
  }
  const fundId = currentFund._id
  console.log('currentFund', currentFund)
  if (currentFund._syncType !== 'mutual' && !window.__isTradingNetwork) {
    router.push('/404')
    return null
  }
  const defaultEndDate = currentFund.ref_fund_end_date_ts || currentFund.navEndDate
  const defaultStartDate = currentFund.ref_fund_start_date_ts ||
    moment(defaultEndDate).subtract(currentFund._syncType === `mutual` && !isDetect ? 10 : 1, 'years')
  const [originData, setOriginData] = useState(null)
  const [dateRange, setDateRange] = useState([
    moment(new Date(positionQueryDates.startDate || defaultStartDate)),
    moment(new Date(positionQueryDates.endDate || defaultEndDate)),
  ])
  const handleDateRangeChange = (dates: any) => {
    if (!dates || !dates.length) {
      return
    }
    setDateRange(dates)
    localStorage.setItem('positionQueryDates', JSON.stringify({
      startDate: dates[0].format('YYYY-MM-DD'),
      endDate: dates[1].format('YYYY-MM-DD'),
    }))
  }
  const isPortfolio = currentFund.isPortfolio
  const isActiveFund = currentFund._syncType === 'activeFund' || currentFund._syncType === 'penetrateFund'
  const [isOrigin, setIsOrigin] = useState(false)
  const [viewType, setViewType] = useState('category')
  const [valueType, setValueType] = useState('price')
  const defaultFrequency = currentFund._syncType === 'mutual' && !isDetect ? 'quarterly' : 'daily'
  const [frequency, setFrequency] = useState(defaultFrequency)
  const [dataType, setDataType] = useState(isDetect ? 'ratioOfNetAsset' : 'amount')
  const [dateOrder, setDateOrder] = useState('desc')
  const [dataRange, setDataRange] = useState('all')
  const handleIsOriginChange = isOrigin => {
    setIsOrigin(isOrigin)
  }
  const isBmkComponent = ['000922_component', '000300_component'].includes(viewType)
  const dateStr = `${dateRange[0].format('YYYYMMDD')}_${dateRange[1].format('YYYYMMDD')}`
  const { run: doGetPositionSeries, data = { positionSeriesData: [], mutualAssetData: [] }, loading }
    = useRequest(() => {
      const params = {
        startDate: dateRange[0].format('YYYYMMDD'),
        endDate: dateRange[1].format('YYYYMMDD'),
      }
      if (isDetect) {
        return getDetectPositionDetail(id || fundId, params)
      } else if (isOrigin) {
        return getOriginFundPositionSeries(currentFund, params)
      } else if (isBmkComponent) {
        return getPositionSeriesWithBmkComponent(fundId || id, {
          ...params,
          viewType,
        })
      } else {
        return getPositionSeries(fundId || id, {
          ...params
        })
      }
    }, {
      refreshDeps: [isOrigin, viewType, dateStr]
    })
  const { data: childFundList = [] } = useRequest(() => {
    return queryChildFunds(fundId)
  })
  const { loading: loadingPriceSeries, run: doGetPositionPriceSeries, data: priceSeries } = useRequest((positionItem) => {
    const code = positionItem.REF_CODE
    const assetTypeMap = {
      '股票': '2',
      '债券': '4',
      '基金': '5',
      '可转债': '4_1',
    }
    const assetType = assetTypeMap[positionItem.ASSET_TYPE]
    const exchangeType = positionItem.EXCHANGE_TYPE
    return getPositionPriceSeries({ code, assetType, exchangeType })
  }, {
    manual: true,
  })
  const { data: industryPrices = [], run: runGetSwIndustryPrices } = useRequest((industry) => {
    return getSwIndustryPrices({ industry })
  }, {
    manual: true,
  })

  const [currentPositionItem, setCurrentPositionItem] = useState({})
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const handleNameClick = (item) => {
    setVisibleTrue()
    doGetPositionPriceSeries(item)
    setCurrentPositionItem(item)
    if (item.ASSET_TYPE === '股票') {
      runGetSwIndustryPrices(item.INDUSTRY)
    }
  }
  const buildAssetSummaryData = (data, balanceIndex, balanceRatioIndex) => {
    return data.reduce((out, item) => {
      out[`BALANCE_RATIO_${item[1]}`] = item[balanceRatioIndex]
      out[`BALANCE_${item[1]}`] = item[balanceIndex]
      return out
    }, {})
  }
  const assetSummaryData = {
    股票: buildAssetSummaryData(data.mutualAssetData, 2, 3),
    债券: buildAssetSummaryData(data.mutualAssetData, 4, 5),
    可转债: buildAssetSummaryData(data.mutualAssetData, 6, 7),
    基金: buildAssetSummaryData(data.mutualAssetData, 8, 9),
  }

  const getOriginData = useCallback((data) => {
    console.log('-------我是提出来的处理函数，渲染了--------')
    if (data && data.positionSeriesData) {
      const positionData = data.positionSeriesData.map(item => {
        const class1 = item[1] === '股票' ? item[10] : item[9]
        const class2 = (item[1] === '股票' || item[10] === item[9]) ? null : item[10]
        let stockName = item[3]
        if (item[1] === '股票') {
          stockName = `${stockName} (${item[7]})`
        }
        return {
          ASSET_TYPE: item[1],
          BIZ_DATE: item[2],
          STOCK_NAME: stockName,
          STOCK_CODE: item[4],
          BALANCE: item[5],
          AMOUNT: item[6],
          REF_CODE: item[7],
          EXCHANGE_TYPE: item[8],
          CLASS1: class1,
          CLASS2: class2,
          INDUSTRY: item[10],
          BALANCE_RATIO: item[11],
          RANK: item[12],
          bmkComponentDesc: item[13] ? '是当前最新成份股' : '非当前最新成份股',
        }
      }).filter(item => item.STOCK_CODE)
      const dates = _.uniq(positionData.map(item => item.BIZ_DATE))
        .sort((fst, snd) => snd - fst)
        .map(dateStr => {
          const date = moment(dateStr)
          return {
            dateStr,
            month: date.format('YYYYMM'),
            quarter: date.endOf('quarter').format('YYYYMMDD'),
          }
        })
      const positions = _.map(_.groupBy(positionData, 'REF_CODE'), (values) => {
        const row = values[0]
        const ret = {
          ASSET_TYPE: row.ASSET_TYPE,
          STOCK_NAME: row.STOCK_NAME,
          REF_CODE: row.REF_CODE,
          EXCHANGE_TYPE: row.EXCHANGE_TYPE,
          CLASS1: row.CLASS1,
          CLASS2: row.CLASS2,
          INDUSTRY: row.INDUSTRY,
          BIZ_DATE: row.BIZ_DATE,
          RANK: row.RANK,
          bmkComponentDesc: row.bmkComponentDesc,
        }
        values.forEach(item => {
          const date = item.BIZ_DATE
          ret[`BALANCE_RATIO_${date}`] = item.BALANCE_RATIO
          ret[`BALANCE_${date}`] = item.BALANCE
          ret[`AMOUNT_${date}`] = item.AMOUNT
        })
        return ret
      })
      return {
        positionData,
        dates,
        positions,
      }
    }
  },
    [JSON.stringify(data)],
  )

  useEffect(() => {
    const newOriginData = getOriginData(data)
    setOriginData(newOriginData)
  }, [JSON.stringify(data)])

  const [selectAll, setselectAll] = useState<boolean>(false)
  const tableProps = getTableProps(originData, dateRange, frequency, handleNameClick, dataType, viewType, dateOrder, assetSummaryData, selectAll, setselectAll, dataRange, isOrigin)
  const assetTypeList = uniq(tableProps.dataSource.map(item => item.ASSET_TYPE))
  const [fundType, setfundType] = useState<any[]>(assetTypeList)
  const exportedColumns = [{
    title: '日期',
    dataIndex: 2
  }, {
    title: '资产类型',
    dataIndex: 1
  }, {
    title: '行业',
    dataIndex: 10
  }, {
    title: '股票代码',
    dataIndex: 7
  }, {
    title: '股票名称',
    dataIndex: 3
  }, {
    title: '市值',
    dataIndex: 5
  }, {
    title: '数量',
    dataIndex: 6
  }, {
    title: '占净资产比',
    dataIndex: 11
  }]
  const actionBloack = (
    <PositionSeriesActionBlock
      {...{
        currentFund, childFundList, assetTypeList, setfundType, setDateRange,
        viewType, setViewType, dataType, setDataType, handleDateRangeChange,
        dateRange, frequency, setFrequency, dateOrder, setDateOrder,
        doGetPositionSeries, fundId, isDetect, setDataRange, dataRange,
        isPortfolio, isOrigin, handleIsOriginChange, isActiveFund
      }}
      exportedData={{
        columns: exportedColumns,
        dataSource: data.positionSeriesData,
      }}
    />
  )
  let content = (
    <Card
      extra={actionBloack}
    />
  )
  if (isDetect) {
    content = (
      <NavTabs
        location={location}
        tabBarExtraContent={actionBloack}
      />
    )
  }
  return (
    <Spin spinning={loading}>
      {content}
      <RenderALiTable
        filterable={viewType === `detail`}
        tableProps={tableProps}
        fundType={fundType}
        selectAll={selectAll}
      />
      {isBmkComponent && <div>
        <Divider orientation="left" plain>
          数据说明
        </Divider>
        <div>
          <p>是当前最新成份股：指在当前筛选日期范围内最新持仓日期时点是成份股。</p>
        </div>
      </div>}
      <Modal
        title={
          <>
            <span>{`${currentPositionItem.STOCK_NAME}价格变动`}</span>
            {currentPositionItem.ASSET_TYPE === '债券' &&
              <Radio.Group
                defaultValue={valueType}
                size="small"
                onChange={(event) => setValueType(event.target.value)}
                style={{
                  fontSize: 12,
                  float: 'right',
                  marginRight: 30,
                }}
              >
                <Radio.Button value="price">价格</Radio.Button>
                <Radio.Button value="ytm">到期收益率</Radio.Button>
              </Radio.Group>}
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={1000}
        footer={[
          <Button
            type="primary"
            onClick={setVisibleFalse}
          >
            关闭
          </Button>,
        ]}
      >
        <Spin spinning={loadingPriceSeries}>
          {renderPriceChart(currentPositionItem, data.positionSeriesData, priceSeries || [], dataType, dateRange[0], tableProps.dates, currentFund._syncType, valueType, industryPrices)}
        </Spin>
      </Modal>
    </Spin>
  )
}

export default connect(
  ({
    fund,
  }: {
    fund: any;
  }) => ({
    currentFund: fund.currentFund,
  }),
)(FundPositionSeriesQuery)

const RenderALiTable = ({ tableProps, fundType, filterable, selectAll }) => {
  const renderData: any = (data: any) => {
    if (data.children) {
      return {
        ...data,
        key: data.STOCK_NAME,
        value: data.STOCK_NAME,
        data: { parent: true },
        children: data.children.map((item: any) => renderData(item)),
      }
    }
    else return {
      ...data,
      key: data.STOCK_NAME,
      value: data.STOCK_NAME,
      data: { parent: true },
    }
  }
  const [openKeys, setOpenKeys] = useState<string[]>([])
  const [sorts, onChangeSorts] = useState<SortItem[]>([])

  const topTree = tableProps.columns.map((item, index) => {
    // const inputSorts = pipeline.getStateAtKey('sort')
    if (index > 0)
      return {
        features: {
          sortable: item.hasSorter ? (a, b) => {
            return sortQuotaFn.compareAscending(a, b, sorts[0]?.order)
          } : false,
        },
        value: item.title,
        key: item.dataIndex,
        code: item.dataIndex,
        id: item.dataIndex,
        name: item.title,
        data: {
          y: index - 1,
          indicator: { render: item.render ? item.render : null },
        },
        align: !!item.hasSorter && 'right',
        width: 110,
      }
    else return null
  }).filter(Boolean)
  const leftTree = tableProps.dataSource.map(item => {
    return renderData(item)
  })

  const onChangeOpenKeys = (nextKeys: string[], key: string, action: 'expand' | 'collapse') => {
    setOpenKeys(nextKeys)
  }

  const renderResult = applyTransforms(
    { columns: topTree, dataSource: leftTree },
    // 从平铺的数据中，根据 id/parent_id 字段构建出树状结构
    makeSortTransform({
      sorts,
      onChangeSorts,
      mode: 'single', // 改为 multiple 可以使用多列排序
    }),
    makeTreeModeTransform({ primaryKey: 'id', indentSize: 20 }),
  )

  useEffect(() => {
    if (selectAll) {
      let list: string[] = []
      const renderList: any = (data: any) => {
        if (data.children) {
          list = [...list, data.key]
          data.children.forEach((item: any) => renderList(item))
        }
      }
      leftTree.forEach(ele => {
        renderList(ele)
      })
      setOpenKeys(list)
    } else {
      setOpenKeys([])
    }
  }, [selectAll])

  return (<DarkSupportBaseTable
    className="dark"
    openKeys={openKeys}
    onChangeOpenKeys={onChangeOpenKeys}
    // BaseTableComponent={WebsiteBaseTable}
    style={{ marginTop: 8, height: 600, overflow: 'auto' }}
    primaryColumn={{
      lock: true,
      width: 200,
      name: tableProps.columns[0]?.title,
      render: (leftNode, index) => tableProps.columns[0]
        .render ? tableProps.columns[0]
          .render(leftNode[tableProps.columns[0]?.dataIndex], leftNode)
        : leftNode[tableProps.columns[0]?.dataIndex],
      id: tableProps.columns[0]?.dataIndex,
    }}
    defaultColumnWidth={120}
    // isLoading={pivot.isLoading}
    topTree={renderResult.columns}
    leftTree={(renderResult.dataSource || []).filter(item =>
      filterable ? (fundType.length === 0 ? true : fundType.includes(item.ASSET_TYPE)) : true)}
    primaryKey='id'
    getValue={(leftNode, topNode) => {
      return (leftNode[topNode.key] || leftNode[topNode.key] === 0) ? leftNode[topNode.key] : '-'
    }}
    render={(value, leftNode, topNode) => {
      const ind = topNode.data.indicator
      return ind?.render ? ind?.render(value, leftNode) : value
    }}
  />)
}
