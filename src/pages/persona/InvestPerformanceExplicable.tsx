import React from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { Collapse, Card } from 'antd'
import IndividualRating from './components/IndividualRating'

const { Panel } = Collapse

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentManager: any;
  match: any;
  location: any;
}

const InvestPerformanceExplicable: React.FC<ComponentProps> = props => {
  const { currentManager } = props
  const { performanceExplicableDesc } = currentManager
  return (
    <div>
      <IndividualRating rating={currentManager.invest_performance_explicable} />
      <Card title="定性分析">
        <Collapse bordered={false} defaultActiveKey={['1']} expandIconPosition="right">
          <Panel header="收益来源定义" key="1">
            {performanceExplicableDesc.income_source_definition}
          </Panel>
          <Panel header="主动承担风险的空间" key="2">
            {performanceExplicableDesc.risk_take_space}
          </Panel>
          <Panel header="团队胜任的原因" key="3">
            {performanceExplicableDesc.team_competency_reason}
          </Panel>
          <Panel header="对超额收益的效果贡献" key="4">
            {performanceExplicableDesc.excess_return_contribution}
          </Panel>
          <Panel header="资源支持" key="5">
            {performanceExplicableDesc.resource_support}
          </Panel>
          <Panel header="流程" key="6">
            {performanceExplicableDesc.process}
          </Panel>
        </Collapse>
      </Card>
    </div>
  )
}

export default connect(
  ({
    manager,
    loading,
  }: {
    manager: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentManager: manager.currentManager,
    loading: loading.models.manager,
  }),
)(InvestPerformanceExplicable)
