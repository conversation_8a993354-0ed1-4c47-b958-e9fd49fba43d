@import '~antd/es/style/themes/default.less';

.panelItemHeader {
  color: #949fb6;
  margin-bottom: 8px;
}

.rightAction {
  float: right;
  margin-top: -27px;
}

.zeroPaddingCard {
  :global(.ant-card-body) {
    padding: 0;
  }
}

.circel {
  height: 14px;
  width: 14px;
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
  -moz-border-radius: 7px;
  -webkit-border-radius: 7px;
  border-radius: 7px;
  background-color: rebeccapurple;
}

.circelLine {
  width: 34px;
  display: inline-block;
  height: 2px;
  background: #fff;
  vertical-align: middle;
  margin-left: -30px;
  margin-right: 10px;
}

.square {
  height: 14px;
  width: 14px;
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
  background-color: rebeccapurple;
}
