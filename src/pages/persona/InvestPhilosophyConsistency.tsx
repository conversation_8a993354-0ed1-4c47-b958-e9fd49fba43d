import React from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { Card, Collapse } from 'antd'
import IndividualRating from './components/IndividualRating'
import FundPositionAnalyze from './FundPositionAnalyze'
import styles from './style.less'

const { Panel } = Collapse

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentManager: any;
  match: any;
  location: any;
  fundPositionData: any;
}

class InvestPhilosophyConsistency extends React.Component<ComponentProps> {
  render() {
    const { currentManager, match, location } = this.props
    const { philosophyConsistency, fundList } = currentManager
    const panelQuotas = [
      {
        name: '资产配置',
        value: 'asset_allocation',
      },
      {
        name: '行业视角',
        value: 'industry_perspective',
      },
      {
        name: '个股选择',
        value: 'stk_selection',
      },
      {
        name: '个股选择-事件驱动短期交易',
        value: 'stk_selection_eventsdrive',
      },
      {
        name: '个股选择-冷门股',
        value: 'stk_selection_unpopular',
      },
    ]

    return (
      <div>
        <IndividualRating rating={currentManager.invest_philosophy_consistency} />
        <Card title="定性分析">
          <Collapse bordered={false} expandIconPosition="right">
            {panelQuotas.map((quota, index) => (
              <Panel header={quota.name} key={`${index + 1}`}>
                <div className={styles.panelItemHeader}>
                  <span>分析框架：</span>
                </div>
                <p>{philosophyConsistency[quota.value]}</p>
                <div className={styles.panelItemHeader}>
                  <span>责任人：</span>
                </div>
                <p>{philosophyConsistency[`${quota.value}_principal`]}</p>
              </Panel>
            ))}
          </Collapse>
        </Card>
        <FundPositionAnalyze fundList={fundList.filter((item: any) => item.endToNow)} match={match} location={location} />
      </div>
    )
  }
}

export default connect(
  ({
    manager,
    fund,
    loading,
  }: {
    manager: any;
    fund: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentManager: manager.currentManager,
    fundPositionData: fund.fundPositionData,
    loading: loading.models.fund,
  }),
)(InvestPhilosophyConsistency)
