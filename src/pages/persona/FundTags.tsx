import React, { useState, useMemo } from 'react'
import {
  Spin,
  Card,
  Space,
  Select,
  message,
} from 'antd'
import { useRequest } from '@umijs/hooks'
import moment from 'moment'
import _ from 'lodash'
import { connect } from 'dva'
import { queryFundTagData, queryFundTagDateList } from './service'
import SearchSelect from '@/components/SearchSelect'
import Echart from '@/components/Chart/Echarts'

const getDateList = (startDate, endDate, freq) => {
  let end = endDate
  let temp = moment(startDate).endOf(freq).format('YYYY-MM-DD')
  const results = []
  while (temp <= end) {
    results.push(temp)
    temp = moment(temp).add(1, freq).endOf(freq).format('YYYY-MM-DD')
  }
  return results.filter(item => item >= startDate && item <= endDate).reverse()
}

const Class1Select = ({ queryOptions, selected, onChange, assetType }) => {
  const class1List = useMemo(() => {
    if (!queryOptions) {
      return []
    }
    const ret = _.uniq((queryOptions.tagList || []).filter(item => item.assetType === assetType).map(item => item.class1))
    onChange(ret)
    return ret
  }, [!!queryOptions])
  return (
    <span>
      <span style={{ marginRight: 10 }}>
        标签分类:
      </span>
      <Select
        style={{ width: '300px' }}
        value={selected}
        mode="multiple"
        maxTagCount="responsive"
        onChange={(values) => {
          if (!values.length) {
            message.warn('至少要选择一个分类')
            return
          }
          onChange(values)
        }}
      >
        {class1List.map(item => <Select.Option key={item}>{item}</Select.Option>)}
      </Select>
    </span>
  )
}

const getChartOptions = (tags) => {
  const option = {
    animation: false,
    series: [{
      type: 'wordCloud',
      shape: 'circle',
      keepAspect: false,
      // maskImage: maskImage,
      left: 'center',
      top: 'center',
      width: '100%',
      height: '90%',
      right: null,
      bottom: null,
      sizeRange: [12, 60],
      rotationRange: [-90, 90],
      rotationStep: 45,
      gridSize: 8,
      drawOutOfBound: false,
      force: {
        layoutAnimation: false,
      },
      textStyle: {
        fontFamily: 'sans-serif',
        fontWeight: 'bold',
        // echarts5 不需要放到 normal 里
        normal: {
          fontFamily: '微软雅黑',
          color: function (point) {
            if (!point.data.isActive) {
              return '#69717a'
            }
            return '#ff9800'
            // return 'rgb(' + [
            //   Math.round(Math.random() * 250),
            //   Math.round(Math.random() * 250),
            //   Math.round(Math.random() * 250)
            // ].join(',') + ')';
          }
        }
      },
      emphasis: {
        // focus: 'self',
        textStyle: {
          textShadowBlur: 3,
          textShadowColor: '#333'
        }
      },
      //data属性中的value值却大，权重就却大，展示字体就却大
      data: tags.map((tag, index) => {
        return {
          name: tag.name,
          // value: _.random(600, 900),
          value: 100,
          isActive: tag.isActive,
          textStyle: {
            color: '#333',
          }
        }
      })
    }]
  }
  return option
}

const FundTags = ({ currentFund, queryOptions }) => {
  const [selected, setSelected] = useState([])
  const startDate = moment(currentFund.navStartDate).format('YYYY-MM-DD')
  const endDate = moment().format('YYYY-MM-DD')
  const dateList = getDateList(startDate, endDate, 'month')
  const [bizDate, setBizDate] = useState('')
  const { loading, data = [] } = useRequest(() => {
    if (bizDate) {
      return queryFundTagData(currentFund._id, { bizDate })
    } else {
      return Promise.resolve([])
    }
  }, {
    refreshDeps: [bizDate],
  })
  const { loading: loadingDateList, data: dateListData = [] } = useRequest(() => {
    return queryFundTagDateList(currentFund._id, {})
  }, {
    onSuccess: (data) => {
      setBizDate(data[0])
    },
  })
  const dateOptions = dateListData.map(item => {
    return {
      title: item,
      dataIndex: item,
    }
  })
  const assetType = currentFund.asset_type || '1'
  const activeCode = data.map(item => item.label_code)
  const tags = (queryOptions ? queryOptions.tagList : [])
    .filter(item => selected.includes(item.class1) && item.assetType === assetType)
    .map(item => {
      const isActive = activeCode.includes(item.label_code)
      return {
        name: item.name,
        isActive,
      }
    })
  const options = getChartOptions(tags)
  return (
    <div>
      <Card className="zero-padding-card" title="" extra={
        <Space>
          <Spin spinning={loadingDateList}>
            <SearchSelect
              value={bizDate}
              options={dateOptions}
              onChange={setBizDate}
              width="150px"
            />
          </Spin>
          <Class1Select
            queryOptions={queryOptions}
            selected={selected}
            onChange={setSelected}
            assetType={assetType}
          />
        </Space>
      }>
          <Spin spinning={loading}>
            <Echart style={{ height: '600px', marginTop: 20 }} options={options} />
          </Spin>
      </Card>
    </div>
  )
}

export default connect(
  ({
    fund,
  }: {
    fund: any;
  }) => ({
    currentFund: fund.currentFund,
    queryOptions: fund.queryOptions,
  }),
)(FundTags)
