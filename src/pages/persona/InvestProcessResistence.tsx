import React from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { Collapse, Card, Tag } from 'antd'
import styles from './style.less'
import IndividualRating from './components/IndividualRating'

const { Panel } = Collapse

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentManager: any;
  match: any;
  location: any;
}

const InvestProcessResistence: React.FC<ComponentProps> = props => {
  const { currentManager } = props
  const { processResistenceDesc } = currentManager
  const panelQuotas = [
    {
      name: '基本面研究',
      items: [
        {
          name: '如何选择研究行业',
          value: 'industry_choose_method',
          principalValue: 'industry_choose_method_principal',
        },
        {
          name: '信息来源',
          value: 'information_sources',
          principalValue: 'information_sources_principal',
        },
        {
          name: '研究结论',
          value: 'analysis_conclusion',
          principalValue: 'analysis_conclusion_principal',
        },
        {
          name: '核心股票推荐',
          value: 'core_stock_recommendation',
          principalValue: 'core_stock_recommendation_principal',
        },
        {
          name: '股票池',
          value: 'stock_pool',
          principalValue: 'stock_pool_principal',
        },
        {
          name: '持续研究',
          value: 'continuous_research',
          principalValue: 'continuous_research_principal',
        },
      ],
    },
    {
      name: '权重与组合构建',
      items: [
        {
          name: '股票集中度',
          value: 'combination_concentration',
          principalValue: 'combination_concentration_principal',
        },
        {
          name: '个股权重',
          value: 'equity',
          principalValue: 'equity_principal',
        },
        {
          name: '核心股票和短期交易区别',
          value: 'corestock_shortterm',
          principalValue: 'corestock_shortterm_principal',
        },
      ],
    },
    {
      name: '卖出决策与条件',
      items: [
        {
          name: '核心股票',
          value: 'core_stock',
          principalValue: 'core_stock_principal',
        },
        {
          name: '短期交易',
          value: 'short_term_trading',
          principalValue: 'short_term_trading_principal',
        },
      ],
    },
  ]
  return (
    <div className={styles.investProcessResistence}>
      <IndividualRating rating={currentManager.invest_process_resistence} />
      <Card title="定性分析">
        <Collapse bordered={false} defaultActiveKey={['1']} expandIconPosition="right">
          {panelQuotas.map((quota, index) => (
            <Panel header={quota.name} key={`${index + 1}`}>
              {quota.items.map(item => (
                <div>
                  <div className={styles.panelItemHeader}>
                    <span>{item.name}</span>
                    {processResistenceDesc[item.principalValue] && (
                      <Tag style={{ float: 'right' }}>
                        {processResistenceDesc[item.principalValue]}
                      </Tag>
                    )}
                  </div>
                  <p>{processResistenceDesc[item.value]}</p>
                </div>
              ))}
            </Panel>
          ))}
        </Collapse>
      </Card>
    </div>
  )
}

export default connect(
  ({
    manager,
    loading,
  }: {
    manager: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentManager: manager.currentManager,
    loading: loading.models.manager,
  }),
)(InvestProcessResistence)
