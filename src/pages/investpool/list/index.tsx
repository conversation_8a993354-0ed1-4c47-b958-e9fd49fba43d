import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { ModelState } from '@/models/investpool'
import { TableListItem, TableListParams } from '@/components/StandardTable'
import InvestPoolList from '@/components/investpool/List'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  investPoolListData: any;
  location: any;
  mutualFundFilters: any;
}

interface ComponentState {
  selectedRows: TableListItem[];
  isPage?: boolean;
  defaultIds?: string;
  type: string;
  dataType: string;
}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    investpool,
    fund,
    loading,
  }: {
    investpool: ModelState;
    fund: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    investPoolListData: investpool.investPoolListData,
    mutualFundFilters: fund.mutualFundFilters,
    loading: loading.effects['investpool/fetch'],
    groupList: investpool.groupList,
  }),
)
class InvestPool extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    const {
      location: { pathname },
    } = props
    this.state = {
      selectedRows: [],
      type: pathname.includes('/static') ? 'static' : 'dynamic',
      dataType: pathname.includes('/fund') ? 'fund' : 'manager',
    }
  }

  componentDidMount() {
    const { dispatch } = this.props
    dispatch({
      type: 'investpool/fetchGroupList',
    })
    this.loadData({})
  }

  loadData = (params: Partial<TableListParams>) => {
    const { dispatch } = this.props
    dispatch({
      type: 'investpool/fetch',
      payload: {
        ...params,
        type: this.state.type,
        dataType: this.state.dataType,
      },
    })
  }

  render() {
    const { investPoolListData, loading, dispatch, mutualFundFilters, groupList } = this.props
    console.log(groupList)
    return (
      <InvestPoolList
        {...{ dispatch, investPoolListData, loading, mutualFundFilters, groupList }}
        loadData={this.loadData}
        type={this.state.type}
        dataType={this.state.dataType}
      />
    )
  }
}

export default InvestPool
