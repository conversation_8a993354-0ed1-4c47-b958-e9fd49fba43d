import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { Empty, Breadcrumb } from 'antd'
import Link from 'umi/link'
import PageLoading from '@/components/PageLoading'
import { ModelState } from '@/models/investpool'
import FundList from '@/pages/fund/MutualFund'
import ManagerList from '@/pages/manager/list'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  match: any;
  currentInvestPool: any;
}

interface ComponentState {
  isPage?: boolean;
  input?: string;
  type: string;
}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    investpool,
    loading,
  }: {
    investpool: ModelState;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentInvestPool: investpool.currentInvestPool,
    loading: loading.models.investpool,
  }),
)
class InvestPoolDetail extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    const {
      location: { pathname },
    } = props
    this.state = {
      selectedRows: [],
      type: pathname.includes('/static') ? 'static' : 'dynamic',
    }
  }

  componentDidMount() {
    this.loadInvestPool()
  }

  loadInvestPool() {
    const {
      dispatch,
      match: {
        params: { id },
      },
    } = this.props
    if (id !== 'investable') {
      dispatch({
        type: 'investpool/fetchOne',
        payload: { id },
      })
    }
  }

  render() {
    const { currentInvestPool, loading, ...restProps } = this.props

    if (!currentInvestPool && loading) {
      return <PageLoading />
    }
    if (!currentInvestPool) {
      return <Empty style={{ padding: '100px' }} description="你查看的跟踪列表不存在" />
    }

    let filterValues = {}
    if (currentInvestPool.config) {
      filterValues = JSON.parse(currentInvestPool.config)
    }
    return (
      <div>
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>
            <Link to="/reportbuilder/trackinglist">
              跟踪名单
            </Link>
          </Breadcrumb.Item>
          {false &&
          <Breadcrumb.Item>{currentInvestPool.dataType === 'manager' ? '基金经理' : '基金'}跟踪列表</Breadcrumb.Item>}
          {false &&
          <Breadcrumb.Item>
            <Link to={`/pm/tracking/${currentInvestPool.dataType}/${currentInvestPool.type}`}>
              {currentInvestPool.type === 'static' ? '静态条件筛选' : '动态条件筛选'}
            </Link>
          </Breadcrumb.Item>}
          <Breadcrumb.Item>{currentInvestPool.name}</Breadcrumb.Item>
        </Breadcrumb>
        {currentInvestPool.dataType === 'fund' ?
        <FundList
          filterValues={filterValues}
          currentInvestPool={currentInvestPool}
          {...restProps}
        /> :
        <ManagerList
          filterValues={filterValues}
          currentInvestPool={currentInvestPool}
          {...restProps}
        />}
      </div>
    )
  }
}

export default InvestPoolDetail
