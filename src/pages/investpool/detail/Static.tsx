import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import Link from 'umi/link'
import { useRequest } from '@umijs/hooks'
import { DeleteOutlined, EyeOutlined, PlusOutlined, CloudDownloadOutlined, DownOutlined } from '@ant-design/icons'
import { Card, Input, Tooltip, Divider, Popconfirm, Breadcrumb, Button, Empty, Space, Popover, notification, Upload, Dropdown } from 'antd'
import { ModelState } from '@/models/investpool'
import PageLoading from '@/components/PageLoading'
import StandardTable, {
  TableListItem,
  TableListParams,
} from '@/components/StandardTable'
import SelectFundModal from '@/components/SelectFundModal'
import SelectManagerModal from '@/components/SelectManagerModal'
import NavPortfolioModal from '@/components/NavPortfolioModal'
import { styleTypeMap, compositTypeMap } from '@/utils/kymDefMapping'
import { formatMessage } from 'umi-plugin-react/locale'
import { getToken } from '@/utils/utils'
import { deleteFundsFromInvestsPool } from '@/services/investpool'

const { Search } = Input
const t = (id: string, params?: any) => formatMessage({ id }, params)

const RemoveButton = ({
  id,
  selectedRows,
}) => {
  const { loading, run: doDelete } = useRequest((data, params) => {
    return deleteFundsFromInvestsPool(id, data, params)
  }, {
    manual: true,
    onSuccess: () => {
      notification.success({
        message: '删除成功',
      })
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    },
  })

  const handleMenuClick = (e) => {
    if (e.key === '1') {
      doDelete({ ids: selectedRows.map(item => item._id) })
    }
    if (e.key === '2') {
      doDelete({ ids: [] }, { deleteAll: 'Y' })
    }
  }
  const items = [
    {
      label: `删除选中的${selectedRows.length}基金`,
      key: '1',
    },
    {
      label: '清空所有基金',
      key: '2',
    },
  ]
  const menuProps = {
    items,
    onClick: handleMenuClick,
  }
  return (
      <Dropdown.Button
        icon={<DownOutlined />}
        loading={loading}
        menu={menuProps}
      >
        批量删除
      </Dropdown.Button>
  )
}

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  fundListData: any;
  location: any;
  match: any;
  currentInvestPool: any;
}

interface ComponentState {
  selectedRows: TableListItem[];
  isPage?: boolean;
  input?: string;
  type: string;
}

/* eslint react/no-multi-comp:0 */
@connect(
  ({
    investpool,
    loading,
    user,
  }: {
    investpool: ModelState;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    fundListData: investpool.fundListData,
    currentInvestPool: investpool.currentInvestPool,
    loading: loading.models.investpool,
    currentUser: user.currentUser,
  }),
)
class InvestPoolDetail extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    const {
      location: { pathname },
    } = props
    this.state = {
      selectedRows: [],
      type: pathname.includes('/static') ? 'static' : 'dynamic',
    }
  }

  componentDidMount() {
    this.loadFundData({})
    this.loadInvestPool()
  }

  loadInvestPool() {
    const {
      dispatch,
      match: {
        params: { id },
      },
    } = this.props
    if (id !== 'investable') {
      dispatch({
        type: 'investpool/fetchOne',
        payload: { id },
      })
    }
  }

  loadFundData = (params: Partial<TableListParams>) => {
    const {
      dispatch,
      match: {
        params: { id },
      },
    } = this.props
    dispatch({
      type: 'investpool/fetchFunds',
      payload: {
        id,
        params: {
          ...params,
          input: this.state.input,
        },
      },
    })
  }

  removeFundsFromInvestPool(ids: string[]) {
    const {
      dispatch,
      match: {
        params: { id },
      },
    } = this.props
    dispatch({
      type: 'investpool/removeFunds',
      payload: {
        id,
        data: {
          ids,
        },
      },
    })
  }

  handleStandardTableChange = (params: Partial<TableListParams>) => {
    this.loadFundData(params)
  };

  handleSeachInput = (value: string) => {
    this.setState({ input: value }, () => {
      this.loadFundData({
        page: 1,
      })
    })
  };

  handleSelectRows = (rows: TableListItem[]) => {
    this.setState({
      selectedRows: rows,
    })
  };

  handleSelectFund = (fundList: TableListItem[]) => {
    const {
      fundListData,
      dispatch,
      match: {
        params: { id },
      },
    } = this.props
    const fundIds = fundListData.list.map(item => item.originalFundId)
    const newFunds = fundList.filter(item => !fundIds.includes(item._id))
    if (newFunds.length) {
      const ids = newFunds.map(item => item._id)
      dispatch({
        type: 'investpool/addFunds',
        payload: {
          id,
          data: {
            ids,
          },
        },
      })
    }
  };

  render() {
    const { fundListData, currentInvestPool, loading, currentUser } = this.props
    const { selectedRows } = this.state
    if (!currentInvestPool && loading) {
      return <PageLoading />
    }
    if (!currentInvestPool) {
      return <Empty style={{ padding: '100px' }} description="你查看的跟踪列表不存在" />
    }
    const getLink = (record: any) => {
      if (currentInvestPool.dataType !== 'manager') {
        return  `/fund/${record.originalFundId}/invest_performance`
      }
      if (record._syncType === 'mutual') {
        return `/manager/persona/${record.originalFundId}/factor_evaluation`
      }
      if (record.isActiveManager && record.ref_manager_id) {
        return `/manager/persona/${record.ref_manager_id}/factor_evaluation`
      }
      return `/activefund/${record.ref_fund_id}/invest_performance`
    }
    let columns = [
      {
        title: '名称',
        dataIndex: 'name',
        render: (text, record) => (
          <Link to={getLink(record)}>{record.name}</Link>
        ),
      },
    ]
    if (currentInvestPool.dataType === 'manager') {
      columns = columns.concat([
        {
          title: '类型',
          dataIndex: 'isActiveManager',
          render: (text: string, record) => {
            if (record._syncType === 'mutual' || (record.isActiveManager && record.ref_manager_id)) {
              return '公募'
            }
            return '专户'
          },
        },
        {
          title: '风格分类',
          dataIndex: 'style_type',
          sorter: true,
          render: (text: string) => styleTypeMap[text] || text,
        },
        {
          title: '基金公司',
          dataIndex: 'company_abbr_name',
          format: 'text',
          sorter: true,
        },
      ])
    } else {
      columns = columns.concat([
        {
          title: '成立时间',
          dataIndex: 'startDate',
          format: 'date',
          align: 'right',
          sorter: true,
        },
        {
          title: '净值结束日期',
          dataIndex: 'navEndDate',
          align: 'right',
          format: 'date',
          sorter: true,
        },
        {
          title: '累计收益',
          dataIndex: 'accReturn',
          sorter: true,
          align: 'right',
          format: 'percentage',
        },
        {
          title: '年化收益',
          dataIndex: 'yearReturn',
          sorter: true,
          align: 'right',
          format: 'percentage',
        },
        {
          title: '最大回撤',
          dataIndex: 'maxDrawdown',
          align: 'right',
          format: 'valPercentage',
          sorter: true,
        },
        {
          title: '波动率',
          dataIndex: 'vol',
          align: 'right',
          format: 'valPercentage',
          sorter: true,
        },
      ])
    }
    columns = columns.concat([{
      title: '操作',
      dataIndex: 'op',
      render: (text, record) => (
        <>
          <Tooltip title="查看详情">
            <a href={getLink(record)} rel="noopener noreferrer" target="_blank">
              <EyeOutlined />
            </a>
          </Tooltip>
          <Divider type="vertical" />
          {currentInvestPool.authorId === currentUser._id &&
          <Popconfirm
            title={`确认删除${record.name}吗？`}
            onConfirm={() => this.removeFundsFromInvestPool([record._id])}
            onCancel={() => {}}
            okText="确认"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>}
        </>
      ),
    }])
    const dataTypeName = currentInvestPool.dataType === 'manager' ? '基金经理' : '基金'
    const uploadProps = {
      name: 'attachment',
      action: `/api/investpools/${currentInvestPool._id}/fund/excel`,
      showUploadList: false,
      headers: {
        Authorization: `${getToken()}`,
      },
      onChange: (info) => {
        const { status } = info.file
        if (status !== 'uploading') {
        }
        if (status === 'done') {
          notification.success({ message: `成功上传 ${info.file.response.length} 个基金.` })
          this.loadFundData({})
        } else if (status === 'error') {
          notification.error({ message: `${info.file.name} 上传失败.` })
        }
      },
    }
    return (
      <div>
        {currentInvestPool && (
          <Breadcrumb className="breadcrumb">
            <Breadcrumb.Item>
              <Link to="/reportbuilder/trackinglist">
                跟踪名单
              </Link>
            </Breadcrumb.Item>
            {false &&
            <Breadcrumb.Item>{dataTypeName}跟踪列表</Breadcrumb.Item>}
            {false &&
            <Breadcrumb.Item>
              <Link to={`/pm/tracking/${currentInvestPool.dataType}/${currentInvestPool.type}`}>
                {currentInvestPool.type === 'static' ? '静态条件筛选' : '动态条件筛选'}
              </Link>
            </Breadcrumb.Item>}
            <Breadcrumb.Item>{currentInvestPool.name}</Breadcrumb.Item>
          </Breadcrumb>
        )}
        <Card
          bordered={false}
          title={
            <>
              <Search
                style={{ width: '300px' }}
                placeholder="按回车进行搜索"
                onSearch={this.handleSeachInput}
              />
            </>
          }
          extra={
            <Space>
              {currentInvestPool.authorId !== currentUser._id ?
              null : currentInvestPool.dataType === 'manager' ?
              <SelectManagerModal onChange={this.handleSelectFund}>
                <Button ghost type="primary" icon={<PlusOutlined />} size="small">
                  添加{dataTypeName}
                </Button>
              </SelectManagerModal> :
              <SelectFundModal dispatch={this.props.dispatch} onChange={this.handleSelectFund}>
                <Button ghost type="primary" icon={<PlusOutlined />} size="small">
                  添加{dataTypeName}
                </Button>
              </SelectFundModal>}
              {currentInvestPool.dataType !== 'manager' && currentInvestPool.authorId === currentUser._id &&
              <Popover
                content={
                  <div>
                    您可以通过上传文件的方式更新基金列表，点击<a href="/files/工作报表-基金跟踪名单模版.xlsx">此处</a>下载模板文件
                  </div>
                }
                title="上传基金列表"
              >
                <Upload {...uploadProps}>
                  <Button icon={<CloudDownloadOutlined />}>上传</Button>
                </Upload>
              </Popover>}
              {currentInvestPool.dataType !== 'manager' && currentInvestPool.authorId === currentUser._id &&
              <NavPortfolioModal
                t={t}
                funds={selectedRows.map(item => {
                  return {
                    ...item,
                    _id: item.originalFundId,
                  }
                })}
              >
                <Button
                  ghost
                  disabled={!selectedRows.length}
                  type="primary"
                  icon={<PlusOutlined />}
                  size="small"
                >
                  创建组合
                </Button>
              </NavPortfolioModal>}
              {selectedRows.length !== 0 &&
              <RemoveButton
                selectedRows={selectedRows}
                id={currentInvestPool._id}
                reloadData={this.loadFundData}
              />}
            </Space>
          }
        >
          <div>
            <StandardTable
              bordered
              disableRowSlection={currentInvestPool.dataType === 'manager'}
              selectedRows={selectedRows}
              loading={loading}
              data={fundListData}
              columns={columns}
              onSelectRow={this.handleSelectRows}
              onChange={this.handleStandardTableChange}
              size="small"
              rowKey="_id"
              rowSelectionType="checkbox"
            />
          </div>
        </Card>
      </div>
    )
  }
}

export default InvestPoolDetail
