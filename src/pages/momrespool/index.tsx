import React, { useState } from 'react'
import {
  Card,
  Affix,
  Tabs,
  Spin,
} from 'antd'
import { connect } from 'dva'
import { useRequest } from '@umijs/hooks'
import { getLongMidDateList } from './service'
import LongMidList from './components/LongMidList'
import ShortEmpList from './components/ShortEmpList'

const { TabPane } = Tabs

function MoMResPool({
  currentUser,
}) {
  const menus = currentUser.menus || []
  const tabs = []
  if (menus.find(item => item.menuId === 'MRP_equity_view')) {
    tabs.push({
      name: '权益',
      tab: '1',
    })
  }
  if (menus.find(item => item.menuId === 'MRP_bond_view')) {
    tabs.push({
      name: '固收',
      tab: '2',
    })
  }
  const [activeTab, setActiveTab] = useState(tabs[0] && tabs[0].tab)
  const handleTabChange = currentTab => {
    setActiveTab(currentTab)
  }
  const { data, loading } = useRequest(() => {
    return getLongMidDateList()
  })
  // const activeTabObj = tabs.find(item => item.tab === activeTab)
  const [childTab, setChildTab] = useState('longListParent')
  const [childTab3rd, setChildTab3rd] = useState('longList')
  return (
    <div>
      <Card className="nav-tab-wrapper">
        <Tabs
          animated={false}
          activeKey={activeTab}
          onChange={handleTabChange}
        >
          {tabs.map(item => {
              return (
                <TabPane tab={item.name} key={item.tab}>
                  <Tabs
                    animated={false}
                    activeKey={childTab}
                    onChange={setChildTab}
                    tabBarExtraContent={null}
                    destroyInactiveTabPane
                  >
                    <TabPane tab="长名单" key="longListParent">
                      <Tabs
                        animated={false}
                        activeKey={childTab3rd}
                        onChange={setChildTab3rd}
                        tabBarExtraContent={null}
                        destroyInactiveTabPane
                      >
                        <TabPane tab="全部" key="longList">
                          <Spin spinning={loading}>
                            <ShortEmpList fundType={item} type="longList" currentUser={currentUser} listName="长名单-全部"/>
                          </Spin>
                        </TabPane>
                        <TabPane tab="定量长名单" key="midList">
                          <Spin spinning={loading}>
                            {!loading &&
                            <LongMidList listName="定量长名单" type="midList" dates={data[item.tab] || []} fundType={item}/>}
                          </Spin>
                        </TabPane>
                        <TabPane tab="提名名单" key="nominationList">
                          <Spin spinning={loading}>
                            <ShortEmpList fundType={item} type="nominationList" currentUser={currentUser} listName="提名名单"/>
                          </Spin>
                        </TabPane>
                        <TabPane tab="专户基金经理" key="activeManager">
                          <Spin spinning={loading}>
                            <ShortEmpList fundType={item} type="activeManager" currentUser={currentUser} listName="专户基金经理"/>
                          </Spin>
                        </TabPane>
                      </Tabs>
                    </TabPane>
                    <TabPane tab="短名单" key="shortList">
                      <Spin spinning={loading}>
                        <ShortEmpList fundType={item} type="shortList" currentUser={currentUser} listName="短名单"/>
                      </Spin>
                    </TabPane>
                    <TabPane tab="聘用名单" key="empList">
                      <Spin spinning={loading}>
                        <ShortEmpList fundType={item} type="empList" currentUser={currentUser} listName="聘用名单"/>
                      </Spin>
                    </TabPane>
                  </Tabs>
                </TabPane>
              )
            }
          )}
        </Tabs>
      </Card>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(MoMResPool)
