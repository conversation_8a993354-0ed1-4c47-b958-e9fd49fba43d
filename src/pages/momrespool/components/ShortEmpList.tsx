import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import _ from 'lodash'
import {
  Table,
  Card,
  Space,
  Button,
  Popover,
  Upload,
  notification,
} from 'antd'
import { CloudUploadOutlined } from '@ant-design/icons'
import { getShortEmpList } from '../service'
import buildTableColumn from '@/utils/buildTableColumn'
import ExportData from '@/components/ExportData'
import { getToken } from '@/utils/utils'
import UplodRecordModal from './UplodRecordModal'

export default ({
  fundType,
  type,
  currentUser,
  listName,
}: {
  fundType: any,
  type: string,
  currentUser: any,
  listName: string,
}) => {
  const [refreshCount, setRefreshCount] = useState(0)
  const [uploading, setUploading] = useState(false)
  const { data = [], loading } = useRequest(() => {
    return getShortEmpList({
      fundType: fundType.tab,
    })
  }, {
    refreshDeps: [refreshCount],
  })
  const yesOrNoRender = val => val === 1 ? '是' : '否'
  const yesOrNoFilter = [{
    text: '是',
    value: 1,
  }, {
    text: '否',
    value: 0,
  }]
  const getColumnFilterFn = dataIndex => (value, record) => value === 1 ? record[dataIndex] === 1 : record[dataIndex] !== 1
  const getOtherCols = type => [{
    title: '是否长名单',
    dataIndex: 'is_longlist',
    typeFilter: 'longList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_longlist'),
  }, {
    title: '是否最新定量长名单',
    dataIndex: 'is_midlist',
    typeFilter: 'midList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_midlist'),
  }, {
    title: '是否提名名单',
    dataIndex: 'is_nominationlist',
    typeFilter: 'nominationList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_nominationlist'),
  }, {
    title: '是否专户',
    dataIndex: 'is_activemanager',
    typeFilter: 'activeManager',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_activemanager'),
  }, {
    title: '是否短名单',
    dataIndex: 'is_shortlist',
    typeFilter: 'shortList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_shortlist'),
  }, {
    title: '是否聘用名单',
    dataIndex: 'is_emplist',
    typeFilter: 'empList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_emplist'),
  }].filter(item => item.typeFilter !== type)
  const columns = [{
    title: '上传日期',
    dataIndex: 'the_date',
    width: 100,
    format: 'date',
  }, {
    title: '基金经理',
    dataIndex: 'manager_name',
    width: 100,
    render: (val, record) => {
      if (!record.manager_ref_id) {
        return val
      }
      return <a href={`/manager/persona/${record.manager_ref_id}/factor_evaluation`} target='__blank'>{val}</a>
    }
  }, {
    title: '管理年限',
    dataIndex: 'management_period',
    // width: 100,
    format: 'number',
    align: 'right',
  },  {
    title: '基金公司',
    dataIndex: 'company_abbr_name',
    // width: 150,
  }, {
    title: '公司非货管理规模(亿元)',
    dataIndex: 'management_scale_non_monetary',
    // width: 160,
    format: 'number',
    align: 'right',
  },
  ]
  if (type === 'nominationList') {
    columns.push({
      title: '提名日期',
      dataIndex: 'nomination_date',
      width: 100,
      format: 'date',
    })
  } else if (type === 'shortList') {
    columns.push({
      title: '短名单日期',
      dataIndex: 'shortlist_date',
      width: 100,
      format: 'date',
    })
  } else if (type === 'empList') {
    columns.push({
      title: '聘用日期',
      dataIndex: 'emp_date',
      width: 100,
      format: 'date',
    })
  }
  const remarkCol = {
    title: '备注',
    dataIndex: 'remark',
  }
  let shortColumns = [
    ...columns,
    ...getOtherCols(type),
    remarkCol,
  ]
  if (type === 'nominationList') {
    shortColumns.push({
      title: '提名人',
      dataIndex: 'nomination_person',
    })
  }
  shortColumns = shortColumns.map(buildTableColumn)
  const shortData = data.filter(item => {
    if (type === 'nominationList') {
      return item.is_nominationlist === 1
    } else if (type === 'shortList') {
      return item.is_shortlist === 1
    } else if (type === 'empList') {
      return item.is_emplist === 1
    } else if (type === 'longList') {
      return item.is_longlist === 1
    } else if (type === 'activeManager') {
      return item.is_activemanager === 1
    }
    return false
  })
  const uploadProps = {
    name: 'attachment',
    action: `/api/momrespool/shortemp/upload?fundType=${fundType.tab}&type=${type === 'nominationList' ? type : 'shortEmpList'}`,
    showUploadList: false,
    headers: {
      Authorization: `${getToken()}`,
    },
    onChange(info) {
      const { status } = info.file
      if (status === 'uploading') {
        setUploading(true)
      } else if (status === 'done') {
        const resp = info.file.response
        if (resp.message) {
          notification.warning({ message: resp.message })
        } else {
          notification.success({ message: `成功上传 ${resp.length} 个基金经理.` })
          setRefreshCount(refreshCount + 1)
        }
        setUploading(false)
      } else if (status === 'error') {
        notification.error({ message: `${info.file.name} 上传失败.` })
        setUploading(false)
      }
    },
  }
  const templateUrl = `/api/momrespool/shortemp/downloadtmpl?fundType=${fundType.tab}&type=${type}&token=${getToken().slice(7)}`
  const canUpload =
    currentUser
      && currentUser.menus.find(item => {
        if (fundType.tab === '1') {
          if (type === 'nominationList') {
            return item.menuId === 'MRP_equity_nomi'
          } else {
            return item.menuId === 'MRP_equity_shortemp'
          }
        } else {
          if (type === 'nominationList') {
            return item.menuId === 'MRP_bond_nomi'
          } else {
            return item.menuId === 'MRP_bond_shortemp'
          }
        }
      })
      && ['shortList', 'empList', 'nominationList'].includes(type)
  let uploadTitle = type === 'nominationList'
    ? '提名' : '短/聘用'
  return (
    <div>
      <Card
        bordered={false}
        className="zero-padding-card"
        extra={
          <Space>
            {canUpload &&
            <Popover
              content={
                <div>
                  上传前请务必点击<a href={templateUrl} target="_blank">此处</a>下载名单模板，上传后将会全量更新{uploadTitle}名单列表，请同一个文件里上传{uploadTitle}名单。
                </div>
              }
              title={`上传${uploadTitle}名单`}
            >
              <Upload {...uploadProps}>
                <Button ghost type="primary" size="small" loading={uploading} icon={<CloudUploadOutlined />}>上传</Button>
              </Upload>
            </Popover>}
            {canUpload &&
            <UplodRecordModal fundType={fundType} listType={type === 'nominationList' ? 'nominationList' : 'shortEmpList'}>
              <Button ghost type="primary" size="small">历史记录</Button>
            </UplodRecordModal>}
            <ExportData title="导出数据" columns={shortColumns} dataSource={shortData} filename={`${fundType.name}-${listName}`}/>
          </Space>
        }
      >
        <Table
          size="small"
          columns={shortColumns}
          loading={loading}
          dataSource={shortData}
        />
      </Card>
    </div>
  )
}
