import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import _ from 'lodash'
import {
  Table,
  Card,
  Space,
} from 'antd'
import { getLongMidList } from '../service'
import buildTableColumn from '@/utils/buildTableColumn'
import SearchSelect from '@/components/SearchSelect'
import ExportData from '@/components/ExportData'

export default ({
  dates,
  type,
  listName,
  fundType,
}: {
  dates: any,
  type: string,
  listName: string,
  fundType: any,
}) => {
  const [currentDate, setCurrentDate] = useState(dates[0])
  const { data, loading } = useRequest(() => {
    return getLongMidList({
      type,
      bizDate: currentDate,
      fundType: fundType.tab,
    })
  }, {
    refreshDeps: [currentDate],
  })
  const yesOrNoRender = val => val === 1 ? '是' : '否'
  const yesOrNoFilter = [{
    text: '是',
    value: 1,
  }, {
    text: '否',
    value: 0,
  }]
  const getColumnFilterFn = dataIndex => (value, record) => value === 1 ? record[dataIndex] === 1 : record[dataIndex] !== 1
  const commonCols = [{
    title: '是否最新长名单',
    dataIndex: 'is_longlist',
    typeFilter: 'longList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_longlist'),
  }, {
    title: '是否定量长名单',
    dataIndex: 'is_midlist',
    typeFilter: 'midList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_midlist'),
  }, {
    title: '是否最新提名名单',
    dataIndex: 'is_nominationlist',
    typeFilter: 'nominationList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_nominationlist'),
  }, {
    title: '是否专户',
    dataIndex: 'is_activemanager',
    typeFilter: 'activeManager',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_activemanager'),
  }, {
    title: '是否最新短名单',
    dataIndex: 'is_shortlist',
    typeFilter: 'shortList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_shortlist'),
  }, {
    title: '是否最新聘用名单',
    dataIndex: 'is_emplist',
    typeFilter: 'empList',
    render: yesOrNoRender,
    filters: yesOrNoFilter,
    onFilter: getColumnFilterFn('is_emplist'),
  }].filter(item => item.typeFilter !== type)
  const columns = [{
    title: '上传日期',
    dataIndex: 'the_date',
    width: 100,
    format: 'date',
  }, {
    title: '基金经理',
    dataIndex: 'manager_name',
    width: 100,
    render: (val, record) => {
      if (!record.manager_ref_id) {
        return val
      }
      return <a href={`/manager/persona/${record.manager_ref_id}/factor_evaluation`} target='__blank'>{val}</a>
    }
  }, {
    title: '管理年限',
    dataIndex: 'management_period',
    // width: 100,
    format: 'number',
    align: 'right',
  },  {
    title: '基金公司',
    dataIndex: 'company_abbr_name',
    // width: 150,
  }, {
    title: '公司非货管理规模(亿元)',
    dataIndex: 'management_scale_non_monetary',
    // width: 160,
    format: 'number',
    align: 'right',
  }, ...commonCols,].map(buildTableColumn)
  return (
    <div>
      <Card
        bordered={false}
        className="zero-padding-card"
        extra={
          <Space>
            <SearchSelect
              placeholder="清选择日期"
              value={currentDate}
              options={dates.map(date => {
                return {
                  title: date,
                  dataIndex: date,
                }
              })}
              onChange={setCurrentDate}
              width="150px"
            />
            <ExportData title="导出数据" columns={columns} dataSource={data} filename={`${fundType.name}-${listName}-${currentDate}`}/>
          </Space>
        }
      >
        <Table
          size="small"
          columns={columns}
          loading={loading}
          dataSource={data}
        />
      </Card>
    </div>
  )
}
