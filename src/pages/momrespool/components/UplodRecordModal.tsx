import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import moment from 'moment'
import {
  Space,
  Modal,
  Button,
  Table,
} from 'antd'
import { getUploadRecord } from '../service'
import { getToken } from '@/utils/utils'
import ExportData from '@/components/ExportData'

export default ({
  children,
  fundType,
  listType,
}: {
  children: any,
  fundType: any,
  listType: any,
}) => {
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [refreshCount, setRefreshCount] = useState(0)
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    p.fundType = fundType.tab
    p.listType = listType
    return getUploadRecord(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount],
  })
  const handleModalOpen = () => {
    setVisibleTrue()
    setRefreshCount(refreshCount + 1)
  }
  const handleClickDownload = (record) => {
    const href = `/api/momrespool/shortemp/excel/${record._id}/download?token=${getToken().slice(7)}`
    window.open(href)
  }
  const columns = [
    {
      title: '文件名',
      dataIndex: 'name',
      fixed: 'left',
    },
    {
      title: '上传人',
      dataIndex: 'author',
      width: 120,
      render: (val) => {
        return val && val.nickname
      }
    },
    {
      title: '上传时间',
      dataIndex: 'updated_at',
      width: 150,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD HH:mm')
      }
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space>
            <ExportData isIcon title="下载" onClick={() => handleClickDownload(record)}/>
          </Space>
        )
      },
    },
  ]

  return (
    <div style={{ display: 'inline-block', width: '100%' }}>
      <div onClick={handleModalOpen}>{children}</div>
      <Modal
        title={
          <>
            <span>上传历史记录</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={800}
        footer={[
          <Button type="primary" onClick={() => {
            setVisibleFalse()
          }}>
            关闭
          </Button>,
        ]}
      >
        <Table
          {...tableProps}
          columns={columns}
          size="small"
          rowKey="_id"
          bordered
        />
      </Modal>
    </div>
  )
}
