import { Effect } from 'dva'
import { Reducer } from 'redux'
import { load, getFundId, getValuation, updateStatRemark } from './service'

export interface StatModelState {
  survey?: any,
  currentFundId?: string
  currentValuation?: any
}

export interface ModelType {
  namespace: 'stat';
  state: StatModelState;
  effects: {
    fetchSurvey: Effect;
    fetchFundId: Effect;
    fetchValuation: Effect;
    changeRemark: Effect;
  };
  reducers: {
    save: Reducer<StatModelState>;
    resetSurveyState: Reducer<StatModelState>;
    saveRemarks: Reducer<StatModelState>;
  };
}

const StatModel: ModelType = {
  namespace: 'stat',
  state: {
    survey: {},
    currentFundId: '',
    currentValuation: {}

  },
  effects: {
    *fetchSurvey({ payload: { id, params } }, { call, put }) {
      const response = yield call(load, id, params)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
    },
    *fetchFundId({ payload: { id, params } }, { call, put }) {
      const response = yield call(getFundId, id, params)
      yield put({
        type: 'save',
        payload: {
          currentFundId: response._id,
        },
      })
    },
    *fetchValuation({ payload: { id, params } }, { call, put }) {
      const response = yield call(getValuation, id, params)
      yield put({
        type: 'save',
        payload: {
          currentValuation: response,
        },
      })
    },
    *changeRemark({ payload: { id, data } }, { call, put }) {
      console.log(1111)
      const response = yield call(updateStatRemark, id, data)
      yield put({
        type: 'saveRemarks',
        payload: {
          data: response,
        },
      })
    },

  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    resetSurveyState(state, action) {
      return {
        ...state,
        ...action.payload.data,
      }
    },
    saveRemarks(state, action) {
      const newSurvey = {
        ...state.survey,
        ...action.payload.data
      }
      return {
        ...state,
        survey: newSurvey,
      }
    }
  },
}

export default StatModel
