import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'dva'
import classnames from 'classnames'
import StatItem from '@/components/StatItem'
import router from 'umi/router'
import moment from 'moment'
import { parse } from 'qs'
import Link from 'umi/link'
import { Spin, Button, Breadcrumb, Tooltip, Row, Col, Tabs, message, Breadcrumb } from 'antd'
import { LeftOutlined, PrinterOutlined } from '@ant-design/icons';
import { getToken } from '@/utils/utils'
import { StatModelState } from './model'
import styles from './style.less'
import ExportData from '@/components/ExportData'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  match: any;
  survey?: any,
  currentFundId?: string,
  currentValuation?: any
}


interface ComponentState {
  currentQuestions: any,
  currentQuestionIndex: number,
  mode: any,
  tabs: any,
  currentTab: string
}

@connect(
  ({
    stat,
    loading,
  }
    : {
      stat: StatModelState;
      loading: {
        effects: {
          [key: string]: boolean;
        };
      };
    }
  ) => {
    return ({
      survey: stat.survey,
      currentFundId: stat.currentFundId,
      currentValuation: stat.currentValuation,
      loading: loading.effects['stat/fetchSurvey'],
    })
  }
)


export default class Stat extends Component<ComponentProps, ComponentState>  {
  constructor(props) {
    super(props)
    const query = this.getQuery().mode || 'view'
    this.state = {
      currentQuestions: [],
      currentQuestionIndex: 1,
      mode: query,
      tabs: [],
      currentTab: ''
    }
  }

  componentDidMount() {
    const {
      match: {
        params: { id }
      },
    } = this.props
    const query = this.getQuery()
    this.load(id, { tab: query.tab })
  }

  load(id: String, params: any) {
    const { dispatch } = this.props
    dispatch({
      type: 'stat/fetchSurvey',
      payload: { id, params },
    })
  }
  getFundId(id: String, param: any) {
    const { dispatch } = this.props
    dispatch({
      type: 'stat/fetchFundId',
      payload: { id, param },
    })
  }

  getValuation(id: String, param: any) {
    const { dispatch } = this.props
    dispatch({
      type: 'stat/fetchValuation',
      payload: { id, param },
    })
  }

  updateStatRemarks(id: string, data: any, dispatch: any) {
    dispatch({
      type: 'stat/changeRemark',
      payload: { id, data },
    })
  }

  resetSurveyState(data: any) {
    const { dispatch } = this.props
    dispatch({
      type: 'stat/resetSurveyState',
      payload: { data },
    })
  }



  componentWillReceiveProps(newProps: any) {
    const {
      currentFundId,
      getFundIdError,
      getValuationIdError,
      currentValuation,
      survey
    } = newProps
    if (currentFundId && !this.props.currentFundId) {
      window.open(`/analysis/net/${currentFundId}`)
      this.resetSurveyState({ currentFundId: null })
    }
    if (getFundIdError) {
      this.resetSurveyState({ getFundIdError: null })
      message.error(getFundIdError.message)
    }
    if (getValuationIdError) {
      this.resetSurveyState({ getValuationIdError: null })
      message.error(getValuationIdError.message)
    }
    if (currentValuation && !this.props.currentValuation) {
      window.open(
        `/analysis/products/${currentValuation.productId}/valuations/${moment(
          currentValuation.date
        ).format('YYYYMMDD')}`
      )
      this.resetSurveyState({ currentValuation: null })
    }
    if (survey && survey !== this.props.survey) {
      const questions = survey.questions
      const tabFilter = item => {
        return questions.filter(
          question => question.tab === item.id && question.type !== 'paragraph'
        ).length
      }
      const tabs = survey.tabs.filter(tabFilter)
      const query = this.getQuery()
      const currentTab = query.tab || (!!tabs && tabs[0].id)
      const currentQuestions = survey.questions.filter(
        question =>
          (question.tab === currentTab) &&
          question.type !== 'paragraph'
      )
      this.setState({
        tabs,
        currentTab,
        currentQuestions
      })
    }
  }

  getQuery() {
    const { location: { search } } = this.props
    return search ? parse(search.substr(1)) : {}
  }

  handleLoadfMore = () => {
    this.setState({
      currentQuestionIndex: this.state.currentQuestionIndex + 1
    })
  }

  handleExportCsv = () => {
    const { survey } = this.props
    const token = getToken()
    const url = `/api/admin/stat/${survey._id}/csv?token=${token.slice(7)}`
    window.open(url)
  }
  onTab = (id) => {
    const { mode } = this.state
    const { survey } = this.props
    this.load(survey._id, { tab: id })
    router.push(`/duediligence/stat/${survey._id}?tab=${id}&mode=${mode}`)
  }
  render() {
    const { currentTab, tabs, currentQuestions, currentQuestionIndex, mode } = this.state
    const pdfFormat =
      (!!this.props.location.search &&
        parse(this.props.location.search.substr(1)).pdfFormat) ||
      (this.props.location.query && this.props.location.query.pdfFormat)
    const isNotA4 = pdfFormat !== 'A4'
    const {
      survey,
      loading,
      location: { pathname }
    } = this.props
    const category = survey && survey.category
    const title = survey && survey.title
    const questions = currentQuestions.slice(0, currentQuestionIndex * 5)
    return (
      <div>
        <div className={classnames(styles.statPage, 'js-pdf-wapper')}>
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>
            <Link to="/duediligence/history">问卷列表</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={`/duediligence/surveyDetail/${survey._id}`}>{survey.title}</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            统计分析
          </Breadcrumb.Item>
        </Breadcrumb>
          {false &&
          <Row className={classnames(styles.headerNav)}>
            <Col span={6}>
              <div style={{ height: 60, display: 'flex', justifyContent: 'start', alignItems: 'center' }}>
                <Breadcrumb>
                  <Breadcrumb.Item>
                    <LeftOutlined />
                    <a onClick={() => router.push(`/duediligence/survey/${survey._id}`)}>问卷详情</a></Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Col>
            <Col span={12}>
              <div style={{ height: 60, fontSize: '18px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <span title={title}>{title}</span>
              </div>
            </Col>
            <Col span={6}>
              <div style={{ height: 60, display: 'flex', justifyContent: 'space-evenly', alignItems: 'center' }}>
                <Tooltip title='导出CSV'>
                  <ExportData onClick={this.handleExportCsv} shape="circle"/>
                </Tooltip>
                <Tooltip title='打印PDF'>
                  <Button type="primary" shape="circle" onClick={() => window.print()} ><PrinterOutlined /></Button>
                </Tooltip>
                <Tooltip title={mode === 'view' ? '当前为浏览模式，点击切换为打印模式' : '当前为打印模式，点击切换为浏览模式'} placement="left">
                  <Button type="dashed">
                    <a href={`${pathname}?tab=${currentTab}&mode=${mode === 'view' ? 'print' : 'view'}`} style={{ textDecoration: 'none' }}>
                      {mode === 'view' ? '浏览模式' : '打印模式'}
                    </a>
                  </Button>
                </Tooltip>
                {isNotA4 && category && (
                  <Button onClick={() => router.push(`/duediligence/stat/cross?category=${category}`)}>
                    交叉分析
                  </Button>
                )}
              </div>
            </Col>
          </Row>}
          <div className={classnames('container', styles.main)}>
            {
              isNotA4 && tabs && tabs.length > 1 && <Tabs activeKey={currentTab} style={{ textAlign: 'center' }} onChange={this.onTab}>
                {
                  tabs.map(tab => <Tabs.TabPane tab={tab.name} key={tab.id} />)
                }
              </Tabs>
            }
            {questions && questions.map((question, index) => {
              return <div key={`${question._id}-${index}`}>
                <StatItem
                  getFundId={this.getFundId}
                  key={question._id}
                  index={index}
                  question={question}
                  isPrint={mode === 'print'}
                  updateStatRemarks={this.updateStatRemarks}
                  survey={survey}
                  {...this.props}
                />
                {(index + 1) % 2 === 0 && <div className="pagebreak" />}
              </div>
            })}
            {(currentQuestions.length >= currentQuestionIndex * 5) && <LoadMoreButton handleClick={this.handleLoadfMore} currentQuestionIndex={currentQuestionIndex} />}
          </div>
        </div>
        {
          loading && <div className={styles.loading}>
            <Spin tip='数据加载中' />
          </div>
        }
      </div >
    )
  }
}

class LoadMoreButton extends Component {
  static propTypes = {
    handleClick: PropTypes.func.isRequired
  }

  state = {
    loading: false
  }

  componentWillReceiveProps() {
    this.state.loading = false
  }

  onClick = () => {
    this.setState({ loading: true })
    setTimeout(() => {
      this.props.handleClick()
    }, 500)
  }

  render() {
    return (
      <Button className="js-pdf-ignore" loading={this.state.loading} type="primary" style={{ marginBottom: '30px' }} block onClick={this.onClick} >加载更多</Button>
    )
  }
}
