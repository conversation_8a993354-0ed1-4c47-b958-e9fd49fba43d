import request from '@/utils/request'
export async function load(id: string, params: any): Promise<any> {
  return request(`/api/admin/stat/${id}`, {
    method: 'GET',
    params,
  })
}

export async function getFundId(id: string, params: any): Promise<any> {
  return request(`/api/admin/survey/${id}/fundid`, {
    method: 'GET',
    params,
  })
}

export async function getValuation(id: string, params: any): Promise<any> {
  return request(`/api/admin/survey/${id}/valuation`, {
    method: 'GET',
    params,
  })
}

export async function updateStatRemark(id: string, data: any) {
  return request(`/api/admin/survey/${id}/statremarks`, {
    method: 'PUT',
    data,
  })

}





