.loading {
  position: fixed;
  margin-left: -54px;
  top: 30%;
  left: 50%;
  z-index: 10000;
}

.statPage {
  margin-top: 20px;
  color:white;
  &:global(.pdf-view) {
    margin-top: 0;
    & > :global(.container) {
      padding-top: 0;
    }
  }
  .headerNav {
    border-top: 1px solid  #ddd;
    border-bottom: 1px solid  #ddd;
    margin-bottom: 10px;
    padding: 0 20px;
  }
  .navButton > a {
    padding: 8px 8px;
  }
  .dataButton {
    padding: 8px;
    float: right;
  }
}

.main {
  .buttonWapper {
    margin-top: -45px;
    float: right;
    :global(.btn) {
      margin-left: 10px;
    }
  }
  :global(.panel) {
    margin-left: -1px;
    margin-right: -1px;
  }
  :global(.panel-default) {
   border: #747474;
   background-color: black;
  }
}
