import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import { connect } from 'dva'
import { Tabs, Affix, Col, Row, Spin } from 'antd'
import BasicInfo from './components/BasicInfo'
import StrategyStyle from './components/StrategyStyle'
import ManagerGanttChart from './components/ManagerGanttChart'
const { TabPane } = Tabs

interface ComponentProps {
  currentFund: any;
  currentManager: any;
  match: any;
  location: any;
}


const Manager: React.FC<ComponentProps> = ({ currentFund, currentManager, location: { pathname } }) => {
  const isFund = /fund|activefund|portfolios/.test(pathname)
  const currentData = isFund ? currentFund : currentManager
  const [activeTab, setActiveTab] = useState('basicinfo')
  return (
    <>
      <Affix offsetTop={44}>
        <Tabs
          onChange={setActiveTab}
          activeKey={activeTab}
          style={{
            marginBottom: 15,
            background: 'rgb(24, 31, 41)',
          }}
        >
          <TabPane tab="基本信息" key="basicinfo">
          </TabPane>
          <TabPane tab="策略风格" key="strategystyle">
          </TabPane>
        </Tabs>
      </Affix>
      {activeTab === 'basicinfo' &&
      <BasicInfo managerData={currentData} />}
      {activeTab === 'strategystyle' &&
      <StrategyStyle managerData={currentData} />}
    </>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    manager: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    currentManager: fund.currentManager,
    loading: loading.models.fund,
  }),
)(Manager)
