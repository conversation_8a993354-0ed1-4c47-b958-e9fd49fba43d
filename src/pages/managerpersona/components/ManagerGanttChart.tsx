import React, { useState } from 'react'
import Echart from '@/components/Chart/Echarts'
import moment from 'moment'

const echarts = Echart.echarts

var HEIGHT_RATIO = 0.6
var DIM_CATEGORY_INDEX = 0
var DIM_TIME_ARRIVAL = 1
var DIM_TIME_DEPARTURE = 2

var _cartesianXBounds = []
var _cartesianYBounds = []

function makeOption(managerData) {
  return {
    tooltip: {
      formatter: (obj) => {
        const { marker, data } = obj
        return `
        ${marker} ${data[3]}<br>
        ${moment(data[1]).format('YYYY-MM-DD')} ~ ${moment(data[2]).format('YYYY-MM-DD')}
        `
      },
    },
    animation: false,
    dataZoom: [{
      type: 'slider',
      xAxisIndex: 0,
      filterMode: 'weakFilter',
      height: 20,
      bottom: 10,
      handleIcon: 'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
      handleSize: '80%',
      showDetail: false,
    }],
    grid: {
      show: true,
      top: 40,
      bottom: 40,
      left: 20,
      right: 20,
      borderWidth: 0,
    },
    xAxis: {
      type: 'time',
      position: 'top',
      splitLine: {
        lineStyle: {
          color: ['#E9EDFF'],
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        lineStyle: {
          color: '#929ABA',
        },
      },
      axisLabel: {
        color: '#929ABA',
        inside: false,
        // align: 'center',
        formatter: (value) => {
          const date = moment(new Date(value))
          return `${date.year()}\n${date.format('MM-DD')}`
        },
      },
    },
    yAxis: {
      axisTick: { show: false },
      splitLine: { show: false },
      axisLine: { show: false },
      axisLabel: { show: false },
      min: 0,
      max: managerData.length,
    },
    series: [{
      id: 'flightData',
      type: 'custom',
      renderItem: renderGanttItem,
      dimensions: ['index', 'startDate', 'endDate', 'name'],
      encode: {
        x: [DIM_TIME_ARRIVAL, DIM_TIME_DEPARTURE],
        y: DIM_CATEGORY_INDEX,
        tooltip: [DIM_CATEGORY_INDEX, DIM_TIME_ARRIVAL, DIM_TIME_DEPARTURE],
      },
      data: managerData,
    }],
  }
}

function renderGanttItem(params, api) {
  var categoryIndex = api.value(DIM_CATEGORY_INDEX)
  var timeArrival = api.coord([api.value(DIM_TIME_ARRIVAL), categoryIndex])
  var timeDeparture = api.coord([api.value(DIM_TIME_DEPARTURE), categoryIndex])

  var coordSys = params.coordSys
  _cartesianXBounds[0] = coordSys.x
  _cartesianXBounds[1] = coordSys.x + coordSys.width
  _cartesianYBounds[0] = coordSys.y
  _cartesianYBounds[1] = coordSys.y + coordSys.height

  var barLength = timeDeparture[0] - timeArrival[0]
  // Get the heigth corresponds to length 1 on y axis.
  var barHeight = api.size([0, 1])[1] * HEIGHT_RATIO
  var x = timeArrival[0]
  var y = timeArrival[1] - barHeight

  var flightNumber = api.value(3) + ''
  var flightNumberWidth = echarts.format.getTextRect(flightNumber).width
  var text = (barLength > flightNumberWidth)
    ? flightNumber : ''

  var rectNormal = clipRectByRect(params, {
    x: x, y: y, width: barLength, height: barHeight,
  })
  var rectVIP = clipRectByRect(params, {
    x: x, y: y, width: (barLength), height: barHeight,
  })
  var rectText = clipRectByRect(params, {
    x: x, y: y, width: barLength, height: barHeight,
  })

  return {
    type: 'group',
    children: [{
      type: 'rect',
      ignore: !rectNormal,
      shape: rectNormal,
      style: api.style(),
    }, {
      type: 'rect',
      ignore: true,
      shape: rectVIP,
      style: api.style({ fill: '#ddb30b' }),
    }, {
      type: 'rect',
      ignore: !rectText,
      shape: rectText,
      style: api.style({
        fill: 'transparent',
        stroke: 'transparent',
        text: text,
        textFill: '#fff',
      }),
    }],
  }
}

function renderAxisLabelItem(params, api) {
  var y = api.coord([0, api.value(0)])[1]
  if (y < params.coordSys.y + 5) {
    return
  }
  return {
    type: 'group',
    position: [
      10,
      y,
    ],
    children: [{
      type: 'path',
      shape: {
        d: 'M0,0 L0,-20 L30,-20 C42,-20 38,-1 50,-1 L70,-1 L70,0 Z',
        x: 0,
        y: -20,
        width: 90,
        height: 20,
        layout: 'cover',
      },
      style: {
        fill: '#368c6c',
      },
    }, {
      type: 'text',
      style: {
        x: 24,
        y: -3,
        text: api.value(1),
        textVerticalAlign: 'bottom',
        textAlign: 'center',
        textFill: '#fff',
      },
    }, {
      type: 'text',
      style: {
        x: 75,
        y: -2,
        textVerticalAlign: 'bottom',
        textAlign: 'center',
        text: api.value(2),
        textFill: '#000',
      },
    }],
  }
}


function clipRectByRect(params, rect) {
  return echarts.graphic.clipRectByRect(rect, {
    x: params.coordSys.x,
    y: params.coordSys.y,
    width: params.coordSys.width,
    height: params.coordSys.height,
  })
}

export default function ManagerChart({ managerFundData, isFund }) {
  const options = makeOption(managerFundData || [])
  const onEvents = {
    click: (params) => {
      if (params.componentType === 'series') {
        const id = params.data[4]
        const url = isFund ? `/fund/${id}/factor_evaluation` : `/manager/persona/${id}/factor_evaluation`
        window.open(url)
      }
    },
  }
  return (
    <div>
      <Echart style={{ height: 400 }} options={options} onEvents={onEvents}/>
    </div>
  )
}
