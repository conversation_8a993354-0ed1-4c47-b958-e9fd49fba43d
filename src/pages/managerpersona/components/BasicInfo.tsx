import React from 'react'
import _ from 'lodash'
import moment from 'moment'
import { Table, Space, Avatar, Row, Col, Row, Col, Empty } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import { assetClassMap, styleTypeMap } from '@/utils/kymDefMapping'
import HistoryMgtScale from './HistoryMgtScale'
import ManagerGanttChart from './ManagerGanttChart'

const BasicInfo = ({ managerData }) => {
  const columns = [
    {
      dataIndex: 'name',
    },
    {
      dataIndex: 'value',
      align: 'right',
    },
  ]
  const data = [
    {
      name: '报告日期',
      value: !managerData.hasPersona ? '' : moment(managerData.the_date).format('YYYY-MM-DD'),
    },
    {
      name: '代表产品',
      value: managerData.ref_fund_name,
    },
    {
      name: '资产类别',
      value: assetClassMap[managerData.asset_type],
    },
    {
      name: '风格类别',
      value: styleTypeMap[managerData.style_type],
    },
    {
      name: '基金公司',
      value: managerData.company,
    },
    {
      name: '管理规模',
      value: managerData.asset_scale ? _.round(managerData.asset_scale / *********, 2) + '亿' : '',
    },
    {
      name: '管理产品数',
      value: managerData.fund_num,
    },
  ]
  const getDateStr = (date, emptyTip) => {
    if (!date || date === 'null') {
      return emptyTip || '未知'
    }
    const dateObj = new Date(date)
    if (dateObj.toString() === 'Invalid Date') {
      return date
    }
    return moment(dateObj).format('YYYY/MM/DD')
  }
  const renderDateRange = (emptyStr) => (val: string, record: any, index: number) => {
    return `${getDateStr(record.start_date, emptyStr)} - ${getDateStr(record.end_date, emptyStr)}`
  }
  const resumeColumns = [
    {
      title: '时间',
      dataIndex: 'start_date',
      render: renderDateRange('至今'),
    },
    {
      title: '机构',
      dataIndex: 'company_abbr_name',
    },
    {
      title: '部门',
      dataIndex: 'department',
    },
    {
      title: '职位',
      dataIndex: 'post',
    },
  ]
  const managerIds = (managerData.managerFundList || []).map(item => item.fundId)
  const managerFundData = (managerData.managerFundList || []).map((item) => {
    return [
      managerIds.indexOf(item.fundId), +new Date(item.startDate),
      item.endToNow ? Date.now() : +new Date(item.endDate),
      item.name,
      item.fundId,
    ]
  })
  return (
    <>
      <Row gutter={16}>
        <Col lg={6} md={24}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Row gutter={16}>
              <Col lg={8} md={24}>
                <div style={{ textAlign: 'center' }}>
                  <Avatar
                    shape="square"
                    size={120}
                    style={{
                      marginTop: 30,
                      width: 'auto',
                      height: 150,
                      background: 'none',
                      verticalAlign: 'middle',
                    }}
                    icon={<UserOutlined />}
                    src={managerData.avatar}
                  />
                  <div style={{ marginTop: 10 }}>{managerData.name}</div>
                </div>
              </Col>
              <Col lg={16} md={24}>
                <Table
                  showHeader={false}
                  pagination={false}
                  columns={columns}
                  dataSource={data}
                  size="small"
                />
              </Col>
            </Row>
            <div>
              <span>综合评价</span>
              <div>{managerData.integrated_evaluation || <Empty style={{ marginTop: 50 }}/>}</div>
            </div>
          </Space>
        </Col>
        <Col lg={18} md={24}>
          <span>工作履历</span>
          <Table
            pagination={false}
            columns={resumeColumns}
            dataSource={managerData.resumeList || []}
            size="small"
          />
          <div style={{ marginTop: 15 }}/>
          <span>历史管理产品</span>
          <ManagerGanttChart managerFundData={managerFundData} isFund/>
          <div style={{ marginTop: 15 }}/>
          <HistoryMgtScale managerData={managerData} />
        </Col>
      </Row>
    </>
  )
}

export default BasicInfo
