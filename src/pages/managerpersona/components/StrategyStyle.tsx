import React from 'react'
import _ from 'lodash'
import { Space, Row, Col } from 'antd'
import FactorSeriesChart from '@/pages/companypersona/components/FactorSeriesChart'
import FactorBarChart from '@/pages/persona/components/FactorBarChart'

const StrategyStyle = ({ managerData }) => {
  const factorSeriesData = managerData.factorSeriesData || []
  const factorDataMap = _.mapValues(_.groupBy(factorSeriesData, 'factor_code'), (values) => {
    return values.sort((fst, snd) => snd.factor_date > fst.factor_date ? 1 : -1)[0]
  })
  const strategyFactors = [{
    title: '长期导向',
    dataIndex: 'FdLongTermOrientation_H_12M_A',
  }, {
    title: '大市值偏好',
    dataIndex: 'FdMsSizeScoPctRnkPosMcap_H_6M_E',
  }, {
    title: '价值约束',
    dataIndex: 'FdMsValueScoPctRnkPosMcap_H_6M_E',
  }, {
    title: '成长驱动',
    dataIndex: 'FdMsGrowthScoPctRnkPosMcap_H_6M_E',
  }, {
    title: '选股专长',
    dataIndex: 'FdTop10Interpretation_Q_3M_E',
  }, {
    title: '左侧倾向',
    dataIndex: 'FdLeftTrdeTrend_H_6M_E',
  }, {
    title: '集中配置',
    dataIndex: 'FdTop10Concentration_Q_3M_E',
  }, {
    title: 'TMT专家',
    dataIndex: 'FdTMTSectorPrefer_H_3Y_A',
  }, {
    title: '消费专家',
    dataIndex: 'FdConsumerSectorPrefer_H_3Y_A',
  }, {
    title: '金融专家',
    dataIndex: 'FdBigFinanceSectorPrefer_H_3Y_A',
  }]
  const perfFactors = [{
    title: '长期收益',
    dataIndex: 'FdRet_M_3Y_A',
  }, {
    title: '短期收益',
    dataIndex: 'FdRet_M_YTD_A',
  }, {
    title: '长期风险',
    dataIndex: 'FdAnnShibShrp_M_3Y_A',
  }, {
    title: '涨市捕获率',
    dataIndex: 'FdMUpCapture_M_3Y_A',
  }, {
    title: '跌市捕获率',
    dataIndex: 'FdMDwnCapture_M_3Y_A',
  }, {
    title: '年度回撤',
    dataIndex: 'FdDb_M_12M_A',
  }, {
    title: '长期波动',
    dataIndex: 'FdAnnVol_M_3Y_A',
  }, {
    title: '配置效应',
    dataIndex: 'StkFdBrisonSwHs300AstAllocRet_H_12M_A',
  }, {
    title: '选股效应',
    dataIndex: 'StkFdBrisonSwHs300StkSelRet_H_12M_A',
  }, {
    title: '超额收益',
    dataIndex: 'FdAbsExcsRet_M_12M_A',
  }]
  const renderFactorBar = factors => {
    return (
      <Row gutter={16}>
        {
          factors.map(factor => {
            const factorValue = factorDataMap[factor.dataIndex] || {}
            return (
              <Col md={4} sm={12} xs={12}>
                <FactorBarChart
                  showLabel
                  factorData={[{
                    name: factor.title,
                    ...factorValue,
                  }]}
                  width={50}
                  height={220}
                />
              </Col>
            )
          })
        }
      </Row>
    )
  }
  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Row gutter={16}>
        <Col lg={12} md={24}>
          <span>策略特征</span>
          {renderFactorBar(strategyFactors.slice(0, 5))}
          {renderFactorBar(strategyFactors.slice(-5))}
        </Col>
        <Col lg={12} md={24}>
          <span>业绩特征</span>
          {renderFactorBar(perfFactors.slice(0, 5))}
          {renderFactorBar(perfFactors.slice(-5))}
        </Col>
      </Row>
      <Row gutter={16}>
        <Col lg={12} md={24}>
          <FactorSeriesChart
            isvpct
            isSelectFilter
            title="大市值偏好"
            data={factorSeriesData || []}
            factors={[{
              title: '大市值偏好',
              dataIndex: 'FdMsSizeScoPctRnkPosMcap_H_6M_E',
            }, {
              title: '价值约束',
              dataIndex: 'FdMsValueScoPctRnkPosMcap_H_6M_E',
            }]}
          />
        </Col>
        <Col lg={12} md={24}>
          <FactorSeriesChart
            isvpct
            title="长期收益"
            data={(factorSeriesData || []).filter(item => /(06-30|12-31)$/.test(item.factor_date))}
            factors={[{
              title: '长期收益',
              dataIndex: 'FdRet_M_3Y_A',
            }]}
          />
        </Col>
      </Row>
    </Space>
  )
}

export default StrategyStyle
