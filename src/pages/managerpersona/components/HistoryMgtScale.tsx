import React, { useState } from 'react'
import { Card, Radio } from 'antd'
import FactorSeriesChart from '@/pages/companypersona/components/FactorSeriesChart'

const HistoryMgtScale = ({ managerData }) => {
  const [assetType, setAssetType] = useState('stock')
  const chartOptions = {
    yAxis: [{
      type: 'value',
      name: '规模(亿)',
    }, {
      type: 'value',
      name: '数量',
    }],
    legend: {
      right: 'auto',
    },
    grid: {
      top: '10%',
    },
  }
  const seriesOptions = {
    yAxisIndex: 1,
    type: 'line',
    stack: 'none',
    smooth: false,
  }
  const factorMap = {
    stock: [{
      title: '管理规模',
      dataIndex: 'FdPmPureStkFdAum_Q_3M_E',
      format: 'hundredMillion',
    }, {
      title: '管理个数',
      dataIndex: 'FdPmPureStkFdNum_Q_3M_E',
      seriesOptions,
    }],
    mix: [{
      title: '管理规模',
      dataIndex: 'FdPmAllocFdAum_Q_3M_E',
      format: 'hundredMillion',
    }, {
      title: '管理个数',
      dataIndex: 'FdPmAllocFdNum_Q_3M_E',
      seriesOptions,
    }],
    bond: [{
      title: '管理规模',
      dataIndex: 'FdPmPureBdFdAum_Q_3M_E',
      format: 'hundredMillion',
    }, {
      title: '管理个数',
      dataIndex: 'FdPmPureBdFdNum_Q_3M_E',
      seriesOptions,
    }],
  }
  return (
    <Card
      size="small"
      title="历史管理情况"
      extra={
        <Radio.Group defaultValue="stock" size="small" onChange={(event) => setAssetType(event.target.value)}>
          <Radio.Button value="stock">纯股</Radio.Button>
          <Radio.Button value="mix">配置</Radio.Button>
          <Radio.Button value="bond">纯债</Radio.Button>
        </Radio.Group>
      }
    >
      <FactorSeriesChart
        title=""
        data={managerData.factorSeriesData || []}
        factors={factorMap[assetType]}
        chartOptions={chartOptions}
      />
    </Card>
  )
}

export default HistoryMgtScale
