import React, { useState } from 'react'
import _ from 'lodash'
import {
  Card,
  Table,
  Space,
  Select,
  Spin,
  Tooltip,
} from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import ExportData from '@/components/ExportData'
import buildTableColumn from '@/utils/buildTableColumn'
import { useRequest } from '@umijs/hooks'
import { getMomVaR99Data } from './service'

const { Option } = Select

const FundVar = ({ data, dataPos }) => {
  const dates = _.uniq(data.map(item => item.BIZ_DATE)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const datesPos = _.uniq(dataPos.map(item => item.BIZ_DATE)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const [date, setDate] = useState(dates[0])
  const [datePos, setDatePos] = useState(datesPos[0])
  const tableData = data
    .filter(item => item.BIZ_DATE === date)
    .map((item, index) => {
      item.NUMBER = (index + 1)
      return item
    })
  const tableDataPos = dataPos
    .filter(item => item.BIZ_DATE === datePos)
    .sort((fst, snd) => {
      return fst.ORDER - snd.ORDER
    })
    .map((item, index) => {
      item.NUMBER = (index + 1)
      return item
    })
  const nameFilters = tableData.map(item => {
    return {
      text: item.FUND_NAME,
      value: item.FUND_NAME,
    }
  })
  const nameFiltersPos = tableDataPos.map(item => {
    return {
      text: item.FUND_NAME,
      value: item.FUND_NAME,
    }
  })
  const columns = [{
    title: '序号',
    dataIndex: 'NUMBER',
    width: 50,
    align: 'center',
  }, {
    title: '产品/账户名',
    dataIndex: 'FUND_NAME',
    width: 120,
    filters: nameFilters,
    onFilter: (value, record) => record.FUND_NAME === value,
  }, {
    title: 'VaR_99',
    dataIndex: 'VAR_99',
    width: 120,
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  }, {
    title: <Space>期末净资产<Tooltip title="仅统计成立满一年的组合"><QuestionCircleOutlined /></Tooltip></Space>,
    titleText: '期末净资产',
    dataIndex: 'NET_ASSET',
    width: 140,
    format: 'hundredMillion',
    align: 'right',
    hasSorter: true,
  }, {
    title: <Space>计量资产规模<Tooltip title="统计所有组合(包含新成立不满一年)"><QuestionCircleOutlined /></Tooltip></Space>,
    titleText: '计量资产规模',
    dataIndex: 'NET_ASSET1',
    width: 140,
    format: 'hundredMillion',
    align: 'right',
    hasSorter: true,
  }, {
    title: <Space>VaR-1yr<Tooltip title="计量资产规模*VaR_99"><QuestionCircleOutlined /></Tooltip></Space>,
    titleText: 'VaR-1yr',
    dataIndex: 'LOSS_AMOUNT',
    width: 100,
    format: 'hundredMillion',
    align: 'right',
    hasSorter: true,
  }].map(buildTableColumn)
  const columnsPos = _.cloneDeep(columns)
    .filter(item => item.dataIndex !== 'NET_ASSET1')
    .map(item => {
      item.title = item.titleText || item.title
      if (item.dataIndex === 'FUND_NAME') {
        item.filters = nameFiltersPos
      }
      return item
    })
  const sxColumns = [{
    title: '产品/账户名',
    dataIndex: 'FUND_NAME',
    width: 120,
    filters: nameFiltersPos,
    onFilter: (value, record) => record.FUND_NAME === value,
  }, {
    title: 'Var-1yr',
    dataIndex: 'LOSS_AMOUNT',
    width: 100,
    format: 'hundredMillion',
    align: 'right',
    hasSorter: true,
  }, {
    title: '计量资产规模',
    dataIndex: 'NET_ASSET1',
    width: 100,
    format: 'hundredMillion',
    align: 'right',
    hasSorter: true,
  }, {
    title: '其中:股票规模',
    dataIndex: 'STOCK_VALUE',
    width: 100,
    format: 'hundredMillion',
    align: 'right',
    hasSorter: true,
  }, {
    title: '其中:债券规模',
    dataIndex: 'BOND_VALUE',
    width: 100,
    format: 'hundredMillion',
    align: 'right',
    hasSorter: true,
  }, {
    title: '其中:基金规模',
    dataIndex: 'FUND_VALUE',
    width: 100,
    format: 'hundredMillion',
    align: 'right',
    hasSorter: true,
  }, {
    title: '差值',
    dataIndex: 'OTHER_VALUE',
    width: 100,
    format: 'hundredMillion',
    align: 'right',
    hasSorter: true,
  }].map(buildTableColumn)
  return (
    <div>
      <Card
        size="small"
        title="委外专户VaR追踪表-净值法"
        extra={
          <Space>
            <Select
              showSearch
              size="small"
              placeholder="选择日期"
              style={{
                // marginTop: '5px',
                width: '120px',
              }}
              value={date}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onChange={setDate}
            >
              {dates.map(item => {
                return (
                  <Option value={item}>
                    {item}
                  </Option>
                )
              })}
            </Select>
            <ExportData title="下载数据" columns={columns} dataSource={tableData} filename={`委外专户组合近10天VaR_99_净值法_${date}`}/>
          </Space>
        }
      >
        <Table
          bordered
          columns={columns}
          dataSource={tableData}
          pagination={false}
          size="small"
          scroll={{ x: 700 }}
        />
        <div style={{ marginBottom: 15 }}></div>
      </Card>
      <Card
        size="small"
        title="委外专户VaR追踪表-持仓穿透法"
        extra={
          <Space>
            <Select
              showSearch
              size="small"
              placeholder="选择日期"
              style={{
                // marginTop: '5px',
                width: '120px',
              }}
              value={datePos}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onChange={setDatePos}
            >
              {datesPos.map(item => {
                return (
                  <Option value={item}>
                    {item}
                  </Option>
                )
              })}
            </Select>
            <ExportData title="下载数据" columns={columnsPos} dataSource={tableDataPos} filename={`委外专户组合近10天VaR_99_持仓穿透法_${datePos}`}/>
          </Space>
        }
      >
        <Table
          bordered
          columns={columnsPos}
          dataSource={tableDataPos}
          pagination={false}
          size="small"
          scroll={{ x: 700 }}
        />
        <div style={{ marginBottom: 15 }}></div>
      </Card>
      <Card
        size="small"
        title="VaR结果月度汇总表"
        extra={
          <Space>
            <ExportData title="VaR结果月度汇总表" columns={sxColumns} dataSource={tableDataPos} filename={`VaR结果月度汇总表_${datePos}`}/>
          </Space>
        }
      >
        <Table
          bordered
          columns={sxColumns}
          dataSource={tableDataPos}
          pagination={false}
          size="small"
          scroll={{ x: 700 }}
        />
        <div style={{ marginBottom: 15 }}></div>
      </Card>
    </div>
  )
}

const FundVarWrapper = () => {
  const { loading, data } = useRequest(() => {
    return getMomVaR99Data()
  }, {
    cacheKey: 'getMomVaR99Data',
  })
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        {!loading &&
        <FundVar data={data.varData} dataPos={data.varDataPos}/>}
      </Spin>
    </div>
  )
}

export default FundVarWrapper
