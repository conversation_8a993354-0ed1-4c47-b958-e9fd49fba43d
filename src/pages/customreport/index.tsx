import React, { useState } from 'react'
import _ from 'lodash'
import { DownloadOutlined } from '@ant-design/icons'
import {
  Card,
  Table,
  Space,
  Select,
  Spin,
  Divider,
  Button,
  Tooltip,
} from 'antd'
import ExportData from '@/components/ExportData'
import { exportNArrayDataAsExcel } from '@/utils/exportAsExcel'
import buildTableColumn from '@/utils/buildTableColumn'
import { useRequest } from '@umijs/hooks'
import { getMomPerfData, getFixedBmkReturnData, getFundBmkRetData } from './service'
import { getToken } from '@/utils/utils'
import { connect } from 'dva'
import ExportDailyReviewModal from '@/components/ExportDailyReviewModal'

const { Option } = Select

const columns = [{
  title: '产品账户',
  fixed: 'left',
  children: [{
    title: '序号',
    dataIndex: 'number',
    fixed: 'left',
    width: 50,
    align: 'center',
  }, {
    title: '委托人',
    dataIndex: 'client',
    fixed: 'left',
    width: 60,
  }, {
    title: '资产类别',
    dataIndex: 'asset_type',
    fixed: 'left',
    width: 80,
  }, {
    title: '产品类型',
    dataIndex: 'fund_type',
    fixed: 'left',
    width: 80,
  }, {
    title: '策略',
    dataIndex: 'strategy',
    fixed: 'left',
    width: 80,
  }, {
    title: '受托人',
    dataIndex: 'trustee',
    fixed: 'left',
    width: 80,
  }, {
    title: '产品/账户名',
    dataIndex: 'fund_name',
    fixed: 'left',
    width: 120,
  }]
}, {
  title: '资金规模',
  children: [{
    title: '年初规模',
    dataIndex: 'net_asset_start',
    width: 80,
    format: 'hundredMillion',
    align: 'right',
  }, {
    title: '期末规模',
    dataIndex: 'net_asset_end',
    width: 80,
    format: 'hundredMillion',
    align: 'right',
  }, {
    title: '平均资金占用',
    dataIndex: 'net_asset_avg',
    width: 100,
    format: 'hundredMillion',
    align: 'right',
  }].map(buildTableColumn),
}, {
  title: '业绩',
  children: [{
    title: 'YTD收益率',
    dataIndex: 'acc_ret',
    width: 100,
    format: 'percentage',
    align: 'right',
  }, {
    title: '业绩基准',
    dataIndex: 'benchmark',
    width: 100,
    ellipsis: true,
  }, {
    title: '基准收益率',
    dataIndex: 'bmk_acc_ret',
    width: 100,
    format: 'percentage',
    align: 'right',
  }, {
    title: '超额收益率',
    dataIndex: 'excess_ret',
    width: 100,
    format: 'percentage',
    align: 'right',
  }].map(buildTableColumn)
}, {
  title: '风险',
  children: [{
    title: 'alpha',
    dataIndex: 'alpha',
    width: 80,
    format: 'percentage',
    align: 'right',
  }, {
    title: 'beta',
    dataIndex: 'beta',
    width: 60,
    format: 'number',
    align: 'right',
  }, {
    title: '年化跟踪误差',
    dataIndex: 'tracking_error',
    width: 100,
    format: 'percentage',
    align: 'right',
  }, {
    title: '最大回撤',
    dataIndex: 'max_drawdown',
    width: 100,
    format: 'percentage',
    align: 'right',
  }, {
    title: '基准最大回撤',
    dataIndex: 'bmk_max_drawdown',
    width: 100,
    format: 'percentage',
    align: 'right',
  }, {
    title: '夏普比率',
    dataIndex: 'sharpe_ratio',
    width: 100,
    format: 'number',
    align: 'right',
  }, {
    title: '信息比率',
    dataIndex: 'information_ratio',
    width: 100,
    format: 'number',
    align: 'right',
  }, {
    title: '卡玛比率',
    dataIndex: 'calmar_ratio',
    width: 100,
    format: 'number',
    align: 'right',
  }].map(buildTableColumn)
}, {
  title: '考核基准名称',
  dataIndex: 'benchmark_name',
  width: 200,
  ellipsis: true,
  align: 'center',
}].map(item => {
  if (item.children) {
    item.children = item.children.map(c => {
      c.dataIndex = c.dataIndex.toUpperCase()
      return c
    })
  } else {
    item.dataIndex = item.dataIndex.toUpperCase()
  }
  return item
})

const CustomReport = ({ data, currentUser }) => {
  const [fund, setFund] = useState({})
  const { loading: loadingFixed, run: runGetFixedBmkReturnData} = useRequest((item) => {
    setFund(item)
    return getFixedBmkReturnData({
      fundId: item.F_CODE,
      benchmarkId: item.BENCHMARK
    })
  }, {
    manual: true,
    onSuccess: (data) => {
      data.unshift(['日期', '收益率'])
      const filename = `${fund.BENCHMARK}收益率序列`
      exportNArrayDataAsExcel(filename, data)
    },
  })
  const { loading: loadingRet, run: runGetFundBmkRetData} = useRequest((item) => {
    setFund(item)
    return getFundBmkRetData({
      fundId: item.F_CODE,
      benchmarkId: item.BENCHMARK
    })
  }, {
    manual: true,
    onSuccess: (data) => {
      data.unshift(['日期', '组合收益率', '基准收益率', '净资产'])
      const filename = `${fund.FUND_NAME}收益率序列`
      exportNArrayDataAsExcel(filename, data)
    },
  })
  const exportCols = columns.reduce((out, item) => {
    return out.concat(item.children ? item.children : [item])
  }, [])
  const dates = _.uniq(data.map(item => item.BIZ_DATE)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const [date, setDate] = useState(dates[0])
  const tableData = data
    .filter(item => item.BIZ_DATE === date)
    .map((item, index) => {
      item.NUMBER = (index + 1)
      return item
    })
  const nameFilters = tableData.map(item => {
    return {
      text: item.FUND_NAME,
      value: item.FUND_NAME,
    }
  })
  const detialColumns = [{
    title: '产品/账户名',
    dataIndex: 'fund_name',
    fixed: 'left',
    width: 120,
    filters: nameFilters,
    onFilter: (value, record) => record.FUND_NAME === value,
  }, {
    title: '操作',
    width: 80,
    dataIndex: '_action',
    render: (value, record) => {
      return (
        <Space>
          <ExportData title="导出组合收益率数据" onClick={() => runGetFundBmkRetData(record)}  loading={loadingRet && fund.F_CODE === record.F_CODE}/>
          {record.STRATEGY !== '相对收益' && false &&
          <ExportData title="导出绝对收益基准数据" onClick={() => runGetFixedBmkReturnData(record)}  loading={loadingFixed && fund.F_CODE === record.F_CODE}/>}
        </Space>
      )
    }
  }, {
    title: 'YTD年化收益率',
    dataIndex: 'annual_ret',
    width: 120,
    format: 'percentage',
    align: 'right',
  }, {
    title: '基准YTD年化收益率',
    dataIndex: 'bmk_annual_ret',
    width: 140,
    format: 'percentage',
    align: 'right',
  }, {
    title: 'YTD年化超额',
    dataIndex: 'annual_excess_ret',
    width: 100,
    format: 'percentage',
    align: 'right',
  }, {
    title: '波动率',
    dataIndex: 'vol_d',
    width: 80,
    format: 'percentage',
    align: 'right',
  }, {
    title: '年化波动率',
    dataIndex: 'vol',
    width: 100,
    format: 'percentage',
    align: 'right',
  }, {
    title: '跟踪误差',
    dataIndex: 'tracking_error_d',
    width: 80,
    format: 'percentage',
    align: 'right',
  }, {
    title: '交易日天数',
    dataIndex: 'trade_days',
    width: 100,
    format: 'integer',
    align: 'right',
  }, {
    title: '年化天数',
    dataIndex: 'ANNUAL_DAYS',
    width: 100,
    format: 'integer',
    align: 'right',
  }, {
    title: '年化无风险利率',
    dataIndex: 'rfr',
    width: 110,
    format: 'percentage',
    align: 'right',
  }, {
    title: '截止日期',
    dataIndex: 'end_date',
    width: 100,
    align: 'right',
  }, {
    title: '基准截止日期',
    dataIndex: 'bmk_end_date',
    width: 100,
    align: 'right',
  }].map(item => {
    if (item.children) {
      item.children = item.children.map(c => {
        c.dataIndex = c.dataIndex.toUpperCase()
        return c
      })
    } else {
      item.dataIndex = item.dataIndex.toUpperCase()
    }
    return item
  }).map(buildTableColumn)
  const handleDownloadDailyReview = () => {
    const href = `/api/products/download/dailyreview?token=${getToken().slice(7)}`
    window.open(href)
  }
  const dailyReviewButton = currentUser.networkSegment === 'trading'
    ? <ExportData onClick={handleDownloadDailyReview}>
      <Button icon={<DownloadOutlined />} size="small">
        日报
      </Button>
    </ExportData>
    : <Tooltip title="由于合规要求，办公网暂不支持日报下载功能">
      <Button icon={<DownloadOutlined />} size="small" disabled>
        日报
      </Button>
    </Tooltip>
  return (
    <div>
      <Card
        size="small"
        title="委外专户业绩追踪表"
        extra={
          <Space>
            <Select
              showSearch
              size="small"
              placeholder="选择日期"
              style={{
                // marginTop: '5px',
                width: '120px',
              }}
              value={date}
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onChange={setDate}
            >
              {dates.map(item => {
                return (
                  <Option value={item}>
                    {item}
                  </Option>
                )
              })}
            </Select>
            <ExportData title="导出业绩追踪数据" columns={exportCols} dataSource={tableData} filename={`委外专户业绩追踪表_${date}`}/>
            <ExportData buttonShape="circle" title="导出中间数据" columns={detialColumns} dataSource={tableData} filename={`委外专户业绩中间数据_${date}`}/>
            <ExportDailyReviewModal networkSegment={currentUser.networkSegment}/>
          </Space>
        }
      >
        <Table
          bordered
          columns={columns}
          dataSource={tableData}
          pagination={false}
          size="small"
          scroll={{ x: 700 }}
        />
        <Divider orientation="center">委外专户业绩中间数据</Divider>
        <Table
          bordered
          columns={detialColumns}
          dataSource={tableData}
          pagination={false}
          size="small"
          scroll={{ x: 700 }}
          rowClassName={(record) => record.BMK_END_DATE !== record.END_DATE ? 'date-error' : ''}
        />
        <div style={{ marginBottom: 15 }}></div>
      </Card>
    </div>
  )
}

const CustomReportWrapper = ({ currentUser }) => {
  const { loading, data } = useRequest(() => {
    return getMomPerfData()
  }, {
    cacheKey: 'getMomPerfData',
  })
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        {!loading &&
        <CustomReport data={data} currentUser={currentUser}/>}
      </Spin>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(CustomReportWrapper)

