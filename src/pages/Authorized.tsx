import React from 'react'
import Redirect from 'umi/redirect'
import { connect } from 'dva'
import pathToRegexp from 'path-to-regexp'
import Authorized from '@/utils/Authorized'
import { ConnectProps, ConnectState, Route, UserModelState } from '@/models/connect'
import config from '../../config/config'
interface AuthComponentProps extends ConnectProps {
  user: UserModelState;
}

const getRouteAuthority = (path: string, routeData: Route[]) => {
  let authorities: string[] | string | undefined
  let authoritiesParent: string[] | string | undefined
  routeData.forEach(route => {
    // match prefix
    if (pathToRegexp(`${route.path}(.*)`).test(path)) {
      authoritiesParent = route.authority || authoritiesParent
      // exact match
      if (route.path === path) {
        authorities = route.authority || authorities || authoritiesParent
      }
      // get children authority recursively
      if (route.routes) {
        authorities = getRouteAuthority(path, route.routes) || authorities
      }
    }
  })
  return authorities || authoritiesParent
}

const AuthComponent: React.FC<AuthComponentProps> = ({
  children,
  route = {
    routes: [],
  },
  location = {
    pathname: '',
  },
  user,
}) => {
  const { currentUser } = user
  // const { routes = [] } = route
  const isLogin = currentUser && currentUser.nickname
  return (
    <Authorized
      authority={getRouteAuthority(location.pathname, config.routes) || ''}
      noMatch={isLogin ? <Redirect to="/exception/403" /> : <Redirect to="/user/login" />}
    >
      {children}
    </Authorized>
  )
}

export default connect(({ user }: ConnectState) => ({
  user,
}))(AuthComponent)
