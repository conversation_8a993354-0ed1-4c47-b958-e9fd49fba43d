/* eslint-disable standard/no-callback-literal */
import React from 'react'
import { connect } from 'dva'
import { Button, Input, Form } from 'antd'
import validator from 'validator'

interface ComponentProps {
  dispatch: any;
  loading?: boolean;
}

interface ComponentState {
  confirmDirty?: boolean;
}

class ChangePassword extends React.Component<ComponentProps, ComponentState> {
  state = {
    confirmDirty: false,
  };

  handleConfirmBlur = (e: any) => {
    const { value } = e.target
    this.setState({ confirmDirty: this.state.confirmDirty || !!value })
  };

  handleSubmit = (values) => {
    const { dispatch } = this.props
    dispatch({
      type: 'user/changePassword',
      payload: {
        password: values.oldPassword,
        newpassword: values.password,
      },
    })
  };

  onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo)
  };

  render() {
    const {
      loading,
    } = this.props
    const { confirmDirty } = this.state
    return (
      <Form
        hideRequiredMark
        layout="vertical"
        onFinish={this.handleSubmit}
        onFinishFailed={this.onFinishFailed}
        style={{ maxWidth: 400 }}
      >
        <Form.Item
          hasFeedback
          label="原始密码"
          name="oldPassword"
          rules={
            [
              {
                required: true,
                message: '请输入原始密码',
              },
            ]
          }
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          hasFeedback
          label="新密码"
          name="password"
          rules={
            [
              {
                required: true,
                message: '请输入密码',
              },
              () => ({
                validator(rule, value) {
                  if (value.length < 8) {
                    return Promise.reject('密码长度至少8位')
                  } else if (!validator.isStrongPassword(value)) {
                    return Promise.reject('密码必须包含数字、大小写字母和特殊字符')
                  } else {
                    return Promise.resolve()
                  }
                },
              }),
            ]
          }
        >
          <Input.Password onBlur={this.handleConfirmBlur} />
        </Form.Item>
        <Form.Item
          hasFeedback
          label="确认密码"
          name="confirm"
          rules={
            [
              {
                required: true,
                message: '请再次输入新密码',
              },
              ({ getFieldValue }) => ({
                validator(rule, value) {
                  if (value && value !== getFieldValue('password')) {
                    return Promise.reject('两次输入的密码不一致')
                  } else {
                    return Promise.resolve()
                  }
                },
              }),
            ]
          }
        >
          <Input.Password onBlur={this.handleConfirmBlur} />
        </Form.Item>
        <Button loading={loading} type="primary" htmlType="submit">
          提交
        </Button>
      </Form>
    )
  }
}

export default connect(
  ({
    loading,
  }: {
    user: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    loading: loading.effects['user/changePassword'],
  }),
)(ChangePassword)
