import React, { Component } from 'react'
import { GridContent } from '@ant-design/pro-layout'
import { Menu } from 'antd'
import { Dispatch } from 'redux'
import styles from './style.less'

const { Item } = Menu

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading?: boolean;
  match: any;
}

interface ComponentState {
  mode: 'inline' | 'horizontal';
  menuMap: {
    [key: string]: string;
  };
  selectKey: string;
}

class AccountSettings extends Component<ComponentProps, ComponentState> {
  main: HTMLDivElement | undefined = undefined;

  constructor(props: ComponentProps) {
    super(props)
    this.state = {
      mode: 'inline',
      selectKey: 'password',
      menuMap: {
        password: '修改密码',
      },
    }
  }

  componentDidMount() {
    window.addEventListener('resize', this.resize)
    this.resize()
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.resize)
  }

  getMenu = () => {
    const { menuMap } = this.state
    return Object.keys(menuMap).map(item => <Item key={item}>{menuMap[item]}</Item>)
  };

  getRightTitle = () => {
    const { selectKey, menuMap } = this.state
    return menuMap[selectKey]
  };

  selectKey = (key: string) => {
    this.setState({
      selectKey: key,
    })
  };

  resize = () => {
    if (!this.main) {
      return
    }
    requestAnimationFrame(() => {
      if (!this.main) {
        return
      }
      let mode: 'inline' | 'horizontal' = 'inline'
      const { offsetWidth } = this.main
      if (this.main.offsetWidth < 641 && offsetWidth > 400) {
        mode = 'horizontal'
      }
      if (window.innerWidth < 768 && offsetWidth > 400) {
        mode = 'horizontal'
      }
      this.setState({
        mode,
      })
    })
  };

  render() {
    const { mode, selectKey } = this.state
    return (
      <GridContent>
        <div
          className={styles.main}
          ref={ref => {
            if (ref) {
              this.main = ref
            }
          }}
        >
          <div className={styles.leftMenu}>
            <Menu mode={mode} selectedKeys={[selectKey]} onClick={({ key }) => this.selectKey(key)}>
              {this.getMenu()}
            </Menu>
          </div>
          <div className={styles.right}>
            <div className={styles.title}>{this.getRightTitle()}</div>
            {this.props.children}
          </div>
        </div>
      </GridContent>
    )
  }
}

export default AccountSettings
