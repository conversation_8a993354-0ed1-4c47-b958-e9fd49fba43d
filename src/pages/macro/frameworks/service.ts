import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryFrameworks(params: TableListParams) {
  return request('/api/historystates', {
    params,
  })
}

export async function queryFramework(id: string) {
  return request(`/api/historystates/${id}`)
}

export async function deleteFramework(id: string) {
  return request(`/api/historystates/${id}`, {
    method: 'delete',
  })
}

export async function updateFramework(id: string, data: any) {
  return request(`/api/historystates/${id}`, {
    method: 'put',
    data,
  })
}

export async function createFramework(data: any) {
  return request(`/api/historystates`, {
    method: 'post',
    data,
  })
}

export async function queryStyles() {
  // const data = require('./test-data.json')
  // const startDate = +new Date('2012-12-31')
  // return data.map(item => {
  //   if (item._qutkeId === 'CredSlect_benchmark') {
  //     item.nets = item.nets.filter(it => it.date >= startDate)
  //   }
  //   return item
  // })//.filter(item => item._qutkeId === 'CredSlect_benchmark')
  return request('/api/funds/styledata')
}
