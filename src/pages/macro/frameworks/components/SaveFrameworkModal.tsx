import React from 'react'
import sum from 'lodash/sum'
import cloneDeep from 'lodash/cloneDeep'
import { Button, Modal, Input, message, Form } from 'antd'
import { isDateValidateError } from '@/utils/allocation'

const TextArea = Input.TextArea

interface ComponentProps {
  framework?: any;
  dispatch: any;
  states?: any;
}

class SaveFrameworkModal extends React.Component<ComponentProps> {
  constructor(props: ComponentProps) {
    super(props)
    const { framework } = props
    this.state = {
      show: false,
      isEdit: framework._id,
      name: framework.name,
      description: framework.description,
    }
  }

  handleNameChange = event => {
    this.setState({ name: event.target.value })
  };

  handleDescriptionChange = event => {
    this.setState({ description: event.target.value })
  };

  close = () => {
    this.setState({ show: false })
  };

  open = () => {
    this.setState({ show: true })
  };

  transformStates = states => {
    return states.map(item => {
      const dateConditions = item.conditions.map(condition => {
        return [
          {
            name: 'gt',
            restrict: +condition.startDate.startOf('date'),
          },
          {
            name: 'lte',
            restrict: +condition.endDate.startOf('date'),
          },
        ]
      })
      return {
        name: item.name,
        probability: Number(item.probability),
        dateConditions,
      }
    })
  };

  save = () => {
    const { name, description, isEdit } = this.state
    if (!name) {
      message.error('请填写名字')
      return
    }
    const { dispatch } = this.props
    const states = this.transformStates(cloneDeep(this.props.states))
    const total = sum(states.map(state => state.probability))
    if (total !== 1) {
      return message.error('状态概率之和不为1')
    }
    if (isDateValidateError(states)) {
      return message.error('状态日期不完备')
    }
    const resultCond = cloneDeep(states).map(state => {
      let conditions = []
      state.dateConditions.forEach((condition, index) => {
        conditions = conditions.concat(
          condition.map(dateCond => ({
            ...dateCond,
            isDate: true,
            group: +index,
          })),
        )
      })
      return {
        ...state,
        dateConditions: undefined,
        conditions: conditions,
      }
    })
    const data = {
      name,
      description,
      states: resultCond,
      stateType: 'date',
    }
    const action = isEdit ? 'framework/editFramework' : 'framework/addFramework'
    dispatch({
      type: action,
      payload: {
        id: this.props.framework._id,
        data,
      },
    })
    this.close()
  };

  render() {
    const { children } = this.props
    const { name, description, isEdit } = this.state

    return (
      <div style={{ display: 'inline-block' }}>
        <div onClick={this.open}>{children}</div>
        <Modal
          title={
            <>
              <span>{isEdit ? '更新分析框架' : '创建分析框架'}</span>
            </>
          }
          visible={this.state.show}
          onCancel={this.close}
          width={700}
          footer={[
            <Button type="primary" onClick={this.save}>
              保存
            </Button>,
          ]}
        >
          <Form layout="vertical">
            <Form.Item label="名称">
              <Input placeholder="名称" value={name} onChange={this.handleNameChange} />
            </Form.Item>
            <Form.Item label="描述">
              <TextArea
                placeholder="描述"
                value={description}
                onChange={this.handleDescriptionChange}
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    )
  }
}

export default SaveFrameworkModal
