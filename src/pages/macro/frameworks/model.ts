import { Effect } from 'dva'
import { Reducer } from 'redux'
import { notification } from 'antd'
import moment from 'moment'
import { TableListData } from '@/components/StandardTable'
import {
  queryFramework,
  queryFrameworks,
  deleteFramework,
  updateFramework,
  createFramework,
} from './service'

export interface ModelState {
  frameworkData?: TableListData;
  framework?: any;
}

export interface ModelType {
  namespace: 'framework';
  state: ModelState;
  effects: {
    fetchFrameworks: Effect;
    fetchFramework: Effect;
    deleteFramework: Effect;
    addFramework: Effect;
    editFramework: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
  };
}

function buildListPaylod(params: any, response: any) {
  const { list, totalNum } = response
  const { page, per_page } = params
  return {
    list,
    pagination: {
      total: totalNum,
      pageSize: per_page || 10,
      current: page || 1,
    },
  }
}

function newDateQuota(startDate: any, endDate: any) {
  let start = +moment(startDate || '2010-01-01')
  const end = endDate ? +moment(endDate) : +moment()
  const day = 1000 * 60 * 60 * 24
  const result = []
  while (start < end) {
    result.push({
      date: start,
      value: start,
    })
    start += day
  }
  return {
    isDate: true,
    name: 'time',
    data: result,
  }
}

const Model: ModelType = {
  namespace: 'framework',

  state: {
    frameworkData: {
      list: [],
      pagination: {},
    },
  },

  effects: {
    *fetchFrameworks({ payload }, { call, put }) {
      const response = yield call(queryFrameworks, payload)
      const data = buildListPaylod(payload, response)
      yield put({
        type: 'save',
        payload: {
          frameworkData: data,
        },
      })
    },
    *fetchFramework({ payload: { id } }, { call, put }) {
      const response = yield call(queryFramework, id)
      yield put({
        type: 'save',
        payload: {
          framework: response,
        },
      })
    },
    *deleteFramework({ payload: { id } }, { call, put }) {
      yield call(deleteFramework, id)
      yield put({
        type: 'removeFromList',
        payload: { id },
      })
      notification.success({ message: '删除成功！' })
    },
    *addFramework({ payload: { data } }, { call, put }) {
      const response = yield call(createFramework, data)
      yield put({
        type: 'save',
        payload: {
          framework: response,
        },
      })
      notification.success({ message: '创建成功！' })
    },
    *editFramework({ payload: { id, data } }, { call, put }) {
      const response = yield call(updateFramework, id, data)
      yield put({
        type: 'save',
        payload: {
          framework: response,
        },
      })
      notification.success({ message: '更新成功！' })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    removeFromList(state: any, action) {
      const { frameworkData } = state
      const newList = frameworkData.list.filter(item => item._id !== action.payload.id)
      return {
        ...state,
        frameworkData: {
          ...frameworkData,
          list: newList,
        },
      }
    },
    addState(state, action) {
      return {
        ...state,
        historyItem: {
          ...state.historyItem,
          quotas: state.historyItem ? state.historyItem.quotas : [newDateQuota()],
          states: Object.assign([], state.historyItem ? state.historyItem.states : null).concat(
            action.payload,
          ),
        },
      }
    },
  },
}

export default Model
