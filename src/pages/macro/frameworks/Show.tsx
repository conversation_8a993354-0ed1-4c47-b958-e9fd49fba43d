import React from 'react'
import _ from 'lodash'
import router from 'umi/router'
import Echart from '@/components/Chart/Echarts'
import { useRequest } from '@umijs/hooks'
import { Card, Breadcrumb, Spin, Row, Col, Affix, Tabs } from 'antd'
import Link from 'umi/link'
import { queryFramework, queryStyles } from './service'
import { getScenarioOptions, getScenarioRankOptions, getScenarioData, getCycleRetChartOptions } from './utils'

const { TabPane } = Tabs

export default ({
  match,
}: {
  match: any,
}) => {
  const {
    params: { id },
  } = match
  const { loading, data: framework } = useRequest(() => {
    return queryFramework(id)
  })
  const { loading: loadingStyle, data: styleData } = useRequest(() => {
    return queryStyles()
  })
  const scenarioData = getScenarioData(framework, styleData)
  const styleNames = (styleData || []).map(item => item.name)
  const cycleCharts = _.map(_.groupBy(scenarioData.cyclesSummary, 'cycle'), (values, name) => {
    return {
      name,
      options: getCycleRetChartOptions(values.sort((fst, snd) => snd.accReturn - fst.accReturn), styleNames),
    }
  })
  let tabs = []
  let parentPath = ''
  if (framework && framework.frameworkStrategy === 'industryProsperity') {
    tabs = [{
      name: '景气跟踪',
      tab: 'overview',
    }, {
      name: '行业景气指数',
      tab: 'detail',
    }, {
      name: '景气度与经济周期',
      tab: 'cycle',
    }, {
      name: '宏观框架',
      tab: 'framework'
    }]
    parentPath = 'industryprosperity'
  } else if (framework && framework.frameworkStrategy === 'ashareRetEst') {
    tabs = [{
      name: '盈利增速预测',
      tab: 'yoyprofit',
    }, {
      name: '估值变化预测',
      tab: 'yoype',
    }, {
      name: '收益率预测',
      tab: 'yoyret',
    }, {
      name: '宏观框架',
      tab: 'framework'
    }]
    parentPath = 'ashareretest'
  }

  const handleTabChange = currentTab => {
    if (currentTab !== 'framework') {
      router.push(`/${parentPath}/overview?id=${framework._id}&name=${framework.name}&tab=${currentTab}`)
    }
  }
  return (
    <Spin spinning={loading || loadingStyle}>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>宏观策略研究</Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link to={'/research/macrostrategy/framework'}>宏观框架</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{framework && framework.name}</Breadcrumb.Item>
      </Breadcrumb>
      {tabs.length !== 0 &&
      <Affix offsetTop={0}>
        <Card className="nav-tab-wrapper">
          <Tabs
            animated={false}
            activeKey="framework"
            onTabClick={handleTabChange}
          >
            {tabs.map(item => {
              return (
                <TabPane tab={item.name} key={item.tab} />
              )
            })}
          </Tabs>
        </Card>
      </Affix>}
      <Card title="框架描述">
        <div>{framework && framework.description}</div>
      </Card>
      <div style={{ marginTop: 10 }} />
      {styleData && !!styleData.length &&
      <Card title="宏观场景分布">
        <Echart options={getScenarioOptions(scenarioData)} />
      </Card>}
      {styleData && !!styleData.length &&
      <Card title="风格收益排名">
        <Echart style={{ height: 400 }} options={getScenarioRankOptions(scenarioData)} />
      </Card>}
      <Card title="风格收益表现">
        <Row gutter={12}>
          {cycleCharts.map(item => (
            <Col lg={12} md={24} xs={24}>
              <span>{item.name}</span>
              <Echart options={item.options} />
            </Col>
          ))}
        </Row>
      </Card>
    </Spin>
  )
}
