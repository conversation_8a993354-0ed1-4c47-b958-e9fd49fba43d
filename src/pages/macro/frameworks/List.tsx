import React from 'react'
import { Dispatch } from 'redux'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Breadcrumb, Divider, Input, Card, Popconfirm, Tooltip } from 'antd';
import Link from 'umi/link'
import { connect } from 'dva'
import StandardTable, {
  TableListData,
  StandardTableColumnProps,
  TableListParams,
} from '@/components/StandardTable'
import { ModelState } from './model'
import getRealPathName from '@/utils/getRealPathname'
import { updateIframeHeight, resetIframeScrolling } from '@/utils/iframe'
import styles from './style.less'
import moment from 'moment'
import { formatMessage } from 'umi-plugin-react/locale'

const t = (id: string, params?: any) => formatMessage({ id }, params)
const { Search } = Input

interface ComponentProps {
  dispatch: Dispatch<any>;
  match: any;
  schemaData?: TableListData;
  portfolioListData?: TableListData;
  frameworkData?: TableListData;
  location?: any;
  loading?: Boolean;
  currentUser: any;
}

@connect(
  ({
    framework,
    loading,
    user,
  }: {
    framework: ModelState;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
    user: any;
  }) => ({
    frameworkData: framework.frameworkData,
    loading: loading.models.saa || loading.models.fund,
    currentUser: user.currentUser,
  }),
)
export default class List extends React.Component<ComponentProps> {
  constructor(props: ComponentProps) {
    super(props)
    this.state = {
      viewMode: 'grid',
    }
  }

  componentDidMount() {
    this.loadListData({})
  }

  componentDidUpdate() {
    updateIframeHeight('#allocationList')
  }

  componentWillUnmount() {
    resetIframeScrolling()
  }

  getRealUrl = (url: string) => {
    const {
      location: { pathname },
    } = this.props
    const isPage = /\/page\//.test(pathname)
    if (isPage) {
      return `/page${url}`
    }
    return url
  };

  removeFramework = (id: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'framework/deleteFramework',
      payload: { id },
    })
  };

  loadListData = (params: Partial<TableListParams>) => {
    const { dispatch } = this.props
    dispatch({
      type: `framework/fetchFrameworks`,
      payload: params,
    })
  };

  handleTableChange = (params: Partial<TableListParams> = {}) => {
    this.loadListData(params)
  };

  handleSeachInput = (value: string) => {
    this.loadListData({
      input: value,
      page: 1,
    })
  };

  render() {
    const data = this.props.frameworkData
    const { loading, currentUser } = this.props
    const frameworkColumns: StandardTableColumnProps[] = [
      {
        title: '名称',
        dataIndex: 'name',
        render: (text, record) => {
          let nextPath = getRealPathName(`/research/macrostrategy/framework/${record._id}`)
          if (record.frameworkStrategy === 'industryProsperity') {
            nextPath = `/industryprosperity/overview?id=${record._id}&name=${record.name}`
          } else if (record.frameworkStrategy === 'ashareRetEst') {
            nextPath = `/ashareretest/yoyprofit?id=${record._id}&name=${record.name}`
          }
          return (
            <Link to={nextPath}>
              {record.name}
            </Link>
          )
        },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        format: 'date',
        align: 'right',
      },
      {
        title: '最后修改时间',
        dataIndex: 'updated_at',
        align: 'right',
        render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
      },
      {
        title: '操作',
        align: 'center',
        render: (text, record) => {
          if (record.authorId !== currentUser._id) {
            return false
          }
          return <>
            <Link to={getRealPathName(`/research/macrostrategy/framework/${record._id}/edit`)}>
              <Tooltip title="编辑">
                <EditOutlined />
              </Tooltip>
            </Link>
            <Divider type="vertical" />
            <Popconfirm
              title={`${t('portfolio.delTip')}${record.name}${t('portfolio.questionEnd')}？`}
              onConfirm={() => this.removeFramework(record._id)}
              onCancel={() => {}}
              okText={t('portfolio.confirm')}
              cancelText={t('portfolio.cancel')}
            >
              <Tooltip title="删除">
                <DeleteOutlined />
              </Tooltip>
            </Popconfirm>
          </>;
        },
      },
    ]
    return (
      <div id="allocationList">
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>资本市场研究</Breadcrumb.Item>
          <Breadcrumb.Item>宏观框架</Breadcrumb.Item>
        </Breadcrumb>
        <Card
          title={
            <>
              <Search
                style={{ width: '300px' }}
                placeholder="按回车进行搜索"
                onSearch={this.handleSeachInput}
              />
            </>
          }
          bordered={false}
          extra={
            <>
              <Link to={getRealPathName('/research/macrostrategy/framework/new/edit')}>
                <PlusOutlined />
                创建宏观框架
              </Link>
            </>
          }
        >
          <StandardTable
            disableRowSlection
            loading={loading}
            columns={frameworkColumns}
            data={data}
            selectedRows={[]}
            onChange={this.handleTableChange}
            size="small"
          />
        </Card>
      </div>
    );
  }
}
