import _ from 'lodash'
import moment from 'moment'
import { cucmulativeReturn, _cucmulativeReturn } from '@/utils/calculator'

// const colorList = ['#fc97af', '#87f7cf', '#f7f494', '#72ccff', '#f7c5a0', '#d4a4eb', '#d2f5a6', '#76f2f2']
// const colorList = ['#dd6b66', '#759aa0', '#e69d87', '#8dc1a9', '#ea7e53', '#eedd78', '#73a373', '#73b9bc', '#7289ab', '#91ca8c', '#f49f42']
const colorList = ['#2ec7c9', '#b6a2de', '#ffb980', '#5ab1ef', '#d87a80', '#8d98b3', '#e5cf0d', '#97b552', '#95706d']

// const styleColors = ['#3fb1e3', '#6be6c1', '#b6a2de', '#ffb980']

// const styleColors = ['#2ec7c9', '#b6a2de', '#5ab1ef', '#ffb980', '#d87a80', '#8d98b3', '#e5cf0d', '#97b552', '#95706d']
// const styleColors = ['#dd6b66', '#759aa0', '#e69d87', '#8dc1a9', '#ea7e53', '#eedd78', '#73a373', '#73b9bc', '#7289ab', '#91ca8c', '#f49f42'].reverse()
const styleColors = ['#0EBF9C', '#E49831', '#E85655', '#46A3DF', '#AE5AE2', '#5E6EE3', '#CE9078', '#8094BD', '#DF5353', '#7798BF']

export function getScenarioOptions({ statuses = {}, cycles = [], factors = [], tooltipData = [] } = {}) {
  const statusNames = []
  const statusCodes = []
  let startTimeOffset = 0
  let xAxisMaxValue = 0
  const dates = []
  const names = []
  // let minTime = 0
  // let maxTime = 0
  // const colorList = ['#2ec7c9', '#b6a2de', '#5ab1ef', '#ffb980', '#d87a80', '#8d98b3', '#e5cf0d', '#97b552', '#95706d']
  const series = factors.map((asset, assetIndex) => {
    const timeSeries = asset.timeSeries
    names.push(asset.name)
    const values = []
    for (const _time in timeSeries) {
      if (assetIndex === 0) {
        dates.push(_time)
      }
      values.push({
        value: timeSeries[_time],
      })
    }
    // if (assetIndex === 0) {
    //   minTime = dates[0]
    //   maxTime = dates[dates.length - 1]
    // }
    return {
      name: asset.name,
      xAxisIndex: 0,
      yAxisIndex: 0,
      symbol: 'none',
      type: 'line',
      data: values,
      color: styleColors[assetIndex],
    }
  })
  const cyclesChartData = cycles.map((cycle, cycleIndex) => {
    if (cycleIndex === 0) {
      startTimeOffset = cycle.startTime
    }
    if (cycleIndex === cycles.length - 1) {
      xAxisMaxValue = cycle.endTime - startTimeOffset
    }
    return [
      cycle.startTime - startTimeOffset,
      cycle.endTime - startTimeOffset,
      100,
      cycle.code,
      cycle.name,
      moment(new Date(cycle.startTime)).format('YYYY-MM-DD'),
      moment(new Date(cycle.endTime)).format('YYYY-MM-DD'),
    ]
  })
  for (const statusCode in statuses) {
    statusCodes.push(statusCode)
    statusNames.push(statuses[statusCode])
  }
  const seriesData = cyclesChartData.map((item, index) => {
    var colorIndex = statusCodes.indexOf(item[3])
    return {
      value: item,
      itemStyle: {
        normal: {
          color: colorList[colorIndex],
        },
      },
    }
  })
  // 经济场景部分条形图
  const scenarioSeriesDatas = []
  for (const code in statuses) {
    const cycleSeriesData = seriesData.filter((_) => {
      return _.value[3] === code
    })
    scenarioSeriesDatas.push(cycleSeriesData)
  }
  const scenarioSeries = scenarioSeriesDatas.map((seriesData, i) => {
    const name = statusNames[i]
    return {
      name: name,
      type: 'custom',
      xAxisIndex: 1,
      yAxisIndex: 1,
      renderItem: renderItem,
      label: {
        normal: {
          show: false,
        },
      },
      dimensions: ['from', 'to', 'profit'],
      encode: {
        x: [0, 1],
        y: 2,
        tooltip: [2],
        itemName: 4,
      },
      data: seriesData,
    }
  })
  const tooltipAssets = tooltipData
  function renderItem(params, api) {
    const yValue = api.value(2)
    const start = api.coord([api.value(0), yValue])
    const size = api.size([api.value(1) - api.value(0), yValue])
    const style = api.style()

    return {
      type: 'rect',
      shape: {
        x: start[0],
        y: start[1],
        width: size[0],
        height: size[1],
      },
      style: style,
    }
  }

  return {
    baseOption: {
      color: colorList,
      grid: {
        top: 0,
        right: 0,
        left: 0,
      },
      tooltip: {
        show: true,
        formatter(param) {
          return `${param.marker} ${param.seriesName}</br>${param.value[5]} ~ ${param.value[6]}`
        },
      },
      // tooltip: {
      //   formatter: function(param) {
      //     const rtnArr = []
      //     rtnArr.push('<table class="tooltip-table">')
      //     rtnArr.push('   <tr>')
      //     rtnArr.push(`     <th class="title" colspan="2">${param.name}</th>`)
      //     rtnArr.push('   </tr>')
      //     rtnArr.push('   <tr>')
      //     rtnArr.push('     <th class="sub-title">资产细分</th>')
      //     rtnArr.push('     <th class="sub-title">夏普比</th>')
      //     rtnArr.push('   </tr>')
      //     tooltipAssets.forEach((asset) => {
      //       rtnArr.push('   <tr>')
      //       rtnArr.push(`     <td>${asset.name}</td>`)
      //       rtnArr.push(`     <td class="value">${asset.value}</td>`)
      //       rtnArr.push('   </tr>')
      //     })
      //     rtnArr.push('</table>')
      //     return rtnArr.join('')
      //   },
      // },
      xAxis: [{
        show: false,
        type: 'category',
        scale: true,
        data: dates.map((value) => {
          return { value }
        }),
        // min: minTime,
        // max: maxTime
      }, {
        show: false,
        max: xAxisMaxValue,
      }],
      yAxis: [{
        show: false,
        scale: true,
        type: 'value',
      }, {
        show: false,
      }],
      series: [...series, ...scenarioSeries],
    },
    media: [{
      query: {
        minWidth: 471,
      },
      option: {
        grid: {
          bottom: 45,
        },
        legend: [{
          bottom: 20,
          data: names,
        }, {
          bottom: '0',
          icon: 'circle',
          data: statusNames,
        }],
      },
    }, {
      query: {
        maxWidth: 470,
      },
      option: {
        grid: {
          bottom: 75,
        },
        legend: [{
          bottom: 49,
          data: names,
        }, {
          bottom: '0',
          icon: 'circle',
          data: statusNames,
        }],
      },
    }],
  }
}

export function getScenarioRankOptions ({ statuses = [], cycles = [], factors= [] } = {}) {
  const assetNames = factors.map(item => item.name)
  const assetCodes = factors.map(item => item.code)
  const statusNames = []
  const statusCodes = []
  let startTimeOffset = 0
  let xAxisMaxValue = 0
  const cyclesChartData = cycles.map((cycle, cycleIndex) => {
    if (cycleIndex === 0) {
      startTimeOffset = cycle.startTime
    }
    if (cycleIndex === cycles.length - 1) {
      xAxisMaxValue = cycle.endTime - startTimeOffset
    }
    return [
      cycle.startTime - startTimeOffset, // 情景开始时间
      cycle.endTime - startTimeOffset, // 情景结束时间
      100,
      cycle.code, // 情景的code
      cycle.name, // 情景的name
      moment(new Date(cycle.startTime)).format('YYYY-MM-DD'),
      moment(new Date(cycle.endTime)).format('YYYY-MM-DD'),
    ]
  })
  function getAssetCyclesChartData(cycles, assetType) {
    let startTimeOffset = 0
    const assetCyclesChartData = cycles.map((cycle, cycleIndex) => {
      if (cycleIndex === 0) {
        startTimeOffset = cycle.startTime
      }
      if (cycleIndex === cycles.length - 1) {
        xAxisMaxValue = cycle.endTime - startTimeOffset
      }
      const stocks = cycle.assets.filter((asset) => {
        return asset.code === assetType
      })
      const stock = stocks[0]
      return [
        cycle.startTime - startTimeOffset, // 情景开始时间
        cycle.endTime - startTimeOffset, // 情景结束时间
        100,
        stock.rank, // 资产的等级
        stock.code, // 资产的code
        stock.name, // 资产的name
      ]
    })
    return assetCyclesChartData
  }
  for (const statusCode in statuses) {
    statusCodes.push(statusCode)
    statusNames.push(statuses[statusCode])
  }
  // 经济场景的颜色
  // const scenarioColors = ['#2ec7c9', '#b6a2de', '#5ab1ef', '#ffb980', '#d87a80', '#8d98b3']
  const scenarioColors = colorList
  // 不同排名的颜色
  const rankingColors = styleColors
  const seriesData = cyclesChartData.map((item, index) => {
    var colorIndex = statusCodes.indexOf(item[3])
    return {
      value: item,
      itemStyle: {
        normal: {
          color: scenarioColors[colorIndex],
        },
      },
    }
  })
  // 经济场景部分条形图
  const scenarioSeriesDatas = []
  for (const code in statuses) {
    const cycleSeriesData = seriesData.filter((_) => {
      return _.value[3] === code
    })
    scenarioSeriesDatas.push(cycleSeriesData)
  }
  const scenarioSeries = scenarioSeriesDatas.map((seriesData, i) => {
    const name = statusNames[i]
    return {
      name: name,
      type: 'custom',
      // name: 'custom',
      itemStyle: {
        normal: {
          color: scenarioColors[i],
        },
      },
      renderItem: renderItem,
      label: {
        normal: {
          show: false,
        },
      },
      dimensions: ['from', 'to', 'profit'],
      encode: {
        x: [0, 1],
        y: 2,
        tooltip: [2],
        itemName: 4,
      },
      tooltip: {
        show: true,
        formatter(param) {
          return `${param.marker} ${param.seriesName}</br>${param.value[5]} ~ ${param.value[6]}`
        },
      },
      data: seriesData,
    }
  })

  function getAssetSeriesData(assetCyclesChartData) {
    const assetSeriesData = assetCyclesChartData.map((item, index) => {
      var colorIndex = assetCodes.indexOf(item[4])
      return {
        value: item,
        itemStyle: {
          normal: {
            color: rankingColors[colorIndex],
          },
        },
      }
    })
    return assetSeriesData
  }
  const assetSeriesData = assetCodes.map(assetCode => {
    return getAssetSeriesData(getAssetCyclesChartData(cycles, assetCode))
  })
  function renderItem(params, api) {
    const yValue = api.value(2)
    const start = api.coord([api.value(0), yValue])
    const size = api.size([api.value(1) - api.value(0), yValue])
    const style = api.style()
    return {
      type: 'rect',
      shape: {
        x: start[0],
        y: start[1],
        width: size[0],
        height: size[1] * 0.04,
      },
      style: style,
    }
  }
  const HEIGHT_ADJ = 1 / factors.length + 0.01
  // 0.04 + 0.04 + 0.23 * 4
  // 场景 + 空白 + 排名 * 4
  function renderAssetItem(params, api) {
    const rank = api.value(3)
    const yValue = api.value(2)
    const start = api.coord([api.value(0), yValue])
    const size = api.size([api.value(1) - api.value(0), yValue])
    const style = api.style()

    return {
      type: 'rect',
      shape: {
        x: start[0],
        y: size[1] * (0.08 + (rank - 1) * HEIGHT_ADJ),
        width: size[0],
        height: size[1] * HEIGHT_ADJ,
      },
      style: style,
    }
  }
  const assetChartSeries = assetNames.map((assetName, assetNameIndex) => {
    return {
      type: 'custom',
      name: assetName,
      renderItem: renderAssetItem,
      label: {
        normal: {
          show: false,
        },
      },
      dimensions: ['from', 'to', 'profit'],
      encode: {
        x: [0, 1],
        y: 2,
        tooltip: [2],
        itemName: 5,
      },
      data: assetSeriesData[assetNameIndex],
    }
  })
  return {
    baseOption: {
      color: rankingColors,
      grid: {
        top: 0,
        right: 0,
        left: 0,
      },
      tooltip: {
        show: true,
        formatter(param) {
          return `${param.marker} ${param.seriesName}`
        },
      },
      xAxis: {
        show: false,
        max: xAxisMaxValue,
      },
      yAxis: {
        show: false,
      },
      series: [...assetChartSeries, ...scenarioSeries],
    },
    media: [{
      query: {
        minWidth: 471,
      },
      option: {
        grid: {
          bottom: 100,
        },
        legend: [{
          bottom: 25,
          data: assetNames,
        }, {
          bottom: 0,
          icon: 'circle',
          data: statusNames,
        }],
      },
    }, {
      query: {
        maxWidth: 470,
      },
      option: {
        grid: {
          bottom: 100,
        },
        legend: [{
          bottom: 54,
          data: assetNames,
        }, {
          bottom: '0',
          icon: 'circle',
          data: statusNames,
        }],
      },
    }],
  }
}

export function getScenarioData({ states } = {}, styles = []) {
  if (!states) {
    return {}
  }
  const statuses = states.reduce((out, item) => {
    out[item.name] = item.name
    return out
  }, {})
  let cycles = states.reduce((out, item) => {
    const resutls = _.map(_.groupBy(item.conditions, 'group'), (values) => {
      const restricts = values.map(item => item.restrict)
      return {
        code: item.name,
        name: item.name,
        startTime: _.min(restricts),
        endTime: _.max(restricts),
      }
    })
    return out.concat(resutls)
  }, [])

  cycles = cycles.map((cycle) => {
    const styleReturns = styles.map((item, index) => {
      const nav = item.nets
        .filter(item => item.date >= cycle.startTime && item.date <= cycle.endTime)
        .map(item => item.value)
      const duration = moment(cycle.endTime).diff(moment(cycle.startTime), 'year', true)
      return {
        code: item.name,
        name: item.name,
        accReturn: cucmulativeReturn(nav),
        duration,
      }
    })
    .sort((fst, snd) => snd.accReturn - fst.accReturn)
    .map((item, index) => {
      item.rank = index + 1
      return item
    })
    cycle.assets = styleReturns
    return cycle
  }).sort((fst, snd) => fst.startTime - snd.startTime)

  const navStartDate = _.min(cycles.map(item => item.startTime))
  if (styles.length) {
    const firstCycle = cycles.find(item => item.endTime >= navStartDate)
    cycles = cycles.filter(item => firstCycle && item.startTime >= firstCycle.startTime)
    if (cycles.length) {
      cycles[0].startTime = navStartDate
    }
  }
  const dateList = _.uniq(
    styles.reduce((out, item) => {
      return out.concat(item.nets.map(item => item.date))
    }, [])
  ).filter(item => item >= navStartDate)
  // const initialTimeSeries = dateList.reduce((out, item) => {
  //   out[item] = null
  //   return out
  // }, {})
  const factors = styles.map(item => {
    const navList = item.nets.filter(item => item.date >= navStartDate)
    const initialNav = navList[0] && navList[0].value
    const initialDate = navList[0] && navList[0].date
    const initialTimeSeries = dateList
      .filter(item => item < initialDate)
      .reduce((out, date) => {
        out[date] = null
        return out
      }, {})
    console.log(item.name, navList.length, new Date(navList[0].date), new Date(navList[navList.length-1].date), new Date(initialDate))
    return {
      code: item.name,
      name: item.name,
      timeSeries: navList.reduce((out, nav) => {
        out[nav.date] = nav.value / initialNav
        return out
      }, { ...initialTimeSeries }),
    }
  })

  const styleReturns = cycles.reduce((out, item) => {
    return out.concat(item.assets.map(asset => {
      asset.cycle = item.name
      return asset
    }))
  }, [])
  const cyclesSummary = _.map(_.groupBy(styleReturns, item => `${item.cycle}-${item.name}`), values => {
    const val = values[0]
    const yearDiff = _.sumBy(values, 'duration')
    const accReturn = _cucmulativeReturn(values.map(item => item.accReturn))
    const yearReturn = Math.pow(1 + accReturn, 1 / yearDiff) - 1
    return {
      cycle: val.cycle,
      name: val.name,
      accReturn: yearReturn,
    }
  })
  return {
    statuses,
    cycles,
    factors,
    tooltipData: [],
    cyclesSummary,
  }
}

export function getCycleRetChartOptions(styles, styleNames) {
  return {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%',
    },
    legend: {
      show: false,
    },
    grid: {
      top: '6%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: styles.map(item => item.name.replace('风格指数', '')),
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        interval: 0,
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '收益率',
        type: 'bar',
        barWidth: '60%',
        data: styles.map(item => {
          return {
            name: item.name,
            value: _.round(item.accReturn * 100, 2),
            itemStyle: {
              color: styleColors[styleNames.indexOf(item.name)],
            },
          }
        }),
      },
    ],
  }
}
