import React from 'react'
import {
  Card,
  Affix,
  Tabs,
} from 'antd'
import router from 'umi/router'

const { TabPane } = Tabs

export default (props) => {
  const tabs = [{
    name: '市场风格',
    tab: 'market',
  }, {
    name: 'Arch<PERSON><PERSON>因子风格',
    tab: 'barra',
  }]
  const splits = props.location.pathname.split('/')
  let activeKey = splits.pop()
  const pathPrefix = splits.join('/')
  const handleTabChange = (activeKey: string) => {
    router.push(`${pathPrefix}/${activeKey}`)
  }
  return (
    <div>
      <div style={{ marginBottom: 15 }}>
        <Affix offsetTop={0}>
          <Card className="nav-tab-wrapper">
            <Tabs
              animated={false}
              activeKey={activeKey}
              onTabClick={handleTabChange}
              tabBarExtraContent={null}
            >
              {tabs.map(item => {
                return (
                  <TabPane tab={item.name} key={item.tab} />
                )
              })}
            </Tabs>
          </Card>
        </Affix>
      </div>
      {props.children}
    </div>
  )
}
