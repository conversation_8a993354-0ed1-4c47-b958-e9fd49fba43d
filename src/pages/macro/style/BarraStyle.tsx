import React, { useState } from 'react'
import _ from 'lodash'
import {
  Spin,
  Card,
  Space,
  Button,
} from 'antd'
import { useRequest } from '@umijs/hooks'
import { DownloadOutlined } from '@ant-design/icons'
import { getBarraStyleData } from './service'
import Echart from '@/components/Chart/Echarts'
import SelectDate from '@/components/SelectDate'
import ExportData from '@/components/ExportData'
import { exportMultiTableAsExcel } from '@/utils/exportAsExcel'

const getChartOptions = (data) => {
  const option = {
    grid: {
      left: '4%',
      right: '4%',
      bottom: '18%',
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        interval: 0,
        rotate: 30,
      },
      data: data.map(item => item.style),
      boundaryGap: true,
      splitArea: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [{
      type: 'value',
    }, {
      type: 'value',
    }],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const param1 = params[0]
        return [
          param1.axisValue + ': ',
          '月度收益率: ' + param1.data + '%',
        ].join('<br/>')
      },
    },
    series: [
      {
        name: '月度收益率',
        type: 'bar',
        data: data.map(item => _.round(item.month_ret * 100, 2)),
      },
    ],
  }

  return option
}

const getFactorChartOptions = (data) => {
  const option = {
    grid: {
      left: '4%',
      right: '4%',
      bottom: '18%',
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        // interval: 0,
        rotate: 50,
      },
      data: data.map(item => item.the_date),
      boundaryGap: true,
      splitArea: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [{
      type: 'value',
    }, {
      type: 'value',
    }],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const param1 = params[0]
        const param2 = params[1]
        return [
          param1.axisValue + ': ',
          '月度收益率: ' + param1.data + '%',
          '月度净值: ' + param2.data,
        ].join('<br/>')
      },
    },
    series: [
      {
        name: '月度收益率',
        type: 'bar',
        data: data.map(item => _.round(item.month_ret * 100, 2)),
      },
      {
        name: '月度净值',
        type: 'line',
        data: data.map(item => _.round(item.month_nav, 4)),
        smooth: false,
        showSymbol: false,
        yAxisIndex: 1,
      },
    ],
  }

  return option
}

const BarraStyle = ({ styleData, dates, factors }) => {
  const [date, setDate] = useState(dates[0])
  const [factor, setFactor] = useState(factors[0])
  const onEvents = {
    click: (action) => {
      setFactor(action.name)
    },
  }
  const chartData = styleData.filter(item => {
    return item.the_date === date
  })
  const factorData = styleData.filter(item => {
    return item.style === factor
  })
  const options = getChartOptions(chartData)
  const factorOptions = getFactorChartOptions(factorData)
  const styleRetCols = [{
    title: '因子',
    dataIndex: 'style',
  }, {
    title: '月度收益率',
    dataIndex: 'month_ret',
  }]
  const factorDataCols = [{
    title: '日期',
    dataIndex: 'the_date',
  }, {
    title: '月度收益率',
    dataIndex: 'month_ret',
  }, {
    title: '月度净值',
    dataIndex: 'month_nav',
  }]
  return (
    <div>
      <Card
        size="small"
        title="风格因子收益率"
        extra={
          <Space>
            <SelectDate
              date={date}
              dates={dates}
              onChange={setDate}
            />
            <ExportData title="导出" columns={styleRetCols} dataSource={chartData} filename="风格因子收益率"/>
          </Space>
        }
      >
        <Echart style={{ height: '400px' }} options={options} onEvents={onEvents}/>
      </Card>
      <div style={{ marginBottom: 15 }}></div>
      <Card
        size="small"
        title={`${factor} 收益率与净值`}
        extra={
          <Space>
            <SelectDate
              date={factor}
              dates={factors}
              onChange={setFactor}
              width="150px"
            />
            <ExportData title="导出" columns={factorDataCols} dataSource={factorData} filename={`${factor} 收益率与净值`}/>
          </Space>
        }
      >
        <Echart style={{ height: '400px' }} options={factorOptions}/>
      </Card>
    </div>
  )
}

const getSummaryDownloadData = (data, dates, factors) => {
  const columns1 = [{
    title: '日期',
    dataIndex: 'the_date',
  }, {
    title: '风格',
    dataIndex: 'style',
  }, {
    title: '月度收益率',
    dataIndex: 'month_ret',
  }, {
    title: '净值',
    dataIndex: 'month_nav',
  }]
  const dataMap = data.reduce((out, item) => {
    out[`${item.the_date}_${item.style}`] = item
    return out
  }, {})
  const columns2 = factors.map(item => {
    return {
      title: item,
      dataIndex: item,
    }
  })
  columns2.unshift({
    title: '日期',
    dataIndex: 'the_date',
  })
  const retData = dates.map(date => {
    return factors.reduce((out, factor) => {
      out[factor] = dataMap[`${date}_${factor}`].month_ret
      return out
    }, { the_date: date })
  })
  const navData = dates.map(date => {
    return factors.reduce((out, factor) => {
      out[factor] = dataMap[`${date}_${factor}`].month_nav
      return out
    }, { the_date: date })
  })
  return [{
    name: '原始数据',
    columns: columns1,
    dataSource: data,
  }, {
    name: '因子月度收益',
    columns: columns2,
    dataSource: retData,
  }, {
    name: '因子净值',
    columns: columns2,
    dataSource: navData,
  }]
}

const BarraStyleWrapper = () => {
  const { loading, data=[] } = useRequest(() => {
    return getBarraStyleData()
  }, {
    cacheKey: 'getBarraStyleData',
  })
  const dates = _.uniq(data.map(item => item.the_date)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const factors = _.uniq(data.map(item => item.style))
  const handleDownload = () => {
    const tables = getSummaryDownloadData(data, dates, factors)
    exportMultiTableAsExcel('因子历史数据', tables)
  }
  return (
    <div style={{ minHeight: 400 }}>
      <Card
        size="small"
        extra={
          <Space>
            <Button type="primary" icon={<DownloadOutlined />} size="small" onClick={handleDownload}>
              导出数据
            </Button>
          </Space>
        }
      >
      </Card>
      <Spin spinning={loading}>
        {!loading &&
        <BarraStyle styleData={data} dates={dates} factors={factors}/>}
      </Spin>
    </div>
  )
}

export default BarraStyleWrapper
