import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import moment from 'moment'
import { connect } from 'dva'
import { DeleteOutlined, EditOutlined, PlusOutlined, TagFilled } from '@ant-design/icons'
import { Form } from '@ant-design/compatible'
import '@ant-design/compatible/assets/index.css'
import {
  Table,
  Button,
  Tooltip,
  Divider,
  Popconfirm,
  Modal,
  Input,
  DatePicker,
  Breadcrumb,
  Card,
  notification,
  Row,
  Col,
} from 'antd'
import ColorPicker from '@/components/ColorPicker'
import { queryScenarios, updateScenario, createScenario, deleteScenario } from './service'
import t from '@/utils/t'
import styles from './style.less'

const TextArea = Input.TextArea
const InputGroup = Input.Group
const Search = Input.Search

interface ScenarioListItem {
  _id?: string,
  name: string,
  description: string,
  color: string,
  visibility?: string,
  startDate?: string,
  endDate?: string,
  endToNow?: boolean,
  updated_at?: string,
}

const List = ({
  currentUser,
  form,
}: {
  currentUser: any,
  form: any,
}) => {
  const { getFieldDecorator, setFieldsValue, validateFields } = form
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState(null)
  const [refreshCount, setRefreshCount] = useState(0)
  const [input, setInput] = useState('')
  const [endToNow, setEndToNow] = useState(false)
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    return queryScenarios(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const onSaveScenarioSuccess = () => {
    setVisibleFalse()
    setRefreshCount(refreshCount + 1)
    notification.success({
      message: '保存成功',
    })
  }
  const { loading: updatingScenario, run: doUpdateScenario} = useRequest((id, data) => {
    return updateScenario(id, data)
  }, {
    manual: true,
    onSuccess: onSaveScenarioSuccess,
  })
  const { loading: creatingScenario, run: doCreateScenario} = useRequest((data) => {
    return createScenario(data)
  }, {
    manual: true,
    onSuccess: onSaveScenarioSuccess,
  })
  const { run: doDeleteScenario } = useRequest((id) => {
    return deleteScenario(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const handleEndToNowClick = () => {
    setFieldsValue({
      endDate: moment(),
      endToNow: true,
    })
    setEndToNow(true)
  }
  const handleEndDateChange = () => {
    setFieldsValue({
      endToNow: false,
    })
    setEndToNow(false)
  }
  const handleOpenModal = (record: ScenarioListItem) => () => {
    setCurrentItem(record)
    setVisibleTrue()
    setEndToNow(record.endToNow)
    const newFormValues = {
      ...record,
      dateRange: record._id ? [
        moment(new Date(record.startDate)),
        moment(new Date(record.endDate)),
      ] : [],
    }
    if (record.startDate) {
      newFormValues.startDate = moment(new Date(record.startDate))
    }
    if (record.endDate) {
      newFormValues.endDate = moment(new Date(record.endDate))
    }
    if (record.endToNow) {
      newFormValues.endDate = moment()
    }
    setFieldsValue(newFormValues)
  }
  const handleClickSave = () => {
    validateFields((err: any, values: any) => {
      if (err) {
        return
      }
      const newValues = {
        ...values,
        startDate: values.startDate.format('YYYY-MM-DD'),
        endDate: values.endDate.format('YYYY-MM-DD'),
      }
      if (currentItem._id) {
        doUpdateScenario(currentItem._id, newValues)
      } else {
        doCreateScenario(newValues)
      }
    })
  }
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text: any, record: ScenarioListItem) => {
        return <span><TagFilled style={{ color: record.color, marginRight: 10 }} />{text}</span>
      },
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      sorter: true,
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      sorter: true,
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      align: 'right',
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        if (record.authorId !== currentUser._id) {
          return false
        }
        return <>
          <Tooltip title="编辑">
            <EditOutlined onClick={handleOpenModal(record)} />
          </Tooltip>
          <Divider type="vertical" />
          <Popconfirm
            title={`${t('portfolio.delTip')}${record.name}${t('portfolio.questionEnd')}？`}
            onConfirm={() => { doDeleteScenario(record._id) }}
            onCancel={() => {}}
            okText={t('portfolio.confirm')}
            cancelText={t('portfolio.cancel')}
          >
            <Tooltip title="删除">
              <DeleteOutlined />
            </Tooltip>
          </Popconfirm>
        </>
      },
    },
  ]

  return (
    <div>
      <Breadcrumb className={styles.breadcrumb}>
        <Breadcrumb.Item>资本市场研究</Breadcrumb.Item>
        <Breadcrumb.Item>情景事件</Breadcrumb.Item>
      </Breadcrumb>
      <Card
        title={
          <>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
        extra={
          <>
            <a onClick={
              handleOpenModal({ name: '', description: '', color: '', startDate: null, endDate: null })
            }>
              <PlusOutlined />
              新建情景
            </a>
          </>
        }
      >
        <Table size="small" columns={columns} rowKey="_id" {...tableProps} />
      </Card>
      <Modal
        title={
          <>
            <span>{currentItem && currentItem._id ? '编辑情景' : '新建情景'}</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={700}
        footer={[
          <Button
            type="primary"
            onClick={handleClickSave}
            loading={updatingScenario || creatingScenario}
          >
            保存
          </Button>,
        ]}
      >
        <Form layout="vertical" hideRequiredMark>
          <Row gutter={16}>
            <Col lg={16} md={16}>
              <Form.Item label="名称">
                {getFieldDecorator('name', {
                  rules: [
                    {
                      required: true,
                      message: '请输入名称',
                    },
                  ],
                })(<Input placeholder="名称"/>)}
              </Form.Item>
            </Col>
            <Col lg={8} md={8}>
              <Form.Item label="颜色">
                {getFieldDecorator('color', {
                  rules: [
                    {
                      required: true,
                      message: '请选择颜色',
                    },
                  ],
                })(<ColorPicker />)}
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="描述信息">
            {getFieldDecorator('description', {
              rules: [
                {
                  required: true,
                  message: '请输入描述信息',
                },
              ],
            })(<TextArea placeholder="描述信息"/>)}
          </Form.Item>
          <Row gutter={16} className={styles.dateRange}>
            <Col lg={12} md={12}>
              <Form.Item label="开始时间">
                {getFieldDecorator('startDate', {
                  rules: [
                    {
                      required: true,
                      message: '请选择开始时间',
                    },
                  ],
                })(<DatePicker/>)}
              </Form.Item>
            </Col>
            <Col lg={12} md={12}>
              <Form.Item label="结束时间">
                <InputGroup compact>
                  {getFieldDecorator('endDate', {
                    rules: [
                      {
                        required: true,
                        message: '请选择结束时间',
                      },
                    ],
                  })(
                    <DatePicker
                      style={{ width: '120px' }}
                      onChange={handleEndDateChange}
                    />
                  )}
                  {getFieldDecorator('endToNow')(
                    <Button
                      onClick={handleEndToNowClick}
                      className={{ active: endToNow }}
                    >
                      至今
                    </Button>
                  )}
                </InputGroup>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(Form.create()(List))
