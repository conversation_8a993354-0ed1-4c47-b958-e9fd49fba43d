import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryScenarios(params: TableListParams) {
  return request('/api/scenarios', {
    params,
  })
}

export async function queryScenario(id: string) {
  return request(`/api/scenarios/${id}`)
}

export async function deleteScenario(id: string) {
  return request(`/api/scenarios/${id}`, {
    method: 'delete',
  })
}

export async function updateScenario(id: string, data: any) {
  return request(`/api/scenarios/${id}`, {
    method: 'put',
    data,
  })
}

export async function createScenario(data: any) {
  return request(`/api/scenarios`, {
    method: 'post',
    data,
  })
}
