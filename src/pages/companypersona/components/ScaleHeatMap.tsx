import React from 'react'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import { Card, Table, Spin } from 'antd'
import Echart from '@/components/Chart/Echarts'
import buildTreeDataWithSummary from '@/utils/buildTreeDataWithSummary'
import { queryCompanyFunds } from '../service'

const renderTreeChart = (rawData) => {
  const data = buildTreeDataWithSummary(rawData, [{
    dataIndex: 'latestScale',
  }, {
    dataIndex: 'ratio',
  }, {
    dataIndex: 'value',
    getValue: item => {
      return [item.ratio, item.ratio]
    },
  }, {
    dataIndex: 'name',
    getValue: item => item.name,
  }, {
    dataIndex: 'fundClassWd1',
    getValue: item => item.fundClassWd1,
  }, {
    dataIndex: 'fundClassWd2',
    getValue: item => item.fundClassWd2,
  }], ['fundClassWd1', 'fundClassWd2'], 'name')
  const formatUtil = Echart.echarts.format
  const levelOption = [
    {
      itemStyle: {
        normal: {
          borderColor: '#777',
          borderWidth: 0,
          gapWidth: 1,
        },
      },
      upperLabel: {
        normal: {
          show: false,
        },
      },
      colorMappingBy: 'value',
      visualDimension: 1,
      color: ['#0EBF9C', '#e49732', '#e85654'],
    },
    {
      itemStyle: {
        normal: {
          borderColor: '#252b35',
          borderWidth: 3,
          gapWidth: 1,
        },
        emphasis: {
          borderColor: '#ddd',
        },
      },
      colorMappingBy: 'value',
      visualDimension: 1,
      color: ['#0EBF9C', '#e49732', '#e85654'],
    },
    {
      itemStyle: {
        normal: {
          borderColor: '#252b35',
          borderWidth: 3,
          gapWidth: 1,
        },
        emphasis: {
          borderColor: '#ddd',
        },
      },
      colorMappingBy: 'value',
      visualDimension: 1,
      color: ['#0EBF9C', '#e49732', '#e85654'],
    },
    {
      itemStyle: {
        normal: {
          borderColor: '#252b35',
          borderWidth: 0,
          gapWidth: 0,
        },
        emphasis: {
          borderColor: '#ddd',
        },
      },
    },
  ]
  const options = {
    backgroundColor: '#252b35',
    grid: {
      backgroundColor: '#252b35',
    },
    tooltip: {
      formatter: function (info) {
        const value = info.value[1] || 0
        const treePathInfo = info.treePathInfo
        const treePath = []
        for (let i = 1; i < treePathInfo.length; i++) {
          treePath.push(treePathInfo[i].name)
        }
        return [
          '<div class="tooltip-title">' + formatUtil.encodeHTML(treePath.join('/')) + '</div>',
          '占比: ' + value.toFixed(2) + '%',
        ].join('')
      },
    },
    series: [{
      name: '规模分布',
      type: 'treemap',
      width: '100%',
      height: '100%',
      visibleMin: 300,
      // visualMin: -2,
      // visualMax: 2,
      leafDepth: 2,
      colorMappingBy: 'value',
      visualDimension: 1,
      color: ['#0EBF9C', '#e49732', '#e85654'],
      label: {
        normal: {
          show: true,
          color: '#252b35',
          formatter: function (info) {
            const { value } = info
            return [info.name, `${_.round(value[1], 2)}%`].filter(Boolean).join('\n\n')
          },
        },
      },
      upperLabel: {
        show: true,
        height: 30,
        formatter: param => `${param.name}: ${_.round(param.value[1], 2)}%`,
      },
      itemStyle: {
        normal: {
          borderColor: '#fff',
          backgroundColor: '#252b35',
        },
        borderColor: '#252b35',
      },
      levels: levelOption,
      data: data,
    }],
  }
  return (
    <>
      <Echart options={options} style={{ height: 400 }} />
    </>
  )
}

const ScaleHeatMap = ({ companyData }) => {
  const { loading, data: funds = [] } = useRequest(() => {
    return queryCompanyFunds(companyData.sname)
  })
  return (
    <Spin spinning={loading}>
      {renderTreeChart(funds)}
    </Spin>
  )
}

export default ScaleHeatMap
