import React from 'react'
import _ from 'lodash'
import Echart from '@/components/Chart/Echarts'

function getSeriesChartData(factorData, factors) {
  const sortedData = factorData.sort((fst, snd) => fst.the_date > snd.the_date ? 1 : -1)
  const dates = sortedData.map(item => item.the_date)
  const series = factors.map(factor => {
    return {
      name: factor.title,
      type: factor.chartType || 'bar',
      stack: factor.chartType === 'line' ? 'none' : 'stack',
      data: sortedData.map(item => _.round(item[factor.dataIndex], 2)),
    }
  })
  return {
    dates, series,
  }
}

const getChartOptions = (factorData, factors) => {
  const { dates, series } = getSeriesChartData(factorData, factors)
  const option = {
    legend: {
      data: series.map(item => item.name),
      textStyle: {
        fontSize: 10,
      },
      right: 0,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '5%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: dates,
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series,
  }

  return option
}

export default ({
  title,
  data,
  factors,
}: {
  data: any,
  title: string,
  factors: any,
}) => {
  const options = getChartOptions(data, factors)
  return (
    <div>
      <span>{title}</span>
      <Echart style={{ height: 400 }} options={options} />
    </div>
  )
}
