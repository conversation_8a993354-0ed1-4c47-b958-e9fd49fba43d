import React from 'react'
import { Table, Typography, Space } from 'antd'
import Echart from '@/components/Chart/Echarts'

const { Paragraph } = Typography

const CompanyBasicInfo = ({ companyData }) => {
  const data = [
    {
      value: companyData.name,
      name: '',
    },
    {
      name: companyData.briefing,
      value: '',
      id: 'briefing',
    },
    {
      name: '法人代表',
      value: companyData.chairman,
    },
    {
      name: '非货规模排名',
      value: companyData.noneMoneyScale,
    },
    {
      name: '核心股东变动次数',
      value: companyData.coreHolderChanges,
    },
  ]
  const columns = [
    {
      dataIndex: 'name',
      width: 160,
      render: (value, row, index) => {
        if (row.id === 'briefing') {
          return {
            children: (
              <Paragraph
                style={{
                  maxHeight: 180,
                  overflowY: 'scroll',
                }}
                ellipsis={{
                  rows: 8,
                  expandable: true,
                }}
                title={value || ''}
              >
                {value || ''}
              </Paragraph>
            ),
            props: { colSpan: 2, align: 'left' },
          }
        }
        return value
      },
    },
    {
      dataIndex: 'value',
      align: 'right',
      render: (value, row, index) => {
        if (row.id === 'briefing') {
          return {
            props: { colSpan: 0, align: 'left' },
          }
        }
        return value
      },
    },
  ]
  const chartConfig = {
    grid: {
      containLabel: true,
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : <br/>{c} ({d}%)',
      position: ['50%', '50%'],
    },
    series: [
      {
        name: '股权占比',
        type: 'pie',
        radius: '92%',
        center: ['50%', '50%'],
        label: {
          show: false,
        },
        data: (companyData.ownershipStructure || []).map(item => {
          return {
            name: item.hodlerName,
            value: item.holderQuantity,
          }
        }),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }
  return (
    <Space direction="vertical">
      <Table
        showHeader={false}
        pagination={false}
        columns={columns}
        dataSource={data}
        size="small"
        style={{ marginBottom: 10 }}
        className="thin-table"
      />
      <div>
        <span>股权结构</span>
        <Echart options={chartConfig} style={{ height: 200 }} />
      </div>
    </Space>
  )
}

export default CompanyBasicInfo
