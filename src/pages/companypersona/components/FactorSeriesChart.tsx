import React, { useState } from 'react'
import _ from 'lodash'
import Echart from '@/components/Chart/Echarts'
import {
  Select,
} from 'antd'

const { Option } = Select

function getSeriesChartData(factorData, factors, isvpct) {
  const factorCodes = factors.map(item => item.dataIndex)
  const data = factorData.filter(item => factorCodes.includes(item.factor_code))
  const dates = _.uniq(data.map(item => item.factor_date)).sort((fst, snd) => fst > snd ? 1 : -1)
  const dataMap = _.mapValues(_.groupBy(data, 'factor_code'), (values) => {
    const dateValueMap = values.reduce((out, item) => {
      out[item.factor_date] = isvpct ? item.vpct : item.factor_value
      return out
    }, {})
    return dates.map(date => dateValueMap[date])
  })
  const series = factors.map(factor => {
    const seriesOptions = { type: 'bar', stack: 'stack', ...factor.seriesOptions }
    return {
      name: factor.title,
      data: (dataMap[factor.dataIndex] || []).map(item => {
        let value = item
        if (factor.format === 'hundredMillion') {
          value = item / 100000000
        }
        return _.round(value, 2)
      }),
      ...seriesOptions,
    }
  })
  return {
    dates, series,
  }
}

const getChartOptions = (factorData, factors, chartOptions, isvpct) => {
  const { dates, series } = getSeriesChartData(factorData, factors, isvpct)
  const option = _.merge({
    legend: {
      show: series.length > 1,
      data: series.map(item => item.name),
      textStyle: {
        fontSize: 10,
      },
      right: 0,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '5%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: dates,
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
  }, chartOptions)

  return {
    ...option, series,
  }
}

export default ({
  title,
  data,
  factors,
  chartOptions,
  isvpct,
  isSelectFilter,
}: {
  data: any,
  title: string,
  factors: any,
  chartOptions?: any,
  isvpct?: any,
  isSelectFilter?: boolean,
}) => {
  const [factor, setFactor] = useState(factors[0].dataIndex)
  // const curFactor = factors.find(item => item.dataIndex === factor) || {}
  const curFactors = factors.filter(item => {
    if (!isSelectFilter) {
      return true
    }
    return item.dataIndex === factor
  })
  const options = getChartOptions(data, curFactors, chartOptions, isvpct)
  const [newTitle, setNewTitle] = useState(isSelectFilter ? factors[0].title : title)
  return (
    <div>
      {isSelectFilter &&
      <Select
        size="small" onChange={(value) => {
          setFactor(value)
          const curFactor = factors.find(item => item.dataIndex === value) || {}
          setNewTitle(curFactor.title)
        }} value={factor}
        style={{ float: 'right', marginTop: -3, width: 110 }}
      >
        {factors.map(factor => {
          return <Option value={factor.dataIndex}>{factor.title}</Option>
        })}
      </Select>}
      <div>{newTitle}</div>
      <Echart style={{ height: 400 }} options={options} />
    </div>
  )
}
