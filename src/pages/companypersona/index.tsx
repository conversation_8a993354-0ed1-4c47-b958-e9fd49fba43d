import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import { connect } from 'dva'
import { Tabs, Affix, Col, Row, Spin } from 'antd'
import { queryCompany } from './service'
import BasicInfo from './components/BasicInfo'
import ScaleHeatMap from './components/ScaleHeatMap'
import FactorSeriesChart from './components/FactorSeriesChart'
import SeriesChart from './components/SeriesChart'

const { TabPane } = Tabs

interface ComponentProps {
  currentFund: any;
  currentManager: any;
  match: any;
  location: any;
}


const Company: React.FC<ComponentProps> = ({ currentFund, currentManager, location: { pathname } }) => {
  const isFund = /fund|activefund|portfolios/.test(pathname)
  const currentData = isFund ? currentFund : currentManager
  const companyId = currentData.company_id
  const { loading, data: companyData = {} } = useRequest(() => {
    return queryCompany(companyId)
  })
  const [activeTab, setActiveTab] = useState('development')
  const chartOptions = {
    yAxis: [{
      type: 'value',
      name: '亿',
    }],
    grid: {
      top: '10%',
    },
  }
  const seriesOptions = {
    type: 'line',
    stack: false,
  }
  return (
    <>
      <Affix offsetTop={44}>
        <Tabs
          onChange={setActiveTab}
          activeKey={activeTab}
          style={{
            marginBottom: 15,
            background: 'rgb(24, 31, 41)',
          }}
        >
          <TabPane tab="业务发展" key="development">
          </TabPane>
          <TabPane tab="投研团队" key="team">
          </TabPane>
        </Tabs>
      </Affix>
      {activeTab === 'development' &&
      <Spin spinning={loading}>
        <Row gutter={16}>
          <Col lg={6} md={24}>
            <BasicInfo companyData={companyData} />
          </Col>
          <Col lg={18} md={24}>
            <Row>
              <Col lg={12} md={24}>
                {companyData.sname &&
                <ScaleHeatMap companyData={companyData} />}
              </Col>
              <Col lg={12} md={24}>
                <FactorSeriesChart
                  title="近一年规模新增"
                  data={companyData.factorSeriesData || []}
                  factors={[{
                    title: '近一年规模新增',
                    dataIndex: 'FdCompNearly1YFdAum_Q_3M_E',
                    format: 'hundredMillion',
                  }]}
                  chartOptions={chartOptions}
                />
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 15 }}>
              <Col lg={12} md={24}>
                <FactorSeriesChart
                  title="机构客户占比"
                  data={companyData.factorSeriesData || []}
                  factors={[{
                    title: '股和混合基机构客户',
                    dataIndex: 'FdComp2TypesInsShrRto_H_6M_E',
                  }, {
                    title: '债基机构客户',
                    dataIndex: 'FdCompBondInsShrRto_H_6M_E',
                  }, {
                    title: '非货基管理人员工',
                    dataIndex: 'FdCompNon_MEMPShrRto_H_6M_E',
                  }].map(item => ({...item, seriesOptions}))}
                />
              </Col>
              <Col lg={12} md={24}>
                <FactorSeriesChart
                  title="近一年份额新增"
                  data={companyData.factorSeriesData || []}
                  factors={[{
                    title: '近一年份额新增',
                    dataIndex: 'FdCompNearly1YFdShare_Q_3M_E',
                    format: 'hundredMillion',
                  }]}
                  chartOptions={chartOptions}
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </Spin>}
      {activeTab === 'team' &&
      <Spin spinning={loading}>
        <Row gutter={16}>
          <Col lg={8} md={24}>
            <SeriesChart
              title="基金经理数"
              data={(companyData.teamData || []).map(item => {
                item.dimission_num = -item.dimission_num
                return item
              })}
              factors={[{
                title: '新入职',
                dataIndex: 'new_num',
              }, {
                title: '留任',
                dataIndex: 'duty_num',
              }, {
                title: '离任',
                dataIndex: 'dimission_num',
              }]}
            />
          </Col>
          <Col lg={8} md={24}>
            <SeriesChart
              title="基金经理来源分布"
              data={companyData.managerDistributionData || []}
              factors={[{
                title: '本公司',
                dataIndex: 'inner_num',
              }, {
                title: '同行',
                dataIndex: 'occupation_num',
              }, {
                title: '其他',
                dataIndex: 'other_num',
              }]}
            />
          </Col>
          <Col lg={8} md={24}>
            <SeriesChart
              title="基金经理年资分布"
              data={companyData.managerDistributionData || []}
              factors={[{
                title: '1年以内',
                dataIndex: 'tenurewith1y_num',
              }, {
                title: '1-2年',
                dataIndex: 'tenure1to2y_num',
              }, {
                title: '2-3年',
                dataIndex: 'tenure2to3y_num',
              }, {
                title: '3-5年',
                dataIndex: 'tenure3to5y_num',
              }, {
                title: '5年以上',
                dataIndex: 'tenureover5y_num',
              }]}
            />
          </Col>
        </Row>
        <Row gutter={16} style={{ marginTop: 15 }}>
          <Col lg={8} md={24}>
            <FactorSeriesChart
              title="纯股团队业绩分位数"
              data={companyData.factorSeriesData || []}
              factors={[{
                title: '近1年',
                dataIndex: 'FdCompStkAvgRetPct_Q_12M_A',
              }, {
                title: '近3年',
                dataIndex: 'FdCompStkAvgRetPct_Q_3Y_A',
              }].map(item => ({...item, seriesOptions}))}
            />
          </Col>
          <Col lg={8} md={24}>
            <FactorSeriesChart
              title="纯债团队业绩分位数"
              data={companyData.factorSeriesData || []}
              factors={[{
                title: '近1年',
                dataIndex: 'FdCompBondAvgRetPct_Q_12M_A',
              }, {
                title: '近3年',
                dataIndex: 'FdCompBondAvgRetPct_Q_3Y_A',
              }].map(item => ({...item, seriesOptions}))}
            />
          </Col>
          <Col lg={8} md={24}>
            <FactorSeriesChart
              title="配置团队业绩分位数"
              data={companyData.factorSeriesData || []}
              factors={[{
                title: '近1年',
                dataIndex: 'FdCompAllocAvgRetPct_Q_12M_A',
              }, {
                title: '近3年',
                dataIndex: 'FdCompAllocAvgRetPct_Q_3Y_A',
              }].map(item => ({...item, seriesOptions}))}
            />
          </Col>
        </Row>
      </Spin>}
    </>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    manager: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    currentManager: fund.currentManager,
    loading: loading.models.fund,
  }),
)(Company)
