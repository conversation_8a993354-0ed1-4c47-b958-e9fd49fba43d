import { Button, Result } from 'antd'
import { FrownOutlined } from '@ant-design/icons'
import React from 'react'

// 这里应该使用 antd 的 404 result 组件，
// 但是还没发布，先来个简单的。

const NoFoundPage: React.FC<{}> = () => (
  <Result
    status="404"
    title={<FrownOutlined />}
    subTitle="对不起，您的登录信息已失效，请点击下方链接返回行E通网站重新进行登录。"
    extra={
      <Button type="primary">
        <a href="https://cor.etbank.com.cn/">返回行E通</a>
      </Button>
    }
  ></Result>
)

export default NoFoundPage
