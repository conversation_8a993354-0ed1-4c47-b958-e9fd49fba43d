import React from 'react'
import { useRequest } from '@umijs/hooks'
import Link from 'umi/link'
import _ from 'lodash'
import {
  Table,
  Tabs,
  Spin,
  Breadcrumb,
} from 'antd'
import { queryFactorSchema } from './service'
import buildTableColumn from '@/utils/buildTableColumn'

const { TabPane } = Tabs

export default ({
  match: {
    params: { id }
  },
}) => {
  const { loading, data: currrentSchema = {} } = useRequest(() => {
    return queryFactorSchema(id)
  })
  const tabs = (currrentSchema.factorData || []).map(item => {
    return {
      name: `${item.factorTier}(${item.weight}%)`,
      value: item.factorTier,
      data: item.factors,
    }
  })
  const columns = [{
    title: '因子',
    dataIndex: 'name',
  }, {
    title: '打分规则',
    dataIndex: 'scoringOrder',
    width: '15%',
    render: (val) => {
      if (val === 'asc') {
        return '正序'
      } else {
        return '逆序'
      }
    }
  }, {
    title: '权重(%)',
    dataIndex: 'weight',
    width: '25%',
    format: 'number',
  }]
  return (
    <div style={{ marginBottom: 30 }}>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link to="/factorschema">因子方案</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          {currrentSchema.name}
        </Breadcrumb.Item>
      </Breadcrumb>
      <Spin spinning={loading}>
        <Tabs
        >
          {tabs.map(tab => (
            <TabPane tab={tab.name} key={tab.value}>
              <Table
                size="small"
                columns={columns}
                dataSource={tab.data || []}
                pagination={false}
              />
            </TabPane>
          ))}
        </Tabs>
      </Spin>
    </div>
  )
}
