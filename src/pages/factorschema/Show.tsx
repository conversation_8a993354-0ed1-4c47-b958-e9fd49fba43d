import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import Link from 'umi/link'
import _ from 'lodash'
import {
  Table,
  Button,
  Input,
  Card,
  Form,
  notification,
  Steps,
  Result,
  Spin,
  Breadcrumb,
  Space,
  message,
  Typography,
  Select,
  Row,
  Col,
} from 'antd'
import useStepsForm from '@/hooks/useStepsForm'
import EditableTable from '@/components/EditableTable'
import { queryFactorSchema, getFactorList, updateFactorSchema, createFactorSchema } from './service'
import FactorTabTableForm from './components/FactorTabTableForm'

const { Step } = Steps
const { Text } = Typography
const { Option } = Select

export default ({
  match: {
    params: { id }
  },
}) => {
  const [currentId, setCurrentId] = useState(id)
  const [currrentSchema, setCurrentSchema] = useState({})
  const onSaveSuccess = () => {
    notification.success({
      message: '保存成功',
    })
  }
  const { data: factorList = [] } = useRequest(() => {
    return getFactorList()
  }, {
    onSuccess: () => {
      doQuerySchema(currentId)
    },
  })
  const factorDataMap = factorList.reduce((out, item) => {
    out[item.factor_code] = item
    return out
  }, {})
  const { loading, run: doQuerySchema } = useRequest((currentId) => {
    if (currentId !== 'new') {
      return queryFactorSchema(currentId)
    }
    return Promise.resolve({})
  }, {
    manual: true,
    onSuccess: (data) => {
      const weights = data.factorData.map(item => {
        return {
          factorTier: item.factorTier,
          weight: item.weight,
          key: item.factorTier,
        }
      })
      const factorData = data.factorData.map(item => {
        return {
          ...item,
          factors: item.factors.map(ft => {
            return {
              ...ft,
              key: ft.code,
            }
          }),
        }
      })
      form.setFieldsValue({
        ...data,
        weights,
        factorData,
      })
      setCurrentSchema(data)
      return data
    },
  })
  const { loading: updating, run: doUpdate} = useRequest((id, data) => {
    return updateFactorSchema(id, data)
  }, {
    manual: true,
    onSuccess: onSaveSuccess,
  })
  const { loading: creating, run: doCreate} = useRequest((data) => {
    return createFactorSchema(data)
  }, {
    manual: true,
    onSuccess: onSaveSuccess,
  })
  const {
    form,
    current,
    gotoStep,
    stepsProps,
    formProps,
    submit,
    formLoading,
  } = useStepsForm({
    async submit(values) {
      const { weights } = values
      console.log(values)
      const weightMap = weights.reduce((out, item) => {
        out[item.factorTier] = item.weight
        return out
      }, {})
      const factorData = values.factorData.map(item => {
        return {
          ...item,
          weight: weightMap[item.factorTier] || 0,
          factors: item.factors.map(ft => {
            const factorObj = factorDataMap[ft.code] || {}
            return {
              ...ft,
              name: factorObj.factor_name,
              fund_type: factorObj.factor_fund_type,
            }
          }),
        }
      })
      let result = {}
      if (currentId === 'new') {
        result = await doCreate({
          ...values,
          factorData,
        })
      } else {
        result = await doUpdate(currentId, {
          ...values,
          factorData,
        })
      }
      setCurrentId(result._id)
      return 'ok'
    },
    total: 4,
    isBackValidate: false,
  })

  const factorTierColumns = [{
    title: '因子类别',
    width: 200,
    dataIndex: 'factorTier',
  }, {
    title: '权重(%)',
    dataIndex: 'weight',
    format: 'inputNumber',
    width: 200,
    editable: true,
  }]
  const formList = [
    <>
      {factorList.length !== 0 && (id === 'new' || currrentSchema.name) &&
      <Form.Item
        validateStatus="success"
        name="factorData"
        rules={[
          {
            required: true,
            type: 'array',
            message: '请设置因子分类',
          },
          {
            validator: (rule, factorData) => {
              if (!factorData || !factorData.length) {
                message.error('请设置因子分类')
                return Promise.reject()
              }
              const isWeightSumValid = (factorData || []).every(item => {
                const weightDiff = Math.abs(100 - _.sumBy(item.factors || [], 'weight'))
                return weightDiff <= 0.1
              })
              if (factorData && factorData.length !== 0 && !isWeightSumValid) {
                message.error('因子权重之和不等于100')
                return Promise.reject('因子权重之和不等于100')
              } else {
                return Promise.resolve()
              }
            },
          },
        ]}
      >
        <FactorTabTableForm factorList={factorList} onChange={(factorData) => {
          const formData = form.getFieldsValue()
          const oldWeightData = (formData.weights || []).reduce((out, item) => {
            out[item.factorTier] = item.weight
            return out
          }, {})
          const weights = factorData.map(item => {
            return {
              factorTier: item.factorTier,
              weight: oldWeightData[item.factorTier] || 0,
              key: item.factorTier,
            }
          })
          form.setFieldsValue({ weights })
        }}/>
      </Form.Item>}
      <Form.Item style={{ marginTop: 30, textAlign: 'center' }}>
        <Button type="primary" onClick={() => {
          gotoStep(current + 1)
        }}>下一步</Button>
      </Form.Item>
      <Form.Item
        hidden
        name="weights"
      />
    </>,

    <>
      <Form.Item
        name="weights"
        rules={[
          {
            required: true,
            type: 'array',
            message: '请填写权重',
          },
          {
            validator: (rule, value) => {
              const weightSum = _.sumBy(value || [], 'weight')
              const weightDiff = Math.abs(100 - weightSum)
              if (weightDiff > 0.1) {
                return Promise.reject('权重之和不等于100')
              } else {
                return Promise.resolve()
              }
            },
          },
        ]}
      >
        <EditableTable
          size="small"
          columns={factorTierColumns}
          summary={pageData => {
            let total = 0

            pageData.forEach(({ weight }) => {
              total += Number(weight)
            })

            return (
              <>
                <Table.Summary.Row>
                  <Table.Summary.Cell>合计</Table.Summary.Cell>
                  <Table.Summary.Cell>
                    <Text type={total === 100 ? 'success' : 'danger'}>{total}</Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </>
            );
          }}
        />
      </Form.Item>
      <Form.Item style={{ marginTop: 30, textAlign: 'center' }}>
        <Space>
          <Button type="primary" onClick={() => gotoStep(current + 1)}>下一步</Button>
          <Button onClick={() => gotoStep(current - 1)}>上一步</Button>
        </Space>
      </Form.Item>
    </>,
    <>
      <Row gutter={8}>
        <Col span={12} sm={12} xs={24}>
          <Form.Item
            label="方案名称"
            name="name"
            rules={[
              {
                required: true,
                message: '请输入方案名称',
              },
            ]}
          >
            <Input placeholder="请输入方案名称" />
          </Form.Item>
        </Col>
        <Col span={12} sm={12} xs={24}>
          <Form.Item
            label="方案类型"
            name="schemaType"
            rules={[
              {
                required: true,
                message: '请选择方案类型',
              },
            ]}
          >
            <Select style={{ width: 200 }}>
              <Option value="stock">纯股</Option>
              <Option value="bond">纯债</Option>
              <Option value="allocate">配置</Option>
              {/* <Option value="passive">被动</Option> */}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Form.Item
        label="描述信息"
        name="description"
        rules={[
          {
            required: true,
            message: '请输入描述信息',
          },
        ]}
      >
        <Input.TextArea placeholder="请输入描述信息" />
      </Form.Item>
      <Form.Item style={{ marginTop: 30, textAlign: 'center' }}>
        <Space>
          <Button
            type="primary"
            loading={formLoading}
            onClick={() => {
              submit().then(result => {
                if (result === 'ok') {
                  gotoStep(current + 1)
                }
              })
            }}
          >
            保存
          </Button>
          <Button onClick={() => gotoStep(current - 1)}>上一步</Button>
        </Space>
      </Form.Item>
    </>,
  ]

  return (
    <div style={{ marginBottom: 30 }}>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link to="/factorschema">因子方案</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          {currrentSchema.name || '新建方案'}
        </Breadcrumb.Item>
      </Breadcrumb>
      <Spin spinning={loading || updating || creating}>
        <div>
          <Card>
            <Steps {...stepsProps} style={{ marginBottom: 30 }}>
              <Step title="选择因子" />
              <Step title="设置分类权重" />
              <Step title="保存方案" />
              <Step title="成功" />
            </Steps>
            <div>
              <Form {...formProps}>
                {formList[current]}
              </Form>
              {current === 3 && (
                <Result
                  status="success"
                  title="方案保存成功！"
                  extra={
                    <>
                      {/* <Button
                        type="primary"
                        onClick={() => {
                          form.resetFields()
                          gotoStep(0)
                        }}
                      >
                        再次创建
                      </Button> */}
                      <Button type="primary"><Link to="/factorschema">返回方案列表</Link></Button>
                    </>
                  }
                />
              )}
            </div>
          </Card>
        </div>
      </Spin>
    </div>
  )
}
