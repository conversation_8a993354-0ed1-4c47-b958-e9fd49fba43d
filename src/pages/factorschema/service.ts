import request from '@/utils/request'
import { TableListParams } from '@/components/StandardTable'

export async function queryFactorSchemas(params: TableListParams) {
  return request('/api/factorschemas', {
    params,
  })
}

export async function queryFactorSchema(id: string) {
  return request(`/api/factorschemas/${id}`)
}

export async function deleteFactorSchema(id: string) {
  return request(`/api/factorschemas/${id}`, {
    method: 'delete',
  })
}

export async function updateFactorSchema(id: string, data: any) {
  return request(`/api/factorschemas/${id}`, {
    method: 'put',
    data,
  })
}

export async function createFactorSchema(data: any) {
  return request(`/api/factorschemas`, {
    method: 'post',
    data,
  })
}

export async function copyFactorSchema(id: string) {
  return request(`/api/factorschemas/${id}/copy`, {
    method: 'post',
  })
}

export async function getFactorList() {
  return request(`/api/factorschemas/factorList`)
}

export async function getFactorResults(id, type, fundId, params) {
  return request(`/api/factorschemas/${id}/${type}/${fundId}/results`, {
    params
  })
}

export async function calculateFundFactorScore(id, data, params) {
  return request(`/api/products/${id}/factorscore`, {
    method: 'post',
    data,
    params,
  })
}

export async function calculateManagerFactorScore(id, data, params) {
  return request(`/api/managers/${id}/factorscore`, {
    method: 'post',
    data,
    params,
  })
}

export async function queryFundFactorScoreData(id, params) {
  return request(`/api/factorschemas/${id}/fundscores`, {
    params
  })
}
