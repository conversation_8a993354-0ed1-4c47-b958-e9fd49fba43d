import React from 'react'
import { useRequest } from '@umijs/hooks'
import { FundOutlined, SolutionOutlined } from '@ant-design/icons'
import { Tooltip, Spin } from 'antd'
import { queryFundFactorScoreData } from '../service'
import { exportNArrayDataAsExcel } from '@/utils/exportAsExcel'

const exportFactorScoreDataAsExcel = (title, data, isManager) => {
  const scoreData = data.reduce((out, item) => {
    return out.concat([[
      isManager ? item.name : item.manager_name, isManager ? item.ref_fund_name : item.name,
      isManager ? item.ref_fund_id : item._qutkeId,
      item.company, item.asset_type, item.style_type, item.factorEvalDate,
      item.totalFactorScore || '-', item.incomeFactorScore || '-', item.riskFactorScore || '-',
      item.attributionFactorScore || '-', item.strategyFactorScore || '-', item.companyFactorScore || '-',
      item.managerFactorScore || '-', item.positionFactorScore || '-',
    ]])
  }, [[
    '基金经理', '基金名称', '基金代码', '基金公司', '资产分类', '策略分类', '评价日期',
    '总分', '收益类', '风险类', '归因类', '策略类', '基金公司类', '基金经理类', '持仓类',
  ]])
  exportNArrayDataAsExcel(title, scoreData)
}

export default ({ factorSchema, type }) => {
  const name = type === 'manager' ? '基金经理' : '基金'
  const { run: getFundFactorScoreData, loading } = useRequest((id) => {
    return queryFundFactorScoreData(id, { type })
  }, {
    manual: true,
    onSuccess: (data) => {
      const title = `${factorSchema.name}-${name}因子方案评分结果`
      exportFactorScoreDataAsExcel(title, data, type === 'manager')
    },
  })
  if (loading) {
    return <Tooltip title="因子模型正在运行，请稍等片刻">
      <Spin size="small" />
    </Tooltip>
  }
  const Icon = type === 'manager' ? SolutionOutlined : FundOutlined
  return (
    <Tooltip title={`下载${name}因子评分结果`}>
      <a><Icon onClick={() => getFundFactorScoreData(factorSchema._id) } /></a>
    </Tooltip>
  )
}
