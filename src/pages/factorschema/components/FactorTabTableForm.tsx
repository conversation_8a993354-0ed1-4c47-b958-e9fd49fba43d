import React, { useState } from 'react'
import _ from 'lodash'
import { PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Space,
  Menu,
  Dropdown,
  Tabs,
  message,
} from 'antd'
import TableForm from '@/components/TableForm'

const { TabPane } = Tabs

export default ({ factorList, value, onChange }) => {
  const allFactorTiers = _.uniq(factorList.map(item => {
    return item.level5_tier
  }))
  const factorGroups = _.groupBy(factorList, 'level5_tier')
  let defaultTabs = (value || []).map(item => item.factorTier)
  if (defaultTabs.length === 0) {
    defaultTabs = [allFactorTiers[0]]
  }
  const [tabs, setTabs] = useState(defaultTabs)
  const [activeTabKey, setActiveTabKey] = useState(defaultTabs[0])
  const [tabTableData, setTabTableData] = useState((value || []).reduce((out, item) => {
    out[item.factorTier] = item.factors
    return out
  }, {}))
  const handleTabChange = newTabKey => {
    const tabData = tabTableData[activeTabKey]
    const weightSum = _.sumBy(tabData || [], 'weight')
    const weightDiff = Math.abs(100 - weightSum)
    if (weightDiff > 0.1 && tabData && tabData.length !== 0) {
      message.error('因子权重之和不等于100')
      return
    }
    setActiveTabKey(newTabKey)
  }
  const handleAddTab = tabItem => {
    setTabs(_.uniq([...tabs, tabItem.key]))
    handleTabChange(tabItem.key)
  }
  const confirmDataChange = newTabData => {
    setTabTableData(newTabData)
    const newData = _.map(newTabData, (factors, factorTier) => {
      return {
        factors, factorTier,
      }
    }).filter(item => item.factors.length)
    onChange && onChange(newData)
  }
  const handleRemoveTab = targetKey => {
    const newTabs = tabs.filter(item => item !== targetKey)
    setTabs(newTabs)
    setActiveTabKey(newTabs[0])
    const newData = _.omit(tabTableData, [targetKey])
    confirmDataChange(newData)
  }
  const handleEditTab = (targetKey, action) => {
    if (action === 'remove') {
      handleRemoveTab(targetKey)
    }
  }
  const handleTabDataChange = (factorTier) => data => {
    const newTabData = {
      ...tabTableData,
      [factorTier]: data,
    }
    confirmDataChange(newTabData)
  }
  const unselectFactors = allFactorTiers.filter(item => !tabs.includes(item))
  const menu = (
    <Menu>
      {unselectFactors.map(item => (
        <Menu.Item key={item} onClick={handleAddTab}>
          {item}
        </Menu.Item>
      ))}
    </Menu>
  )
  const getTableFormColumns = (tabKey) => {
    const options = (factorGroups[tabKey] || []).map(item => {
      return {
        ...item,
        title: item.factor_name,
        dataIndex: item.factor_code,
        key: item.factor_code,
      }
    })
    return [{
      title: '选择因子',
      dataIndex: 'code',
      width: '40%',
      key: 'code',
      format: 'Select',
      options: options,
      optionDisplayRender: (dataIndex) => {
        const fundAssetTypeMap = {
          1: [{ name: '股', color: '#52c41a' }],
          2: [{ name: '债', color: '#faad14' }],
          3: [{ name: '配', color: '#eb2f96' }],
          4: [{ name: '股', color: '#52c41a' }, { name: '配', color: '#eb2f96' }],
          5: [{ name: '股', color: '#52c41a' }, { name: '债', color: '#faad14' }, { name: '配', color: '#eb2f96' }],
        }
        const factor = factorList.find(item => item.factor_code === dataIndex) || {}
        const fundAssets = fundAssetTypeMap[factor.factor_fund_type] || []
        return (
          <Space>
            {fundAssets.map(fundAsset => (
              <Button type="primary" size="small" ghost style={{
                height: 20,
                padding: '0.8px 5px',
                fontSize: 12,
                borderColor: fundAsset.color,
                color: fundAsset.color,
              }}>
                {fundAsset.name}
              </Button>
            ))}
            <span>{factor.factor_name}</span>
          </Space>
        )
      },
    }, {
      title: '打分规则',
      dataIndex: 'scoringOrder',
      width: '15%',
      key: 'scoringOrder',
      format: 'Select',
      options: [{
        title: '正序',
        dataIndex: 'asc',
      }, {
        title: '逆序',
        dataIndex: 'desc',
      }],
    }, {
      title: '设置权重(%)',
      dataIndex: 'weight',
      width: '25%',
      key: 'weight',
      format: 'InputNumber',
      refColumn: 'factor',
    }]
  }
  return (
    <Tabs
      type="editable-card"
      hideAdd={unselectFactors.length === 0}
      activeKey={activeTabKey}
      addIcon={
        <Dropdown overlay={menu}>
          <a className="ant-dropdown-link" onClick={e => e.preventDefault()}>
            <PlusOutlined />
          </a>
        </Dropdown>
      }
      onEdit={handleEditTab}
      onChange={handleTabChange}
    >
      {tabs.map(tab => (
        <TabPane tab={tab} key={tab}>
          <TableForm
            iconAction
            isEdditing
            // hideEmptyTable
            value={tabTableData[tab]}
            newItemText="添加因子"
            size="small"
            tableColumns={getTableFormColumns(tab)}
            onChange={handleTabDataChange(tab)}
            defaultNewValues={{
              scoringOrder: 'asc',
            }}
            validateRow={(item, index) => {
              if (!item.code) {
                return '请选择因子'
              }
              if (!item.scoringOrder) {
                return '请选择打分规则'
              }
              if (!item.weight && item.weight !== 0) {
                return '请设置权重'
              }
              return null
            }}
          />
        </TabPane>
      ))}
    </Tabs>
  )
}
