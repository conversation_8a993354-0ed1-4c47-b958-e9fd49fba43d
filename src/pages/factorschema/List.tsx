import React, { useState } from 'react'
import { useRequest, useBoolean } from '@umijs/hooks'
import Link from 'umi/link'
import { connect } from 'dva'
import moment from 'moment'
import { DeleteOutlined, EditOutlined, PlusOutlined, CopyOutlined, FileExcelOutlined } from '@ant-design/icons'
import {
  Table,
  Button,
  Tooltip,
  Popconfirm,
  Modal,
  Input,
  Card,
  Form,
  notification,
  Row,
  Col,
  Spin,
  Select,
  Switch,
  Space,
} from 'antd'
import { queryFactorSchemas, deleteFactorSchema, copyFactorSchema, updateFactorSchema, createFactorSchema } from './service'
import ExportFactorScoreData from './components/ExportFactorScoreData'
const { Search, TextArea } = Input
const { Option } = Select

const List = ({
  currentUser,
}: {
  currentUser: any,
}) => {
  const [form] = Form.useForm()
  const deafultValues = { name: '', description: '', schemaType: '', isDefault: false }
  const { setFieldsValue } = form
  const { state: visible, setTrue: setVisibleTrue, setFalse: setVisibleFalse } = useBoolean(false)
  const [currentItem, setCurrentItem] = useState({})
  const [refreshCount, setRefreshCount] = useState(0)
  const [input, setInput] = useState('')
  const { tableProps } = useRequest(({ current, pageSize, sorter: s, filters: f }) => {
    const p: any = { current, pageSize }
    if (s && s.field && s.order) {
      p.sort = s.field
      p.order = s.order
    }
    if (f) {
      Object.entries(f).forEach(([filed, value]) => {
        p[filed] = value
      })
    }
    if (input) {
      p.input = input
    }
    return queryFactorSchemas(p)
  }, {
    paginated: true,
    defaultPageSize: 10,
    refreshDeps: [refreshCount, input],
  })
  const { loading: copying, run: doCopy} = useRequest((id) => {
    return copyFactorSchema(id)
  }, {
    manual: true,
    onSuccess: () => {
      setVisibleFalse()
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '复制成功',
      })
    },
  })
  const { run: doDelete } = useRequest((id) => {
    return deleteFactorSchema(id)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      notification.success({
        message: '删除成功',
      })
    },
  })
  const { loading: updatingFactorSchema, run: doUpdateFactorSchema} = useRequest((id, data) => {
    return updateFactorSchema(id, data)
  }, {
    manual: true,
    onSuccess: () => {
      setRefreshCount(refreshCount + 1)
      setVisibleFalse()
      setFieldsValue(deafultValues)
      notification.success({
        message: '更新成功',
      })
    },
  })
  const handleOpenModal = (record) => () => {
    setCurrentItem(record)
    setVisibleTrue()
    setFieldsValue(record)
  }
  const handleClickSave = (values: any) => {
    if (currentItem._id) {
      doUpdateFactorSchema(currentItem._id, values)
    }
  }

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text, record) => {
        if (currentUser._id !== record.authorId) {
          return <Link to={`/factorschema/${record._id}/readonly`}>{text}</Link>
        }
        return <Link to={`/factorschema/${record._id}`}>{text}</Link>
      },
    },
    {
      title: '描述信息',
      dataIndex: 'description',
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      align: 'right',
      render: text => moment(new Date(text)).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => {
        if (currentUser._id !== record.authorId) {
          return (
            <Space>
              <ExportFactorScoreData factorSchema={record} type="fund"/>
              <ExportFactorScoreData factorSchema={record} type="manager"/>
              <Tooltip title="复制">
              <a><CopyOutlined spin={copying} onClick={() => doCopy(record._id)} /></a>
            </Tooltip>
            </Space>
          )
        }
        return (
          <Space>
            <ExportFactorScoreData factorSchema={record} type="fund"/>
            <ExportFactorScoreData factorSchema={record} type="manager"/>
            <Tooltip title="编辑">
              <a><EditOutlined onClick={handleOpenModal(record)} /></a>
            </Tooltip>
            <Tooltip title="复制">
              <a><CopyOutlined spin={copying} onClick={() => doCopy(record._id)} /></a>
            </Tooltip>
            <Popconfirm
              title="确认删除吗？"
              onConfirm={() => { doDelete(record._id) }}
              onCancel={() => {}}
              okText="确认"
              cancelText="取消"
              placement="left"
            >
              <Tooltip title="删除">
                <a><DeleteOutlined /></a>
              </Tooltip>
            </Popconfirm>
          </Space>
        )
      },
    },
  ]

  return (
    <div>
      <Card
        title={
          <>
            <Search
              style={{ width: '300px' }}
              placeholder="按回车进行搜索"
              onSearch={setInput}
            />
          </>
        }
        bordered={false}
        extra={
          <>
            <Link to="/factorschema/new"><PlusOutlined />新建方案</Link>
          </>
        }
      >
        <Table size="small" columns={columns} rowKey="_id" {...tableProps} />
      </Card>
      <Modal
        title={
          <>
            <span>{currentItem && currentItem._id ? '编辑方案' : '新建方案'}</span>
          </>
        }
        visible={visible}
        onCancel={setVisibleFalse}
        width={700}
        footer={[
          <Button
            type="primary"
            loading={updatingFactorSchema}
            onClick={() => form.submit()}
          >
            保存
          </Button>,
        ]}
      >
        <Spin spinning={updatingFactorSchema}>
          <Form
            hideRequiredMark
            form={form}
            layout="vertical"
            onFinish={handleClickSave}
          >
            <Row gutter={8}>
              <Col span={24}>
                <Form.Item
                  label="名称"
                  name="name"
                  rules={[
                    {
                      required: true,
                      message: '请输入名称',
                    },
                  ]}
                >
                  <Input placeholder="名称"/>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="描述"
                  name="description"
                >
                  <TextArea placeholder="描述"/>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="方案类型"
                  name="schemaType"
                  rules={[
                    {
                      required: true,
                      message: '请选择方案类型',
                    },
                  ]}
                >
                  <Select style={{ width: 200 }}>
                    <Option value="stock">纯股</Option>
                    <Option value="bond">纯债</Option>
                    <Option value="allocate">配置</Option>
                    {/* <Option value="passive">被动</Option> */}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="设为默认"
                  name="isDefault"
                  valuePropName="checked"
                >
                  <Switch checkedChildren="是" unCheckedChildren="否"/>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Spin>
      </Modal>
    </div>
  )
}

export default connect(
  ({
    user,
  }: {
    user: any,
  }) => ({
    currentUser: user.currentUser,
  }),
)(List)
