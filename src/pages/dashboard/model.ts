import { Effect } from 'dva'
import { Reducer } from 'redux'

import { getDashboardData, getFundData } from './service'

import { calculateReturns } from '@/utils/calculator'

export interface ModelState {
  dashboardData: any;
  fundData: any;
}

export interface ModelType {
  namespace: 'dashboard';
  state: ModelState;
  effects: {
    fetchDashboardData: Effect;
    fetchFundData: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
  };
}

const Model: ModelType = {
  namespace: 'dashboard',

  state: {
    dashboardData: {
      stylesData: [],
      portfolios: [],
      activeFunds: [],
      managers: [],
      timeRangeMap: {},
      managerPortraitInfoAvg: [],
    },
    fundData: [],
  },

  effects: {
    *fetchDashboardData({ payload }, { call, put }) {
      const response = yield call(getDashboardData)
      const dashboardData = {
        ...response,
        stylesData: response.stylesData.map((item: any) => {
          item.returns = calculateReturns(item.nets)
          return item
        }),
      }
      yield put({
        type: 'save',
        payload: { dashboardData },
      })
    },
    *fetchFundData({ payload: { id } }, { call, put }) {
      const response = yield call(getFundData, id)
      yield put({
        type: 'save',
        payload: { fundData: response },
      })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
  },
}

export default Model
