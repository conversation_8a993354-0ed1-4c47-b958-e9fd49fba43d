import React from 'react'
import Chart from '@/components/Chart/Chart'

const FundNavChart = ({ funds, afterChartCreated, navigatorEnabled, height }: { funds: any, afterChartCreated?: any, navigatorEnabled?: boolean, height?: number }) => {
  const config = {
    chart: {
      type: 'line',
      height: height || 240,
    },
    tooltip: {
      pointFormat:
        '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.4f}({point.change:.2f}%)</b><br/>',
    },
    navigator: {
      enabled: navigatorEnabled,
    },
    scrollbar: {
      enabled: false,
    },
    yAxis: {
      labels: {
        format: '{value}%',
      },
    },
    series: funds.map(fund => {
      return {
        name: fund.name,
        compare: 'percent',
        data: fund.nets.map(item => [item.date, item.value]),
      }
    }),
  }
  return <Chart options={config} constructorType="stockChart" afterChartCreated={afterChartCreated} />
}

export default FundNavChart
