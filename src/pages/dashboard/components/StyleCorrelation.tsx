import React, { Component } from 'react'
import { Card, Radio } from 'antd'
import _ from 'lodash'
import moment from 'moment'
import CorrelationTable from '@/components/CorrelationTable'
import calculateFundCorrelation from '@/utils/calculateFundCorrelation'
import styles from '../style.less'

interface ComponentProps {
  funds: any;
}

export default class StyleCorrelation extends Component<ComponentProps> {
  state = {
    type: 'all',
  }

  getTimeRangeMap = () => {
    const { funds } = this.props
    const maxDateTs = _.max(funds.map(item => item.navEndDate))
    const minDateTs = _.min(funds.map(item => item.navStartDate))
    const maxDate = new Date(maxDateTs)
    const timeRangeMap = {
      ytd: +moment(maxDate).startOf('year').startOf('date'),
      '1y': +moment(maxDate).subtract(1, 'year').startOf('date'),
      '3y': +moment(maxDate).subtract(3, 'year').startOf('date'),
      all: minDateTs,
      maxDateTs,
    }
    return timeRangeMap
  }

  onTimeRangeChange = (event) => {
    const type = event.target.value
    this.setState({ type })
  }

  getCorrelationData = (type) => {
    const timeRangeMap = this.getTimeRangeMap()
    const startDate = timeRangeMap[type]
    const endDate = timeRangeMap.maxDateTs
    console.log(this.props.funds)
    const funds = this.props.funds.map(item => {
      return {
        ...item,
        returns: item.returns.filter(ret => ret.date >= startDate && ret.date <= endDate),
      }
    })
    return calculateFundCorrelation(funds)
  }

  render() {
    const type = this.state.type
    const { funds } = this.props
    const correlatoinData = this.getCorrelationData(type)
    const fundList = funds.map(item => {
      item.name = item.name.replace(/指数$/, '')
      return item
    })
    return (
      <Card
        title="风格相关性"
        className={styles.styleCard}
        extra={
          <Radio.Group
            defaultValue="all"
            size="small"
            onChange={this.onTimeRangeChange}
            style={{
              fontSize: 12,
            }}
          >
            <Radio.Button value="ytd">YTD</Radio.Button>
            <Radio.Button value="1y">1Y</Radio.Button>
            <Radio.Button value="3y">3Y</Radio.Button>
            <Radio.Button value="all">ALL</Radio.Button>
          </Radio.Group>
        }
      >
        <CorrelationTable funds={fundList} cor={correlatoinData} />
      </Card>
    )
  }
}
