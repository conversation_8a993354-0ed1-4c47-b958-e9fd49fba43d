import React from 'react'
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Row, Col, Card, Statistic, Divider, Tag, Tooltip } from 'antd';
import Chart from '@/components/Chart/Chart'
import renderFundQuota from '@/utils/renderFundQuota'
import { styleTypeMap } from '@/utils/kymDefMapping'

const ManagerCard = ({
  manager,
  managerPortraitInfoAvg,
  gotoPrev,
  gotoNext,
}: {
  manager: any;
  managerPortraitInfoAvg: any;
  gotoPrev: any;
  gotoNext: any;
}) => {
  const chartQuotas = [{
    name: '收益类',
    value: 'incomeFactorScore',
    format: 'number',
  }, {
    name: '风险类',
    value: 'riskFactorScore',
    format: 'number',
  }, {
    name: '归因类',
    value: 'attributionFactorScore',
    format: 'number',
  }, {
    name: '策略类',
    value: 'strategyFactorScore',
    format: 'number',
  }, {
    name: '基金公司类',
    value: 'companyFactorScore',
    format: 'number',
  }, {
    name: '基金经理类',
    value: 'managerFactorScore',
    format: 'number',
  }, {
    name: '持仓类',
    value: 'positionFactorScore',
    format: 'number',
  }]
  const avgPortraitInfo = managerPortraitInfoAvg.find(item => item.style_type === manager.style_type) || {}
  const filteredQuotas = chartQuotas.filter(item => manager[item.value])
  const categories = filteredQuotas.map(item => item.name)
  const chartConfig = {
    chart: {
      polar: true,
      type: 'line',
      height: 210,
    },
    pane: {
      size: '83%',
    },
    tooltip: {
      shared: true,
      pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.2f}</b><br/>',
    },
    legend: {
      enabled: false,
    },
    xAxis: {
      categories,
      tickmarkPlacement: 'on',
      lineWidth: 0,
      labels: {
        useHTML: true,
        style: {
          fontSize: '10px',
        },
        formatter: function formatter() {
          const labelValue = this.value + ''
          const start = labelValue.slice(0, 4)
          const end = labelValue.slice(4)
          if (!end) {
            return `<span>${start}</span>`
          }
          return `<span>${start}</span><br/><span>${end}</span>`
        },
      },
    },
    yAxis: {
      gridLineInterpolation: 'polygon',
      min: 0,
      max: 100,
      labels: {
        enabled: false,
        format: '{value}',
      },
    },
    series: [{
      pointPlacement: 'on',
      name: manager.name,
      data: filteredQuotas.map(quota => manager[quota.value] || 0),
    },
    // {
    //   pointPlacement: 'on',
    //   name: '同业平均',
    //   data: chartQuotas.map(quota => avgPortraitInfo[quota.value] || 0),
    // }
    ],
  }
  const quotas = [
    {
      name: '收益率(1day)',
      value: 'last1DAccReturn',
      format: 'percentage',
    }, {
      name: '收益率(1Mo)',
      value: 'last1MAccReturn',
      format: 'percentage',
    }, {
      name: '收益率(YTD)',
      value: 'ytdAccReturn',
      format: 'percentage',
    },
  ]
  return (
    <Card
      title={
        <>
          <a
            href={`/manager/persona/${manager._id}/factor_evaluation`}
            rel="noopener noreferrer"
            target="_blank"
          >
            {manager.name}
          </a>
          <Tag style={{ marginLeft: 10 }}>{styleTypeMap[manager.style_type]}</Tag>
        </>
      }
      extra={gotoPrev &&
        <>
          <Tooltip title="上一个">
            <LeftOutlined onClick={gotoPrev} />
          </Tooltip>
          <Tooltip title="下一个">
            <RightOutlined style={{ marginLeft: 10 }} onClick={gotoNext} />
          </Tooltip>
        </>
      }
    >
      <Chart options={chartConfig} />
      <Divider dashed style={{ margin: '8px 0' }}/>
      <Row>
        {quotas.map(quota => (
          <Col key={`${quota.value}`} span={8}>
            <Statistic title={quota.name} formatter={() => renderFundQuota(quota, manager)} />
          </Col>
        ))}
      </Row>
    </Card>
  );
}

export default ManagerCard
