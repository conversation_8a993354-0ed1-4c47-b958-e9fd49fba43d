import React, { Component } from 'react'
import { Card, Radio, Space, Select } from 'antd'
import _ from 'lodash'
import moment from 'moment'
import FundNavChart from './FundNavChart'
import ExportData from '@/components/ExportData'

const { Option } = Select

interface ComponentProps {
  funds: any;
  title: string;
  navigatorEnabled?: boolean;
  height?: number,
  fundFilterEnabled?: boolean;
}

export default class FundNavCard extends Component<ComponentProps> {
  state = {
    refChart: null,
    fundIds: this.props.funds.sort((fst, snd) => snd.ytdAccReturn - fst.ytdAccReturn).slice(0, 5).map(item => item._id),
  }

  getTimeRangeMap = () => {
    const { funds } = this.props
    const maxDateTs = _.max(funds.map(item => item.navEndDate))
    const minDateTs = _.min(funds.map(item => item.navStartDate))
    const maxDate = new Date(maxDateTs)
    const timeRangeMap = {
      ytd: +moment(maxDate).startOf('year').startOf('date'),
      '1y': +moment(maxDate).subtract(1, 'year').startOf('date'),
      '3y': +moment(maxDate).subtract(3, 'year').startOf('date'),
      all: minDateTs,
      maxDateTs,
    }
    return timeRangeMap
  }

  handleTimeRangeChange = (type) => {
    const timeRangeMap = this.getTimeRangeMap()
    const { refChart } = this.state
    if (refChart && refChart.xAxis) {
      refChart.xAxis[0].setExtremes(timeRangeMap[type], timeRangeMap.maxDateTs)
    }
  }

  onTimeRangeChange = (event) => {
    this.handleTimeRangeChange(event.target.value)
  }

  handleSelectFunds = (fundIds) => {
    this.setState({ fundIds })
  }

  afterChartCreated = (refChart) => {
    this.setState({
      refChart,
    }, () => {
      // setTimeout(() => {
      //   this.handleTimeRangeChange('1y')
      // }, 1000)
    })
  }

  render() {
    const { fundIds } = this.state
    const { funds, title, navigatorEnabled, height, fundFilterEnabled } = this.props
    const fundList = fundFilterEnabled ? funds.filter(item => fundIds.includes(item._id)) : funds
    const exportCols = [{
      title: '日期',
      dataIndex: 'date',
    }, ...funds.map(fund => {
      return {
        title: fund.name,
        dataIndex: fund._id,
      }
    })]
    const getExportData = () => {
      const dataKey = 'nets'
      const data = funds.map(fund => {
        return _.reduce(fund[dataKey] || [], (out, item) => {
          out[item.date] = _.round(item.value, 6)
          return out
        }, {})
      })
      let dates = funds.reduce((out, fund) => {
        const items = (fund[dataKey] || []).map(item => item.date)
        return out.concat(items)
      }, [])
      dates = _.uniq(dates).sort((fst, snd) => fst - snd)
      const navData = dates.map(date => {
        return funds.reduce((out, fund, index) => {
          out[fund._id] = data[index][date] || ''
          return out
        }, {
          date: moment(new Date(date)).format('YYYYMMDD'),
        })
      })
      return navData
    }
    return (
      <Card
        title={title}
        // className={styles.styleCard}
        extra={
          <Space>
            {fundFilterEnabled &&
            <Select
              mode="multiple"
              placeholder="请选择"
              onChange={this.handleSelectFunds}
              defaultValue={fundIds}
            >
              {funds.map(item => (
                <Option key={item._id} value={item._id}>{item.name}</Option>
              ))}
            </Select>}
            <Radio.Group
              defaultValue="all"
              size="small"
              onChange={this.onTimeRangeChange}
              style={{
                fontSize: 12,
              }}
            >
              <Radio.Button value="ytd">YTD</Radio.Button>
              <Radio.Button value="1y">1Y</Radio.Button>
              <Radio.Button value="3y">3Y</Radio.Button>
              <Radio.Button value="all">ALL</Radio.Button>
            </Radio.Group>
            <ExportData columns={exportCols} getData={getExportData} filename={title} />
          </Space>
        }
      >
        {funds.length !== 0 &&
        <FundNavChart funds={fundList} afterChartCreated={this.afterChartCreated} navigatorEnabled={navigatorEnabled} height={height} />}
      </Card>
    )
  }
}
