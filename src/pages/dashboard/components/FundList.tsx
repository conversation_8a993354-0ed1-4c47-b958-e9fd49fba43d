import React, { useEffect } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { DoubleRightOutlined } from '@ant-design/icons';
import { Spin, Row, Col, Card, Statistic, Tabs } from 'antd';
import renderFundQuota from '@/utils/renderFundQuota'
import FundNavChart from './FundNavChart'

const { TabPane } = Tabs

interface WrapperProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  dashboardData: any;
  fundData: any;
}

const quotas = [
  {
    name: '最新净值',
    value: 'unitNavEndOfTerm',
    format: 'number',
  },
  {
    name: '年化收益',
    value: 'yearReturn',
    format: 'percentage',
  },
]

const CustomTab = ({ fund }: { fund: any }) => (
  <div>
    <h4 style={{ fontWeight: 'bold', paddingLeft: 8, fontSize: 16 }}>{fund.name}</h4>
    <Row gutter={16} style={{ width: 138, margin: '8px 0' }} type="flex">
      {quotas.map(quota => (
        <Col span={12}>
          <Statistic title={quota.name} formatter={() => renderFundQuota(quota, fund)} />
        </Col>
      ))}
    </Row>
  </div>
)

const FundList: React.FC<WrapperProps> = props => {
  const {
    dispatch,
    dashboardData: { activeFunds },
    loading,
    fundData,
  } = props
  const getFundData = (fundId: stirng) => {
    dispatch({
      type: 'dashboard/fetchFundData',
      payload: { id: fundId },
    })
  }
  useEffect(() => {
    getFundData(activeFunds[0]._id)
  }, [])
  return (
    <div>
      <Card
        style={{ marginTop: 15 }}
        title="实盘净值走势"
        extra={
          <a href="/activefund">
            查看更多
            <DoubleRightOutlined />
          </a>
        }
      >
        <Tabs activeKey={'activeKey'} onChange={(fundId: string) => getFundData(fundId)}>
          {activeFunds.map((fund: any) => (
            <TabPane tab={<CustomTab fund={fund} />} key={fund._id}></TabPane>
          ))}
        </Tabs>
        <div style={{ marginTop: 15 }}>
          <Spin spinning={loading}>
            <FundNavChart funds={fundData} />
          </Spin>
        </div>
      </Card>
    </div>
  );
}

export default connect(
  ({
    dashboard,
    loading,
  }: {
    dashboard: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    dashboardData: dashboard.dashboardData,
    loading: loading.effects['dashboard/fetchFundData'],
    fundData: dashboard.fundData,
  }),
)(FundList)
