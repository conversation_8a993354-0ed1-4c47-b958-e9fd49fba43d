import React, { useEffect, useRef } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { Spin, Row, Col, Card, Carousel } from 'antd'
import { useRequest } from '@umijs/hooks'
import PortfolioCard from '@/components/PortfolioCard'
import StandardTable from '@/components/StandardTable'
import { calculateReturns } from '@/utils/calculator'
import FundList from './components/FundList'
import FundNavCard from './components/FundNavCard'
import ManagerCard from './components/ManagerCard'
import StyleCorrelation from './components/StyleCorrelation'
import { getDashboardData } from './service'
import styles from './style.less'
interface WrapperProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  dashboardData: any;
}

const Dashboard: React.FC<WrapperProps> = props => {
  if (props.currentUser && props.currentUser.menus && !(props.currentUser.menus.map(item => item.menuId).includes(1))) {
    const nextPath = props.currentUser.menus.filter(item => item.path && item.component)[0]
    window.location.href = nextPath? nextPath.path : '/404'
    return false
  }
  const { loading, data: dashboardData = {
    stylesData: [],
    portfolios: [],
    activeFunds: [],
    managers: [],
    timeRangeMap: {},
    managerPortraitInfoAvg: [],
  } } = useRequest(() => {
    return getDashboardData()
  })
  const { activeFunds, managers, managerPortraitInfoAvg } = dashboardData
  const stylesData = dashboardData.stylesData.map((item: any) => {
    item.returns = calculateReturns(item.nets)
    return item
  })
  const stockStyles = stylesData.filter(item =>
    ['Value_benchmark', 'Growth_benchmark', 'Blend_benchmark'].includes(item._qutkeId),
  )
  const bondStyles = stylesData.filter(item =>
    ['CredSlect_benchmark', 'DuraTiming_benchmark', 'Balance_benchmark'].includes(item._qutkeId),
  )
  const styleQuotas = [{
    title: '名称',
    dataIndex: 'name',
    width: '150',
    ellipsis: true,
  }, {
    title: '收益率(1day)',
    dataIndex: 'last1DAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(1Mo)',
    dataIndex: 'last1MAccReturn',
    format: 'percentage',
  }, {
    title: '收益率(YTD)',
    dataIndex: 'ytdAccReturn',
    format: 'percentage',
  }]
  const activeFundQuotas = [
    {
      name: '收益率(1day)',
      value: 'last1DAccReturn',
      format: 'percentage',
    }, {
      name: '收益率(1Mo)',
      value: 'last1MAccReturn',
      format: 'percentage',
    }, {
      name: '收益率(YTD)',
      value: 'ytdAccReturn',
      format: 'percentage',
    }, {
      name: '超额收益率(1day)',
      value: 'last1DExcessReturn',
      format: 'percentage',
    }, {
      name: '超额收益率(1Mo)',
      value: 'last1MExcessReturn',
      format: 'percentage',
    }, {
      name: '超额收益率(YTD)',
      value: 'ytdExcessReturn',
      format: 'percentage',
    },
  ]
  const fundCardRef = useRef(null)
  const managerCardRef = useRef(null)
  const handleFundPrev = () => fundCardRef && fundCardRef.current.prev()
  const handleFundNext = () => fundCardRef && fundCardRef.current.next()
  const handleManagerPrev = () => managerCardRef && managerCardRef.current.prev()
  const handleManagerNext = () => managerCardRef && managerCardRef.current.next()
  return (
    <div className={styles.fixedHeightCard}>
      <Spin spinning={loading}>
        <Row gutter={12}>
          <Col lg={8} md={24}>
            <FundNavCard funds={stockStyles} title="风格历史(股票)" />
          </Col>
          <Col lg={8} md={24}>
            <FundNavCard funds={bondStyles} title="风格历史(债券)" />
          </Col>
          <Col lg={8} md={24}>
            <StyleCorrelation funds={stylesData} />
          </Col>
        </Row>
        <div style={{ marginTop: 15 }} />
        <Row gutter={12}>
          <Col lg={8} md={24}>
            <Card title={<span>最新行情</span>} className={styles.styleCard1}>
              <StandardTable disableRowSlection size="small" columns={styleQuotas} data={{
                list: stylesData,
              }} />
            </Card>
          </Col>
          <Col lg={8} md={24}>
            <Carousel autoplay dots={false} autoplaySpeed={5000} speed={1000} ref={fundCardRef}>
              {activeFunds.map((item: any) => (
                <PortfolioCard quotas={activeFundQuotas} portfolio={item} gotoPrev={handleFundPrev} gotoNext={handleFundNext} />
              ))}
            </Carousel>
          </Col>
          <Col lg={8} md={24}>
            <Carousel autoplay dots={false} autoplaySpeed={5000} speed={1000} ref={managerCardRef}>
              {managers.map((item: any) => (
                <ManagerCard manager={item} managerPortraitInfoAvg={managerPortraitInfoAvg} gotoPrev={handleManagerPrev} gotoNext={handleManagerNext} />
              ))}
            </Carousel>
          </Col>
        </Row>
      </Spin>
      {activeFunds.length === -1 && <FundList />}
    </div>
  )
}

export default connect(
  ({
    dashboard,
    loading,
    user,
  }: {
    dashboard: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
    user: any,
  }) => ({
    dashboardData: dashboard.dashboardData,
    loading: loading.effects['dashboard/fetchDashboardData'],
    currentUser: user.currentUser,
  }),
)(Dashboard)
