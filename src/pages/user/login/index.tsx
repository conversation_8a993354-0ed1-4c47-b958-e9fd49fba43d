import { Alert } from 'antd'
import { FormattedMessage, formatMessage } from 'umi-plugin-react/locale'
import React, { Component } from 'react'

import { CheckboxChangeEvent } from 'antd/es/checkbox'
import { Dispatch, AnyAction } from 'redux'
import { connect } from 'dva'
import { StateType } from '@/models/login'
import LoginComponents from './components/Login'
import styles from './style.less'
import { LoginParamsType } from '@/services/login'
import { ConnectState } from '@/models/connect'
import router from 'umi/router'

const { Tab, UserName, Password, Submit, ImgCaptcha } = LoginComponents

interface LoginProps {
  dispatch: Dispatch<AnyAction>;
  userLogin: StateType;
  submitting: boolean;
}
interface LoginState {
  type: string;
  autoLogin: boolean;
}

@connect(({ login, loading, user }: ConnectState) => ({
  userLogin: login,
  submitting: loading.effects['login/login'],
  systemInfo: user.systemInfo,
}))
class Login extends Component<LoginProps, LoginState> {
  loginForm: undefined | null = undefined;

  state: LoginState = {
    type: 'account',
    autoLogin: true,
  };

  componentDidMount(): void {
    const hostname = window.location.hostname
    const HOST_ETBANK = 'archimedes-bank.fund.pingan.com.cn'
    if (hostname === HOST_ETBANK) {
      router.push('/transit/etbank')
    }
  }

  changeAutoLogin = (e: CheckboxChangeEvent) => {
    this.setState({
      autoLogin: e.target.checked,
    })
  };

  handleSubmit = (err: unknown, values: LoginParamsType) => {
    const { type } = this.state
    if (!err) {
      const { dispatch } = this.props
      dispatch({
        type: 'login/login',
        payload: {
          ...values,
          type,
        },
      })
    }
  };

  onTabChange = (type: string) => {
    this.setState({ type })
  };

  onGetCaptcha = () =>
    new Promise<boolean>((resolve, reject) => {
      if (!this.loginForm) {
        return
      }
      this.loginForm.validateFields(
        ['mobile'],
        {},
        async (err: unknown, values: LoginParamsType) => {
          if (err) {
            reject(err)
          } else {
            const { dispatch } = this.props
            try {
              const success = await ((dispatch({
                type: 'login/getCaptcha',
                payload: values.mobile,
              }) as unknown) as Promise<unknown>)
              resolve(!!success)
            } catch (error) {
              reject(error)
            }
          }
        },
      )
    });

  renderMessage = (content: string) => (
    <Alert style={{ marginBottom: 24 }} message={content} type="error" showIcon />
  );

  render() {
    const { userLogin, submitting, systemInfo } = this.props
    const { status, type: loginType, captchaTs } = userLogin
    const requireCaptcha = systemInfo.userFrom === 'iolab_ex' || systemInfo.captchaRequired || userLogin.requireCaptcha
    const { type } = this.state
    return (
      <div className={styles.main}>
        <div className={styles.landing}></div>
        <LoginComponents
          defaultActiveKey={type}
          onTabChange={this.onTabChange}
          onSubmit={this.handleSubmit}
          onCreate={() => {
            // todo
          }}
        >
          <Tab key="account" tab={formatMessage({ id: 'user-login.login.tab-login-credentials' })}>
            {status === 'error' &&
              loginType === 'account' &&
              !submitting &&
              this.renderMessage(
                formatMessage({ id: 'user-login.login.message-invalid-credentials' }),
              )}
            <UserName
              name="email"
              placeholder={`${formatMessage({ id: 'user-login.login.userName' })}`}
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: 'user-login.userName.required' }),
                },
              ]}
            />
            <Password
              name="password"
              placeholder={`${formatMessage({ id: 'user-login.login.password' })}`}
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: 'user-login.password.required' }),
                },
              ]}
              onPressEnter={e => {
                e.preventDefault()
                if (this.loginForm) {
                  this.loginForm.validateFields(this.handleSubmit)
                }
              }}
            />
            {requireCaptcha &&
            <ImgCaptcha
              name="captcha"
              captchaTs={captchaTs}
              placeholder={formatMessage({ id: 'user-login.verification-code.placeholder' })}
              countDown={120}
              onGetCaptcha={this.onGetCaptcha}
              getCaptchaButtonText={formatMessage({ id: 'user-login.form.get-captcha' })}
              getCaptchaSecondText={formatMessage({ id: 'user-login.captcha.second' })}
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: 'user-login.verification-code.required' }),
                },
              ]}
            />}
          </Tab>
          <Submit loading={submitting}>
            <FormattedMessage id="user-login.login.login" />
          </Submit>
        </LoginComponents>
      </div>
    )
  }
}

export default Login
