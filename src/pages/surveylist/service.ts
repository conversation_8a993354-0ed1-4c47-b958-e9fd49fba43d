import request from '@/utils/request'

export async function querySurveyList(params: any): Promise<any> {
  return request(`/api/admin/survey`, {
    method: 'GET',
    params,
  })
}

export async function queryCategories(): Promise<any> {
  return request(`/api/admin/survey/categories`, {
    method: 'GET',
  })
}
export async function queryTags(): Promise<any> {
  return request(`/api/admin/survey/tags`, {
    method: 'GET',
  })
}


export async function queryUsers(): Promise<any> {
  return request(`/api/admin/users`, {
    method: 'GET',
  })
}

export async function update(id: string, data: any): Promise<any> {
  return request(`/api/admin/survey/${id}`, {
    method: 'PUT',
    data
  })
}

export async function deleteSurvey(id: string) {
  return request(`/api/admin/survey/${id}`, {
    method: 'delete',
  })
}


export async function copy(id: string) {
  return request(`/api/admin/survey/${id}/copy`, {
    method: 'post',
  })
}

