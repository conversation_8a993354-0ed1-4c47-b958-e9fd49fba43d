import { Effect } from 'dva'
import { Reducer } from 'redux'
import { querySurveyList, queryCategories, queryTags, queryUsers, update, deleteSurvey, copy } from './service'
import { notification } from 'antd'

export interface ModelState {
  surveys?: any;
  categories?: Array<string>;
  tags?: Array<string>;
  users?: Array<string>;
  survey?: Object
}

export interface ModelType {
  namespace: 'surveyList';
  state: ModelState;
  effects: {
    fetchSurveyList: Effect;
    fetchCategories: Effect;
    fetchTags: Effect;
    fetchUsers: Effect;
    update: Effect,
    deleteSurvey: Effect,
    copy: Effect,
  };
  reducers: {
    save: Reducer<ModelState>;
    saveTags: Reducer<ModelState>;
    updateCategories: Reducer<ModelState>;
    copySurvey: Reducer<ModelState>;
    delete: Reducer<ModelState>;
  };
}

const SurveysListModel: ModelType = {
  namespace: 'surveyList',
  state: {
    surveys: [],
    categories: [],
    tags: [],
    users: [],
    survey: {},

  },
  effects: {
    *fetchSurveyList({ payload: { params } }, { call, put }) {
      const response = yield call(querySurveyList, params)
      yield put({
        type: 'save',
        payload: {
          surveys: response,
        },
      })
    },
    *fetchCategories({ payload: { params } }, { call, put }) {
      const response = yield call(queryCategories, params)
      yield put({
        type: 'save',
        payload: {
          categories: response,
        },
      })
    },
    *fetchTags({ payload: { params } }, { call, put }) {
      const response = yield call(queryTags, params)
      yield put({
        type: 'save',
        payload: {
          tags: response,
        },
      })
    },
    *fetchUsers({ }, { call, put }) {
      const response = yield call(queryUsers)
      yield put({
        type: 'save',
        payload: {
          users: response,
        },
      })
    },
    *update({ payload: { data, id } }, { call, put }) {
      const response = yield call(update, id, data)
      yield put({
        type: 'saveTags',
        payload: response,
      })
      notification.success({ message: '更新成功！' })
    },
    *deleteSurvey({ payload: { id } }, { call, put }) {
      const res = yield call(deleteSurvey, id)
      yield put({
        type: 'delete',
        payload: res,
      })
      notification.success({ message: '删除成功' })
    },
    *copy({ payload: { id } }, { call, put }) {
      const response = yield call(copy, id)
      yield put({
        type: 'copySurvey',
        payload: response,
      })
    }
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    saveTags(state, action) {
      const list = [...state.surveys.list].map(item => {
        if (item._id === action.payload._id) {
          return action.payload
        }
        return item
      })
      return {
        ...state,
        surveys: {
          list,
          total: state.surveys.total
        }
      }
    },
    updateCategories(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    copySurvey(state, action) {
      const list = [action.payload, ...state.surveys.list]
      return {
        ...state,
        surveys: {
          list,
          total: state.surveys.total
        }
      }
    },
    delete(state, action) {
      const list = [...state.surveys.list].filter(
        item => item._id !== action.payload._id
      )
      return {
        ...state,
        surveys: {
          list,
          total: state.surveys.total
        }
      }
    }
  },
}

export default SurveysListModel
