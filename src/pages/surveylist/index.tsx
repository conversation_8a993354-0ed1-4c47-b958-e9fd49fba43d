import { Button, Input, Table, Tag, Tooltip } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { ModelState } from './model'
import { CurrentUser, UserModelState } from '@/models/user'
import Link from 'umi/link'
import CheckboxPopover from '@/components/CheckboxPopover'
import EditTagsModal from '@/components/EditTagsModal'
import SurveyActionDropDownList from '@/components/SurveyActionDropDownList'
import SettingModal from '@/components/SettingModal'
import SurveyShareModal from '@/components/SurveyShareModal'
import EmailNotificationModal from '@/components/EmailNotificationModal'
import moment from 'moment';
import classnames from 'classnames'

const { Search } = Input;
import styles from './style.less'
// import 'bootstrap/dist/css/bootstrap.css'
interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  currentUser?: CurrentUser;
  surveys: any,
  categories: any,
  tags: any
}

interface ComponentState {
  input?: string,
  page?: number,
  viewMode?: string,
  selectedTags?: Array<any>,
  selectedCategories?: Array<any>,
  showModal?: boolean,
  showShareModal?: boolean,
  showEmailModal: boolean,
  id?: string
}

@connect(
  ({
    user,
    surveyList,
    loading,
  }: {
    user: UserModelState,
    surveyList: ModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentUser: user.currentUser,
    surveys: surveyList.surveys,
    categories: surveyList.categories,
    tags: surveyList.tags,
    users: surveyList.users,
    loading: loading.effects['surveyList/fetchSurveyList'],
  }),
)
class AdminSurvey extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    this.state = {
      page: 1,
      selectedTags: [],
      selectedCategories: [],
      showModal: false,
      showShareModal: false,
      showEmailModal: false,
      id: ''
    }
  }
  componentDidMount() {
    this.loadSurveys()
    // this.loadUsers()
    this.loadCategories()
    this.loadTags()
  }

  loadSurveys = () => {
    const { page, input, selectedTags, selectedCategories } = this.state
    const { dispatch } = this.props
    const tags = selectedTags.map(tag => tag.name).join(',')
    const category = selectedCategories
      .map(selectedCategory => selectedCategory.name)
      .join(',')
    const params = { page, input, tags, category }
    dispatch({
      type: 'surveyList/fetchSurveyList',
      payload: { params },
    })
  }

  loadUsers = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyList/fetchUsers',
      payload: {},
    })
  }

  loadCategories = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyList/fetchCategories',
      payload: {},
    })
  }
  loadTags = () => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyList/fetchTags',
      payload: {},
    })
  }

  updateSurvey = (survey: object, value: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyList/update',
      payload: {
        id: survey._id,
        data: { ...value },
      },
    })
  }

  updateCategories = (category: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyList/updateCategories',
      payload: { category },
    })
  }

  deleteSurvey = (id: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyList/deleteSurvey',
      payload: { id },
    })
  }
  copy = (id: string) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyList/copy',
      payload: { id },
    })
  }

  onSurveyTagsChange = selectedTags => {
    this.setState({ selectedTags, page: 1 }, this.loadSurveys)
  }

  onSurveyCategoriesChange = selectedCategories => {
    this.setState({ selectedCategories, page: 1 }, this.loadSurveys)
  }
  onSearchInputChange = event => {
    const input = event.target.value
    this.setState({ input }, () => {
      if (!this.props.surveys.list.length && !input) {
        this.loadSurveys()
      }
    })
  }

  totalReceiver(survey: any) {
    const { users } = this.props
    return survey.isSpecial || survey.isOpen
      ? users.filter(
        user =>
          ~user.user_scopes.indexOf('researcher') &&
          user.fof_status === 'actived'
      ).length
      : survey.whitelist.length
  }

  hasMore = () => {
    const {
      surveys: { list }
    } = this.props
    const { page } = this.state
    if (list && !list.length && page === 1) {
      return false
    }
    return list.length % 10 === 0 && list.length / 10 === page
  }

  close = () => {
    this.setState({ showModal: false })
  }

  closeShareModal = () => {
    this.setState({ showShareModal: false })
  }

  closeEmailModal = () => {
    this.setState({ showEmailModal: false })
  }

  onShowModal = (modal, id) => {
    this.setState({ [`${modal}`]: true, id })
  }

  updateSetting = (survey, setting) => {
    const { categories } = this.props
    const { category } = setting
    this.updateSurvey(survey, setting)
    if (category && !~categories.indexOf(category)) {
      this.updateCategories(category)
    }
    this.close()
  }


  getColums = () => {
    const { tags, currentUser } = this.props
    const columns = [
      {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        render: (text, row) => {
          return <div>
            <Link style={{ color: '#e3e3e3', cursor: 'pointer' }} to={{ pathname: `/duediligence/surveyDetail/${row._id}` }}>{text}</Link>
            {row.isSticky && (
              <span
                className={classnames('label label-warning')}
                style={{ cursor: 'pointer', marginLeft: 10 }}
              >
                置顶
              </span>
            )}
          </div>
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (text: string, row) => {
          return text === 'on' ?
            <div style={{ display: 'flex' }}>
              <div className={styles.circularon}></div>
              正在回收
            </div>
            :
            <div style={{ display: 'flex' }}>
              <div className={styles.circularoff}></div>
              暂停回收
            </div>
        }
      },
      {
        title: '分类',
        dataIndex: 'category',
        key: 'category',
        render: (text, row) => {
          return `${row.category || '未分类'}`
        }
      },
      {
        title: '标签',
        dataIndex: 'tags',
        key: 'tags',
        render: (text: Array<string>, row: any) => {
          return <div>
            {row.tags.map((item: string) => {
              return <Tag color="#ff9800">{item}</Tag>
            })}
            <EditTagsModal
              id={row._id}
              save={(value) => {
                this.updateSurvey(row, { tags: value })
              }}
              items={tags}
              selectedItems={row.tags}
            >
              <Tooltip placement="top" title='编辑标签'>
                <EditOutlined />
              </Tooltip>
            </EditTagsModal>
          </div>
        },
      },
      {
        title: '回收量',
        dataIndex: 'category',
        key: 'category',
        render: (text, row) => {
          return `${row.feedbacks.length}/${this.totalReceiver(row)}`
        }
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        render: (text, row) => {
          return moment(row.updated_at).from(Date.now())
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        render: (text, row) => {
          return <div>
            <SurveyActionDropDownList
              onShowModal={(mode, id) => { this.onShowModal(mode, id) }}
              {...{
                currentUser,
                survey: row,
                update: this.updateSurvey,
                copy: this.copy,
                deleteSurvey: this.deleteSurvey,
                // initialize
              }}
            />
          </div>
        }
      },
    ]
    return columns
  }

  render() {
    if (!this.props.surveys) {
      return false
    }
    const { input, selectedTags, selectedCategories, page, id, showModal, showShareModal, showEmailModal } = this.state
    const {
      surveys: { list, total },
      users,
      categories,
      tags,
      currentUser,
      loading
    } = this.props
    console.log('list', list)
    const surveyTags = tags && tags.map(tag => ({ name: tag }))
    const surveyCategories = categories && categories.map(category => ({ name: category }))
    const columns = this.getColums()
    const survey = list && list.length && list.length > 0 && list.filter(item => item._id == id)[0]
    const subject = `来自${currentUser && currentUser.company}邀请的问卷：${survey && survey.title}`
    return (
      <div className={styles.adminSurveyPage}>
        <div className={styles.navbar} style={{ display: 'flex', justifyContent: 'flex-end', paddingTop: 10, paddingBottom: 10 }}>
          <div style={{ marginRight: 10, width: 250 }}>
            {/* <Search
              value={input}
              onChange={(e) => { this.setState({ input: e.target.value }) }}
              placeholder='输入名称，按回车搜索'
              onSearch={(value, e) => { }}
              enterButton /> */}
          </div>
          <div style={{ marginRight: 10 }}>
            <CheckboxPopover
              title='过滤标签'
              id="surveyTags"
              list={surveyTags}
              selected={selectedTags}
              onChange={this.onSurveyTagsChange}
            />
          </div>
          <div style={{ marginRight: 10 }}>
            <CheckboxPopover
              title='过滤分类'
              id="surveyCategories"
              list={surveyCategories}
              selected={selectedCategories}
              onChange={this.onSurveyCategoriesChange}
            />
          </div>
          <div style={{ marginRight: 10 }}>
            <Link to="/duediligence/survey/edit">
              <Button type='primary'>添加</Button>
            </Link>
          </div>
        </div>
        {
          list && Array.isArray(list) &&
          <div>
            <Table
              columns={columns}
              dataSource={list}
              loading={loading}
              pagination={{
                total: total * 10,
                current: page,
                showSizeChanger: false,
                onChange: (page) => { this.setState({ page }, this.loadSurveys) }
              }}
            />
            {
              id &&
              <div>
                <SettingModal
                  show={showModal}
                  submit={(survey, setting) => { this.updateSetting(survey, setting) }}
                  {...{
                    users,
                    survey: survey,
                    categories,
                    currentUser,
                  }}
                  close={this.close}
                />
                <EmailNotificationModal
                  show={showEmailModal}
                  close={this.closeEmailModal}
                  survey={survey}
                  subject={subject}
                  users={users}
                />
                <SurveyShareModal
                  show={showShareModal}
                  close={this.closeShareModal}
                  url={`${window.location && window.location.origin}/duediligence/survey/${id}`}
                />
              </div>
            }
          </div>

        }
      </div>
    )
  }

}
export default AdminSurvey
