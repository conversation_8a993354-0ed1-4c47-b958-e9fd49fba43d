import { Button, Result } from 'antd'
import { FrownOutlined } from '@ant-design/icons'
import React from 'react'
import router from 'umi/router'

// 这里应该使用 antd 的 404 result 组件，
// 但是还没发布，先来个简单的。

const NoFoundPage: React.FC<{}> = () => (
  <Result
    status="404"
    title={<FrownOutlined />}
    subTitle="对不起，您访问的页面不存在。"
    extra={
      <Button type="primary" onClick={() => router.push('/')}>
        返回
      </Button>
    }
  ></Result>
)

export default NoFoundPage
