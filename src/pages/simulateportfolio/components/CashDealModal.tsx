import React, { Component } from 'react'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Modal, Radio, Input, message } from 'antd';

interface ComponentProps {
  avaCash: number;
  cashDeal: number;
  cashDealDirection: 'in' | 'out';
  onSave?: any;
  form: any;
  children: any;
  className?: any;
}

interface ComponentState {
  show: boolean;
}

class CashDealModal extends Component<ComponentProps, ComponentState> {
  state = {
    show: false,
  };

  open = () => {
    this.setState({ show: true })
  };

  close = () => {
    this.setState({ show: false })
  };

  render() {
    const { show } = this.state
    const { form, avaCash, cashDeal, cashDealDirection, children, className, onSave } = this.props
    const { getFieldDecorator, validateFields } = form
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    }
    const handleSave = () => {
      validateFields((err: any, values: any) => {
        if (!err) {
          const { cashDealDirection } = values
          const cashDeal = Number(values.cashDeal)
          if (cashDealDirection === 'out' && cashDeal > avaCash) {
            message.warn('可用资金不足')
            return
          }
          onSave({
            cashDealDirection,
            cashDeal,
          })
          this.close()
        }
      })
    }
    const directions = [
      {
        name: '入金',
        value: 'in',
      },
      {
        name: '出金',
        value: 'out',
      },
    ]
    return (
      <div style={{ display: 'inline-block' }} className={className}>
        <div onClick={this.open}>{children}</div>
        <Modal
          title="资金划转"
          visible={show}
          onCancel={this.close}
          width={700}
          footer={[
            <Button key="save" type="primary" onClick={handleSave}>
              保存
            </Button>,
          ]}
        >
          <Form {...formItemLayout} style={{ maxWidth: 400 }}>
            <Form.Item>
              <div>
                <span>可用现金：</span>
                {avaCash.toFixed(2)}。
              </div>
            </Form.Item>
            <Form.Item label="交易方向">
              {getFieldDecorator('cashDealDirection', {
                initialValue: cashDealDirection,
                rules: [{ required: true, message: '请选择交易方向' }],
              })(
                <Radio.Group buttonStyle="solid" size="small">
                  {directions.map(item => (
                    <Radio.Button key={item.value} value={item.value}>
                      {item.name}
                    </Radio.Button>
                  ))}
                </Radio.Group>,
              )}
            </Form.Item>
            <Form.Item label="交易金额">
              {getFieldDecorator('cashDeal', {
                initialValue: cashDeal,
                rules: [
                  { required: true, message: '请输入交易金额' },
                  {
                    type: 'number',
                    message: '请输入数字',
                    transform: (value: string) => Number(value),
                  },
                ],
              })(<Input suffix="元" />)}
            </Form.Item>
          </Form>
        </Modal>
      </div>
    )
  }
}

export default Form.create()(CashDealModal)
