import React from 'react'
import moment, { Moment } from 'moment'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { StateType } from '../model'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, InputNumber, DatePicker, Button } from 'antd';

interface ComponentProps extends FormComponentProps {
  form: any;
  basicInfo?: StateType['basicInfo'];
  dispatch?: Dispatch<any>;
}

const BasicInfoForm = ({ form, dispatch, basicInfo }: ComponentProps) => {
  const { getFieldDecorator, validateFields } = form
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  }
  const disabledDate = (current?: Moment) => {
    return current && current > moment()
  }
  const onValidateForm = () => {
    validateFields((err: any, values: StateType['basicInfo']) => {
      if (!err && dispatch) {
        dispatch({
          type: 'simulatePortfolio/saveBasicInfo',
          payload: {
            ...values,
            initialScale: Number(values.initialScale),
          },
        })
        dispatch({
          type: 'simulatePortfolio/save',
          payload: {
            currentStep: 'confirm',
          },
        })
      }
    })
  }

  return (
    <Form {...formItemLayout} style={{ maxWidth: 400 }}>
      <Form.Item label="组合名称">
        {getFieldDecorator('name', {
          initialValue: basicInfo.name,
          rules: [{ required: true, message: '请输入组合名称' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="初始规模">
        {getFieldDecorator('initialScale', {
          initialValue: basicInfo.initialScale,
          rules: [
            { required: true, message: '请输入初始规模' },
            { type: 'number', message: '请输入数字', transform: value => Number(value) },
          ],
        })(<Input suffix="元" />)}
      </Form.Item>
      <Form.Item label="成立日期">
        {getFieldDecorator('startDate', {
          initialValue: basicInfo.startDate,
          rules: [{ required: true, message: '请选择成立日期' }],
        })(<DatePicker disabledDate={disabledDate} />)}
      </Form.Item>
      <Form.Item label="初费设置(%)">
        {getFieldDecorator('initialRate', {
          initialValue: basicInfo.initialRate,
          rules: [{ required: true, message: '请输入初始费率' }],
        })(<InputNumber />)}
      </Form.Item>
      <Form.Item
        wrapperCol={{
          xs: { span: 24, offset: 0 },
          sm: {
            span: formItemLayout.wrapperCol.sm.span,
            offset: formItemLayout.labelCol.sm.span,
          },
        }}
        label=""
      >
        <Button type="primary" onClick={onValidateForm}>
          下一步
        </Button>
      </Form.Item>
    </Form>
  )
}

export default connect(({ simulatePortfolio }: { simulatePortfolio: StateType }) => ({
  basicInfo: simulatePortfolio.basicInfo,
}))(Form.create<ComponentProps>()(BasicInfoForm))
