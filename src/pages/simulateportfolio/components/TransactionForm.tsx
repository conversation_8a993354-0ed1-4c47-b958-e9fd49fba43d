import React from 'react'
import _ from 'lodash'
import moment, { Moment } from 'moment'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { PlusOutlined, SwapOutlined } from '@ant-design/icons';
import { Card, Button, DatePicker, notification, Popconfirm } from 'antd'
import { StateType } from '../model'
import EditableTable from '@/components/EditableTable'
import SelectFundModal from '@/components/SelectFundModal'
import renderFundQuota from '@/utils/renderFundQuota'
import styles from '../style.less'
import CashDealModal from './CashDealModal'

const formatNumber = (val: number, format: string) => {
  return renderFundQuota(
    {
      value: 'val',
      format,
    },
    {
      val,
    },
  )
}

const TransactionForm = ({
  dispatch,
  basicInfo,
  injections,
  latestScale,
  currentDate,
  latestInjectionDate,
  latestCash,
  loading,
  isEdit,
  portfolioData,
  cashDealDirection,
  cashDeal,
}: {
  dispatch: Dispatch;
  basicInfo: StateType['basicInfo'];
  injections: StateType['injections'];
  latestScale: StateType['latestScale'];
  currentDate: StateType['currentDate'];
  cashDealDirection: StateType['cashDealDirection'];
  cashDeal: number;
  portfolioData?: any;
  latestCash: number;
  latestInjectionDate: any;
  loading?: boolean;
  isEdit?: boolean;
}) => {
  const sumInjection = _.sumBy(injections.filter(item => item.direction === 'in'), 'injection')
  const avaCash = latestCash - sumInjection
  const columns = [
    {
      title: '基金名称',
      dataIndex: 'name',
    },
    {
      title: '配置权重(%)',
      dataIndex: 'weight',
      render: (text: number) => {
        return formatNumber(text, 'valPercentage')
      },
    },
    {
      title: '可卖出金额',
      dataIndex: 'balance',
      render: (text: number) => {
        return formatNumber(text, 'commaNumber')
      },
    },
    {
      title: '交易方向',
      dataIndex: 'direction',
      editable: true,
      format: 'radioGroup',
      getCompProps: (record: any) => {
        return {
          disabled: !record.balance,
          size: 'small',
          data: [
            {
              name: '买入',
              value: 'in',
            },
            {
              name: '卖出',
              value: 'out',
            },
          ],
        }
      },
    },
    {
      title: '交易金额',
      dataIndex: 'injection',
      editable: true,
      format: 'inputNumber',
      getCompProps: (record: any) => {
        let max
        if (record.direction === 'out') {
          max = record.balance
        }
        return {
          max,
          min: 0,
          size: 'small',
        }
      },
    },
    {
      title: '交易费率(%)',
      dataIndex: 'rate',
      editable: true,
      format: 'inputNumber',
      getCompProps: (record: any) => {
        return {
          min: 0,
          size: 'small',
        }
      },
    },
  ]
  const gotoPrevStep = () => {
    dispatch({
      type: 'simulatePortfolio/save',
      payload: {
        currentStep: 'info',
      },
    })
  }
  const handleAddFunds = funds => {
    const fundIds = injections.map(item => item._id)
    const newFuns = funds
      .filter(item => !fundIds.includes(item._id))
      .map(item => {
        return {
          key: item._id,
          _id: item._id,
          name: item.name,
          balance: 0,
          weight: 0,
          direction: 'in',
          injection: 0,
          rate: 0,
        }
      })
    dispatch({
      type: 'simulatePortfolio/save',
      payload: {
        injections: injections.concat(newFuns),
      },
    })
  }
  const handleInjectionChange = data => {
    dispatch({
      type: 'simulatePortfolio/save',
      payload: {
        injections: data,
      },
    })
  }
  const handleCashDealChange = data => {
    dispatch({
      type: 'simulatePortfolio/save',
      payload: data,
    })
  }
  const handleDateChange = date => {
    dispatch({
      type: 'simulatePortfolio/saveCurrentDate',
      payload: {
        currentDate: date,
        isEdit,
      },
    })
  }
  const handleSubmit = () => {
    if (avaCash < 0) {
      notification.warning({ message: '剩余可用资金不足' })
      return
    }
    const newInjections = injections.filter(item => item.injection)
    if (!newInjections.length && cashDeal === 0) {
      notification.warning({ message: '交易金额没有变化' })
      return
    }
    if (!isEdit) {
      const segments = [
        {
          startDate: basicInfo.startDate.toDate(),
          endDate: currentDate.toDate(),
          injections: [
            {
              _id: 'cash',
              name: '现金',
              injection: basicInfo.initialScale,
            },
          ],
        },
        {
          startDate: currentDate.toDate(),
          endDate: new Date(),
          endToNow: true,
          injections: injections,
        },
      ]
      const data = {
        ...basicInfo,
        startDate: basicInfo.startDate.toDate(),
        segments,
      }
      dispatch({
        type: 'simulatePortfolio/add',
        payload: data,
      })
    } else {
      if (cashDeal) {
        newInjections.push({
          _id: 'cash',
          name: '现金',
          injection: cashDeal,
          direction: cashDealDirection,
        })
      }
      const data = {
        startDate: currentDate.toDate(),
        injections: newInjections.map(item => {
          return {
            ...item,
            injection: item.injection * (item.direction === 'in' ? 1 : -1),
          }
        }),
      }
      dispatch({
        type: 'simulatePortfolio/addTransaction',
        payload: {
          id: portfolioData._id,
          data,
        },
      })
    }
  }
  const disabledDate = (current?: Moment) => {
    if (!current) {
      return false
    }
    const currentTs = +current.startOf('date')

    return currentTs <= +latestInjectionDate.startOf('date') || currentTs > +moment()
  }
  return (
    <Card
      title="设置调仓信息"
      extra={
        <>
          <>
            <span>调仓日期：</span>
            <DatePicker
              value={currentDate}
              disabledDate={disabledDate}
              placeholder="选择日期"
              size="small"
              onChange={handleDateChange}
            />
          </>
          <SelectFundModal dispatch={dispatch} onChange={handleAddFunds}>
            <Button
              ghost
              type="primary"
              icon={<PlusOutlined />}
              size="small"
              style={{ marginLeft: 15 }}
              disabled={!currentDate}
            >
              添加基金
            </Button>
          </SelectFundModal>
          {isEdit && (
            <CashDealModal
              {...{ cashDealDirection, cashDeal, avaCash }}
              onSave={handleCashDealChange}
            >
              <Button ghost type="primary" icon={<SwapOutlined />} size="small" style={{ marginLeft: 15 }}>
                资金划转
              </Button>
            </CashDealModal>
          )}
        </>
      }
    >
      <EditableTable
        loading={loading}
        className={styles.transactionTable}
        columns={columns}
        size="small"
        pagination={false}
        dataSource={injections}
        onDataChange={handleInjectionChange}
        footer={() => (
          <div style={{ textAlign: 'right' }}>
            <span style={{ float: 'left' }}>共{injections.length}个产品</span>
            <span>当前产品规模为{formatNumber(latestScale, 'commaNumber')}，</span>
            {cashDeal !== 0 && (
              <span>
                本次{cashDealDirection === 'in' ? '入金' : '出金'}
                {formatNumber(cashDeal, 'commaNumber')}元，
              </span>
            )}
            <span>
              剩余可用资金为
              {formatNumber(
                avaCash + cashDeal * (cashDealDirection === 'in' ? 1 : -1),
                'commaNumber',
              )}
              。
            </span>
          </div>
        )}
        scroll={{
          y: 350,
        }}
      />
      {!isEdit && (
        <div className="ant-result-extra">
          <Button type="primary" onClick={gotoPrevStep}>
            上一步
          </Button>
          <Button
            ghost
            loading={loading}
            type="primary"
            onClick={handleSubmit}
            disabled={!currentDate || !injections.length}
          >
            创建组合
          </Button>
        </div>
      )}
      {isEdit && (
        <div className="ant-result-extra">
          <Popconfirm
            title="确认调仓吗？"
            onConfirm={handleSubmit}
            onCancel={() => {}}
            okText="确认"
            cancelText="取消"
          >
            <Button
              ghost
              loading={loading}
              type="primary"
              disabled={!currentDate || !injections.length}
            >
              确认调仓
            </Button>
          </Popconfirm>
        </div>
      )}
    </Card>
  );
}

export default connect(
  ({
    simulatePortfolio,
    loading,
  }: {
    simulatePortfolio: StateType;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    basicInfo: simulatePortfolio.basicInfo,
    injections: simulatePortfolio.injections,
    latestScale: simulatePortfolio.latestScale,
    currentDate: simulatePortfolio.currentDate,
    latestCash: simulatePortfolio.latestCash,
    latestInjectionDate: simulatePortfolio.latestInjectionDate,
    portfolioData: simulatePortfolio.portfolioData,
    cashDealDirection: simulatePortfolio.cashDealDirection,
    cashDeal: simulatePortfolio.cashDeal,
    loading: loading.models.simulatePortfolio,
  }),
)(TransactionForm)
