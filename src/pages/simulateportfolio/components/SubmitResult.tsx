import React from 'react'
import Link from 'umi/link'
import { Button, Result } from 'antd'

const SubmitResult = ({ currentPortfolio }: { currentPortfolio: any }) => {
  const extra = (
    <>
      <Button type="primary">
        <a href={`/fof/portfolios/simulate/${currentPortfolio._id}`}>继续调仓</a>
      </Button>
      <Button ghost type="primary">
        <Link to={`/fof/portfolios/${currentPortfolio._id}/overview`}>查看组合</Link>
      </Button>
    </>
  )
  return <Result status="success" title="操作成功" extra={extra} />
}

export default SubmitResult
