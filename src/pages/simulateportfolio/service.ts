import request from '@/utils/request'

export function getPortfolio(id: string) {
  return request(`/api/portfolios/${id}`, {
    method: 'GET',
  })
}

export function createPortfolio(data: any) {
  return request(`/api/portfolios/simulate`, {
    method: 'POST',
    data,
  })
}

export function updateTransaction(id, data: any) {
  return request(`/api/portfolios/simulate/${id}/transaction`, {
    method: 'PUT',
    data,
  })
}
