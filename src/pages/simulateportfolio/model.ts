import _ from 'lodash'
import { Effect } from 'dva'
import { Reducer } from 'redux'
import moment from 'moment'

import { getPortfolio, createPortfolio, updateTransaction } from './service'

export interface StateType {
  currentStep?: string;
  basicInfo?: {
    name?: string;
    initialScale?: number;
    startDate?: any;
    initialRate: Number;
  };
  injections: {
    _id?: string;
    isCash?: boolean;
    name: string;
    injection: number;
  }[];
  portfolioData: any;
  latestScale: number;
  cashDeal: number;
  currentDate?: any;
  latestInjectionDate?: any;
  latestCash?: any;
  cashDealDirection: 'in' | 'out';
}

export interface ModelType {
  namespace: 'simulatePortfolio';
  state: StateType;
  effects: {
    fetchPortfolioData: Effect;
    add: Effect;
    addTransaction: Effect;
  };
  reducers: {
    save: Reducer<StateType>;
    saveBasicInfo: Reducer<StateType>;
    state: Reducer<StateType>;
  };
}

const buildTrasactionState = (portfolio: any, date?: any, prevInjections?: any) => {
  const prevInjectionDatMap = (prevInjections || []).reduce((out, item) => {
    out[item._id] = _.pick(item, ['direction', 'injection', 'rate'])
    return out
  }, {})
  const { segments } = portfolio
  let nets = portfolio.nets
  let scale = portfolio.scale
  if (date) {
    const dateTs = +date.startOf('date')
    nets = nets.filter(item => item.date < dateTs)
    scale = scale.filter(item => item.date < dateTs)
  }
  const latestScaleItem = scale[scale.length - 1]
  const latestScale = latestScaleItem.value
  const latestItem = nets[nets.length - 1]
  const weights = latestItem.weights
  const segment = segments[segments.length - 1]
  const funds = segment.funds.map((fund, index) => {
    const prevData = prevInjectionDatMap[fund._id] || {}
    return {
      ...fund,
      key: fund._id,
      balance: latestScale * weights[index],
      weight: weights[index],
      direction: 'in',
      injection: 0,
      rate: 0,
      ...prevData,
    }
  })
  const cashInfo = funds.find(item => item._id === 'cash') || {}
  const injections = funds
    .filter(item => item._id !== 'cash')
    .sort((fst, snd) => snd.weight - fst.weight)
  return {
    portfolioData: portfolio,
    latestScale: latestScale,
    latestInjectionDate: moment(segment.startDate),
    currentDate: date,
    latestCash: cashInfo.balance,
    injections,
    cashDeal: 0,
  }
}

const initialState: StateType = {
  portfolioData: {},
  currentStep: 'info',
  basicInfo: {
    initialRate: 0,
  },
  injections: [],
  latestScale: 0,
  cashDeal: 0,
  latestCash: 0,
  cashDealDirection: 'in',
}

const Model: ModelType = {
  namespace: 'simulatePortfolio',

  state: {
    ...initialState,
  },

  effects: {
    *fetchPortfolioData({ payload: { id } }, { call, put }) {
      const portfolio = yield call(getPortfolio, id)
      const stateData = buildTrasactionState(portfolio)
      yield put({
        type: 'save',
        payload: stateData,
      })
    },
    *add({ payload }, { call, put }) {
      const response = yield call(createPortfolio, payload)
      yield put({
        type: 'save',
        payload: {
          portfolioData: response,
          currentStep: 'result',
        },
      })
    },
    *addTransaction({ payload: { id, data } }, { call, put }) {
      const response = yield call(updateTransaction, id, data)
      yield put({
        type: 'save',
        payload: {
          portfolioData: response,
          currentStep: 'result',
        },
      })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    reset(state, action) {
      return {
        ...initialState,
      }
    },
    saveBasicInfo(state, action) {
      const basicInfo = action.payload
      return {
        ...state,
        latestScale: basicInfo.initialScale,
        latestInjectionDate: basicInfo.startDate,
        latestCash: basicInfo.initialScale,
        cashDeal: 0,
        basicInfo,
      }
    },
    saveCurrentDate(state, action) {
      const { currentDate, isEdit } = action.payload
      if (!isEdit) {
        return {
          ...state,
          currentDate,
        }
      }
      const stateData = buildTrasactionState(state.portfolioData, currentDate, state.injections)
      return {
        ...state,
        ...stateData,
      }
    },
  },
}

export default Model
