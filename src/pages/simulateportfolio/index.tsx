import React, { useEffect } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Card, Breadcrumb, Steps } from 'antd';
import { StateType } from './model'
import BasicInfoForm from './components/BasicInfoForm'
import TransactionForm from './components/TransactionForm'
import SubmitResult from './components/SubmitResult'
import styles from './style.less'

const { Step } = Steps

interface WrapperProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  form: any;
  portfolioData: any;
  currentStep: string;
  location: any;
  match: any;
}

const SimulatePortfolio: React.FC<WrapperProps> = props => {
  const {
    portfolioData,
    currentStep,
    dispatch,
    match: {
      params: { id },
    },
  } = props
  const isEdit = id.length === 24
  useEffect(() => {
    if (isEdit) {
      dispatch({
        type: 'simulatePortfolio/save',
        payload: {
          currentStep: 'confirm',
        },
      })
      dispatch({
        type: 'simulatePortfolio/fetchPortfolioData',
        payload: { id },
      })
    } else {
      dispatch({
        type: 'simulatePortfolio/reset',
        payload: {},
      })
    }
  }, [])
  const getCurrentStepIndex = () => {
    const steps = isEdit ? ['confirm', 'result'] : ['info', 'confirm', 'result']
    return steps.indexOf(currentStep)
  }
  let stepComponent
  if (currentStep === 'info') {
    stepComponent = <BasicInfoForm />
  } else if (currentStep === 'confirm') {
    stepComponent = <TransactionForm isEdit={isEdit} />
  } else {
    stepComponent = <SubmitResult currentPortfolio={portfolioData} />
  }
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>组合构建</Breadcrumb.Item>
        <Breadcrumb.Item>模拟组合</Breadcrumb.Item>
        <Breadcrumb.Item>{isEdit ? portfolioData && portfolioData.name : '新建'}</Breadcrumb.Item>
      </Breadcrumb>
      <Card style={{ marginBottom: '15px' }}>
        <Steps current={getCurrentStepIndex()} className={styles.steps}>
          {!isEdit && <Step title="基本信息" />}
          <Step title="添加调仓" />
          <Step title="完成" />
        </Steps>
        {stepComponent}
      </Card>
    </div>
  )
}

const WarpForm = Form.create()(SimulatePortfolio)

export default connect(
  ({
    loading,
    simulatePortfolio,
  }: {
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
    simulatePortfolio: StateType;
  }) => ({
    loading: loading.models.simulatePortfolio,
    currentStep: simulatePortfolio.currentStep,
    portfolioData: simulatePortfolio.portfolioData,
  }),
)(WarpForm)
