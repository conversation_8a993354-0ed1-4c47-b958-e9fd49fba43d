import React, { useState } from 'react'
import moment from 'moment'
import _ from 'lodash'
import { useRequest } from '@umijs/hooks'
import router from 'umi/router'
import { connect } from 'dva'
import { QuestionCircleOutlined } from '@ant-design/icons'
import Chart from '@/components/Chart/Chart'
import Echart from '@/components/Chart/Echarts'
import ExportData from '@/components/ExportData'
import sortQuotaFn from '@/utils/sortQuotaFn'
import theme from '@/components/Chart/theme'
import buildTreeData from '@/utils/buildTreeDataWithSummary'
import { Card, Row, Col, Select, Tooltip, Spin, Table, DatePicker, Tabs, Space, Affix, Switch, Alert } from 'antd'
import { getTopDownAttrDetail, getTopDownAttrSeries, getTopDownSelectDetail } from './service'
import styles from './style.less'
import buildTableColumn from '@/utils/buildTableColumn'
import NavTabs from '@/pages/persona/detection/NavTabs'
import AlgDesc from './components/AlgDesc'
import AnalyzeRangeAlert from './components/AnalyzeRangeAlert'

const { Option } = Select
const { RangePicker } = DatePicker
const { TabPane } = Tabs

const swIndustryMapping = {
  '通信(申万)': '801770.SI',
  '计算机(申万)': '801750.SI',
  '传媒(申万)': '801760.SI',
  '电子(申万)': '801080.SI',
  '银行(申万)': '801780.SI',
  '房地产(申万)': '801180.SI',
  '非银金融(申万)': '801790.SI',
  '医药生物(申万)': '801150.SI',
  '商贸零售(申万)': '801200.SI',
  '轻工制造(申万)': '801140.SI',
  '农林牧渔(申万)': '801010.SI',
  '社会服务(申万)': '801210.SI',
  '食品饮料(申万)': '801120.SI',
  '纺织服饰(申万)': '801130.SI',
  '家用电器(申万)': '801110.SI',
  '汽车(申万)': '801880.SI',
  '美容护理(申万)': '801980.SI',
  '建筑装饰(申万)': '801720.SI',
  '公用事业(申万)': '801160.SI',
  '交通运输(申万)': '801170.SI',
  '环保(申万)': '801970.SI',
  '综合(申万)': '801230.SI',
  '建筑材料(申万)': '801710.SI',
  '基础化工(申万)': '801030.SI',
  '钢铁(申万)': '801040.SI',
  '有色金属(申万)': '801050.SI',
  '煤炭(申万)': '801950.SI',
  '石油石化(申万)': '801960.SI',
  '电力设备(申万)': '801730.SI',
  '机械设备(申万)': '801890.SI',
  '国防军工(申万)': '801740.SI',
}

const swBenchmarkList = _.map(swIndustryMapping, (value, label) => {
  return {
    label, value,
  }
})

const getExportFilename = (suffix) => {
  // return suffix
  const { currentFund, dateRange } = global
  let filename = `${currentFund.name}_${suffix}`
  if (dateRange && dateRange.length) {
    filename = `${filename}_${dateRange[0].format('YYYYMMDD')}-${dateRange[1].format('YYYYMMDD')}`
  }
  return filename
  // const { model, startDate, endDate } = this.state
  // let name = 'Brinson模型'
  // if (model === 'campisi') {
  //   name = 'Campisi模型'
  // } else if (model === 'return') {
  //   name = '绝对收益模型'
  // }
  // return `${currentFund.name}_${name}_${moment(startDate).format('YYYYMMDD')}-${moment(endDate).format('YYYYMMDD')}_${suffix}`
}

const renderReturnValue = (value: number) => {
  if (!value && value !== 0) {
    return '-'
  }
  const className = value > 0 ? 'colorUp' : 'colorDown'
  return <span className={className}>{(value * 100).toFixed(2) + '%'}</span>
}

const renderPercentageValue = (value: number) => {
  if (!value && value !== 0) {
    return '-'
  }
  return <span>{(value * 100).toFixed(2) + '%'}</span>
}

const renderTableNumericColumn = (val: number) => (
  <div style={{ textAlign: 'right' }}>{renderReturnValue(val)}</div>
)

const renderSeriesChart = (quotas: any, title: string, chartType?: string, origData?: any) => {
  const model = 'brinson'
  const quota0 = quotas[0]
  if (!quota0.data.length) {
    return false
  }
  const afterChartCreated = (refChart) => {
    const data = quota0.data
    if (refChart && data.length > 60 && model === 'brinson') {
      const latest100Data = data.slice(-60)
      refChart.xAxis[0].setExtremes(latest100Data[0][0], latest100Data[latest100Data.length - 1][0])
    }
  }
  const columns = quotas.map(item => {
    return {
      title: item.name,
      dataIndex: item.value,
    }
  })
  columns.unshift({
    title: '日期',
    dataIndex: 'END_DATE',
  })
  const options = {
    chart: {
      type: quota0.data.length === 1 ? 'column' : (chartType || 'area'),
    },
    scrollbar: {
      enabled: false,
    },
    navigator: {
      enabled: quota0.data.length > 1,
    },
    plotOptions: {
      column: {
        stacking: 'normal',
      },
    },
    yAxis: [{}, { opposite: true }],
    series: quotas.map((quota, index) => {
      return {
        visible: quota.visible,
        name: quota.name,
        data: quota.data,
        fillOpacity: 1 - 0.1 * index,
        ...(quota.chartConfig || {}),
      }
    }),
  }
  return (
    <Card
      title={title}
      bordered={false}
      extra={<ExportData columns={columns} dataSource={origData} filename={getExportFilename(title)}/>}
    >
      <Chart options={options} constructorType="stockChart" afterChartCreated={afterChartCreated} />
    </Card>
  )
}

const renderAttrSeries = (data, assetType) => {
  const quotas = [
    {
      name: assetType === 'multiasset' ? '资产配置' : '行业配置',
      value: 'AR',
      visible: true,
      chartConfig: {
        type: 'column',
      },
    },
    {
      name: assetType === 'multiasset' ? '个券选择' : '个股选择',
      value: 'SR',
      visible: true,
      chartConfig: {
        type: 'column',
      },
    },
    {
      name: '超额',
      value: 'RETURN_EX',
      visible: true,
      chartConfig: {
        type: 'line',
        lineWidth: 0,
        states: {
          hover: {
            lineWidthPlus: 0,
          },
        },
        color: '#E49831',
        marker: {
          enabled: true,
          lineWidth: 4,
          symbol: 'circle',
          lineColor: '#9c27b0',
        },
      },
    },
    {
      name: '累计超额(右轴)',
      value: 'ACC_RETURN_EX',
      visible: true,
      chartConfig: {
        type: 'line',
        yAxis: 1,
      },
    },
  ].map(quota => {
    return {
      ...quota,
      data: data.map(item => [+moment(item.END_DATE), item[quota.value] * 100]),
    }
  })
  return renderSeriesChart(quotas, '超额收益历史变化情况', '', data)
}

const renderAttrDetail = (data, assetType) => {
  const quotas = [
    {
      title: assetType === 'multiasset' ? '资产配置' : '行业配置',
      dataIndex: 'AR',
    },
    {
      title: assetType === 'multiasset' ? '个券选择' : '个股选择',
      dataIndex: 'SR',
    },
  ]
  const columns = [
    {
      title: '行业',
      dataIndex: 'INDUSTRY',
    },
    ...quotas,
  ]
  const options = {
    chart: {
      type: 'column',
    },
    xAxis: {
      categories: data.map(item => item.INDUSTRY),
    },
    plotOptions: {
      column: {
        stacking: 'normal',
      },
    },
    series: quotas.map(quota => {
      return {
        name: quota.title,
        data: data.map(item => item[quota.dataIndex] * 100),
      }
    }),
  }
  let otherRet
  if (data.length) {
    otherRet = data[0].OTHER_RET
  }
  return (
    <Card
      title="收益详细信息"
      bordered={false}
      extra={<ExportData columns={columns} dataSource={data} filename={getExportFilename('收益详细信息')}/>}
    >
      {assetType === 'multiasset' && otherRet !== undefined &&
      <p>其他 = 组合实际收益 – 代理组合收益 = <span>{renderReturnValue(otherRet)}</span></p>
      }
      <Chart options={options} />
    </Card>
  )
}

const renderStockReturnChart = (chartData) => {
  const itemStyle = {
    opacity: 0.8,
    shadowBlur: 10,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: 'rgba(0, 0, 0, 0.5)',
  }
  const options = {
    color: [
      '#dd4444', '#fec42c', '#80F1BE',
    ],
    grid: {
      right: 80,
      left: 40,
    },
    legend: {
      top: 2,
      right: 10,
      data: ['最佳选券', '最差选券'],
    },
    tooltip: {
      padding: 10,
      // backgroundColor: '#222',
      // borderColor: '#777',
      borderWidth: 1,
      axisPointer: {
        type: 'cross',
      },
      formatter: function (obj) {
        const value = obj.value
        if (!value) {
          return ''
        }
        if (obj.componentType === 'markPoint') {
          return `${obj.name}：${value}%`
        }
        return '<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'
            + obj.seriesName + ': ' + value[4]
            + '</div>'
            + '主动收益：' + value[0] + '%<br>'
            + '主动比例：' + value[1] + '%<br>'
            + '选券效应：' + value[3] + '%<br>'
      },
    },
    xAxis: {
      type: 'value',
      name: '主动收益',
      axisLabel: {
        formatter: '{value} %',
      },
    },
    yAxis: {
      type: 'value',
      name: '主动比例',
      axisLabel: {
        formatter: '{value} %',
      },
      axisPointer: {
        snap: true,
      },
    },
    series: chartData.map(item => {
      const itemData = item.data.map((item, index) => [
        _.round((item.RETURN_P - (item.RETURN_B || 0)) * 100, 2),
        _.round((item.WEIGHT_P - (item.WEIGHT_B || 0)) * 100, 2),
        index + 1,
        _.round(item.SELECT_EFFECT_A * 100, 2),
        item.STOCK_NAME,
      ])
      return {
        name: item.name,
        type: 'scatter',
        itemStyle: itemStyle,
        symbolSize: 35,
        data: itemData,
        markPoint: {
          data: [
            {
              name: item.name === '最佳选券' ? '最佳收益' : '最差收益',
              value: itemData[0] && itemData[0][3],
              coord: itemData[0] && itemData[0].slice(0, 2),
            },
          ],
          itemStyle: {
            color: '#0EBF9C',
          },
        },
        label: {
          show: true,
          formatter: '{@[2]}',
        },
      }
    }),
  }
  return <Echart style={{ height: '600px' }} options={options}/>
}

const renderReturnDetailTable = (stockReturnsData, detailData) => {
  const [showZeroPosition, setShowZeroPosition] = useState(false)
  const columns = [{
    titleText: '组合平均权重',
    title: (
      <span>
        组合平均权重
        <Tooltip title="组合中个股多期平均权重，累加到行业" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'WEIGHT_P',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  }, {
    titleText: '组合收益',
    title: (
      <span>
        组合收益
      </span>
    ),
    dataIndex: 'REF_RETURN_P',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  },  {
    titleText: '组合贡献',
    title: (
      <span>
        组合贡献
        <Tooltip title="组合中个股多期平均权重*股票多期算法计算的收益率，累加到行业上" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'RETURN_P',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  }, {
    titleText: '基准平均权重',
    title: (
      <span>
        基准平均权重
        <Tooltip title="基准中个股多期平均权重，累加到行业上" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'WEIGHT_B',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  }, {
    titleText: '基准收益',
    title: (
      <span>
        基准收益
      </span>
    ),
    dataIndex: 'REF_RETURN_B',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  }, {
    titleText: '基准贡献',
    title: (
      <span>
        基准贡献
        <Tooltip title="基准中个股多期平均权重*股票多期算法计算的收益率，累加到行业上" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'RETURN_B',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  }, {
    titleText: '主动比例',
    title: (
      <span>
        主动比例
        <Tooltip title="组合中股票的多期平均权重-基准中股票的多期平均权重，累加到行业上" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'WEIGHT_EX',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  }, {
    titleText: '主动收益',
    title: (
      <span>
        主动收益
        <Tooltip title="个股主动比例*股票区间收益率，累加到行业上" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'RETURN_EX',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  }, {
    titleText: '选券效应',
    title: (
      <span>
        选券效应
        <Tooltip title="Topdown算法中的选券效应，累加到行业上" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'SELECT_EFFECT_A',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  }, {
    titleText: '配置效应',
    title: (
      <span>
        配置效应
        <Tooltip title="Topdown算法中行业的配置效应" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'ALLOCATION_EFFECT_A',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  }, {
    title: '超额收益',
    dataIndex: 'RETURN_EX',
    format: 'percentage',
    align: 'right',
    hasSorter: true,
  }].map(buildTableColumn)
  const treeColumns = [{
    title: '行业',
    dataIndex: 'STOCK_NAME',
    format: 'text',
    width: 150,
  },
  ...columns,
  ]
  const commonColumns = [{
    title: '名称',
    dataIndex: 'STOCK_NAME',
    format: 'text',
  }, {
    title: '代码',
    dataIndex: 'STOCK_CODE',
    format: 'text',
  }, {
    title: '行业',
    dataIndex: 'INDUSTRY',
    format: 'text',
  }, ...columns]
  const arMap = detailData.reduce((out, item) => {
    out[item.INDUSTRY] = item.AR
    return out
  }, {})
  const exRetMap = detailData.reduce((out, item) => {
    out[item.INDUSTRY] = item.AR + item.SR
    return out
  }, {})
  const refRetP = detailData.reduce((out, item) => {
    out[item.INDUSTRY] = item.REF_RETURN_P
    return out
  }, {})
  const refRetB = detailData.reduce((out, item) => {
    out[item.INDUSTRY] = item.REF_RETURN_B
    return out
  }, {})
  const zeroFilter = item => {
    if (showZeroPosition) {
      return true
    }
    return item.WEIGHT_P !== 0
  }
  const data = buildTreeData(stockReturnsData, treeColumns.slice(1), ['INDUSTRY'], 'STOCK_NAME', zeroFilter)
    .map(item => {
      item.ALLOCATION_EFFECT_A = arMap[item.STOCK_NAME]
      item.RETURN_EX = exRetMap[item.STOCK_NAME]
      item.REF_RETURN_P = refRetP[item.STOCK_NAME]
      item.REF_RETURN_B = refRetB[item.STOCK_NAME]
      return item
    })
  return (
    <Card
      title="选券效应明细"
      bordered={false}
      extra={
        <Space>
          <span>展示权重为0个券</span>
          <Switch size="small" onChange={setShowZeroPosition} />
          <ExportData columns={commonColumns} dataSource={stockReturnsData} filename={getExportFilename('选券效应明细')}/>
        </Space>
      }
    >
      <Table
        rowKey="id"
        pagination={false}
        columns={treeColumns}
        dataSource={data}
        size="small"
        summary={pageData => {
          const summaryData = columns.map(col => {
            return {
              value: _.sum(pageData.map(item => item[col.dataIndex])),
              format: col.format,
              dataIndex: col.dataIndex,
            }
          })
          return (
            <>
              <Table.Summary.Row>
                <Table.Summary.Cell>合计</Table.Summary.Cell>
                {summaryData.map(item => {
                  return (
                    <Table.Summary.Cell>
                      <div style={{ textAlign: 'right' }}>
                        {
                          ['REF_RETURN_B', 'REF_RETURN_P'].includes(item.dataIndex)
                            ? '' : item.format === 'percentage' ?
                            renderReturnValue(item.value) : renderPercentageValue(item.value)
                        }
                      </div>
                    </Table.Summary.Cell>
                  )
                })}
              </Table.Summary.Row>
            </>
          )
        }}
      />
    </Card>
  )
}

const renderStockReturn = (stockReturnsData) => {
  const columns1 = [{
    title: '最佳选券',
    dataIndex: 'STOCK_NAME',
    width: 110,
  }, {
    title: '行业',
    dataIndex: 'INDUSTRY',
  }, {
    titleText: '主动比例',
    title: (
      <span>
        主动比例
        <Tooltip title="组合中股票的多期平均权重-基准中股票的多期平均权重" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'WEIGHT_EX',
    format: 'valPercentage',
    align: 'right',
    render: renderPercentageValue,
  }, {
    titleText: '主动收益',
    title: (
      <span>
        主动收益
        <Tooltip title="主动比例*股票区间收益率" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'RETURN_EX',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }, {
    titleText: '效应',
    title: (
      <span>
        效应
        <Tooltip title="Topdown算法中的选券效应" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'SELECT_EFFECT_A',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }]
  const columns2 = [{
    title: '最差选券',
    dataIndex: 'STOCK_NAME',
    width: 110,
  },{
    title: '行业',
    dataIndex: 'INDUSTRY',
  }, {
    titleText: '主动比例',
    title: (
      <span>
        主动比例
        <Tooltip title="组合中股票的多期平均权重-基准中股票的多期平均权重" placement="right">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'WEIGHT_EX',
    format: 'valPercentage',
    align: 'right',
    render: renderPercentageValue,
  }, {
    titleText: '主动收益',
    title: (
      <span>
        主动收益
        <Tooltip title="主动比例*股票区间收益率" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'RETURN_EX',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }, {
    titleText: '效应',
    title: (
      <span>
        效应
        <Tooltip title="Topdown算法中的选券效应" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'SELECT_EFFECT_A',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }]
  const commonColumns = [{
    title: '股票名称',
    dataIndex: 'STOCK_NAME',
    width: 150,
  }, {
    title: '行业',
    dataIndex: 'INDUSTRY',
  }, {
    title: '主动比例',
    dataIndex: 'WEIGHT_EX',
    format: 'valPercentage',
    align: 'right',
    render: renderPercentageValue,
  }, {
    title: '主动收益',
    dataIndex: 'RETURN_EX',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }, {
    title: '效应',
    dataIndex: 'SELECT_EFFECT_A',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }]
  let data1 = stockReturnsData.slice(0, 10)
  let data2 = stockReturnsData.slice(-10)
  if (stockReturnsData.length < 20) {
    const slicePoint = Math.ceil(stockReturnsData.length / 2)
    data1 = stockReturnsData.slice(0, slicePoint)
    data2 = stockReturnsData.slice(slicePoint)
  }
  data2 = data2.reverse()
  const chartData = [{
    name: '最佳选券',
    data: data1,
  }, {
    name: '最差选券',
    data: data2,
  }]
  return (
    <>
      <div style={{ marginBottom: '10px' }} />
      <Card
        title={
          <span>
            选券效益
            <Tooltip title="横轴主动收益，纵轴主动比例" placement="right">
              <span style={{ marginLeft: '5px' }}>
                <QuestionCircleOutlined />
              </span>
            </Tooltip>
          </span>
        }
        extra={<ExportData columns={commonColumns} dataSource={data1.concat(data2)} filename={getExportFilename('选券效益_最佳前10_最差前10')}/>}
      >
        <Row gutter={10} className={styles.fixHeightWapper}>
          <Col md={16} sm={24}>
            {renderStockReturnChart(chartData)}
          </Col>
          <Col md={8} sm={24}>
            <Table className={styles.xsTable} columns={columns1} dataSource={data1} size="small" pagination={false} />
            <Table className={styles.xsTable} columns={columns2} dataSource={data2} size="small" pagination={false} />
          </Col>
        </Row>
      </Card>
    </>
  )
}
const getBrisonDetailTableData = (data) => {
    const dataSource = data.filter(item => item.INDUSTRY !== 'OTHER')
    .map(item => {
      item.SER = item.AR + item.SR
      return item
    })
  const columns = [
    {
      title: '行业',
      dataIndex: 'INDUSTRY',
      format: 'text',
    },
    {
      titleText: '配置',
      title: (
        <span>
          配置
          <Tooltip title="Topdown算法中行业的配置效应" placement="left">
            <span style={{ marginLeft: '5px' }}>
              <QuestionCircleOutlined />
            </span>
          </Tooltip>
        </span>
      ),
      dataIndex: 'AR',
      align: 'right',
      format: 'percentage',
      render: renderTableNumericColumn,
    },
    {
      titleText: '选择',
      title: (
        <span>
          选择
          <Tooltip title="行业内个股选券效应的累加" placement="left">
            <span style={{ marginLeft: '5px' }}>
              <QuestionCircleOutlined />
            </span>
          </Tooltip>
        </span>
      ),
      dataIndex: 'SR',
      align: 'right',
      format: 'percentage',
      render: renderTableNumericColumn,
    },
    {
      titleText: '超额',
      title: (
        <span>
          超额
          <Tooltip title="配置效应+选券效应" placement="left">
            <span style={{ marginLeft: '5px' }}>
              <QuestionCircleOutlined />
            </span>
          </Tooltip>
        </span>
      ),
      dataIndex: 'SER',
      align: 'right',
      format: 'percentage',
      render: renderTableNumericColumn,
    },
  ].map(item => {
    item.sorter = sortQuotaFn(item, 'asc')
    return item
  })
  const summaryItem = columns.reduce((out, item) => {
    const dataIndex = item.dataIndex
    if (dataIndex === 'INDUSTRY') {
      out.INDUSTRY = '合计'
    } else {
      out[dataIndex] = _.sum(dataSource.map(row => row[dataIndex]))
    }
    return out
  }, {})
  dataSource.unshift(summaryItem)
  return {
    columns, dataSource,
  }
}

const renderIndustryDetailChart = (data) => {
  const itemStyle = {
    color: (params) => {
      return params.value ? params.value[4] : ''
    },
    opacity: 0.8,
    shadowBlur: 10,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: 'rgba(0, 0, 0, 0.5)',
  }
  const itemData = data.filter(item => item.INDUSTRY !== 'OTHER').map((item, index) => [
    _.round(item.AR * 100, 2),
    _.round(item.WEIGHT_EX * 100, 2),
    _.round((item.AR + item.SR) * 100, 2),
    item.INDUSTRY,
    theme.colors[index % theme.colors.length],
  ]).sort((fst, snd) => snd[2] - fst[2])
  const bestItem = itemData[0] || []
  const exReturns = itemData.map(item => item[2])
  const series = [{
    name: '行业',
    type: 'scatter',
    itemStyle: itemStyle,
    data: itemData,
    label: {
      show: true,
      position: 'inside',
      formatter: function(params) {
        return params.value[3]; // 显示行业名称
      },
      fontSize: 10,
      fontWeight: 'bold',
      color: '#fff',
      textShadowColor: 'rgba(0, 0, 0, 0.8)',
      textShadowBlur: 2,
      textShadowOffsetX: 1,
      textShadowOffsetY: 1,
    },
    markPoint: {
      data: [
        {
          name: `最佳主动收益(${bestItem[3]})`,
          value: bestItem[2],
          coord: bestItem.slice(0, 2),
        },
      ],
      itemStyle: {
        color: '#0EBF9C',
      },
    },
  }]
  const options = {
    grid: {
      right: 80,
      left: 40,
    },
    tooltip: {
      padding: 10,
      // backgroundColor: '#222',
      // borderColor: '#777',
      borderWidth: 1,
      axisPointer: {
        type: 'cross',
      },
      formatter: function (obj) {
        const value = obj.value
        if (!value) {
          return ''
        }
        if (obj.componentType === 'markPoint') {
          return `${obj.name}：${value}%`
        }
        return '<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'
            + value[3]
            + '</div>'
            + '行业超额：' + value[0] + '%<br>'
            + '主动比例：' + value[1] + '%<br>'
            + '主动收益：' + value[2] + '%<br>'
      },
    },
    xAxis: {
      type: 'value',
      name: '行业超额',
      axisLabel: {
        formatter: '{value} %',
      },
    },
    yAxis: {
      type: 'value',
      name: '主动比例',
      axisLabel: {
        formatter: '{value} %',
      },
      axisPointer: {
        snap: true,
      },
    },
    visualMap: [{
      right: '0%',
      orient: 'horizontal',
      top: '0%',
      dimension: 2,
      min: _.min(exReturns) || 0,
      max: _.max(exReturns) || 0,
      itemWidth: 10,
      itemHeight: 40,
      calculable: true,
      precision: 0.01,
      text: ['主动收益'],
      textGap: 30,
      textStyle: {
        color: '#fff',
      },
      inRange: {
        symbolSize: [15, 60],
      },
      outOfRange: {
        symbolSize: [15, 60],
        color: ['rgba(255,255,255,.2)'],
      },
      controller: {
        inRange: {
          color: ['#0EBF9C'],
        },
        outOfRange: {
          color: ['#444'],
        },
      },
    }],
    series,
  }
  return (
    <Echart style={{ height: 480 }} options={options}/>
  )
}

const renderIndustryDetailTable = (tableData) => {
  return (
    <Table
      className={styles.xsTable}
      pagination={false}
      size="small"
      scroll={{ y: 500 }}
      {...tableData}
    />
  )
}

const renderIndustryDetail = (data) => {
  const tableData = getBrisonDetailTableData(data)
  return (
    <Card
      title={
        <span>
          收益分布
          <Tooltip title="横轴行业超额，纵轴主动比例，气泡大小代表主动收益" placement="right">
            <span style={{ marginLeft: '5px' }}>
              <QuestionCircleOutlined />
            </span>
          </Tooltip>
        </span>
      }
      bordered={false}
      extra={<ExportData {...tableData} filename={getExportFilename('收益详细信息')}/>}
    >
      <Row gutter={10} className={styles.fixHeightWapper}>
        <Col md={16} sm={24}>
          {renderIndustryDetailChart(data)}
        </Col>
        <Col md={8} sm={24}>
          {renderIndustryDetailTable(tableData)}
        </Col>
      </Row>
    </Card>
  )
}

const renderMultiAssetAttrResult = ({
  loading, data,
  loadingDetail, detailData,
  assetType,
}) => {
  const tableData = getBrisonDetailTableData(detailData)
  return (
    <>
      <Spin spinning={loading}>
        <div style={{ minHeight: 300 }}>
          {renderAttrSeries(data, assetType)}
        </div>
      </Spin>
      <Spin spinning={loadingDetail}>
        <Row gutter={10} className={styles.fixHeightWapper}>
          <Col md={8} sm={24}>
            <div style={{ minHeight: 300 }}>
              {renderAttrDetail(detailData, assetType)}
            </div>
            {renderIndustryDetailTable(tableData)}
          </Col>
          <Col md={16} sm={24}>
            <Card
              title={
                <span>
                  收益分布
                  <Tooltip title="横轴行业超额，纵轴主动比例，气泡大小代表主动收益" placement="right">
                    <span style={{ marginLeft: '5px' }}>
                      <QuestionCircleOutlined />
                    </span>
                  </Tooltip>
                </span>
              }
              bordered={false}
              extra={<ExportData {...tableData} filename={getExportFilename('收益详细信息')}/>}
            >
              {renderIndustryDetailChart(detailData)}
            </Card>
          </Col>
        </Row>
      </Spin>
    </>
  )
}

const renderStockAttrResult = ({
  loading, data,
  loadingDetail, detailData,
  loadingSelectDetail, selectDetailData,
  assetType,
}) => {
  return (
    <>
      <Spin spinning={loading}>
        <div style={{ minHeight: 300 }}>
          {renderAttrSeries(data)}
        </div>
      </Spin>
      <Spin spinning={loadingDetail}>
        <div style={{ minHeight: 300 }}>
          {renderAttrDetail(detailData)}
        </div>
      </Spin>
      <Spin spinning={loadingSelectDetail}>
        <div style={{ minHeight: 300 }}>
          {renderStockReturn(selectDetailData.filter(item => {
            return !['可转债', '债券', '其他'].includes(item.INDUSTRY)
          }))}
        </div>
      </Spin>
      <Spin spinning={loadingDetail}>
        <div style={{ minHeight: 300 }}>
          {renderIndustryDetail(detailData)}
        </div>
      </Spin>
      {['stock', 'convtbond'].includes(assetType) &&
      <Spin spinning={loadingSelectDetail}>
        <div style={{ minHeight: 300 }}>
          {renderReturnDetailTable(selectDetailData, detailData)}
        </div>
      </Spin>}
    </>
  )
}

const TopDown = ({
  currentFund,
  location: { pathname },
}) => {
  const id = currentFund._id
  const isDetect = location.pathname.includes('detection_stock_attr')
  if (currentFund._syncType !== 'mutual' && !window.__isTradingNetwork) {
    router.push('/404')
    return null
  }
  global.currentFund = currentFund
  const navStartDate = currentFund.ref_fund_start_date_ts || currentFund.navStartDate
  const navEndDate = currentFund.ref_fund_end_date_ts || currentFund.navEndDate
  const dates = currentFund.dates
  let assetType = pathname.split('/').pop().split('_')[0]
  if (isDetect) {
    assetType = 'stock'
  }
  let attributionDate = {}
  const activeKey = location.pathname.split('/')[1]
  const activeTab = pathname.split('/').pop()
  try {
    const cachedDates = localStorage.getItem(`atrributionDate:${activeKey}`)
    if (cachedDates) {
      attributionDate = JSON.parse(cachedDates)
    }
  } catch (error) {
  }
  let benchmarkList = [{
    label: '宽基指数',
    options: [{
      label: '沪深300',
      value: '000300.SH',
    }, {
      label: '中证500',
      value: '000905.SH',
    }, {
      label: '中证800',
      value: '000906.SH',
    }]
  }, {
    label: '风格指数',
    options: [{
      label: '中证红利',
      value: '000922.CSI',
    }, {
      label: '国证成长',
      value: 'CN2370'
    }]
  }, {
    label: '行业指数',
    options: swBenchmarkList,
  }]
  if (isDetect || currentFund.isPortfolio) {
    benchmarkList = [{
      label: '沪深300',
      value: '000300.SH',
    }, {
      label: '中证500',
      value: '000905.SH',
    }, {
      label: '中证800',
      value: '000906.SH',
    }]
  }
  if (assetType === 'convtbond') {
    benchmarkList = [{
      label: '上证转债',
      value: '000139.SH',
    }, {
      label: '中证转债',
      value: '000832.CSI',
    }]
  } else if (assetType === 'multiasset') {
    benchmarkList = [{
      label: '99%股+1%债',
      value: 'S99B1.CST',
    }, {
      label: '80%股+20%债',
      value: 'S80B20.CST',
    }, {
      label: '60%股+40%债',
      value: 'S60B40.CST',
    }, {
      label: '40%股+60%债',
      value: 'S40B60.CST',
    }, {
      label: '20%股+80%债',
      value: 'S20B80.CST',
    }, {
      label: '10%股+90%债',
      value: 'S10B90.CST',
    }, {
      label: '5%股+95%债',
      value: 'S5B95.CST',
    }, {
      label: '1%股+99%债',
      value: 'S1B99.CST',
    }, {
      label: '80%债+20%可转',
      value: 'B80CB20.CST',
    }, {
      label: '85%债+15%可转',
      value: 'B85CB15.CST',
    }]
  }
  const startDate = moment(attributionDate.startDate || dates[0] || navStartDate)
  const endDate = moment(attributionDate.endDate || dates[dates.length - 1] || navEndDate)
  // const initialStartDate = moment(dates[0] || navStartDate)
  // const initialEndDate = moment(dates[dates.length - 1] || navEndDate)
  const initialStartDate = moment(navStartDate)
  const initialEndDate = moment(navEndDate)
  const initialBmk = benchmarkList[0].options ? benchmarkList[0].options[0].value : benchmarkList[0].value
  const [benchmark, setBenchmark] = useState(initialBmk)
  const initialDates = [
    startDate,
    endDate,
  ]
  global.dateRange = initialDates
  const [dateRange, setDateRange] = useState(initialDates)
  const handleDateRangeChange = (dates: any) => {
    if (!dates || !dates.length) {
      return
    }
    global.dateRange = dates
    setDateRange(dates)
  }
  const requestParams = {
    assetType: isDetect ? 'stock_det' : assetType,
    benchmark,
    startDate: dateRange[0] && dateRange[0].format('YYYY-MM-DD'),
    endDate: dateRange[1] && dateRange[1].format('YYYY-MM-DD'),
    isDetect: isDetect ? 'Y' : 'N',
  }
  const { data = [], loading } = useRequest(() => {
    localStorage.setItem(`atrributionDate:${activeKey}`, JSON.stringify({
      startDate: requestParams.startDate,
      endDate: requestParams.endDate,
    }))
    return getTopDownAttrSeries(id, requestParams)
  }, {
    refreshDeps: [dateRange, benchmark],
  })
  const { data: detailData = [], loading: loadingDetail } = useRequest(() => {
    return getTopDownAttrDetail(id, requestParams)
  }, {
    refreshDeps: [dateRange, benchmark],
  })
  const { data: selectDetailData = [], loading: loadingSelectDetail } = useRequest(() => {
    return getTopDownSelectDetail(id, requestParams)
  }, {
    refreshDeps: [dateRange, benchmark],
  })
  const attrMenuData = [{
    name: '多资产归因',
    value: 'multiasset_attribution',
  }, {
    name: '权益归因',
    value: 'stock_attribution',
  }, {
    name: '债券归因',
    value: 'bond_attribution',
  }, {
    name: '可转债归因',
    value: 'convtbond_attribution',
  }, {
    name: '绝对贡献',
    value: 'absolute_attribution',
  }]
  if (!currentFund.isPortfolio) {
    attrMenuData.push({
      name: '风格归因',
      value: 'style_attribution',
    })
  } else {
    attrMenuData.shift()
  }
  const handleTabChange = currentTab => {
    const splits = pathname.split('/')
    const pathPrefix = splits.slice(0, -1).join('/')
    router.push(`${pathPrefix}/${currentTab}`)
  }

  const tabBarExtraContent = (
    <Space>
      <div>
        <span style={{ marginRight: '10px' }}>基准选择:</span>
        <Select
          showSearch
          size="small"
          value={benchmark}
          onChange={setBenchmark}
          style={{ width: assetType === 'multiasset' ? 180 : 130 }}
          filterOption={(input, option) =>
            option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
          options={benchmarkList}
        />
      </div>
      <div>
        <span style={{ marginRight: 15 }}>
          日期范围：<RangePicker
            style={{ width: 220 }}
            size="small"
            ranges={{
              本周以来: [initialEndDate.clone().startOf('week'), initialEndDate],
              本月以来: [initialEndDate.clone().startOf('month'), initialEndDate],
              本年以来: [initialEndDate.clone().startOf('year'), initialEndDate],
            }}
            onChange={handleDateRangeChange}
            disabledDate={(current) => {
              const currentTs = +current.startOf('date')
              return currentTs < initialStartDate || currentTs > initialEndDate
            }}
            value={dateRange}
          />
        </span>
      </div>
    </Space>
  )
  return (
    <>
      {!isDetect &&
      <Affix offsetTop={44}>
        <Tabs
          style={{
            background: 'rgb(24, 31, 41)',
          }}
          onChange={handleTabChange}
          activeKey={activeTab}
          tabBarExtraContent={
            tabBarExtraContent
          }
        >
          {attrMenuData.map(tab => (
            <TabPane tab={tab.name} key={tab.value}>
            </TabPane>
          ))}
        </Tabs>
      </Affix>}
      {isDetect &&
       <NavTabs
        location={location}
        tabBarExtraContent={tabBarExtraContent}
      />
      }
      <AnalyzeRangeAlert startDate={requestParams.startDate} endDate={requestParams.endDate} loading={loadingDetail} data={detailData}/>
      {assetType !== 'multiasset' && renderStockAttrResult({
        loading, data,
        loadingDetail, detailData,
        loadingSelectDetail, selectDetailData,
        assetType,
      })}
      {assetType === 'multiasset' && renderMultiAssetAttrResult({
        loading, data,
        loadingDetail, detailData,
        assetType,
      })}
      <AlgDesc/>
    </>
  )
}

export default connect(
  ({
    fund,
  }: {
    fund: any;
  }) => ({
    currentFund: fund.currentFund,
  }),
)(TopDown)
