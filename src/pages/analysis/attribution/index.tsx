import React, { Component } from 'react'
import moment, { Moment } from 'moment'
import { Dispatch } from 'redux'
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Card, Row, Col, Select, Table, DatePicker, Spin, Empty, Tooltip, Button, Tabs, Space, Radio, Divider } from 'antd';
import _ from 'lodash'
import { connect } from 'dva'
import router from 'umi/router'
import Chart from '@/components/Chart/Chart'
import Echart from '@/components/Chart/Echarts'
import sortQuotaFn from '@/utils/sortQuotaFn'
import styles from './style.less'
import { StateType } from './model'
import theme from '@/components/Chart/theme'
import buildTreeData from '@/utils/buildTreeDataWithSummary'
import ExportData from '@/components/ExportData'
import BestTicketSelect from './components/BestTickerSelect'
import AlgDesc from './components/AlgDesc'
import AnalyzeRangeAlert from './components/AnalyzeRangeAlert'

const { Option } = Select
const { TabPane } = Tabs

interface AttributionProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  loadingStockReturn: boolean;
  analyzeData: StateType;
  computedMatch: any;
  currentFund: any;
  location: any;
}

@connect(
  ({
    fund,
    attribution,
    loading,
    computedMatch,
  }: {
    fund: any;
    attribution: StateType;
    computedMatch: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    computedMatch,
    analyzeData: attribution,
    loading: !!(
      loading.effects['attribution/fetch'] ||
      loading.effects['attribution/fetchSeries'] ||
      loading.effects['attribution/fetchReturnDetail'] ||
      loading.effects['attribution/fetchReturnSeries']
    ),
    loadingStockReturn: loading.effects['attribution/fetchStockReturns'],
  }),
)
class Attribution extends Component<AttributionProps> {
  constructor(props: AttributionProps) {
    super(props)
    const { currentFund, location } = props
    const dates = currentFund.positionDates
    const attrType = location.pathname.split('/').pop()
    let model = 'brinson'
    if (attrType === 'bond_attribution') {
      model = 'campisi'
    } else if (attrType === 'absolute_attribution') {
      model = 'return'
    }
    // const isBond = currentFund.name.includes('固收') || currentFund.fundNature === '债券型'
    let attributionDate = {}
    try {
      const activeKey = location.pathname.split('/')[1]
      const cachedDates = localStorage.getItem(`atrributionDate:${activeKey}`)
      if (cachedDates) {
        attributionDate = JSON.parse(cachedDates)
      }
    } catch (error) {
    }
    this.state = {
      model,
      benchmark: '沪深300',
      returnViewType: 'industry',
      startDate: moment(attributionDate.startDate || dates[0]),
      endDate: moment(attributionDate.endDate || dates[dates.length - 1]),
      initialStartDate: moment(dates[0]),
      initialEndDate: moment(dates[dates.length - 1]),
      seriesType: 'history',
    }
  }

  componentDidMount() {
    this.runAnalyze()
  }

  isEmptyData = () => {
    const { model, returnViewType } = this.state
    const {
      analyzeData: {
        brinsonSeriesData,
        brinsonDetailData,
        campisiSeriesData,
        campisiDetailData,
        returnDetailData,
        returnSeriesData,
        childFundReturnData,
      },
    } = this.props
    if (model === 'brinson') {
      return !brinsonSeriesData.length && !brinsonDetailData.length
    } else if (model === 'campisi') {
      return !campisiSeriesData.length && !campisiDetailData.length
    } else if (model === 'return') {
      if (returnViewType === 'childFund') {
        return !childFundReturnData.length
      }
      return !returnDetailData.length && !returnSeriesData.length
    }
    return true
  };

  runAnalyze = () => {
    const { model, benchmark, startDate, endDate, returnViewType } = this.state
    const { dispatch, currentFund, location } = this.props
    const params = {
      model,
      startDate: startDate.format('YYYY-MM-DD'),
      endDate: endDate.format('YYYY-MM-DD'),
    }
    const activeKey = location.pathname.split('/')[1]
    localStorage.setItem(`atrributionDate:${activeKey}`, JSON.stringify({
      startDate: params.startDate,
      endDate: params.endDate,
    }))
    if (model === 'brinson') {
      params.benchmark = benchmark
    }
    const fundId = currentFund._id
    if (model === 'return') {
      if (returnViewType === 'childFund') {
        dispatch({
          type: 'attribution/fetchChildFundReturnDetail',
          payload: { fundId, params },
        })
      } else {
        dispatch({
          type: 'attribution/fetchReturnDetail',
          payload: { fundId, params },
        })
        dispatch({
          type: 'attribution/fetchReturnSeries',
          payload: { fundId, params },
        })
      }
    } else {
      dispatch({
        type: 'attribution/fetch',
        payload: { fundId, params },
      })
      dispatch({
        type: 'attribution/fetchSeries',
        payload: { fundId, params },
      })
      if (model === 'campisi') {
        dispatch({
          type: 'attribution/fetchSeries',
          payload: {
            fundId,
            params: {
              ...params,
              model: 'campisidaily',
            },
          },
        })
      }
    }
    if (model === 'brinson') {
      dispatch({
        type: 'attribution/fetchStockReturns',
        payload: { fundId, params },
      })
    }
  };

  handleModelChange = (model: string) => {
    this.setState({ model }, this.runAnalyze)
  };

  handleBenhcmarkChange = (benchmark: string) => {
    this.setState({ benchmark }, this.runAnalyze)
  };

  handleReturnViewTypeChange = (returnViewType: string) => {
    this.setState({ returnViewType }, this.runAnalyze)
  };

  handleStartDateChange = (startDate: Moment | null) => {
    if (startDate) {
      this.setState({ startDate })
    }
  };

  handleEndDateChange = (endDate: Moment | null) => {
    if (endDate) {
      this.setState({ endDate })
    }
  };

  handleSeriesTypeChange = (seriesType: string) => {
    this.setState({ seriesType })
  }

  disabledStartDate = (startDate: Moment): Boolean => {
    const { endDate, initialStartDate } = this.state
    if (!startDate || !endDate) {
      return false
    }
    return (
      startDate.valueOf() > endDate.valueOf() || startDate.valueOf() < initialStartDate.valueOf()
    )
  };

  disabledEndDate = (endDate: Moment): Boolean => {
    const { startDate, initialEndDate } = this.state
    if (!endDate || !startDate) {
      return false
    }
    return endDate.valueOf() <= startDate.valueOf() || endDate.valueOf() > initialEndDate.valueOf()
  };

  renderReturnValue = (value: number, noColor) => {
    if (!value && value !== 0) {
      return '-'
    }
    let className = value > 0 ? 'colorUp' : 'colorDown'
    if (noColor) {
      className = ''
    }
    return <span className={className}>{(value * 100).toFixed(2) + '%'}</span>
  };

  renderPercentageValue = (value: number) => {
    if (!value && value !== 0) {
      return '-'
    }
    return <span>{(value * 100).toFixed(2) + '%'}</span>
  };

  renderTableNumericColumn = (val: number) => (
    <div style={{ textAlign: 'right' }}>{this.renderReturnValue(val)}</div>
  );

  renderSummaryTable = (data: any) => {
    const columns = [
      {
        title: 'Name',
        dataIndex: 'name',
        render: (val: string, record: any) => {
          return (
            <div style={{ paddingLeft: record.paddingLeft || 0 }}>
              <span>{val}</span>
              {record.tooltip && (
                <Tooltip title={record.tooltip} placement="right">
                  <span style={{ marginLeft: '5px' }}>
                    <QuestionCircleOutlined />
                  </span>
                </Tooltip>
              )}
            </div>
          );
        },
      },
      {
        title: 'value',
        dataIndex: 'value',
        render: this.renderTableNumericColumn,
      },
    ]
    return (
      <Table
        className={styles.tableWrapper}
        size="small"
        showHeader={false}
        pagination={false}
        columns={columns}
        dataSource={data}
      />
    )
  };

  renderBrinsonSummary = () => {
    const {
      analyzeData: { brinsonDetailData },
    } = this.props
    if (!brinsonDetailData || !brinsonDetailData.length) {
      return false
    }
    const industryData = brinsonDetailData.filter(item => item.INDUSTRY !== 'OTHER')
    const otherData = brinsonDetailData.find(item => item.INDUSTRY === 'OTHER') || {}
    const latestData = {
      IR: _.sumBy(industryData, 'AR'),
      SR: _.sumBy(industryData, 'SR'),
      OAR: otherData.AR,
      TR: otherData.SR,
      PR: otherData.PPR,
      BR: otherData.PBR,
    }
    latestData.SER = latestData.IR + latestData.SR
    latestData.ER = latestData.PR - latestData.BR
    latestData.TR = latestData.ER - latestData.SER - latestData.OAR
    const data = [
      {
        name: '超额收益',
        value: latestData.ER,
      },
      {
        name: '股票',
        value: latestData.SER,
        paddingLeft: 30,
      },
      {
        name: '行业配置',
        value: latestData.IR,
        paddingLeft: 60,
      },
      {
        name: '个股选择',
        value: latestData.SR,
        paddingLeft: 60,
        tooltip: '包含交互收益',
      },
      {
        name: '非股资产配置',
        value: latestData.OAR,
        paddingLeft: 30,
        tooltip: '非股资产配置把非股类资产当做一个行业，计算该行业的行业配置贡献，其计算公式为：非股类资产配置贡献=(基金非股配置比例-基准非股配置比例)*(非股行业收益-基准收益)，其中非股行业收益取 0。',
      },
      {
        name: '换仓行为',
        value: latestData.TR,
        paddingLeft: 30,
        tooltip: '换仓行为贡献衡量截面估值比无法观察到的个券交易产生的收益贡献，其计算公式为：换仓行为贡献=超额收益-行业配置贡献-个股选择贡献-非股资产配置贡献。',
      },
    ]
    return (
      <Card title="组合超额收益分解" bordered={false}>
        <Card.Grid style={{ width: '50%' }}>
          <div className={styles.statistic}>
            <div className={styles.value}>{this.renderReturnValue(latestData.PR)}</div>
            <div className={styles.text}>组合收益</div>
          </div>
        </Card.Grid>
        <Card.Grid style={{ width: '50%' }}>
          <div className={styles.statistic}>
            <div className={styles.value}>{this.renderReturnValue(latestData.BR)}</div>
            <div className={styles.text}>基准收益</div>
          </div>
        </Card.Grid>
        {this.renderSummaryTable(data)}
      </Card>
    )
  };

  renderCampisiSummary = () => {
    const {
      analyzeData: { campisiSeriesData },
    } = this.props
    const latestData = campisiSeriesData[campisiSeriesData.length - 1]
    if (!latestData) {
      return false
    }
    const data = [
      {
        name: '收入效应',
        value: latestData.收入效应,
      },
      {
        name: '票息效应',
        value: latestData.票息效应,
        paddingLeft: 30,
      },
      {
        name: '价格收敛效应',
        value: latestData.价格收敛效应,
        paddingLeft: 30,
      },
      {
        name: '国债效应',
        value: latestData.国债效应,
      },
      {
        name: '利差效应',
        value: latestData.利差效应,
      },
    ]
    return (
      <Card title="债券收益率分解">
        <Card.Grid style={{ width: '100%' }}>
          <div className={styles.statistic}>
            <div className={styles.value}>{this.renderReturnValue(latestData.纯债收益)}</div>
            <div className={styles.text}>债券收益</div>
          </div>
        </Card.Grid>
        {this.renderSummaryTable(data)}
      </Card>
    )
  };

  buildHeatMapData(rawData, returnViewType) {
    function buildParent(filterKey, children) {
      const arr = filterKey.split('_')
      const temp = children || []
      const val = _.sum(temp.map(item => item.value[0])) || 0
      const realVal = _.sum(temp.map(item => item.value[1])) || 0
      const weight = _.sum(temp.map(item => item.weight)) || 0
      const ret = {
        id: filterKey,
        name: filterKey.split('_').pop(),
        value: [val, realVal],
        weight,
      }
      if (arr.length !== 1) {
        ret.parent = arr.slice(0, -1).join('_')
      } else {
        ret.isRoot = true
      }
      return ret
    }
    function iter(list, keys, resultKey) {
      let parent
      if (keys.length === 0) {
        const children = list.map((item, index) => {
          const realVal = item['收益率贡献'] * 100
          const val = Math.abs(realVal)
          const result = {
            id: `${resultKey}_${index}`,
            parent: resultKey,
            name: item.STOCK_NAME,
            value: [val, realVal],
            note: `${_.round(realVal, 2)}%`,
            isDetail: true,
            weight: item.WEIGHT,
          }
          return result
        })
        parent = buildParent(resultKey, children)
        parent.children = children.sort((fst, snd) => snd.value[1] - fst.value[1])
        return parent
      }
      const result = _.map(_.groupBy(list, keys[0]), (values, dataKey) =>
        iter(
          values,
          keys.slice(1),
          resultKey ? `${resultKey}_${dataKey}` : dataKey
        )
      )
      parent = buildParent(resultKey, result)
      parent.children = result.sort((fst, snd) => snd.value[1] - fst.value[1])
      const hasOneNull = result.length === 1 && result[0].name === 'null'
      if (hasOneNull) {
        parent.children = result[0].children
      }
      return parent
    }

    const groupKeysMap = {
      股票: ['行业板块', '股票行业'],
      债券: ['CREDIT_TYPE', '债券二级分类'],
      期货: ['期货类型', '期货品种'],
      基金: ['基金类型'],
      可转债: ['行业板块', '股票行业'],
      其他: [],
    }
    if (returnViewType === 'style') {
      groupKeysMap['股票'] = ['股票风格']
    } else if (returnViewType === 'mktSize') {
      groupKeysMap['股票'] = ['股票市值']
    }
    const assetTypes = ['股票', '债券', '期货', '基金', '可转债', '其他']
    const result = _.map(_.groupBy(rawData, '资产类型'), (values, key) => {
      const children = iter(values, groupKeysMap[key], key).children
      const parent = buildParent(key, children)
      if (groupKeysMap[key].length) {
        parent.children = children
      } else {
        parent.note = `${_.round(parent.value[1], 2)}%`
      }
      return parent
    })
    return result.sort((fst, snd) => {
      return assetTypes.indexOf(fst.name) - assetTypes.indexOf(snd.name)
    })
  }

  renderReturnDetailChart = () => {
    const { returnViewType } = this.state
    const {
      analyzeData: { returnDetailData, childFundReturnData },
    } = this.props
    let data
    if (returnViewType === 'childFund') {
      data = childFundReturnData.map(item => {
        return {
          id: item.name,
          name: item.name,
          value: [item.ret * 100, item.ret * 100],
        }
      })
    } else {
      data = this.buildHeatMapData(returnDetailData, returnViewType)
    }
    const totalRet = _.sum(data.map(item => item.value[1]))
    const formatUtil = Echart.echarts.format
    const levelOption = [
      {
        itemStyle: {
          normal: {
            borderColor: '#777',
            borderWidth: 0,
            gapWidth: 1,
          },
        },
        upperLabel: {
          normal: {
            show: false,
          },
        },
        colorMappingBy: 'value',
        visualDimension: 1,
        color: ['#0EBF9C', '#e49732', '#e85654'],
      },
      {
        itemStyle: {
          normal: {
            borderColor: '#252b35',
            borderWidth: 3,
            gapWidth: 1,
          },
          emphasis: {
            borderColor: '#ddd',
          },
        },
        colorMappingBy: 'value',
        visualDimension: 1,
        color: ['#0EBF9C', '#e49732', '#e85654'],
      },
      {
        itemStyle: {
          normal: {
            borderColor: '#252b35',
            borderWidth: 3,
            gapWidth: 1,
          },
          emphasis: {
            borderColor: '#ddd',
          },
        },
        colorMappingBy: 'value',
        visualDimension: 1,
        // color: ['#4caf50', '#fff', '#f44336'],
        color: ['#0EBF9C', '#e49732', '#e85654'],
      },
      {
        itemStyle: {
          normal: {
            borderColor: '#252b35',
            borderWidth: 0,
            gapWidth: 0,
          },
          emphasis: {
            borderColor: '#ddd',
          },
        },
      },
    ]
    const options = {
      backgroundColor: '#252b35',
      grid: {
        backgroundColor: '#252b35',
      },
      tooltip: {
        formatter: function (info) {
          const value = info.value[1] || 0
          const treePathInfo = info.treePathInfo
          const treePath = []
          for (let i = 1; i < treePathInfo.length; i++) {
            treePath.push(treePathInfo[i].name)
          }
          return [
            '<div class="tooltip-title">' + formatUtil.encodeHTML(treePath.join('/')) + '</div>',
            '收益率: ' + value.toFixed(2) + '%',
          ].join('')
        },
      },
      series: [{
        name: '组合收益',
        type: 'treemap',
        width: '100%',
        height: '100%',
        visibleMin: 300,
        // visualMin: -2,
        // visualMax: 2,
        leafDepth: 2,
        colorMappingBy: 'value',
        visualDimension: 1,
        color: ['#0EBF9C', '#e49732', '#e85654'],
        label: {
          normal: {
            show: true,
            color: '#252b35',
            formatter: function (info) {
              const { value } = info
              return [info.name, `${_.round(value[1], 2)}%`].filter(Boolean).join('\n\n')
            },
          },
        },
        upperLabel: {
          show: true,
          height: 30,
          formatter: param => `${param.name}: ${_.round(param.value[1], 2)}%`,
        },
        itemStyle: {
          normal: {
            borderColor: '#fff',
            backgroundColor: '#252b35',
          },
          borderColor: '#252b35',
        },
        levels: levelOption,
        data: data,
      }],
    }
    const columns = returnViewType === 'childFund' ? [{
      title: '收益详情',
      dataIndex: 'name',
    }, {
      title: '收益率',
      dataIndex: 'value',
      align: 'right',
      render: (val: any) => (
        <div>{this.renderReturnValue(val[1] / 100)}</div>
      ),
    }] : [{
      title: '收益详情',
      dataIndex: 'name',
    }, {
      title: '投资比例',
      dataIndex: 'weight',
      align: 'right',
      render: (val: any) => (
        <div>{this.renderReturnValue(val, true)}</div>
      ),
      width: 70,
    }, {
      title: '收益率',
      dataIndex: 'value',
      align: 'right',
      render: (val: any) => (
        <div>{this.renderReturnValue(val[1] / 100)}</div>
      ),
      width: 70,
    }]
    const exportDataSource = returnViewType === 'childFund' ? childFundReturnData : returnDetailData
    const exportColumns = returnViewType === 'childFund' ?
    [{
      title: '子基金',
      dataIndex: 'name',
    }, {
      title: '收益率贡献',
      dataIndex: 'ret',
    }] :
    [{
      title: '资产类型',
      dataIndex: '资产类型',
    }, {
      title: '资产名称',
      dataIndex: 'STOCK_NAME',
    }, {
      title: '资产代码',
      dataIndex: 'STOCK_CODE',
    }, {
      title: '投资比例',
      dataIndex: 'WEIGHT',
    }, {
      title: '收益率贡献',
      dataIndex: '收益率贡献',
    }]

    return (
      <Card
        title={
          <div>
            <Space>
              <span>收益详细信息</span>
              <span>（收益合计：{this.renderReturnValue(totalRet / 100)}）</span>
            </Space>
          </div>
        }
        bordered={false}
        extra={<ExportData columns={exportColumns} dataSource={exportDataSource} filename={this.getExportFilename('收益详细信息')}/>}
      >
        <Row gutter={16}>
          <Col lg={16} md={24}>
            <Echart options={options} style={{ height: 400 }} />
          </Col>
          <Col lg={8} md={24}>
            <Table
              columns={columns}
              dataSource={data}
              size="small"
              rowKey="id"
              scroll={{ y: 450 }}
              pagination={false}
              // summary={pageData => {
              //   const total = _.sum(pageData.map(item => item.value[1] / 100))
              //   return (
              //     <>
              //       <Table.Summary.Row>
              //         <Table.Summary.Cell colSpan={1}>合计</Table.Summary.Cell>
              //         <Table.Summary.Cell>
              //           <div style={{ textAlign: 'right' }}>
              //             {this.renderReturnValue(total)}
              //           </div>
              //         </Table.Summary.Cell>
              //       </Table.Summary.Row>
              //     </>
              //   )
              // }}
            />
          </Col>
        </Row>
      </Card>
    )
  }

  renderReturnSummary = () => {
    const {
      analyzeData: { returnSeriesData },
    } = this.props
    const latestData = returnSeriesData[returnSeriesData.length - 1]
    if (!latestData) {
      return false
    }
    const data = [
      {
        name: '股票收益',
        value: latestData.STOCK,
      },
      {
        name: '债券收益',
        value: latestData.BOND,
      },
      {
        name: '可转债收益',
        value: latestData.CONVTBOND,
      },
      {
        name: '基金收益',
        value: latestData.FUND,
      },
      {
        name: '期货收益',
        value: latestData.FUTURE,
      },
      {
        name: '其他收益',
        value: latestData.OTHER,
      },
    ]
    return (
      <Card title="组合收益率分解">
        <Card.Grid style={{ width: '100%' }}>
          <div className={styles.statistic}>
            <div className={styles.value}>{this.renderReturnValue(latestData.TOTAL)}</div>
            <div className={styles.text}>组合收益</div>
          </div>
        </Card.Grid>
        {this.renderSummaryTable(data)}
      </Card>
    )
  };

  renderSeriesChart = (quotas: any, title: string, chartType?: string, origData?: any, chartConfig?: any) => {
    const { model } = this.state
    const quota0 = quotas[0]
    if (!quota0.data.length) {
      return false
    }
    const afterChartCreated = (refChart) => {
      const data = quota0.data
      if (refChart && data.length > 60 && model === 'brinson') {
        const latest100Data = data.slice(-60)
        refChart.xAxis[0].setExtremes(latest100Data[0][0], latest100Data[latest100Data.length - 1][0])
      }
    }
    const columns = quotas.map(item => {
      return {
        title: item.name,
        dataIndex: item.value,
      }
    })
    columns.unshift({
      title: '日期',
      dataIndex: 'END_DATE',
    })
    const options = {
      chart: {
        type: quota0.data.length === 1 ? 'column' : (chartType || 'area'),
      },
      scrollbar: {
        enabled: false,
      },
      navigator: {
        enabled: quota0.data.length > 100,
      },
      plotOptions: {
        column: {
          stacking: 'normal',
        },
      },
      yAxis: [{}, { opposite: true }],
      series: quotas.map((quota, index) => {
        return {
          visible: quota.visible,
          name: quota.name,
          data: quota.data,
          fillOpacity: 1 - 0.1 * index,
          ...(quota.chartConfig || {}),
        }
      }),
      ...chartConfig,
    }
    return (
      <Card
        title={title}
        bordered={false}
        extra={
          <Space>
            {model === 'campisi' &&
            <Radio.Group defaultValue="history" size="small" onChange={(event) => this.handleSeriesTypeChange(event.target.value)}>
              <Radio.Button value="history">累计收益</Radio.Button>
              <Radio.Button value="daily">截面收益</Radio.Button>
            </Radio.Group>}
            <ExportData columns={columns} dataSource={origData} filename={this.getExportFilename(title)}/>
          </Space>
        }
      >
        <Chart options={options} constructorType="stockChart" afterChartCreated={afterChartCreated} />
      </Card>
    )
  };

  renderBrinsonSeries = () => {
    const {
      analyzeData: { brinsonSeriesData },
    } = this.props
    const data = brinsonSeriesData
    const quotas = [
      {
        name: '行业配置',
        value: 'AR',
        visible: true,
        chartConfig: {
          type: 'column',
        },
      },
      {
        name: '个股选择',
        value: 'SR',
        visible: true,
        chartConfig: {
          type: 'column',
        },
      },
      {
        name: '超额',
        value: 'RETURN_EX',
        visible: true,
        chartConfig: {
          type: 'line',
          lineWidth: 0,
          states: {
            hover: {
              lineWidthPlus: 0,
            },
          },
          color: '#E49831',
          marker: {
            enabled: true,
            lineWidth: 4,
            symbol: 'circle',
            lineColor: '#9c27b0',
          },
        },
      },
      {
        name: '累计超额(右轴)',
        value: 'ACC_RETURN_EX',
        visible: true,
        chartConfig: {
          type: 'line',
          yAxis: 1,
        },
      },
    ].map(quota => {
      return {
        ...quota,
        data: data.map(item => [+moment(item.END_DATE), item[quota.value] * 100]),
      }
    })
    return this.renderSeriesChart(quotas, '超额收益历史变化情况', '', data)
  };

  renderCampisiSeries = () => {
    const {
      analyzeData: { campisiSeriesData, campisidailySeriesData },
    } = this.props
    const { seriesType } = this.state
    const data = seriesType === 'daily' ? campisidailySeriesData : campisiSeriesData
    let quotas = [
      {
        name: '收入效应',
        value: '收入效应',
        visible: true,
      },
      {
        name: '票息效应',
        value: '票息效应',
        visible: true,
      },
      {
        name: '价格收敛效应',
        value: '价格收敛效应',
        visible: true,
      },
      {
        name: '国债效应',
        value: '国债效应',
        visible: true,
      },
      {
        name: '利差效应',
        value: '利差效应',
        visible: true,
      },
    ].map(quota => {
      return {
        ...quota,
        data: data.map(item => [+moment(item.END_DATE), item[quota.value] * 100]),
      }
    })
    if (seriesType === 'daily') {
      quotas = quotas.slice(1)
    }
    const chartType = seriesType === 'daily' ? 'column' : 'line'
    return this.renderSeriesChart(quotas, '债券收益历史变化情况', chartType, data)
  };

  renderReturnSeries = () => {
    const {
      analyzeData: { returnSeriesData },
    } = this.props
    const data = returnSeriesData
    const quotas = [
      {
        name: '股票收益',
        value: 'STOCK',
        visible: true,
      },
      {
        name: '债券收益',
        value: 'BOND',
        visible: true,
      },
      {
        name: '可转债收益',
        value: 'CONVTBOND',
        visible: true,
      },
      {
        name: '基金收益',
        value: 'FUND',
        visible: true,
      },
      {
        name: '期货收益',
        value: 'FUTURE',
        visible: true,
      },
      {
        name: '组合净值',
        value: 'NAV',
        visible: true,
        chartConfig: {
          type: 'line',
          yAxis: 1,
          tooltip: {
            pointFormat: '<span style="color:{series.color}"></span>{series.name}: <b>{point.y:.2f}<br/>',
          },
        },
      },
    ].map(quota => {
      return {
        ...quota,
        data: data.map(item => [
          +moment(item.END_DATE),
          quota.value === 'NAV' ? item[quota.value] : item[quota.value] * 100,
        ]),
      }
    })
    const chartConfig = {
      yAxis: [{}, { opposite: true, labels: {
        format: '{value}',
      }}],
    }
    return this.renderSeriesChart(quotas, '组合收益历史变化情况', '', data, chartConfig)
  };

  getBrisonDetailTableData = () => {
    const {
      analyzeData: { brinsonDetailData },
    } = this.props
    const dataSource = brinsonDetailData
      .filter(item => item.INDUSTRY !== 'OTHER')
      .map(item => {
        item.SER = item.AR + item.SR
        return item
      })
    const columns = [
      {
        title: '行业',
        dataIndex: 'INDUSTRY',
        format: 'text',
      },
      {
        title: '行业配置',
        dataIndex: 'AR',
        align: 'right',
        format: 'percentage',
        render: this.renderTableNumericColumn,
      },
      {
        title: '个股选择',
        dataIndex: 'SR',
        align: 'right',
        format: 'percentage',
        render: this.renderTableNumericColumn,
      },
      {
        title: '股票超额收益',
        dataIndex: 'SER',
        align: 'right',
        format: 'percentage',
        render: this.renderTableNumericColumn,
      },
    ].map(item => {
      item.sorter = sortQuotaFn(item, 'asc')
      return item
    })
    const summaryItem = columns.reduce((out, item) => {
      const dataIndex = item.dataIndex
      if (dataIndex === 'INDUSTRY') {
        out.INDUSTRY = '合计'
      } else {
        out[dataIndex] = _.sum(dataSource.map(row => row[dataIndex]))
      }
      return out
    }, {})
    dataSource.unshift(summaryItem)
    return {
      columns, dataSource,
    }
  }

  renderBrinsonDetailChart = () => {
    const {
      analyzeData: { brinsonDetailData },
    } = this.props
    const data = brinsonDetailData.filter(item => item.INDUSTRY !== 'OTHER')
    const quotas = [
      {
        name: '行业配置',
        value: 'AR',
      },
      {
        name: '个股选择',
        value: 'SR',
      },
    ]
    const options = {
      chart: {
        type: 'column',
      },
      xAxis: {
        categories: data.map(item => item.INDUSTRY),
      },
      plotOptions: {
        column: {
          stacking: 'normal',
        },
      },
      series: quotas.map(quota => {
        return {
          name: quota.name,
          data: data.map(item => item[quota.value] * 100),
        }
      }),
    }
    const tableData = this.getBrisonDetailTableData()
    return (
      <Card
        title="收益详细信息"
        bordered={false}
        extra={<ExportData {...tableData} filename={this.getExportFilename('收益详细信息')}/>}
      >
        <Chart options={options} />
      </Card>
    )
  };

  getBrinsonDetailBubbleTableData = () => {
    const {
      analyzeData: { brinsonDetailData },
    } = this.props
    const columns = [{
      title: '行业',
      dataIndex: '0',
    }, {
      title: '行业超额',
      dataIndex: '1',
    }, {
      title: '主动比例',
      dataIndex: '2',
    }, {
      title: '主动收益',
      dataIndex: '3',
    }]
    const dataSource = brinsonDetailData.filter(item => item.INDUSTRY !== 'OTHER').map((item, index) => [
      item.INDUSTRY,
      _.round(item.AR * 100, 2),
      _.round(item.WEIGHT_EX * 100, 2),
      _.round((item.AR + item.SR) * 100, 2),
    ])
    return {
      columns, dataSource,
    }
  }

  renderBrinsonDetailBubbleChart = () => {
    const {
      analyzeData: { brinsonDetailData },
    } = this.props
    const itemStyle = {
      color: (params) => {
        return params.value ? params.value[4] : ''
      },
      opacity: 0.8,
      shadowBlur: 10,
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      shadowColor: 'rgba(0, 0, 0, 0.5)',
    }
    const itemData = brinsonDetailData.filter(item => item.INDUSTRY !== 'OTHER').map((item, index) => [
      _.round(item.AR * 100, 2),
      _.round(item.WEIGHT_EX * 100, 2),
      _.round((item.AR + item.SR) * 100, 2),
      item.INDUSTRY,
      theme.colors[index % theme.colors.length],
    ]).sort((fst, snd) => snd[2] - fst[2])
    const bestItem = itemData[0] || []
    const exReturns = itemData.map(item => item[2])
    const series = [{
      name: '行业',
      type: 'scatter',
      itemStyle: itemStyle,
      data: itemData,
      markPoint: {
        data: [
          {
            name: `最佳主动收益(${bestItem[3]})`,
            value: bestItem[2],
            coord: bestItem.slice(0, 2),
          },
        ],
        itemStyle: {
          color: '#0EBF9C',
        },
      },
    }]
    const options = {
      tooltip: {
        padding: 10,
        // backgroundColor: '#222',
        // borderColor: '#777',
        borderWidth: 1,
        axisPointer: {
          type: 'cross',
        },
        formatter: function (obj) {
          const value = obj.value
          if (!value) {
            return ''
          }
          if (obj.componentType === 'markPoint') {
            return `${obj.name}：${value}%`
          }
          return '<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'
              + value[3]
              + '</div>'
              + '行业超额：' + value[0] + '%<br>'
              + '主动比例：' + value[1] + '%<br>'
              + '主动收益：' + value[2] + '%<br>'
        },
      },
      xAxis: {
        type: 'value',
        name: '行业超额',
        axisLabel: {
          formatter: '{value} %',
        },
      },
      yAxis: {
        type: 'value',
        name: '主动比例',
        axisLabel: {
          formatter: '{value} %',
        },
        axisPointer: {
          snap: true,
        },
      },
      visualMap: [{
        right: '0%',
        orient: 'horizontal',
        top: '0%',
        dimension: 2,
        min: _.min(exReturns) || 0,
        max: _.max(exReturns) || 0,
        itemWidth: 10,
        itemHeight: 40,
        calculable: true,
        precision: 0.01,
        text: ['主动收益'],
        textGap: 30,
        textStyle: {
          color: '#fff',
        },
        inRange: {
          symbolSize: [15, 60],
        },
        outOfRange: {
          symbolSize: [15, 60],
          color: ['rgba(255,255,255,.2)'],
        },
        controller: {
          inRange: {
            color: ['#0EBF9C'],
          },
          outOfRange: {
            color: ['#444'],
          },
        },
      }],
      series,
    }
    return <Echart options={options}/>
  }

  renderStockReturnChart1 = (data1, data2) => {
    const series = [{
      name: '最佳选券',
      dataLabels: {
        enabled: true,
        format: '{point.rank}',
      },
      data: data1.map((item, index) => ({
        name: item.STOCK_NAME,
        x: (item.WEIGHT_P - (item.WEIGHT_B || 0)) * 100,
        y: (item.RETURN_P - (item.RETURN_B || 0)) * 100,
        z: 1,
        ret: item.RETURN_P * 100,
        rank: index + 1,
      })),
    }, {
      name: '最差选券',
      dataLabels: {
        enabled: true,
        format: '{point.rank}',
      },
      data: data2.map((item, index) => ({
        name: item.STOCK_NAME,
        x: (item.WEIGHT_P - (item.WEIGHT_B || 0)) * 100,
        y: (item.RETURN_P - (item.RETURN_B || 0)) * 100,
        z: 1,
        ret: item.RETURN_P * 100,
        rank: index + 1,
      })),
    }]
    const chartConfig = {
      chart: {
        type: 'bubble',
        zoomType: 'xy',
      },
      yAxis: {
        title: {
          text: '超额收益',
          align: 'high',
          offset: -10,
        },
        labels: {
          format: '{value}%',
        },
      },
      xAxis: {
        title: {
          text: '超额比例',
          align: 'high',
          offset: -10,
        },
        labels: {
          format: '{value}%',
        },
      },
      series: {
        dataLabels: {
          enabled: true,
          format: '{point.rank}',
        },
      },
      tooltip: {
        pointFormat:
          '{point.name}<br/>超额收益: <b>{point.y:,.2f}%</b><br/>超额比例: <b>{point.x:,.2f}%</b><br/>主动收益: <b>{point.ret:,.2f}%</b>',
      },
      series,
    }
    return <Chart options={chartConfig} />
  }

  renderStockReturnChart = (chartData) => {
    const itemStyle = {
      opacity: 0.8,
      shadowBlur: 10,
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      shadowColor: 'rgba(0, 0, 0, 0.5)',
    }
    const options = {
      color: [
        '#dd4444', '#fec42c', '#80F1BE',
      ],
      legend: {
        top: 2,
        right: 10,
        data: ['最佳选券', '最差选券'],
      },
      tooltip: {
        padding: 10,
        // backgroundColor: '#222',
        // borderColor: '#777',
        borderWidth: 1,
        axisPointer: {
          type: 'cross',
        },
        formatter: function (obj) {
          const value = obj.value
          if (!value) {
            return ''
          }
          if (obj.componentType === 'markPoint') {
            return `${obj.name}：${value}%`
          }
          return '<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'
              + obj.seriesName + ': ' + value[4]
              + '</div>'
              + '绝对收益：' + value[0] + '%<br>'
              + '主动比例：' + value[1] + '%<br>'
              + '主动收益：' + value[3] + '%<br>'
        },
      },
      xAxis: {
        type: 'value',
        name: '绝对收益',
        axisLabel: {
          formatter: '{value} %',
        },
      },
      yAxis: {
        type: 'value',
        name: '主动比例',
        axisLabel: {
          formatter: '{value} %',
        },
        axisPointer: {
          snap: true,
        },
      },
      series: chartData.map(item => {
        const itemData = item.data.map((item, index) => [
          _.round(item.RETURN_P * 100, 2),
          _.round((item.WEIGHT_P - (item.WEIGHT_B || 0)) * 100, 2),
          index + 1,
          _.round((item.RETURN_P - (item.RETURN_B || 0)) * 100, 2),
          item.STOCK_NAME,
        ])
        return {
          name: item.name,
          type: 'scatter',
          itemStyle: itemStyle,
          symbolSize: 35,
          data: itemData,
          markPoint: {
            data: [
              {
                name: item.name === '最佳选券' ? '最佳收益' : '最差收益',
                value: itemData[0] && itemData[0][0],
                coord: itemData[0] && itemData[0].slice(0, 2),
              },
            ],
            itemStyle: {
              color: '#0EBF9C',
            },
          },
          label: {
            show: true,
            formatter: '{@[2]}',
          },
        }
      }),
    }
    return <Echart options={options}/>
  }

  renderStockReturn = () => {
    const { loadingStockReturn, analyzeData: { stockReturnsData } } = this.props
    const columns1 = [{
      title: '最佳选券',
      dataIndex: 'STOCK_NAME',
      width: 150,
    }, {
      title: '行业',
      dataIndex: 'INDUSTRY',
    }, {
      title: '权重',
      dataIndex: 'WEIGHT_P',
      format: 'valPercentage',
      align: 'right',
      render: this.renderPercentageValue,
    }, {
      title: '收益',
      dataIndex: 'RETURN_P',
      format: 'percentage',
      align: 'right',
      render: this.renderTableNumericColumn,
    }]
    const columns2 = [{
      title: '最差选券',
      dataIndex: 'STOCK_NAME',
      width: 150,
    }, {
      title: '行业',
      dataIndex: 'INDUSTRY',
    }, {
      title: '权重',
      dataIndex: 'WEIGHT_P',
      format: 'valPercentage',
      align: 'right',
      render: this.renderPercentageValue,
    }, {
      title: '收益',
      dataIndex: 'RETURN_P',
      format: 'percentage',
      align: 'right',
      render: this.renderTableNumericColumn,
    }]
    const commonColumns = [{
      title: '资产名称',
      dataIndex: 'STOCK_NAME',
      width: 150,
    }, {
      title: '资产代码',
      dataIndex: 'STOCK_CODE',
    }, {
      title: '行业',
      dataIndex: 'INDUSTRY',
    }, {
      title: '权重',
      dataIndex: 'WEIGHT_P',
      format: 'valPercentage',
      align: 'right',
      render: this.renderPercentageValue,
    }, {
      title: '收益',
      dataIndex: 'RETURN_P',
      format: 'percentage',
      align: 'right',
      render: this.renderTableNumericColumn,
    }]
    let data1 = stockReturnsData.slice(0, 10)
    let data2 = stockReturnsData.slice(-10)
    if (stockReturnsData.length < 20) {
      const slicePoint = Math.ceil(stockReturnsData.length / 2)
      data1 = stockReturnsData.slice(0, slicePoint)
      data2 = stockReturnsData.slice(slicePoint)
    }
    data2 = data2.reverse()
    const chartData = [{
      name: '最佳选券',
      data: data1,
    }, {
      name: '最差选券',
      data: data2,
    }]
    return (
      <Spin spinning={loadingStockReturn}>
        <div style={{ marginBottom: '10px' }} />
        <Card
          title={
            <span>
              选券效益
              <Tooltip title="横轴绝对收益，纵轴主动比例" placement="right">
                <span style={{ marginLeft: '5px' }}>
                  <QuestionCircleOutlined />
                </span>
              </Tooltip>
            </span>
          }
          extra={<ExportData columns={commonColumns} dataSource={data1.concat(data2)} filename={this.getExportFilename('选券效益_最佳前10_最差前10')}/>}
        >
          {this.renderStockReturnChart(chartData)}
          <div style={{ marginBottom: '20px' }} />
          <Row gutter={10} className={styles.fixHeightWapper}>
            <Col md={12} sm={24}>
              <Table columns={columns1} dataSource={data1} size="small" pagination={false} />
            </Col>
            <Col md={12} sm={24}>
              <Table columns={columns2} dataSource={data2} size="small" pagination={false} />
            </Col>
          </Row>
        </Card>
      </Spin>
    );
  }

  getExportFilename = (suffix) => {
    const { model, startDate, endDate } = this.state
    const { currentFund } = this.props
    let name = 'Brinson模型'
    if (model === 'campisi') {
      name = 'Campisi模型'
    } else if (model === 'return') {
      name = '绝对收益模型'
    }
    return `${currentFund.name}_${name}_${moment(startDate).format('YYYYMMDD')}-${moment(endDate).format('YYYYMMDD')}_${suffix}`
  }

  renderCampisiDetailTable = () => {
    const columns = [
      {
        title: '收入效应',
        dataIndex: '收入效应',
        align: 'right',
        format: 'percentage',
        render: this.renderTableNumericColumn,
      },
      {
        title: '票息效应',
        dataIndex: '票息效应',
        align: 'right',
        format: 'percentage',
        render: this.renderTableNumericColumn,
      },
      {
        title: '价格收敛效应',
        dataIndex: '价格收敛效应',
        align: 'right',
        format: 'percentage',
        render: this.renderTableNumericColumn,
      },
      {
        title: '国债效应',
        dataIndex: '国债效应',
        align: 'right',
        format: 'percentage',
        render: this.renderTableNumericColumn,
      },
      {
        title: '利差效应',
        dataIndex: '利差效应',
        align: 'right',
        format: 'percentage',
        render: this.renderTableNumericColumn,
      },
      {
        title: '收益合计',
        dataIndex: '收益合计',
        align: 'right',
        format: 'percentage',
        render: this.renderTableNumericColumn,
      },
    ]
    const {
      analyzeData: { campisiDetailData },
    } = this.props
    const treeColumns = [{
      title: '分类',
      dataIndex: '债券名称',
      format: 'text',
      width: 150,
    }, ...columns]
    .map(item => {
      item.sorter = sortQuotaFn(item, 'asc')
      return item
    })
    const commonColumns = [{
      title: '债券名称',
      dataIndex: '债券名称',
      format: 'text',
    }, {
      title: '一级分类',
      dataIndex: '一级分类',
      format: 'text',
    }, {
      title: '二级分类',
      dataIndex: '二级分类',
      format: 'text',
    }, ...columns]
    .map(item => {
      item.sorter = sortQuotaFn(item, 'asc')
      return item
    })
    const data = buildTreeData(campisiDetailData, treeColumns.slice(1), ['一级分类', '二级分类'], '债券名称')
    return (
      <Card
        title="收益详细信息"
        bordered={false}
        extra={
          <Space>
            <ExportData columns={commonColumns} dataSource={campisiDetailData} filename={this.getExportFilename('收益详细信息')}/>
          </Space>
        }
      >
        <Table
          rowKey="id"
          pagination={false}
          columns={treeColumns}
          dataSource={data}
          size="small"
          summary={pageData => {
            const summaryData = columns.map(col => {
              return _.sum(pageData.map(item => item[col.dataIndex]))
            })
            return (
              <>
                <Table.Summary.Row>
                  <Table.Summary.Cell>合计</Table.Summary.Cell>
                  {summaryData.map(item => {
                    return (
                      <Table.Summary.Cell>
                        <div style={{ textAlign: 'right' }}>
                          {this.renderReturnValue(item)}
                        </div>
                      </Table.Summary.Cell>
                    )
                  })}
                </Table.Summary.Row>
              </>
            )
          }}
        />
      </Card>
    )
  };

  renderBrinsonDetailTable = () => {
    const tableData = this.getBrisonDetailTableData()
    return (
      <Card
        title="收益详细信息"
        bordered={false}
        extra={<ExportData {...tableData} filename={this.getExportFilename('收益详细信息')}/>}
      >
        <Table
          pagination={false}
          size="small"
          scroll={{ y: 450 }}
          {...tableData}
        />
      </Card>
    )
  }

  renderBestTickerSelect = () => {
    const {
      analyzeData: { returnDetailData },
    } = this.props
    const returnData = returnDetailData.filter(item => {
      return ['股票', '可转债'].includes(item.资产类型)
    }).map(item => {
      return {
        STOCK_NAME: item.STOCK_NAME,
        ASSET_TYPE: item.资产类型,
        INDUSTRY: item.股票行业,
        WEIGHT: item.WEIGHT,
        RETURN: item.区间收益率,
        RETURN_CONTRI: item.收益率贡献,
        STOCK_CODE: item.STOCK_CODE,
      }
    }).sort((fst, snd) => {
      return snd.RETURN_CONTRI - fst.RETURN_CONTRI
    })
    return (
      <BestTicketSelect returnData={returnData}/>
    )
  }

  renderModelResult = () => {
    const { loading } = this.props
    const { model, returnViewType } = this.state
    if (this.isEmptyData() && !loading) {
      return (
        <Empty
          style={{ padding: '100px' }}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="您所选择的区间段没有数据"
        />
      )
    }
    if (model === 'return') {
      return (
        <>
          <Row gutter={10}>
            <Col span={24}>
              <Spin spinning={loading}>
                {this.renderReturnDetailChart()}
              </Spin>
            </Col>
          </Row>
          <div style={{ marginBottom: '10px' }} />
          {returnViewType !== 'childFund' &&
          <Spin spinning={this.props.loading}>
            <Row gutter={10} className={styles.fixHeightWapper}>
              <Col md={24} sm={24}>
                {this.renderBestTickerSelect()}
              </Col>
            </Row>
          </Spin>}
          {returnViewType !== 'childFund' &&
          <Spin spinning={this.props.loading}>
            <Row gutter={10} className={styles.fixHeightWapper}>
              <Col md={24} sm={24}>
                {this.renderReturnSeries()}
              </Col>
            </Row>
          </Spin>}
        </>
      )
    }
    return (
      <>
        <Spin spinning={this.props.loading}>
          <Row gutter={10} className={styles.fixHeightWapper}>
            <Col md={6} sm={24}>
              {
                model === 'brinson' ?
                this.renderBrinsonSummary() :
                model === 'campisi' ?
                this.renderCampisiSummary() :
                this.renderReturnSummary()
              }
            </Col>
            {model === 'brinson' &&
            <Col md={18} sm={24}>
              {this.renderBrinsonSeries()}
            </Col>}
            {model === 'campisi' &&
            <Col md={18} sm={24}>
              {this.renderCampisiSeries()}
            </Col>}
            {model === 'return' &&
            <Col md={18} sm={24}>
              {this.renderReturnSeries()}
            </Col>}
          </Row>
        </Spin>
        {model === 'brinson' && (
          <Row gutter={10}>
            <Col span={24}>
              <Card
                title={
                  <span>
                    收益分布
                    <Tooltip title="横轴行业超额，纵轴主动比例，气泡大小代表主动收益" placement="right">
                      <span style={{ marginLeft: '5px' }}>
                        <QuestionCircleOutlined />
                      </span>
                    </Tooltip>
                  </span>
                }
                bordered={false}
                extra={<ExportData {...this.getBrinsonDetailBubbleTableData()} filename={this.getExportFilename('收益分布')}/>}
              >
                {this.renderBrinsonDetailBubbleChart()}
              </Card>
            </Col>
          </Row>
        )}
        {model === 'brinson' && this.renderStockReturn()}
        <div style={{ marginBottom: '10px' }} />
        {model === 'brinson' && (
          <Row gutter={10}>
            <Col span={24}>
              {this.renderBrinsonDetailChart()}
            </Col>
          </Row>
        )}
        <div style={{ marginBottom: '10px' }} />
        <Row gutter={10}>
          <Col span={24}>
            <Spin spinning={loading}>
              {model === 'return' && this.renderReturnDetailChart()}
              {model === 'campisi' && this.renderCampisiDetailTable()}
              {model === 'brinson' && this.renderBrinsonDetailTable()}
            </Spin>
          </Col>
        </Row>
      </>
    )
  };

  render() {
    const { model, benchmark, startDate, endDate, returnViewType } = this.state
    const { currentFund, location: { pathname } } = this.props
    if (currentFund._syncType !== 'mutual' && !window.__isTradingNetwork) {
      router.push('/404')
      return null
    }
    const attrMenuData = [{
      name: '多资产归因',
      value: 'multiasset_attribution',
    }, {
      name: '权益归因',
      value: 'stock_attribution',
    }, {
      name: '债券归因',
      value: 'bond_attribution',
    }, {
      name: '可转债归因',
      value: 'convtbond_attribution',
    }, {
      name: '绝对贡献',
      value: 'absolute_attribution',
    }]
    if (!currentFund.isPortfolio) {
      attrMenuData.push({
        name: '风格归因',
        value: 'style_attribution',
      })
    } else {
      attrMenuData.shift()
    }
    const activeTab = pathname.split('/').pop()
    const handleTabChange = currentTab => {
      const splits = pathname.split('/')
      const pathPrefix = splits.slice(0, -1).join('/')
      router.push(`${pathPrefix}/${currentTab}`)
    }
    const returnViewTypeList = [{
      name: '按股票行业',
      value: 'industry',
    }, {
      name: '按股票风格',
      value: 'style',
    }, {
      name: '按股票市值',
      value: 'mktSize',
    }]
    if (currentFund.childFundCodes && !!currentFund.childFundCodes.length) {
      returnViewTypeList.push({
        name: '按子基金',
        value: 'childFund',
      })
    }
    return (
      <div>
        <Tabs
          className={styles.actionWapper}
          onChange={handleTabChange}
          activeKey={activeTab}
          tabBarExtraContent={
            <Space>
              {false &&
              <div>
                <span style={{ marginRight: '10px' }}>模型选择:</span>
                <Select value={model} onChange={this.handleModelChange} style={{ width: 120 }}>
                  <Option value="brinson">Brinson模型</Option>
                  <Option value="return">绝对收益模型</Option>
                  <Option value="campisi">Campisi模型</Option>
                </Select>
              </div>}
              {model === 'brinson' && (
                <div>
                  <span style={{ marginRight: '10px' }}>基准选择:</span>
                  <Select
                    value={benchmark}
                    onChange={this.handleBenhcmarkChange}
                    style={{ width: 120 }}
                  >
                    <Option value="沪深300">沪深300</Option>
                    <Option value="中证500">中证500</Option>
                    <Option value="恒生指数">恒生指数</Option>
                  </Select>
                </div>
              )}
              {model === 'return' && (
                <div>
                  <span style={{ marginRight: '10px' }}>分解模式:</span>
                  <Select
                    value={returnViewType}
                    onChange={this.handleReturnViewTypeChange}
                    style={{ width: 120 }}
                  >
                    {returnViewTypeList.map(item => <Option value={item.value}>{item.name}</Option>)}
                  </Select>
                </div>
              )}
              <div>
                <span style={{ marginRight: '10px' }}>日期范围:</span>
                <DatePicker
                  allowClear={false}
                  style={{ width: '140px' }}
                  disabledDate={this.disabledStartDate}
                  placeholder="起始日期"
                  value={startDate}
                  onChange={this.handleStartDateChange}
                />
                <span style={{ marginRight: '10px' }}></span>
                <DatePicker
                  allowClear={false}
                  style={{ width: '140px' }}
                  disabledDate={this.disabledEndDate}
                  placeholder="结束日期"
                  value={endDate}
                  onChange={this.handleEndDateChange}
                />
              </div>
              <Button
                type="primary"
                size="small"
                style={{ margin: '0 10px' }}
                onClick={this.runAnalyze}
              >
                确定
              </Button>
            </Space>
          }
        >
          {attrMenuData.map(tab => (
            <TabPane tab={tab.name} key={tab.value}>
            </TabPane>
          ))}
        </Tabs>
        <div style={{ marginBottom: '10px' }} />
        <AnalyzeRangeAlert
          startDate={startDate.format('YYYYMMDD')}
          endDate={endDate.format('YYYYMMDD')}
          loading={this.props.loading}
          data={this.props.analyzeData.returnDetailData}
        />
        {this.renderModelResult()}
        <AlgDesc/>
      </div>
    )
  }
}

export default Attribution
