import request from '@/utils/request'
import { AttrQueryParams } from './data.d'

export async function querySeriesData(fundId: string, params: AttrQueryParams) {
  return request(`/api/products/${fundId}/attributionAnalysis/series`, {
    params,
  })
}

export async function queryData(fundId: string, params: AttrQueryParams) {
  return request(`/api/products/${fundId}/attributionAnalysis`, {
    params,
  })
}

export async function queryStockReturns(fundId: string, params: AttrQueryParams) {
  return request(`/api/products/${fundId}/stockreturns`, {
    params,
  })
}

export async function queryReturnDetail(fundId: string, params: AttrQueryParams) {
  return request(`/api/products/${fundId}/returndecompose`, {
    params,
  })
}

export async function queryReturnSeries(fundId: string, params: AttrQueryParams) {
  return request(`/api/products/${fundId}/returndecompose/series`, {
    params,
  })
}

export async function queryChildFundReturnDetail(fundId: string, params: AttrQueryParams) {
  return request(`/api/products/${fundId}/momreturndecompose`, {
    params,
  })
}

export function getTopDownAttrSeries(id: string, params?: any) {
  return request(`/api/products/${id}/topdownattrseries`, {
    params,
  })
}

export function getTopDownAttrDetail(id: string, params?: any) {
  return request(`/api/products/${id}/topdownattrdetail`, {
    params,
  })
}

export function getTopDownSelectDetail(id: string, params?: any) {
  return request(`/api/products/${id}/topdownselectdetail`, {
    params,
  })
}

export function getBarraStyleData(id: string, params?: any) {
  return request(`/api/products/${id}/barrastyle`, {
    params,
  })
}

export function getBarraSplitRet(id: string, params?: any) {
  return request(`/api/products/${id}/barrasplitret`, {
    params,
  })
}
