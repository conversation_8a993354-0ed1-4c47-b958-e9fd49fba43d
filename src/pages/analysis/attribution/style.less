.statistic {
  text-align: center;
  .value {
    font-size: 22px;
  }
}
.actionWapper {
  :global(.ant-card-body) {
    padding: 14px 10px;
  }
}
.cardActions {
  color: #bcc8d7;
  text-align: right;
  .actionItem {
    display: inline-block;
    margin-right: 15px;
  }
}
.spinWapper {
  text-align: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  margin-bottom: 20px;
  padding: 30px 50px;
  margin: 20px 0;
}

.xsTable {
  thead > tr > th,
  tbody > tr > td,
  tfoot > tr > td {
    padding: 4px 4px !important;
    font-size: 12px !important;
  }
}

.tableWrapper {
  width: 100%;
}
