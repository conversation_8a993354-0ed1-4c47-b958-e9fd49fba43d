

import React from 'react'
import _ from 'lodash'
import { Alert, Spin } from 'antd'

export default function AnalyzeRangeAlert({
  startDate, endDate, data, loading,
}) {
  let message = `所选时间范围: ${startDate}-${endDate}`
  if (data.length) {
    const startDates = data.map(item => item.START_DATE)
    const endDates = data.map(item => item.END_DATE)
    const start = _.min(startDates)
    const end = _.max(endDates)
    message = `${message}, 实际分析区间: ${start}-${end}`
  }
  message = <span><Spin spinning={loading}></Spin>{message}</span>
  return (
    <div style={{ marginTop: 15, marginBottom: 15 }}>
      <Alert style={{ border: '1px solid #15325b' }} message={message} type="info" closable/>
    </div>
  )
}

// background-color: #111a2c;
// border: 1px solid #15325b;
