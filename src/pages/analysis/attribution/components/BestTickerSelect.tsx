import React, {useState} from 'react'
import _ from 'lodash'
import { Card, Row, Col, Tooltip, Table, Space, Radio } from 'antd'
import ExportData from '@/components/ExportData'
import { QuestionCircleOutlined } from '@ant-design/icons'
import Echart from '@/components/Chart/Echarts'
import styles from '../style.less'

const renderReturnValue = (value: number) => {
  if (!value && value !== 0) {
    return '-'
  }
  const className = value > 0 ? 'colorUp' : 'colorDown'
  return <span className={className}>{(value * 100).toFixed(2) + '%'}</span>
}

const renderPercentageValue = (value: number) => {
  if (!value && value !== 0) {
    return '-'
  }
  return <span>{(value * 100).toFixed(2) + '%'}</span>
}

const renderTableNumericColumn = (val: number) => (
  <div style={{ textAlign: 'right' }}>{renderReturnValue(val)}</div>
)

const renderStockReturnChart = (chartData) => {
  const itemStyle = {
    opacity: 0.8,
    shadowBlur: 10,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: 'rgba(0, 0, 0, 0.5)',
  }
  const options = {
    color: [
      '#dd4444', '#fec42c', '#80F1BE',
    ],
    grid: {
      right: 80,
      left: 60,
    },
    legend: {
      top: 2,
      right: 10,
      data: ['最佳选券', '最差选券'],
    },
    tooltip: {
      padding: 10,
      // backgroundColor: '#222',
      // borderColor: '#777',
      borderWidth: 1,
      axisPointer: {
        type: 'cross',
      },
      formatter: function (obj) {
        const value = obj.value
        if (!value) {
          return ''
        }
        if (obj.componentType === 'markPoint') {
          return `${obj.name}：${value}%`
        }
        return '<div style="border-bottom: 1px solid rgba(255,255,255,.3); font-size: 18px;padding-bottom: 7px;margin-bottom: 7px">'
            + obj.seriesName + ': ' + value[4]
            + '</div>'
            + '区间收益率：' + value[0] + '%<br>'
            + '平均权重：' + value[1] + '%<br>'
            + '绝对贡献：' + value[3] + '%<br>'
      },
    },
    xAxis: {
      type: 'value',
      name: '区间收益率',
      axisLabel: {
        formatter: '{value} %',
      },
    },
    yAxis: {
      type: 'value',
      name: '平均权重',
      axisLabel: {
        formatter: '{value} %',
      },
      axisPointer: {
        snap: true,
      },
    },
    series: chartData.map(item => {
      const itemData = item.data.map((item, index) => [
        _.round(item.RETURN * 100, 2),
        _.round(item.WEIGHT * 100, 2),
        index + 1,
        _.round(item.RETURN_CONTRI * 100, 2),
        item.STOCK_NAME,
      ])
      return {
        name: item.name,
        type: 'scatter',
        itemStyle: itemStyle,
        symbolSize: 35,
        data: itemData,
        markPoint: {
          data: [
            {
              name: item.name === '最佳选券' ? '最佳收益' : '最差收益',
              value: itemData[0] && itemData[0][3],
              coord: itemData[0] && itemData[0].slice(0, 2),
            },
          ],
          itemStyle: {
            color: '#0EBF9C',
          },
        },
        label: {
          show: true,
          formatter: '{@[2]}',
        },
      }
    }),
  }
  return <Echart style={{ height: '600px' }} options={options}/>
}

export default ({ returnData }) => {
  const [viewType, setViewType] = useState('stock')
  const handleTypeChange = event => {
    setViewType(event.target.value)
  }
  const stockReturnData = returnData.filter(item => {
    if (viewType === 'stock') {
      return item.ASSET_TYPE === '股票'
    }
    return item.ASSET_TYPE === '可转债'
  })
  const columns1 = [{
    title: '最佳选券',
    dataIndex: 'STOCK_NAME',
    width: 110,
  }, {
    title: '行业',
    dataIndex: 'INDUSTRY',
  }, {
    titleText: '平均权重',
    title: (
      <span>
        平均权重
        <Tooltip title="组合中个券的多期平均权重" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'WEIGHT',
    format: 'valPercentage',
    align: 'right',
    render: renderPercentageValue,
  }, {
    titleText: '收益率',
    title: (
      <span>
        收益率
        <Tooltip title="个券区间收益率" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'RETURN',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }, {
    titleText: '绝对贡献',
    title: (
      <span>
        绝对贡献
        <Tooltip title="个券区间绝对贡献" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'RETURN_CONTRI',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }]
  const columns2 = [{
    title: '最差选券',
    dataIndex: 'STOCK_NAME',
    width: 110,
  },{
    title: '行业',
    dataIndex: 'INDUSTRY',
  }, {
    titleText: '平均权重',
    title: (
      <span>
        平均权重
        <Tooltip title="组合中个券的多期平均权重" placement="right">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'WEIGHT',
    format: 'valPercentage',
    align: 'right',
    render: renderPercentageValue,
  }, {
    titleText: '收益率',
    title: (
      <span>
        收益率
        <Tooltip title="个券区间收益率" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'RETURN',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }, {
    titleText: '绝对贡献',
    title: (
      <span>
        绝对贡献
        <Tooltip title="个券区间绝对贡献" placement="left">
          <span style={{ marginLeft: '5px' }}>
            <QuestionCircleOutlined />
          </span>
        </Tooltip>
      </span>
    ),
    dataIndex: 'RETURN_CONTRI',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }]
  const commonColumns = [{
    title: '资产名称',
    dataIndex: 'STOCK_NAME',
    width: 150,
  }, {
    title: '资产代码',
    dataIndex: 'STOCK_CODE',
  }, {
    title: '行业',
    dataIndex: 'INDUSTRY',
  }, {
    title: '平均权重',
    dataIndex: 'WEIGHT',
    format: 'valPercentage',
    align: 'right',
    render: renderPercentageValue,
  }, {
    title: '收益率',
    dataIndex: 'RETURN',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }, {
    title: '绝对贡献',
    dataIndex: 'RETURN_CONTRI',
    format: 'percentage',
    align: 'right',
    render: renderTableNumericColumn,
  }]
  let data1 = stockReturnData.slice(0, 10)
  let data2 = stockReturnData.slice(-10)
  if (stockReturnData.length < 20) {
    const slicePoint = Math.ceil(stockReturnData.length / 2)
    data1 = stockReturnData.slice(0, slicePoint)
    data2 = stockReturnData.slice(slicePoint)
  }
  data2 = data2.reverse()
  const chartData = [{
    name: '最佳选券',
    data: data1,
  }, {
    name: '最差选券',
    data: data2,
  }]
  return (
    <>
      <div style={{ marginBottom: '10px' }} />
      <Card
        title={
          <span>
            选券效益
            <Tooltip title="横轴区间收益率，纵轴平均权重" placement="right">
              <span style={{ marginLeft: '5px' }}>
                <QuestionCircleOutlined />
              </span>
            </Tooltip>
          </span>
        }
        extra={
          <Space>
            <Radio.Group
              value={viewType}
              size="small"
              onChange={handleTypeChange}
            >
              <Radio.Button value="stock">股票</Radio.Button>
              <Radio.Button value="convtBond">可转债</Radio.Button>
            </Radio.Group>
            <ExportData columns={commonColumns} dataSource={data1.concat(data2)} filename="选券效益_最佳前10_最差前10"/>
          </Space>
        }
      >
        <Row gutter={10} className={styles.fixHeightWapper}>
          <Col md={16} sm={24}>
            {renderStockReturnChart(chartData)}
          </Col>
          <Col md={8} sm={24}>
            <Table className={styles.xsTable} columns={columns1} dataSource={data1} size="small" pagination={false} />
            <Table className={styles.xsTable} columns={columns2} dataSource={data2} size="small" pagination={false} />
          </Col>
        </Row>
      </Card>
    </>
  )
}
