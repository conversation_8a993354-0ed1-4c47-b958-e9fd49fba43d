import React, { useState } from 'react'
import _ from 'lodash'
import {
  Space,
  Spin,
  Select,
  Card,
} from 'antd'
import SelectDate from '@/components/SelectDate'
import { useRequest } from '@umijs/hooks'
import { getBarraSplitRet } from '../service'
import ProgressBarTable from '@/components/ProgressBarTable'
import ExportData from '@/components/ExportData'
import sortQuotaFn from '@/utils/sortQuotaFn'

const { Option } = Select

const BarraFactorPeriodRet = ({ data, benchmarkList }) => {
  const dates = _.uniq(data.map(item => item.the_date)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const [benchmark, setBenchmark] = useState(benchmarkList[0].code)
  const [date, setDate] = useState(dates[0])
  const tableData = data.filter(item => {
    return item.the_date === date && item.benchmark === benchmark
  })
  const columns = [{
    title: '风格因子',
    dataIndex: 'style',
    width: 120,
    ellipsis: true,
    fixed: 'left',
  }, {
    title: '近1月超额暴露',
    dataIndex: 'exposure1M',
    width: 120,
  }, {
    title: '近1月超额收益',
    dataIndex: 'return1M',
    width: 120,
  }, {
    title: '近3月超额暴露',
    dataIndex: 'exposure3M',
    width: 120,
  }, {
    title: '近3月超额收益',
    dataIndex: 'return3M',
    width: 120,
  }, {
    title: '近6月超额暴露',
    dataIndex: 'exposure6M',
    width: 120,
  }, {
    title: '近6月超额收益',
    dataIndex: 'return6M',
    width: 120,
  }, {
    title: '近9月超额暴露',
    dataIndex: 'exposure9M',
    width: 120,
  }, {
    title: '近9月超额收益',
    dataIndex: 'return9M',
    width: 120,
  }, {
    title: 'YTD超额暴露',
    dataIndex: 'exposureYTD',
    width: 120,
  }, {
    title: 'YTD超额收益',
    dataIndex: 'returnYTD',
    width: 120,
  }, {
    title: '近1年超额暴露',
    dataIndex: 'exposure12M',
    width: 120,
  }, {
    title: '近1年超额收益',
    dataIndex: 'return12M',
    width: 120,
  }, {
    title: '近2年超额暴露',
    dataIndex: 'exposure24M',
    width: 120,
  }, {
    title: '近2年超额收益',
    dataIndex: 'return24M',
    width: 120,
  }, {
    title: '成立以来超额暴露',
    dataIndex: 'exposureSi',
    width: 140,
  }, {
    title: '成立以来超额收益',
    dataIndex: 'returnSi',
    width: 140,
  }].map(col => {
    const dataIndex = col.dataIndex
    if (dataIndex.includes('exposure')) {
      col.align = 'right'
      col.format = 'number'
      col.isProgressBar = true
    } else if (dataIndex.includes('return')) {
      col.align = 'right'
      col.format = 'percentage'
      col.isProgressBar = true
    }
    col.sorter = sortQuotaFn({ ...col }, 'asc')
    return col
  })
  return (
    <Card
      size="small"
      title="风格因子区间暴露"
      extra={
        <Space>
          <SelectDate
            date={date}
            dates={dates}
            onChange={setDate}
          />

          <Select
            size="small"
            placeholder="选择基准"
            style={{
              // marginTop: '5px',
              width: '120px',
            }}
            value={benchmark}
            onChange={setBenchmark}
          >
            {benchmarkList.map(item => {
              return (
                <Option value={item.code}>
                  {item.name}
                </Option>
              )
            })}
          </Select>
          <ExportData title="导出" columns={columns} dataSource={tableData} filename="风格因子区间暴露"/>
        </Space>
      }
    >
      <ProgressBarTable tableData={tableData} tableColumns={columns}/>
    </Card>
  )
}

const BarraFactorPeriodRetWrapper = ({
  fund,
  benchmarkList,
}) => {
  const { loading, data } = useRequest(() => {
    return getBarraSplitRet(fund._id)
  }, {
    cacheKey: 'getBarraSplitRet',
  })
  return (
    <div style={{ minHeight: 400 }}>
      <Spin spinning={loading}>
        {!loading &&
        <BarraFactorPeriodRet data={data} benchmarkList={benchmarkList}/>}
      </Spin>
    </div>
  )
}

export default BarraFactorPeriodRetWrapper
