import React, { useState } from 'react'
import _ from 'lodash'
import {
  Select,
  Card,
  Space,
} from 'antd'
import Echart from '@/components/Chart/Echarts'
import SelectDate from '@/components/SelectDate'
import ExportData from '@/components/ExportData'

const { Option } = Select

const getChartOptions = (data, category) => {
  const option = {
    grid: {
      left: '4%',
      right: '4%',
      bottom: '18%',
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        rotate: 30,
      },
      data: data.map(item => item[category]),
      boundaryGap: true,
      splitArea: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [{
      type: 'value',
    }, {
      type: 'value',
    }],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const param1 = params[0]
        const param2 = params[1]
        return [
          param1.axisValue + ': ',
          '因子超额暴露: ' + param1.data,
          '因子超额收益: ' + param2.data + '%',
        ].join('<br/>')
      },
    },
    series: [
      {
        name: '因子超额暴露',
        type: 'bar',
        data: data.map(item => _.round(item.style_excess_exposure_m, 4)),
      },
      {
        name: '因子超额收益',
        type: 'line',
        data: data.map(item => _.round(item.style_excess_return * 100, 2)),
        smooth: false,
        showSymbol: false,
        yAxisIndex: 1,
      },
    ],
  }

  return option
}

const BarraFactorExposure = ({ styleData, dates, benchmarkList, factors }) => {
  const [benchmark, setBenchmark] = useState(benchmarkList[0].code)
  const [date, setDate] = useState(dates[0])
  const [factor, setFactor] = useState(factors[0])
  const onEvents = {
    click: (action) => {
      setFactor(action.name)
    },
  }
  const chartData = styleData.filter(item => {
    return item.the_date === date && item.benchmark === benchmark
  })
  const factorData = styleData.filter(item => {
    return item.style === factor && item.benchmark === benchmark
  })
  const options = getChartOptions(chartData, 'style')
  const factorOptions = getChartOptions(factorData, 'the_date')
  const styleRetCols = [{
    title: '因子',
    dataIndex: 'style',
  }, {
    title: '因子超额暴露',
    dataIndex: 'style_excess_exposure_m',
  }, {
    title: '因子超额收益',
    dataIndex: 'style_excess_return',
  }]
  const factorDataCols = [{
    title: '日期',
    dataIndex: 'the_date',
  }, {
    title: '因子超额暴露',
    dataIndex: 'style_excess_exposure_m',
  }, {
    title: '因子超额收益',
    dataIndex: 'style_excess_return',
  }]
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card
          size="small"
          title="风格因子超额暴露"
          extra={
            <Space>
              <SelectDate
                date={date}
                dates={dates}
                onChange={setDate}
              />

              <Select
                size="small"
                placeholder="选择基准"
                style={{
                  // marginTop: '5px',
                  width: '120px',
                }}
                value={benchmark}
                onChange={setBenchmark}
              >
                {benchmarkList.map(item => {
                  return (
                    <Option value={item.code}>
                      {item.name}
                    </Option>
                  )
                })}
              </Select>
              <ExportData title="导出" columns={styleRetCols} dataSource={chartData} filename="风格因子超额暴露"/>
            </Space>
          }
        >
          <Echart style={{ height: '400px' }} options={options} onEvents={onEvents}/>
        </Card>
        <Card
          size="small"
          title={`${factor}因子超额暴露`}
          extra={
            <Space>
              <SelectDate
                date={factor}
                dates={factors}
                onChange={setFactor}
                width="150px"
              />
              <ExportData title="导出" columns={factorDataCols} dataSource={factorData} filename={`${factor}因子超额暴露`}/>
            </Space>
          }
        >
          <Echart style={{ height: '400px' }} options={factorOptions}/>
        </Card>
      </Space>
    </div>
  )
}

export default BarraFactorExposure
