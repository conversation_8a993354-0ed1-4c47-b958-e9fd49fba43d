import React, { useState } from 'react'
import _ from 'lodash'
import { connect } from 'dva'
import {
  Card,
  Affix,
  Space,
  Spin,
  Tabs,
} from 'antd'
import router from 'umi/router'
import { useRequest } from '@umijs/hooks'
import { getBarraStyleData } from './service'
import BarraFactorExposure from './components/BarraFactorExposure'
import BarraFactorPeriodRet from './components/BarraFactorPeriodRet'

const { TabPane } = Tabs

const BarraStyle = ({ data, fund }) => {
  const benchmarkList = [{
    name: '沪深300', code: '000300.SH',
  },
  // {
  //   name: '天相股票', code: '998105.TX',
  // },
  {
    name: '中证500', code: '000905.SH',
  },
  {
    name: '中证800', code: '000906.SH',
  },
  {
    name: '中证A股', code: '930903.CSI',
  },
  // {
  //   name: '股票基金平均', code: 'StkFdAvg',
  // }
  ]
  const dates = _.uniq(data.map(item => item.the_date)).sort((fst, snd) => {
    return snd > fst ? 1 : -1
  })
  const factors = _.uniq(data.map(item => item.style))
  return (
    <div>
      <BarraFactorExposure fund={fund} styleData={data} benchmarkList={benchmarkList} dates={dates} factors={factors}/>
      <BarraFactorPeriodRet fund={fund} benchmarkList={benchmarkList} />
    </div>
  )
}

const BarraStyleWrapper = ({
  currentFund,
  location: { pathname },
}) => {
  const attrMenuData = [{
    name: '多资产归因',
    value: 'multiasset_attribution',
  }, {
    name: '权益归因',
    value: 'stock_attribution',
  }, {
    name: '债券归因',
    value: 'bond_attribution',
  }, {
    name: '可转债归因',
    value: 'convtbond_attribution',
  }, {
    name: '绝对贡献',
    value: 'absolute_attribution',
  }, {
    name: '风格归因',
    value: 'style_attribution',
  }]
  const { loading, data } = useRequest(() => {
    return getBarraStyleData(currentFund._id)
  }, {
    cacheKey: 'getBarraStyleData',
  })
  const activeTab = pathname.split('/').pop()
  const handleTabChange = currentTab => {
    const splits = pathname.split('/')
    const pathPrefix = splits.slice(0, -1).join('/')
    router.push(`${pathPrefix}/${currentTab}`)
  }
  return (
    <div style={{ minHeight: 400 }}>
      <Affix offsetTop={44}>
        <Tabs
          style={{
            background: 'rgb(24, 31, 41)',
          }}
          onChange={handleTabChange}
          activeKey={activeTab}
        >
          {attrMenuData.map(tab => (
            <TabPane tab={tab.name} key={tab.value}>
            </TabPane>
          ))}
        </Tabs>
      </Affix>
      <Spin spinning={loading}>
        {!loading &&
        <BarraStyle data={data} fund={currentFund}/>}
      </Spin>
    </div>
  )
}

export default connect(
  ({
    fund,
  }: {
    fund: any;
  }) => ({
    currentFund: fund.currentFund,
  }),
)(BarraStyleWrapper)
