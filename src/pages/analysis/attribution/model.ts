import { AnyAction, Reducer } from 'redux'
import { EffectsCommandMap } from 'dva'
import {
  queryData,
  querySeriesData,
  queryStockReturns,
  queryReturnDetail,
  queryReturnSeries,
  queryChildFundReturnDetail,
} from './service'

export interface StateType {
  brinsonSeriesData: any;
  brinsonDetailData: any;
  campisiDetailData: any;
  campisiSeriesData: any;
  campisidailySeriesData: any;
  stockReturnsData: any;
  returnSeriesData: any;
  returnDetailData: any;
  childFundReturnData: any;
}

export type Effect = (
  action: AnyAction,
  effects: EffectsCommandMap & { select: <T>(func: (state: StateType) => T) => T },
) => void;

export interface ModelType {
  namespace: string;
  state: StateType;
  effects: {
    fetch: Effect;
    fetchSeries: Effect;
    fetchStockReturns: Effect;
    fetchReturnDetail: Effect;
    fetchReturnSeries: Effect;
    fetchChildFundReturnDetail: Effect;
  };
  reducers: {
    save: Reducer<StateType>;
  };
}

const Model: ModelType = {
  namespace: 'attribution',

  state: {
    brinsonSeriesData: [],
    brinsonDetailData: [],
    campisiDetailData: [],
    campisiSeriesData: [],
    campisidailySeriesData: [],
    stockReturnsData: [],
    returnSeriesData: [],
    returnDetailData: [],
    childFundReturnData: [],
  },

  effects: {
    *fetch({ payload: { fundId, params } }, { call, put }) {
      const response = yield call(queryData, fundId, params)
      yield put({
        type: 'save',
        payload: {
          [`${params.model}DetailData`]: response,
        },
      })
    },
    *fetchSeries({ payload: { fundId, params } }, { call, put }) {
      const response = yield call(querySeriesData, fundId, params)
      yield put({
        type: 'save',
        payload: {
          [`${params.model}SeriesData`]: response,
        },
      })
    },
    *fetchStockReturns({ payload: { fundId, params } }, { call, put }) {
      const response = yield call(queryStockReturns, fundId, params)
      yield put({
        type: 'save',
        payload: {
          stockReturnsData: response,
        },
      })
    },
    *fetchReturnDetail({ payload: { fundId, params } }, { call, put }) {
      const response = yield call(queryReturnDetail, fundId, params)
      yield put({
        type: 'save',
        payload: {
          returnDetailData: response,
        },
      })
    },
    *fetchReturnSeries({ payload: { fundId, params } }, { call, put }) {
      const response = yield call(queryReturnSeries, fundId, params)
      yield put({
        type: 'save',
        payload: {
          returnSeriesData: response,
        },
      })
    },
    *fetchChildFundReturnDetail({ payload: { fundId, params } }, { call, put }) {
      const response = yield call(queryChildFundReturnDetail, fundId, params)
      yield put({
        type: 'save',
        payload: {
          childFundReturnData: response,
        },
      })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
  },
}

export default Model
