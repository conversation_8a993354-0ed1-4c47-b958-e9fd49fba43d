import React, { Component } from 'react'
import moment, { Moment } from 'moment'
import { Dispatch } from 'redux'
import { formatMessage } from 'umi-plugin-react/locale'
import { Card, Row, Col, Tabs, Affix, Spin } from 'antd'
import { scroller } from 'react-scroll'
import { connect } from 'dva'
import { StateType } from './model'
import DataTable from '@/components/DataTable'
import PositionSeriesModel from './PositionSeriesModel'
import SeriesAnalyzeCard from './components/SeriesAnalyzeCard'
import PositionDateRangeSelect from './components/PositionDateRangeSelect'
import Margin from './components/Margin'
import styles from './style.less'

const { TabPane } = Tabs
const t = (id: string) => formatMessage({ id })

interface SeriesProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  analyzeData: StateType;
  computedMatch: any;
  currentFund: any;
  location: any;
}

interface ComponentState {
  startDate: Moment | undefined;
  endDate: Moment | undefined;
  initialStartDate: Moment | undefined;
  initialEndDate: Moment | undefined;
  activeKey: string;
}

@connect(
  ({
    fund,
    position,
    loading,
    computedMatch,
  }: {
    fund: any;
    position: StateType;
    computedMatch: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    computedMatch,
    analyzeData: position,
    loading: loading.models.position,
  }),
)
class Series extends Component<SeriesProps, ComponentState> {
  constructor(props: SeriesProps) {
    super(props)
    const {
      currentFund,
      location: { query },
    } = props
    const dates = currentFund.dates
    this.state = {
      startDate: moment(query.startDate || dates[0]),
      endDate: moment(query.endDate || dates[dates.length - 1]),
      initialStartDate: moment(dates[0]),
      initialEndDate: moment(dates[dates.length - 1]),
      activeKey: 'overview',
    }
  }

  componentDidMount() {
    this.runAnalyze()
  }

  handleTabChange = (activeKey: string) => {
    scroller.scrollTo(activeKey, { offset: -70 })
  };

  isEmptyData = (keys: string[]) => {
    const {
      analyzeData: { seriesData },
    } = this.props
    return (
      !seriesData ||
      keys.every(key => {
        return !seriesData[key] || seriesData[key].length === 0
      })
    )
  };

  runAnalyze = () => {
    const { startDate, endDate } = this.state
    const { dispatch, currentFund } = this.props
    const fundId = currentFund._id
    dispatch({
      type: 'position/fetchSeries',
      payload: { fundId, params: { startDate, endDate } },
    })
  };

  renderContent() {
    const {
      analyzeData: { seriesData },
    } = this.props
    if (!seriesData) {
      return (
        <Spin>
          <Card style={{ height: '500px' }} />
        </Spin>
      )
    }
    const positionModel = new PositionSeriesModel({ analysisResult: seriesData, t })
    return (
      <>
        <div id="overview" />
        <SeriesAnalyzeCard title="资产类型结构" {...positionModel.buildAssetStructur()} />
        <Margin />
        <Row gutter={10}>
          {false && (
            <Col md={12} sm={24}>
              <SeriesAnalyzeCard title="VaR时间序列" {...positionModel.buildVarsConfig()} />
            </Col>
          )}
          <Col md={24} sm={24}>
            <SeriesAnalyzeCard title="净资产曲线" {...positionModel.buildNetAssets()} />
          </Col>
        </Row>
        <Margin id="stock" />
        {!this.isEmptyData(['concept']) && (
          <>
            <Card title="股票投资概念">
              <DataTable {...positionModel.buildStockConceptConfig()} />
            </Card>
            <Margin />
          </>
        )}
        {!this.isEmptyData(['industry']) && (
          <>
            <Row gutter={10}>
              <Col md={12} sm={24}>
                <SeriesAnalyzeCard
                  title="股票投资行业偏好"
                  {...positionModel.buildIndustryConfig()}
                />
              </Col>
              <Col md={12} sm={24}>
                <SeriesAnalyzeCard
                  title="股票投资板块偏好"
                  {...positionModel.buildIndustryBoardConfig()}
                />
              </Col>
            </Row>
            <Margin />
          </>
        )}
        {!this.isEmptyData(['position']) && (
          <>
            <Card title="前十大股票持仓">
              <DataTable {...positionModel.buildPositionConfig()} />
            </Card>
            <Margin />
          </>
        )}
        {!this.isEmptyData(['peSeries']) && (
          <>
            <SeriesAnalyzeCard title="基金PE值曲线" {...positionModel.buildPeConfig()} />
          </>
        )}
        <Margin id="bond" />
        {!this.isEmptyData(['bondConcept']) && (
          <>
            <Card title="债券投资概念">
              <DataTable {...positionModel.buildBondConceptConfig()} />
            </Card>
            <Margin />
          </>
        )}
        {!this.isEmptyData(['bondPosition']) && (
          <>
            <Row gutter={10}>
              <Col md={12} sm={24}>
                <SeriesAnalyzeCard title="债券类型分布" {...positionModel.buildBondTypeConfig()} />
              </Col>
              <Col md={12} sm={24}>
                <SeriesAnalyzeCard title="债券期限分布" {...positionModel.buildBondTermConfig()} />
              </Col>
            </Row>
            <Margin />
            <Row gutter={10}>
              <Col md={12} sm={24}>
                <SeriesAnalyzeCard
                  title="债券指标统计"
                  {...positionModel.buildBondStatisticsConfig()}
                />
              </Col>
              <Col md={12} sm={24}>
                <SeriesAnalyzeCard title="债券评级分布" {...positionModel.buildBondRateConfig()} />
              </Col>
            </Row>
            <Margin />
            <SeriesAnalyzeCard
              title="债券发行主体行业"
              {...positionModel.buildBondIssuerConfig()}
            />
            <Margin />
            <Card title="前十大债券持仓">
              <DataTable {...positionModel.buildBondPositionConfig()} />
            </Card>
          </>
        )}
      </>
    )
  }

  render() {
    const {
      currentFund: { dates },
      location: { pathname },
    } = this.props
    const { startDate, endDate } = this.state
    const routerBase = (window.routerBase || '').slice(0, -1)
    return (
      <div
        ref={node => {
          this.container = node
        }}
      >
        <Affix offsetTop={0} target={() => this.container}>
          <Tabs
            defaultActiveKey="overview"
            onChange={this.handleTabChange}
            className={styles.tabs}
            tabBarExtraContent={
              dates.length !== 0 && (
                <PositionDateRangeSelect
                  url={`${routerBase}${pathname}`}
                  {...{ dates, startDate, endDate }}
                />
              )
            }
          >
            <TabPane tab="资产概览" key="overview" />
            {!this.isEmptyData(['position', 'concept', 'industry']) && (
              <TabPane tab="股票持仓" key="stock" />
            )}
            {!this.isEmptyData(['bondPosition', 'bondConcept']) && (
              <TabPane tab="债券持仓" key="bond" />
            )}
          </Tabs>
        </Affix>
        {this.renderContent()}
      </div>
    )
  }
}

export default Series
