import request from '@/utils/request'

export async function queryData(fundId: string, date: string) {
  return request(`/api/products/${fundId}/valuations/${date}`)
}

export async function querySeriesData(fundId: string, params: any) {
  return request(`/api/products/${fundId}/analyze`, { params })
}

export async function queryStockPrice(fundId: string, params: any) {
  return request(`/api/products/${fundId}/position/stockprice`, { params })
}

export async function queryBenchmarkIndustryRatio(params: any) {
  return request('/api/valuations/benchmarkIndustryRatio', { params })
}

export async function queryBondCashflow(fundId: string, date: string) {
  return request(`/api/products/${fundId}/cashflows/${date}`)
}
