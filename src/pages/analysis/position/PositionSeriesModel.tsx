import React from 'react'
import moment from 'moment'
import groupBy from 'lodash/groupBy'
import { Tooltip } from 'antd'
import map from 'lodash/map'
import sum from 'lodash/sum'
import transformAssetStructure from '@/utils/transformAssetStructure'
import Sparkline from './components/Sparkline'
import AmountSeriesModal from './components/AmountSeriesModal'

interface ModelProps {
  analysisResult: {};
  t: any;
}
export default class PositionSeriesModel {
  props: ModelProps;

  constructor(props: ModelProps) {
    this.props = props
  }

  buildAssetStructur() {
    let {
      analysisResult: { structur: rows },
    } = this.props
    const {
      analysisResult: {
        product: { _syncType },
      },
      t,
    } = this.props
    const resultMap = rows.reduce((out, row) => {
      const date = row.BIZ_DATE
      out[date] = out[date] || []
      out[date].push(row)
      return out
    }, {})
    rows = Object.keys(resultMap)
      .map(date =>
        transformAssetStructure(resultMap[date], undefined).filter(
          item => item.ASSET_TYPE !== t('common.total'),
        ),
      )
      .reduce((out, items) => out.concat(items), [])
    return {
      isMutual: _syncType === 'mutual',
      rows,
      percentageValue: true,
      chartType: 'area',
      valueField: 'RATIO',
      serieField: 'ASSET_TYPE',
      t: this.props.t,
    }
  }

  filterConcepts(items) {
    const dates = [...new Set(items.map(item => item.BIZ_DATE))]
    const len = dates.length
    let gap = 1
    if (len > 600) gap = Math.ceil(len / 600)
    const gapedDates = dates.filter((date, key) => key % gap === 0)
    return items.filter(item => gapedDates.includes(item.BIZ_DATE))
  }

  buildConceptConfig(concept) {
    concept = this.filterConcepts(concept); // eslint-disable-line
    const { t } = this.props
    const conceptByName = concept.reduce((out, item) => {
      const name = item.CONCEPT_NAME
      out[name] = out[name] || []
      out[name].push([
        moment(item.BIZ_DATE).format('YYYY-MM-DD'),
        +(item.RATIO * 100).toFixed(2),
        item.IS_NOTABLE || 'Y',
      ])
      return out
    }, {})
    const all = Object.keys(conceptByName)
      .map(name => {
        return {
          name,
          data: conceptByName[name].sort((ii, jj) => (ii[0] > jj[0] ? 1 : -1)),
        }
      })
      .filter(item => item.data.some(row => row[2] === 'Y'))
    const dateList = concept.map(item => moment(item.BIZ_DATE))
    const dateStringList = dateList.map(item => item.format('YYYY-MM-DD'))
    const dateNumberList = dateList.map(item => +item)
    const maxDate = Math.max.apply(null, dateNumberList)
    const minDate = Math.min.apply(null, dateNumberList)
    const fillEmptyValue = list => {
      const dateValueMap = list.reduce((out, item) => {
        out[item[0]] = item[1]
        return out
      }, {})
      let temp = minDate
      const result = []

      while (temp <= maxDate) {
        const dateString = moment(temp).format('YYYY-MM-DD')
        if (~dateStringList.indexOf(dateString)) {
          if (dateValueMap[dateString]) {
            result.push([dateString, dateValueMap[dateString]])
          } else {
            result.push([dateString, 0])
          }
        }
        temp = temp + 86400000
      }
      return result
    }

    all.forEach(item => {
      item.data = fillEmptyValue(item.data)
    })

    const fstCol = all.slice(0, Math.round(all.length / 2))
    const sndCol = all.slice(Math.round(all.length / 2))
    const data = fstCol.map((fst, index) => {
      const snd = sndCol[index]
      const ret = {
        name: fst.name,
        data: fst.data,
      }
      if (snd) {
        ret.name1 = snd.name
        ret.data1 = snd.data
      }
      return ret
    })
    const quotas = [
      {
        name: t('quota.concept'),
        value: 'name',
        disableSort: true,
        style: {
          minWidth: '110px',
        },
      },
      {
        name: t('quota.proportChange'),
        disableSort: true,
        value: 'chart',
        formatter: item => (
          <Sparkline series={[{ name: item.name, data: item.data, color: '#e91e63' }]} />
        ),
      },
      {
        name: t('quota.concept'),
        value: 'name1',
        disableSort: true,
        style: {
          minWidth: '110px',
        },
      },
      {
        name: t('quota.proportChange'),
        value: 'chart1',
        disableSort: true,
        formatter: item =>
          item.data1 ? (
            <Sparkline
              id={item.name1}
              series={[{ name: item.name1, data: item.data1, color: '#e91e63' }]}
            />
          ) : (
            ''
          ),
      },
    ]
    return {
      quotas,
      data,
    }
  }

  buildStockConceptConfig() {
    const {
      analysisResult: { concept },
    } = this.props
    return {
      ...this.buildConceptConfig(concept),
      id: 'eq-concept',
    }
  }

  buildBondConceptConfig() {
    const {
      analysisResult: { bondConcept },
    } = this.props
    return {
      ...this.buildConceptConfig(bondConcept),
      id: 'bond-concept',
    }
  }

  buildTop10(rows) {
    const data = rows.reduce((out, row) => {
      const name = row.BIZ_DATE
      out[name] = out[name] || []
      out[name].push(row)
      return out
    }, {})
    return Object.keys(data)
      .map(key => data[key].sort((fst, snd) => snd.BALANCE_RATIO - fst.BALANCE_RATIO).slice(0, 10))
      .reduce((out, items) => out.concat(items), [])
  }

  buildIndustryConfig() {
    const {
      analysisResult: {
        industry,
        product: { _syncType },
      },
    } = this.props
    return {
      isMutual: _syncType === 'mutual',
      rows: industry.filter(item => /(0630|1231)$/.test(item.BIZ_DATE)),
      percentageValue: true,
      chartType: 'area',
      valueField: 'BALANCE_RATIO',
      serieField: 'INDUSTRY',
      t: this.props.t,
    }
  }

  buildIndustryBoardConfig() {
    const {
      analysisResult: { industry, product },
    } = this.props
    const getIndustryBoard = position => {
      const board = position.INDUSTRY
      const date = position.BIZ_DATE
      let name
      if (['采掘', '有色金属'].includes(board)) {
        name = '周期上游'
      } else if (['钢铁', '化工', '公用事业', '交通运输'].includes(board)) {
        name = '周期中游'
      } else if (['建筑材料', '建筑装饰', '汽车', '机械设备'].includes(board)) {
        name = '周期下游'
      } else if (['银行', '非银金融', '房地产'].includes(board)) {
        name = '大金融'
      } else if (
        [
          '轻工制造',
          '商业贸易',
          '休闲服务',
          '家用电器',
          '纺织服装',
          '医药生物',
          '食品饮料',
          '农林牧渔',
        ].includes(board)
      ) {
        name = '消费'
      } else if (['计算机', '传媒', '通信', '电气设备', '电子'].includes(board)) {
        name = 'TMT'
      } else if (board && board.includes('(HS)')) {
        name = '港股'
      } else {
        name = '其他'
      }
      return `${name}::${date}`
    }
    const rows = map(
      groupBy(industry.filter(item => /(0630|1231)$/.test(item.BIZ_DATE)), getIndustryBoard),
      (values, name) => {
        const names = name.split('::')
        const BIZ_DATE = names[1]
        const INDUSTRY = names[0]
        return {
          BIZ_DATE,
          INDUSTRY,
          BALANCE_RATIO: sum(values.map(item => item.BALANCE_RATIO)),
        }
      },
    )
    return {
      isMutual: product._syncType === 'mutual',
      rows,
      percentageValue: true,
      chartType: 'area',
      id: 'eq-industry-board-series',
      valueField: 'BALANCE_RATIO',
      serieField: 'INDUSTRY',
      t: this.props.t,
    }
  }

  buildTop10Position(position, isStock) {
    const {
      t,
      analysisResult: { product },
    } = this.props
    const ranks = [
      t('quota.firstPlace'),
      t('quota.secondPlace'),
      t('quota.thirdPlace'),
      t('quota.fourthPlace'),
      t('quota.fifthPlace'),
      t('quota.sixthPlace'),
      t('quota.seventhPlace'),
      t('quota.eightPlace'),
      t('quota.ninthPlace'),
      t('quota.tenthPlace'),
    ]
    const quotas = ranks
      .reduce(
        (out, rank, index) => {
          const ret = {
            name: rank,
            value: `rank${index}`,
            style: {
              minWidth: '135px',
            },
            width: 136,
            textAlign: 'center',
            formatter: item =>
              item[`name${index}`]
                ? `${item[`name${index}`]} ${(item[`rank${index}`] * 100).toFixed(2)}%`
                : '-',
          }
          if (isStock && isStock === 'isStock') {
            ret.render = (text, fund) => {
              if (fund[`name${index}`]) {
                return (
                  <div style={{ textAlign: 'center' }}>
                    <AmountSeriesModal
                      productId={product._id}
                      bizDate={fund[`bizDate${index}`]}
                      code={fund[`refCode${index}`]}
                      value={fund[`amount${index}`]}
                      stockName={fund[`stockName${index}`]}
                      exchangeType={fund[`exchangeType${index}`]}
                    >
                      <Tooltip placement="top" title={t('quota.clickViewTimeSeriesChange')}>
                        <span>
                          {`${fund[`name${index}`]} ${(fund[`rank${index}`] * 100).toFixed(2)}%`}{' '}
                          <i className="fa fa-angle-right" />
                        </span>
                      </Tooltip>
                    </AmountSeriesModal>
                  </div>
                )
              }
              return <div style={{ textAlign: 'center' }}>-</div>
            }
          }
          out.push(ret)
          return out
        },
        [
          {
            name: t('quota.date'),
            value: 'date',
            fixed: 'left',
          },
        ],
      )
      .map(quota => ({ ...quota, hasSorter: true }))
    const positionByDate = position.reduce((out, item) => {
      const date = item.BIZ_DATE
      out[date] = out[date] || []
      out[date].push(item)
      return out
    }, {})
    const data = Object.keys(positionByDate)
      .sort((fst, snd) => (fst > snd ? -1 : 1))
      .map(key => {
        return positionByDate[key]
          .sort((fst, snd) => snd.BALANCE_RATIO - fst.BALANCE_RATIO)
          .reduce(
            (ret, item, index) => {
              ret[`rank${index}`] = item.BALANCE_RATIO
              ret[`name${index}`] = (item.STOCK_NAME || '').replace(/^\s+/, '').replace(/\s+$/, '')
              if (isStock) {
                ret[`amount${index}`] = item.AMOUNT
                ret[`bizDate${index}`] = item.BIZ_DATE
                ret[`refCode${index}`] = item.REF_CODE
                ret[`stockName${index}`] = item.STOCK_NAME
                ret[`exchangeType${index}`] = item.EXCHANGE_TYPE
              }
              return ret
            },
            {
              date: moment(key).format('YYYY-MM-DD'),
            },
          )
      })
    return { data, quotas }
  }

  buildPositionConfig() {
    const {
      analysisResult: { position },
    } = this.props
    return {
      ...this.buildTop10Position(position, true),
      id: 'eq-position',
    }
  }

  buildBondData(rows, field) {
    const { t } = this.props
    const sum = items => items.reduce((out, item) => out + (item.BALANCE_RATIO || 0), 0) * 100
    const groupByFilter = groupBy(rows, field)
    const result = Object.keys(groupByFilter).reduce((out, key) => {
      const groupByDate = groupBy(groupByFilter[key], 'BIZ_DATE')
      out[key === 'null' || key === 'undefined' ? t('quota.undefined') : key] = Object.keys(
        groupByDate,
      ).reduce((ret, date) => {
        ret.push([+moment(date), sum(groupByDate[date])])
        return ret
      }, [])
      return out
    }, {})
    return result
  }

  buildBondTypeConfig() {
    const {
      analysisResult: {
        bondPosition,
        product: { _syncType },
      },
      t,
    } = this.props
    return {
      id: 'bond-type',
      isMutual: _syncType === 'mutual',
      defaultFilter: {
        name: t('quota.primaryClass'),
        value: 'CLASS1',
      },
      filters: [
        {
          name: t('quota.primaryClass'),
          value: 'CLASS1',
        },
        {
          name: t('quota.secondClass'),
          value: 'CLASS2',
        },
      ],
      rows: bondPosition,
      t: t,
      percentageValue: true,
      buildChartData: (rows, field) => {
        return this.buildBondData(rows, field)
      },
    }
  }

  buildBondTermConfig() {
    const {
      analysisResult: {
        bondPosition,
        product: { _syncType },
      },
      t,
    } = this.props
    const seriesOrder = [
      t('quota.withinYear'),
      t('quota.oneThreeYears'),
      t('quota.threeFiveYears'),
      t('quota.fiveTenYears'),
      t('quota.moreTenYears'),
      t('quota.undefined'),
    ]
    return {
      seriesOrder,
      id: 'bond-term',
      isMutual: _syncType === 'mutual',
      defaultFilter: {
        name: t('quota.DURATION'),
        value: 'DURATION',
      },
      filters: [
        {
          name: t('quota.DURATION'),
          value: 'DURATION',
        },
        {
          name: t('quota.PTM_YEAR'),
          value: 'PTM_YEAR',
        },
      ],
      t: t,
      rows: bondPosition,
      percentageValue: true,
      buildChartData: (rows, field) => {
        const newRows = rows.map(item => {
          const term = item[field]
          let termType
          if (term <= 1) {
            termType = t('quota.withinYear')
          } else if (term <= 3) {
            termType = t('quota.oneThreeYears')
          } else if (term <= 5) {
            termType = t('quota.threeFiveYears')
          } else if (term <= 10) {
            termType = t('quota.fiveTenYears')
          } else {
            termType = t('quota.moreTenYears')
          }
          return { ...item, termType }
        })
        return this.buildBondData(newRows, 'termType')
      },
    }
  }

  buildBondRateConfig() {
    const {
      analysisResult: {
        bondPosition,
        product: { _syncType },
      },
      t,
    } = this.props
    const seriesOrder = [
      'Aaa',
      'AAA',
      'AAA-',
      'AA+',
      'Aa1',
      'Aa2',
      'AA',
      'AA-',
      'Aa3',
      'A1+',
      'A1',
      'A+',
      'A2',
      'A',
      'A-',
      'A3',
      'A-1+',
      'A-1',
      'A-2',
      'A-3',
      'Baa1',
      'BBB+',
      'BBB',
      'Baa2',
      'BBB-',
      'Baa3',
      'Ba1',
      'BB+',
      'BB',
      'Ba2',
      'BB-',
      'Ba3',
      'B+',
      'B1',
      'B2',
      'B',
      'B-',
      'B3',
      'Caa1',
      'CCC',
      'Caa2',
      'Caa3',
      'Ca',
      'CC',
      'C',
      'D',
      'E',
      t('quota.undefined'),
    ]
    return {
      seriesOrder,
      id: 'bond-rate',
      isMutual: _syncType === 'mutual',
      defaultFilter: {
        name: t('quota.bondRating'),
        value: 'CREDITRATING',
      },
      filters: [
        {
          name: t('quota.bondRating'),
          value: 'CREDITRATING',
        },
        {
          name: t('quota.subjectRating'),
          value: 'CREDITRATING_ZT',
        },
      ],
      t: t,
      rows: bondPosition,
      percentageValue: true,
      buildChartData: (rows, field) => {
        return this.buildBondData(rows, field)
      },
    }
  }

  buildBondStatisticsConfig() {
    const {
      analysisResult: {
        bondPosition,
        product: { _syncType },
      },
      t,
    } = this.props
    const quotas = [
      {
        name: t('quota.couponRate'),
        value: 'COUPONRATE',
        sortable: true,
        textAlign: 'right',
      },
      {
        name: t('quota.currentCouponRate'),
        value: 'COUPONRATE_CUR',
        sortable: true,
        textAlign: 'right',
      },
      {
        name: t('quota.yieldToMaturity'),
        value: 'YTM',
        sortable: true,
        textAlign: 'right',
      },
      {
        name: t('quota.DURATION'),
        value: 'DURATION',
        sortable: true,
        textAlign: 'right',
      },
      {
        name: t('quota.remainingPeriod'),
        value: 'PTM_YEAR',
        sortable: true,
        textAlign: 'right',
      },
    ]
    return {
      rows: bondPosition,
      isMutual: _syncType === 'mutual',
      chartType: 'line',
      id: 'bond-statistics',
      filterQuota: {
        name: t('quota.chooseCategory'),
        value: 'CLASS1',
      },
      t: t,
      buildChartData: rows => {
        const groupByDate = groupBy(rows, 'BIZ_DATE')
        const dates = Object.keys(groupByDate)
        const sum = items => items.reduce((out, item) => out + (item || 0), 0)
        return quotas.reduce((out, quota) => {
          out[quota.name] = dates.map(date => {
            const balanceList = groupByDate[date].map(item => item.BALANCE)
            const balance = sum(balanceList)
            const values = groupByDate[date].map(
              (item, index) => item[quota.value] * balanceList[index],
            )
            return [+moment(date), sum(values) / balance]
          })
          return out
        }, {})
      },
    }
  }

  buildBondIssuerConfig() {
    const {
      analysisResult: {
        bondPosition,
        product: { _syncType },
      },
    } = this.props
    return {
      isMutual: _syncType === 'mutual',
      rows: bondPosition,
      percentageValue: true,
      id: 'bond-issuer',
      t: this.props.t,
      valueField: 'BALANCE_RATIO',
      serieField: 'ISSUER_CLASS2',
    }
  }

  buildBondPositionConfig() {
    const {
      analysisResult: { bondPosition },
    } = this.props
    return {
      ...this.buildTop10Position(bondPosition),
      id: 'bond-position',
    }
  }

  buildBondPositionChangeConfig() {
    const {
      analysisResult: { bondPosition, position, product },
    } = this.props
    return { bondPosition, position, productId: product._id }
  }

  buildVarsConfig(customConfig = {}) {
    const {
      analysisResult: {
        vars,
        product: { _syncType },
      },
      t,
    } = this.props
    const quotas = [
      {
        name: 'VaR_95',
        value: 'VAR_95',
      },
      {
        name: 'VaR_99',
        value: 'VAR_99',
      },
    ]
    return {
      isMutual: _syncType === 'mutual',
      rows: vars,
      chartType: 'line',
      id: 'fund-var',
      yAxis: [
        {
          title: {
            text: 'VAR',
          },
          labels: {
            format: '{value}%',
          },
        },
      ],
      yAxisSeriesMap: {
        [`${t('quota.var95')}`]: 0,
        [`${t('quota.var99')}`]: 0,
      },
      percentageValue: true,
      buildChartData: rows => {
        return quotas.reduce((out, quota) => {
          out[quota.name] = rows
            .map(item => [+moment(item.BIZ_DATE), item[quota.value] * 100])
            .sort((fst, snd) => fst[0] - snd[0])
          return out
        }, {})
      },
      t: t,
      ...customConfig,
    }
  }

  buildNetAssets(customConfig = {}) {
    const {
      analysisResult: {
        netAssets,
        product: { _syncType },
      },
      t,
    } = this.props
    const quotas = [
      {
        name: t('quota.NET_ASSET'),
        value: 'NET_ASSET',
      },
    ]
    return {
      isMutual: _syncType === 'mutual',
      isAssetStr: true,
      rows: netAssets,
      chartType: 'line',
      id: 'fund-netAssets',
      yAxis: [
        {
          labels: {
            format: '{value}',
          },
        },
      ],
      yAxisSeriesMap: {
        [`${t('settings.netAsset')}`]: 0,
      },
      buildChartData: rows => {
        return quotas.reduce((out, quota) => {
          out[quota.name] = rows
            .map(item => [+moment(item.BIZ_DATE), item[quota.value]])
            .filter(item => item[1] !== null)
            .sort((fst, snd) => fst[0] - snd[0])
          return out
        }, {})
      },
      t: t,
      ...customConfig,
    }
  }

  buildPeConfig(customConfig = {}) {
    const {
      analysisResult: {
        peSeries,
        product: { _syncType },
      },
      t,
    } = this.props
    const quotas = [
      {
        name: t('quota.peVal'),
        value: 'PE_TTM',
      },
    ]
    return {
      isMutual: _syncType === 'mutual',
      rows: peSeries,
      chartType: 'line',
      buildChartData: rows => {
        return quotas.reduce((out, quota) => {
          out[quota.name] = rows
            .map(item => [+moment(item.BIZ_DATE), item[quota.value]])
            .sort((fst, snd) => fst[0] - snd[0])
          return out
        }, {})
      },
      t: t,
      ...customConfig,
    }
  }

  buildFutTypeConfig() {
    const {
      analysisResult: { futType },
    } = this.props
    return {
      rows: futType,
      chartType: 'line',
      id: 'fut-type',
      valueField: 'BALANCE_NET',
      serieField: 'STD_NAME',
    }
  }
}
