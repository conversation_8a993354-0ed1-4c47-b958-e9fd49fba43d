import React from 'react'
import Highcharts from 'highcharts/highstock'
import {
  FUND_ASSET_STRUCTUR,
  FUND_EQ_CONCEPT,
  FUND_EQ_POSITION,
  FUND_FUT_TYPE,
  FUND_FUT_POSITION,
  BOND_CONCEPT,
  BOND_REGIONAL_DISTRIBUTION,
  BOND_POSITION,
  BOND_CREDIT_RATING,
  BOND_TERM_DISTRIBUTION,
  BOND_STATISTICS,
  BOND_ISSURE_DISTRIBUTION,
  FUND_POSITION,
  OPTION_POSITION,
} from './quotas'
import merge from 'lodash/merge'
import cloneDeep from 'lodash/cloneDeep'
import groupBy from 'lodash/groupBy'
import sum from 'lodash/sum'
import DataTable from '@/components/DataTable'
import AmountSeriesModal from './components/AmountSeriesModal'
import thousandFormatter from '@/utils/thousandFormatter'
import renderFundQuota from '@/utils/renderFundQuota'
import transformAssetStructure from '@/utils/transformAssetStructure'
import styles from './style.less'
import { Tooltip, Popover } from 'antd'

const generateChartBaseConfig = (type: string): object => {
  return { chart: { type } }
}

export default class PositionSeriesModel {
  props: {
    analysisResult: {
      bondConcept: any;
      bondPosition: any;
      bondRegionalDistribution: any;
      concept: any;
      futuresPosition: any;
      industry: any;
      optionPosition: any;
      position: any;
      product: any;
      structur: any;
      summary: any;
      valuation: any;
    };
    t: any;
  };

  constructor(props: any) {
    this.props = props
  }

  buildAssetStructureConfig() {
    const {
      analysisResult: { structur, summary },
      t,
    } = this.props
    const rows = transformAssetStructure(structur, summary)
    const quotas = FUND_ASSET_STRUCTUR
    const data = rows.concat(
      quotas.reduce(
        (out, quota) => {
          if (quota.value === 'ASSET_TYPE') {
            out.ASSET_TYPE = t('quota.total')
            return out
          }
          out[quota.value] = sum(rows.map(row => row[quota.value]))
          return out
        },
        { isSum: true },
      ),
    )
    const hasNegative = rows.some(row => row.RATIO < 0)
    let options = {
      chart: {
        type: hasNegative ? 'column' : 'pie',
        height: '300',
      },
      series: [
        {
          name: t('quota.proportion'),
          data: rows.map((row, idx) => ({
            name: `${row.ASSET_TYPE}`,
            y: row.RATIO * 100,
            sliced: !idx,
            selected: !idx,
          })),
        },
      ],
    }
    if (hasNegative) {
      options = {
        ...options,
        xAxis: {
          categories: [t('quota.proportion')],
        },
        tooltip: {
          shared: true,
          pointFormat: '{series.name}: <b>{point.y:.1f}%</b><br/>',
        },
        series: rows
          .filter(row => row.ASSET_TYPE !== t('common.total'))
          .map(row => ({
            stacking: 'normal',
            name: row.ASSET_TYPE,
            data: [row.RATIO * 100],
          })),
      }
    }
    return {
      quotas,
      data,
      charts: [{ config: options }],
    }
  }

  buildConceptConfig(customConfig = {}, customChartConfig = {}) {
    const {
      analysisResult: { concept },
      t,
    } = this.props
    const rows = concept.sort((fst, snd) => snd.RATIO - fst.RATIO).slice(0, 10)
    const baseConfig = generateChartBaseConfig('bar')
    const chartConfig = {
      ...baseConfig,
      xAxis: {
        categories: rows.map(item => item.CONCEPT_NAME),
      },
      series: [
        {
          name: t('quota.proportion'),
          data: rows.map(row => row.RATIO * 100),
        },
      ],
    }
    const quotas = FUND_EQ_CONCEPT.map(quota => {
      if (quota.value === 'RATIO') {
        quota.filterDropdown = t('quota.stockRatioTooltip')
      }
      if (quota.value === 'RANGE') {
        quota.filterDropdown = (
          <p style={{ whiteSpace: 'pre-wrap' }}>{t('quota.conceptRangeTooltip')}</p>
        )
      }
      if (quota.value === 'CONCEPT_STOCK_NAME') {
        quota.render = this.getListDetailQuotaRender('CONCEPT_STOCK_NAME')
      }
      return quota
    })
    return {
      data: rows,
      quotas,
      charts: [
        {
          id: 'eq-concept',
          config: merge(chartConfig, customChartConfig),
        },
      ],
      id: 'eq-concept',
      ...customConfig,
    }
  }

  buildPositionConfig() {
    const {
      analysisResult: { position, summary },
      t,
    } = this.props
    const rows = position
      .sort((fst, snd) => snd.BALANCE_RATIO - fst.BALANCE_RATIO)
      .map(item => ({
        ...item,
        ASSET_RATIO: item.BALANCE / summary.ASSET,
        NET_ASSET_RATIO: item.BALANCE / summary.NET_ASSET,
      }))
    const quotas = FUND_EQ_POSITION.map(item => {
      if (item.value === 'AMOUNT') {
        item.render = (text, fund) => {
          const value = renderFundQuota({ value: 'AMOUNT', format: 'commaNumber' }, fund)
          return fund.isSum ? (
            <span>{value}</span>
          ) : (
            <div>
              <AmountSeriesModal
                bizDate={fund.BIZ_DATE}
                code={fund.REF_CODE}
                value={value}
                stockName={fund.STOCK_NAME}
                exchangeType={fund.EXCHANGE_TYPE}
              >
                <Tooltip id={'amount-series-AMOUNT'} title={t('quota.positionSeriesTooltip')}>
                  <span style={{ cursor: 'pointer' }}>
                    {value} <i className="fa fa-angle-right" />
                  </span>
                </Tooltip>
              </AmountSeriesModal>
            </div>
          )
        }
      }
      if (item.value === 'BALANCE_GROWTH_RATE') {
        item.tooltip = t('quota.balanceGrowthTooltip')
      }
      if (item.value === 'BALANCE_RATIO') {
        item.filterDropdown = t('quota.stockRatioTooltip')
      }
      return item
    })
    return {
      rows,
      quotas,
    }
  }

  buildFuturesTypeConfig(customConfig = {}) {
    const {
      analysisResult: { futuresType },
      t,
    } = this.props
    const buildChartConfig = (title, valueField, opposite) => {
      const baseConfig = generateChartBaseConfig('bar')
      return {
        ...baseConfig,
        plotOptions: {
          bar: {
            dataLabels: {
              enabled: true,
            },
          },
        },
        xAxis: {
          opposite,
          categories: futuresType.map(item => item.STD_NAME),
        },
        series: [
          {
            name: title,
            data: futuresType
              .sort((fst, snd) => Math.abs(snd[valueField]) - Math.abs(fst[valueField]))
              .map(row => row[valueField]),
          },
        ],
      }
    }
    const rows = futuresType.sort(
      (fst, snd) => Math.abs(snd.BALANCE_NET) - Math.abs(fst.BALANCE_NET),
    )
    const finalRows = rows.concat(
      FUND_FUT_TYPE.reduce(
        (out, quota) => {
          if (quota.value === 'STD_CODE') {
            out.STD_CODE = t('quota.total')
            return out
          }
          if (['STD_NAME'].includes(quota.value)) {
            out[quota.value] = '-'
            return out
          }
          out[quota.value] = rows.reduce((sum, row) => sum + (row[quota.value] || 0), 0)
          return out
        },
        { isSum: true },
      ),
    )
    return {
      charts: [
        {
          id: 'fut-type-BALANCE_NET',
          config: buildChartConfig(t('quota.netExposure'), 'BALANCE_NET'),
        },
        {
          id: 'fut-type-MARGIN_REQ',
          config: buildChartConfig(t('quota.occupationMargin'), 'MARGIN_REQ'),
        },
      ],
      id: 'fut-type',
      quotas: FUND_FUT_TYPE,
      data: finalRows,
      ...customConfig,
    }
  }

  buildFuturesPositionConfig(customConfig = {}) {
    const {
      analysisResult: { futuresPosition },
      t,
    } = this.props
    const rows = futuresPosition.sort(
      (fst, snd) => Math.abs(snd.BALANCE_NET) - Math.abs(fst.BALANCE_NET),
    )
    const finalRows = rows.concat(
      FUND_FUT_POSITION.reduce(
        (out, quota) => {
          if (quota.value === 'EXCHANGE_TYPE') {
            out.EXCHANGE_TYPE = t('quota.total')
            return out
          }
          if (
            [
              'STOCK_CODE',
              'STOCK_NAME',
              'DAYS',
              'MULTIPLIER',
              'PRICE',
              'BASIS',
              'ANNUAL_BASIS',
            ].includes(quota.value)
          ) {
            out[quota.value] = '-'
            return out
          }
          out[quota.value] = rows.reduce((sum, row) => sum + (row[quota.value] || 0), 0)
          return out
        },
        { isSum: true },
      ),
    )
    return {
      data: finalRows,
      id: 'fut-position',
      charts: [],
      quotas: FUND_FUT_POSITION,
      ...customConfig,
    }
  }

  buildBondPositionConfig(customConfig = {}) {
    const {
      analysisResult: { bondPosition },
      t,
    } = this.props
    const rows = cloneDeep(bondPosition).sort((fst, snd) => snd.BALANCE - fst.BALANCE)
    const quotas = BOND_POSITION.map(quota => {
      if (quota.value === 'BALANCE_GROWTH_RATE') {
        quota.tooltip = t('quota.balanceGrowthTooltip')
      }
      return quota
    })
    return {
      data: rows,
      quotas,
      id: 'bond-position',
      charts: [],
      ...customConfig,
    }
  }

  buildOptionConfig(customConfig = {}) {
    const {
      analysisResult: { optionPosition },
    } = this.props
    const rows = optionPosition
    return {
      data: rows,
      id: 'option-position',
      charts: [],
      quotas: OPTION_POSITION,
      ...customConfig,
    }
  }

  buildBondCreditRatingConfig() {
    const {
      analysisResult: { bondPosition },
      t,
    } = this.props
    return {
      t: t,
      rows: bondPosition,
      quotas: BOND_CREDIT_RATING,
    }
  }

  buildBondRegionalDistribution(customConfig = {}) {
    const {
      analysisResult: { bondRegionalDistribution },
      t,
    } = this.props
    const sum = items => items.reduce((out, item) => out + (item || 0), 0)
    const result = groupBy(bondRegionalDistribution, 'PROVINCE')
    const data = Object.keys(result)
      .map(key => {
        const province = key.replace(/(省|自治区|维吾尔自治区|壮族自治区|回族自治区)$/, '')
        const resultByCity = groupBy(result[key], 'CITY')
        const cities = Object.keys(resultByCity).map(cityKey => {
          return {
            CITY: cityKey,
            BALANCE: sum(resultByCity[cityKey].map(cityItem => cityItem.BALANCE)),
          }
        })
        result[province] = cities.sort((fst, snd) => snd.BALANCE - fst.BALANCE)

        return {
          'cn-name': province,
          value: sum(result[key].map(item => item.BALANCE)),
        }
      })
      .filter(item => item['cn-name'] !== 'undefined' && item['cn-name'] !== 'null')
    const mapData = Highcharts.geojson(Highcharts.maps['countries/cn/custom/cn-all'])
    const baseConfig = generateChartBaseConfig('')
    const config = {
      ...baseConfig,
      chart: {
        height: 350,
      },
      mapNavigation: {
        enabled: false,
        enableMouseWheelZoom: false,
      },
      colorAxis: {
        labels: {
          format: '{value}',
        },
      },
      legend: {
        enabled: false,
        layout: 'vertical',
        align: 'right',
        verticalAlign: 'middle',
        x: 0,
      },
      tooltip: {
        formatter: function formatter(): string {
          const province = this.point.properties['cn-name']
          let html = `${province} <br/>`
          result[province].forEach(item => {
            html += `${item.CITY}：${thousandFormatter(item.BALANCE)} <br/>`
          })
          return html
        },
      },
      series: [
        {
          mapData: mapData,
          data: data,
          joinBy: 'cn-name',
          name: t('quota.napOfChina'),
          dataLabels: {
            enabled: true,
            formatter: function formatter() {
              return this.point.properties['cn-name']
            },
            style: {
              fontSize: '10px',
            },
          },
        },
      ],
      ...customConfig,
    }
    const id = 'bond-regional-distribution'
    return {
      id,
      data: bondRegionalDistribution,
      quotas: BOND_REGIONAL_DISTRIBUTION,
      charts: [
        {
          id,
          config,
          constructorType: 'mapChart',
        },
      ],
      ...customConfig,
    }
  }

  buildFundPositionConfig() {
    const {
      analysisResult: { fundPosition },
      t,
    } = this.props
    const finalRows = fundPosition.concat(
      FUND_POSITION.reduce(
        (out, quota) => {
          if (quota.value === 'STOCK_CODE') {
            out.STOCK_CODE = t('quota.total')
            return out
          }
          if (['STOCK_NAME', 'MANAGER', 'FUND_TYPE'].includes(quota.value)) {
            out[quota.value] = '-'
            return out
          }
          out[quota.value] = fundPosition.reduce((sum, row) => sum + (row[quota.value] || 0), 0)
          return out
        },
        { isSum: true },
      ),
    )
    const fundPositionMap = [...new Set(fundPosition.map(item => item.FUND_TYPE))]
    const finalTypedRows = fundPositionMap.map(item => {
      const ret = { FUND_TYPE: item }
      const filteredPosition = fundPosition.filter(position => position.FUND_TYPE === item)
      ret.BALANCE = filteredPosition.reduce((sum, pos) => sum + (pos.BALANCE || 0), 0)
      ret.BALANCE_RATIO = filteredPosition.reduce((sum, pos) => sum + (pos.BALANCE_RATIO || 0), 0)
      return ret
    })
    const options = {
      chart: {
        type: 'pie',
        height: '300',
      },
      series: [
        {
          name: t('quota.proportion'),
          data: finalTypedRows.map((row, idx) => ({
            name: `${row.FUND_TYPE}`,
            y: row.BALANCE_RATIO,
            sliced: !idx,
            selected: !idx,
          })),
        },
      ],
    }
    return {
      quotas: FUND_POSITION,
      data: finalRows,
      charts: [{ config: options }],
    }
  }

  buildBondTermDistributionConfig(customConfig = {}) {
    const {
      analysisResult: { bondPosition },
    } = this.props
    const getTimeRangeFilters = valueField => {
      return [
        {
          name: 'withinOne',
          filter: item => item[valueField] <= 1,
        },
        {
          name: 'oneToThree',
          filter: item => item[valueField] > 1 && item[valueField] <= 3,
        },
        {
          name: 'threeToFive',
          filter: item => item[valueField] > 3 && item[valueField] <= 5,
        },
        {
          name: 'fiveToTen',
          filter: item => item[valueField] > 5 && item[valueField] <= 10,
        },
        {
          name: 'overTen',
          filter: item => item[valueField] > 10,
        },
      ]
    }
    return {
      quotas: BOND_TERM_DISTRIBUTION,
      rows: cloneDeep(bondPosition),
      charts: [],
      getTimeRangeFilters,
      t: this.props.t,
      ...customConfig,
    }
  }

  buildBondStatisticsConfig(customConfig = {}) {
    const {
      analysisResult: { bondPosition },
    } = this.props
    const rows = cloneDeep(bondPosition)
    return {
      rows,
      t: this.props.t,
      quotas: BOND_STATISTICS,
      charts: [],
      ...customConfig,
    }
  }

  buildBondConceptConfig(customConfig = {}, customChartConfig = {}) {
    const {
      analysisResult: { bondConcept },
      t,
    } = this.props
    const rows = bondConcept.sort((fst, snd) => snd.RATIO - fst.RATIO).slice(0, 10)
    const baseConfig = generateChartBaseConfig('bar')
    const chartConfig = {
      ...baseConfig,
      xAxis: {
        categories: rows.map(item => item.CONCEPT_NAME),
      },
      series: [
        {
          name: t('quota.proportion'),
          data: rows.map(row => row.RATIO * 100),
        },
      ],
    }
    const quotas = BOND_CONCEPT.map(quota => {
      if (quota.value === 'CONCEPT_BOND_NAME') {
        quota.render = this.getListDetailQuotaRender('CONCEPT_BOND_NAME')
      }
      return quota
    })
    return {
      quotas,
      data: bondConcept,
      id: 'bond-concept',
      charts: [
        {
          id: 'bond-concept',
          config: merge(chartConfig, customChartConfig),
        },
      ],
      ...customConfig,
    }
  }

  buildBondIssuerDistribution() {
    const {
      analysisResult: { bondPosition },
    } = this.props
    const quotas = BOND_ISSURE_DISTRIBUTION.map(quota => {
      if (quota.value === 'BOND_LIST') {
        quota.render = this.getListDetailQuotaRender('BOND_LIST')
      }
      return quota
    })
    return {
      quotas,
      rows: cloneDeep(bondPosition),
      charts: [],
      t: this.props.t,
    }
  }

  getListDetailQuotaRender(valueField) {
    const { t } = this.props
    return (text, fund) => {
      const value = fund[valueField]
      return (
        <Popover
          trigger="click"
          placement="left"
          content={
            <DataTable
              quotas={[
                {
                  name: t('quota.bondName'),
                  width: 90,
                  value: 'name1',
                  format: 'text',
                },
                {
                  name: t('quota.bondName'),
                  width: 90,
                  value: 'name2',
                  format: 'text',
                },
                {
                  name: t('quota.bondName'),
                  width: 90,
                  value: 'name3',
                  format: 'text',
                },
              ]}
              data={Array(Math.ceil(value.split(',').length / 3))
                .fill(0)
                .map((item, key) => {
                  const arr = value.replace(/,$/, '').split(',')
                  return {
                    name1: arr[3 * key],
                    name2: arr[3 * key + 1],
                    name3: arr[3 * key + 2],
                  }
                })}
              showHeader={false}
            />
          }
        >
          <Tooltip id={`tooltip-${valueField}`} title={t('quota.conceptListTooltip')}>
            <div className={styles.threeColCell}>{value}</div>
          </Tooltip>
        </Popover>
      )
    }
  }
}
