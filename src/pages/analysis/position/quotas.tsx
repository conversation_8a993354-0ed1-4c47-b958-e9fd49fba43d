import React from 'react'
import renderFundQuota from '@/utils/renderFundQuota'

const FUND_ASSET_STRUCTUR = [
  {
    value: 'ASSET_TYPE',
    format: 'text',
    width: 84,
  },
  {
    value: 'BALANCE_LONG',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 150,
  },
  {
    value: 'BALANCE_SHORT',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 150,
  },
  {
    value: 'BALANCE_NET',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 150,
  },
  {
    value: 'RATIO',
    format: 'absPercentage',
    align: 'right',
    hasSorter: true,
    width: 100,
  },
]

const FUND_EQ_CONCEPT = [
  {
    value: 'CONCEPT_NAME',
    format: 'text',
  },
  {
    value: 'RATIO',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'RANGE',
    align: 'right',
    formatter: (fund: any, quota: any) => `Top ${Number(fund[quota]) * 10}%`,
  },
  {
    value: 'CONCEPT_CNT',
    format: 'text',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'CONCEPT_STOCK_NAME',
    format: 'text',
    width: 500,
  },
]

const FUND_EQ_INDUSTRY = [
  {
    value: 'RANK',
    format: 'text',
    align: 'center',
    width: 50,
  },
  {
    value: 'INDUSTRY',
    format: 'text',
    render: (text: any, fund: any) => {
      if (fund.isSum) {
        return <div>-</div>
      }
      return fund.INDUSTRY
    },
    width: 200,
  },
  {
    value: 'BALANCE',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 150,
  },
  {
    value: 'BALANCE_RATIO',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
    width: 150,
  },
  {
    value: 'BASE_RATIO',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
    width: 150,
  },
  {
    value: 'DIFF_RATIO',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
    width: 150,
  },
]

const FUND_EQ_POSITION = [
  {
    value: 'RANK',
    format: 'text',
    align: 'center',
    width: 50,
    fixed: 'left',
  },
  {
    value: 'STOCK_NAME',
    format: 'text',
    width: 80,
    fixed: 'left',
  },
  {
    value: 'STOCK_CODE',
    format: 'text',
    hasSorter: true,
    width: 95,
    fixed: 'left',
  },
  {
    value: 'EXCHANGE_TYPE',
    hasSorter: true,
    translateWithValue: true,
    width: 95,
  },
  {
    value: 'INDUSTRY',
    format: 'text',
    width: 80,
  },
  {
    value: 'AMOUNT',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'BALANCE',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
  },
  // {
  //   value: 'BALANCE_GROWTH_RATE',
  //   format: 'percentage',
  //   align: 'right',
  //   sortable: true,
  //   render: (text: any, fund: any) => {
  //     return fund.isSum ? (
  //       <div>-</div>
  //     ) : (
  //       <span>
  //         {renderFundQuota(
  //           { value: 'BALANCE_GROWTH_RATE', format: 'percentage' },
  //           fund
  //         )}
  //       </span>
  //     )
  //   }
  // },
  // {
  //   value: 'AMOUNT_GROWTH_RATE',
  //   format: 'percentage',
  //   align: 'right',
  //   sortable: true,
  //   render: (text: any, fund: any) => {
  //     return fund.isSum ? (
  //       <div>-</div>
  //     ) : (
  //       <span>
  //         {renderFundQuota(
  //           { value: 'AMOUNT_GROWTH_RATE', format: 'percentage' },
  //           fund
  //         )}
  //       </span>
  //     )
  //   }
  // },
  {
    value: 'BALANCE_RATIO',
    format: 'valPercentage',
    hasSorter: true,
    align: 'right',
    width: 90,
  },
  {
    value: 'ASSET_RATIO',
    format: 'valPercentage',
    hasSorter: true,
    align: 'right',
    render: (text: any, fund: any) => {
      return fund.isSum ? (
        <div>-</div>
      ) : (
        <span>{renderFundQuota({ value: 'ASSET_RATIO', format: 'valPercentage' }, fund)}</span>
      )
    },
  },
  {
    value: 'NET_ASSET_RATIO',
    format: 'valPercentage',
    hasSorter: true,
    align: 'right',
    render: (text: any, fund: any) => {
      return fund.isSum ? (
        <div>-</div>
      ) : (
        <span>{renderFundQuota({ value: 'NET_ASSET_RATIO', format: 'valPercentage' }, fund)}</span>
      )
    },
  },
  {
    value: 'PE_TTM',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'PB_MRQ',
    align: 'right',
    hasSorter: true,
    width: 120,
  },
  {
    value: 'ROE',
    align: 'right',
    hasSorter: true,
    render: (text: any, fund: any) => {
      return fund.isSum ? (
        <div>-</div>
      ) : (
        <span>{renderFundQuota({ value: 'ROE', format: 'commaNumber' }, fund)}</span>
      )
    },
  },
]

const FUND_FUT_TYPE = [
  {
    value: 'STD_CODE',
    format: 'text',
  },
  {
    value: 'STD_NAME',
    format: 'text',
  },
  {
    value: 'BALANCE_L',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'BALANCE_S',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'BALANCE_NET',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'MARGIN_REQ',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
]

const FUND_FUT_POSITION = [
  {
    value: 'EXCHANGE_TYPE',
    sortable: true,
    translateWithValue: true,
  },
  {
    value: 'STOCK_CODE',
    format: 'text',
    sortable: true,
  },
  {
    value: 'STOCK_NAME',
    format: 'text',
  },
  {
    value: 'DAYS',
    format: 'text',
    align: 'right',
    sortable: true,
  },
  {
    value: 'MULTIPLIER',
    format: 'text',
    sortable: true,
  },
  {
    value: 'PRICE',
    format: 'commaNumber',
    sortable: true,
    align: 'right',
    render: (text: any, fund: any) => {
      return fund.isSum ? (
        <div>-</div>
      ) : (
        <span>{renderFundQuota({ value: 'PRICE', format: 'commaNumber' }, fund)}</span>
      )
    },
  },
  {
    value: 'AMOUNT_L',
    format: 'text',
    sortable: true,
  },
  {
    value: 'AMOUNT_S',
    format: 'text',
    sortable: true,
  },
  {
    value: 'BALANCE_L',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'BALANCE_S',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'BALANCE_NET',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'MARGIN_RATIO',
    align: 'right',
    sortable: true,
  },
  {
    value: 'MARGIN_REQ',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'BASIS',
    format: 'valPercentage',
    align: 'right',
    sortable: true,
    render: (text: any, fund: any) => {
      return fund.isSum ? (
        <div>-</div>
      ) : (
        <span>{renderFundQuota({ value: 'BASIS', format: 'valPercentage' }, fund)}</span>
      )
    },
  },
  {
    value: 'ANNUAL_BASIS',
    format: 'valPercentage',
    align: 'right',
    sortable: true,
    render: (text: any, fund: any) => {
      return fund.isSum ? (
        <div>-</div>
      ) : (
        <span>{renderFundQuota({ value: 'ANNUAL_BASIS', format: 'valPercentage' }, fund)}</span>
      )
    },
  },
]

const OPTION_POSITION = [
  {
    value: 'EXCHANGE_TYPE',
    format: 'text',
    sortable: true,
  },
  {
    value: 'STOCK_CODE',
    format: 'text',
    sortable: true,
  },
  {
    value: 'STOCK_NAME',
    format: 'text',
    sortable: true,
  },
  {
    value: 'RO',
    format: 'text',
    sortable: true,
  },
  {
    value: 'CALLPUT',
    format: 'text',
    sortable: true,
  },
  {
    value: 'AMOUNT',
    format: 'commaNumber',
    sortable: true,
  },
  {
    value: 'BALANCE',
    format: 'commaNumber',
    sortable: true,
  },
  {
    value: 'DELTA_BALANCE',
    format: 'commaNumber',
    sortable: true,
  },
  {
    value: 'STRIKE_PRICE',
    sortable: true,
    format: 'commaNumber',
  },
  {
    value: 'END_DATE',
    sortable: true,
    format: 'text',
  },
  {
    value: 'DELTA',
    sortable: true,
    format: 'commaNumber',
  },
  {
    value: 'THETA',
    sortable: true,
    fomrat: 'commaNumber',
  },
  {
    sortable: true,
    fomrat: 'commaNumber',
    value: 'GAMMA',
  },
  {
    sortable: true,
    fomrat: 'commaNumber',
    value: 'VEGA',
  },
  {
    sortable: true,
    fomrat: 'commaNumber',
    value: 'RHO',
  },
]

const BOND_CONCEPT = [
  {
    value: 'CONCEPT_NAME',
    format: 'text',
    hasSorter: true,
  },
  {
    value: 'RATIO',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'CONCEPT_CNT',
    format: 'integer',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'CONCEPT_BOND_NAME',
    format: 'text',
    width: 500,
  },
]

const FUND_POSITION = [
  {
    value: 'STOCK_CODE',
    format: 'text',
    sortable: true,
  },
  {
    value: 'STOCK_NAME',
    format: 'text',
    sortable: true,
  },
  {
    value: 'MANAGER',
    format: 'text',
    sortable: true,
  },
  {
    value: 'FUND_TYPE',
    format: 'text',
    sortable: true,
  },
  {
    value: 'AMOUNT',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'BALANCE',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'BALANCE_RATIO',
    format: 'valPercentage',
    align: 'right',
    sortable: true,
  },
]

const FUND_TYPED_POSITION = [
  {
    value: 'FUND_TYPE',
    format: 'text',
  },
  {
    value: 'BALANCE',
    align: 'right',
    format: 'commaNumber',
  },
  {
    value: 'BALANCE_RATIO',
    align: 'right',
    format: 'valPercentage',
  },
]

const BOND_CREDIT_RATING = [
  {
    format: 'text',
    value: 'STOCK_CODE',
    sortable: true,
    rowSpan: 2,
    width: 95,
  },
  {
    format: 'text',
    value: 'STOCK_NAME',
    sortable: true,
    rowSpan: 2,
    width: 110,
  },
  {
    colSpan: 3,
    hasSorter: false,
    value: 'BOND_RATING',
    children: [
      {
        format: 'text',
        value: 'CREDITRATING',
        sortable: true,
        width: 90,
      },
      {
        format: 'text',
        value: 'CREDIT_DATE',
        sortable: true,
        width: 90,
      },
      {
        format: 'text',
        value: 'CREDITRATING_CHANGE',
        sortable: true,
        width: 125,
      },
      {
        format: 'text',
        value: 'CREDITRATING_AGENCY',
        sortable: true,
        width: 170,
      },
    ],
  },
  {
    colSpan: 3,
    hasSorter: false,
    value: 'BOND_ZT_RATING',
    children: [
      {
        format: 'text',
        value: 'CREDITRATING_ZT',
        sortable: true,
        width: 90,
      },
      {
        format: 'text',
        value: 'CREDIT_DATE_ZT',
        sortable: true,
        width: 90,
      },
      {
        format: 'text',
        value: 'CREDITRATING_CHANGE_ZT',
        sortable: true,
        width: 125,
      },
      {
        format: 'text',
        value: 'CREDITRATING_AGENCY_ZT',
        sortable: true,
        width: 170,
      },
    ],
  },
  {
    format: 'commaNumber',
    value: 'BALANCE',
    sortable: true,
    align: 'right',
    rowSpan: 2,
    width: 95,
  },
  {
    format: 'valPercentage',
    value: 'BALANCE_RATIO',
    sortable: true,
    align: 'right',
    rowSpan: 2,
    width: 95,
  },
]

const BOND_TERM_DISTRIBUTION = [
  {
    value: 'category',
    format: 'text',
    hasSorter: true,
    fixed: 'left',
  },
  {
    value: 'withinOne',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'oneToThree',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'threeToFive',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'fiveToTen',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'overTen',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'sum',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
    width: 90,
  },
]

const BOND_STATISTICS = [
  {
    value: 'category',
    format: 'text',
    hasSorter: true,
    fixed: 'left',
  },
  {
    value: 'COUPONRATE',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 165,
  },
  {
    value: 'YTM',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 145,
  },
  {
    value: 'DURATION',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 65,
  },
  {
    value: 'DURATION_IF_EXE',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 95,
  },
  {
    value: 'PTM_YEAR',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 95,
  },
  {
    value: 'TERM_IF_EXE',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 130,
  },
  {
    value: 'CONVEXITY',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 65,
  },
  {
    value: 'BALANCE',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
    width: 120,
  },
  {
    value: 'BALANCE_RATIO',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
    width: 100,
  },
  // {
  //   value: 'ASSET_RATIO',
  //   format: 'valPercentage',
  //   align: 'right',
  //   hasSorter: true,
  //   width: 120,
  // },
  // {
  //   value: 'NET_ASSET_RATIO',
  //   format: 'valPercentage',
  //   align: 'right',
  //   hasSorter: true,
  //   width: 120,
  // },
]

const BOND_REGIONAL_DISTRIBUTION = [
  {
    format: 'text',
    value: 'STOCK_CODE',
    hasSorter: true,
    width: 110,
  },
  {
    format: 'text',
    value: 'STOCK_NAME',
    hasSorter: true,
  },
  {
    format: 'commaNumber',
    value: 'BALANCE',
    align: 'right',
    hasSorter: true,
  },
  {
    format: 'valPercentage',
    value: 'BALANCE_RATIO',
    align: 'right',
    hasSorter: true,
    width: 110,
  },
  {
    value: 'ISSUER',
    format: 'text',
  },
  {
    value: 'PROVINCE',
    format: 'text',
    hasSorter: true,
    width: 80,
  },
  {
    value: 'CITY',
    format: 'text',
    hasSorter: true,
    width: 80,
  },
]

const BOND_ISSURE_DISTRIBUTION = [
  {
    value: 'category',
    format: 'text',
    hasSorter: true,
  },
  {
    value: 'BALANCE',
    format: 'commaNumber',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'BALANCE_RATIO',
    format: 'valPercentage',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'BOND_COUNT',
    format: 'integer',
    align: 'right',
    hasSorter: true,
  },
  {
    value: 'BOND_LIST',
    width: 600,
  },
]

const BOND_POSITION = [
  {
    format: 'text',
    value: 'STOCK_NAME',
    sortable: true,
    fixed: 'left',
  },
  {
    format: 'text',
    value: 'STOCK_CODE',
    sortable: true,
    fixed: 'left',
  },
  {
    value: 'EXCHANGE_TYPE',
    sortable: true,
    translateWithValue: true,
    fixed: 'left',
  },
  {
    value: 'AMOUNT',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'PRICE',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'UNIT_COST',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'BALANCE',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  // {
  //   value: 'BALANCE_GROWTH_RATE',
  //   format: 'percentage',
  //   align: 'right',
  //   sortable: true,
  //   render: (text: any, fund: any) => {
  //     return fund.isSum ? (
  //       <div>-</div>
  //     ) : (
  //       <span>
  //         {renderFundQuota(
  //           { value: 'BALANCE_GROWTH_RATE', format: 'percentage' },
  //           fund
  //         )}
  //       </span>
  //     )
  //   }
  // },
  {
    value: 'CLASS1',
    format: 'text',
    width: 155,
    sortable: true,
  },
  {
    value: 'CLASS2',
    format: 'text',
    width: 155,
    sortable: true,
  },
  {
    value: 'ISSUER',
    format: 'text',
    width: 155,
  },
  {
    value: 'COUPONRATE_CUR',
    format: 'commaNumber',
    align: 'right',
    width: 155,
    sortable: true,
  },
  {
    value: 'COUPONRATE',
    format: 'commaNumber',
    align: 'right',
    width: 155,
    sortable: true,
  },
  {
    value: 'YTM',
    format: 'commaNumber',
    align: 'right',
    width: 145,
    sortable: true,
  },
  {
    value: 'DURATION',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'DURATION_IF_EXE',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'PTM_YEAR',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'TERM_IF_EXE',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
  {
    value: 'CONVEXITY',
    format: 'commaNumber',
    align: 'right',
    sortable: true,
  },
]

export {
  FUND_EQ_POSITION,
  FUND_EQ_INDUSTRY,
  FUND_EQ_CONCEPT,
  FUND_ASSET_STRUCTUR,
  FUND_FUT_TYPE,
  FUND_FUT_POSITION,
  BOND_CONCEPT,
  BOND_CREDIT_RATING,
  BOND_TERM_DISTRIBUTION,
  BOND_STATISTICS,
  BOND_REGIONAL_DISTRIBUTION,
  BOND_ISSURE_DISTRIBUTION,
  BOND_POSITION,
  FUND_POSITION,
  FUND_TYPED_POSITION,
  OPTION_POSITION,
}
