import { AnyAction, Reducer } from 'redux'
import { EffectsCommandMap } from 'dva'
import {
  queryData,
  querySeriesData,
  queryStockPrice,
  queryBenchmarkIndustryRatio,
  queryBondCashflow,
} from './service'

export interface StateType {
  benchmarkIndustryRatio: object;
  cashflows: Array<any>;
  seriesData?: any;
  dailyData?: {
    bondConcept: any;
    bondPosition: any;
    bondRegionalDistribution: any;
    concept: any;
    futuresPosition: any;
    industry: any;
    optionPosition: any;
    position: any;
    product: any;
    structur: any;
    summary: any;
    valuation: any;
  };
}

export type Effect = (
  action: AnyAction,
  effects: EffectsCommandMap & { select: <T>(func: (state: StateType) => T) => T },
) => void;

export interface ModelType {
  namespace: string;
  state: StateType;
  effects: {
    fetch: Effect;
    fetchSeries: Effect;
    fetchAmountSeries: Effect;
    fetchBenchmarkIndustyRatio: Effect;
    fetchBondCashflow: Effect;
  };
  reducers: {
    save: Reducer<StateType>;
    saveBenchmarkIndustry: Reducer<StateType>;
  };
}

const Model: ModelType = {
  namespace: 'position',

  state: {
    benchmarkIndustryRatio: {},
    cashflows: [],
  },

  effects: {
    *fetch({ payload: { fundId, date } }, { call, put }) {
      const response = yield call(queryData, fundId, date)
      yield put({
        type: 'save',
        payload: {
          dailyData: response,
        },
      })
    },
    *fetchSeries({ payload: { fundId, params } }, { call, put }) {
      const response = yield call(querySeriesData, fundId, params)
      yield put({
        type: 'save',
        payload: {
          seriesData: response,
        },
      })
    },
    *fetchAmountSeries({ payload: { fundId, params } }, { call, put }) {
      const response = yield call(queryStockPrice, fundId, params)
      yield put({
        type: 'save',
        payload: {
          amountSeries: response,
        },
      })
    },
    *fetchBenchmarkIndustyRatio({ payload: { params } }, { call, put }) {
      const { benchmark, date } = params
      const response = yield call(queryBenchmarkIndustryRatio, params)
      yield put({
        type: 'saveBenchmarkIndustry',
        payload: {
          [`${benchmark}-${date}`]: response,
        },
      })
    },
    *fetchBondCashflow({ payload: { fundId, date } }, { call, put }) {
      const response = yield call(queryBondCashflow, fundId, date)
      yield put({
        type: 'save',
        payload: {
          cashflows: response,
        },
      })
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    saveBenchmarkIndustry(state, action) {
      const benchmarkIndustryRatio = state.benchmarkIndustryRatio
      return {
        ...state,
        benchmarkIndustryRatio: {
          ...benchmarkIndustryRatio,
          ...action.payload,
        },
      }
    },
  },
}

export default Model
