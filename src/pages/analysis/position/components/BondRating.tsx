import React, { Component } from 'react'
import countBy from 'lodash/countBy'
import groupBy from 'lodash/groupBy'
import uniq from 'lodash/uniq'
import Chart from '@/components/Chart/Chart'
import DataTable from '@/components/DataTable'
import { Card, Row, Col } from 'antd'

const BOND_RATES_LIST = [
  'Aaa',
  'AAA',
  'AAA-',
  'AA+',
  'Aa1',
  'Aa2',
  'AA',
  'AA-',
  'Aa3',
  'A1+',
  'A1',
  'A+',
  'A2',
  'A',
  'A-',
  'A3',
  'A-1+',
  'A-1',
  'A-2',
  'A-3',
  'Baa1',
  'BBB+',
  'BBB',
  'Baa2',
  'BBB-',
  'Baa3',
  'Ba1',
  'BB+',
  'BB',
  'Ba2',
  'BB-',
  'Ba3',
  'B+',
  'B1',
  'B2',
  'B',
  'B-',
  'B3',
  'Caa1',
  'CCC',
  'Caa2',
  'Caa3',
  'Ca',
  'CC',
  'C',
  'D',
  'E',
]

interface ComponentProps {
  quotas: Array<any>;
  rows: Array<any>;
  t: any;
}

export default class BondRating extends Component<ComponentProps> {
  componentDidMount() {
    this.renderChart()
    this.renderRatioChart()
  }

  calculateRatio(rows: any, type: any) {
    const data = groupBy(rows, type)
    return Object.keys(data).reduce((out, key) => {
      out[key] = data[key].reduce((sum, item) => {
        return sum + item.BALANCE_RATIO
      }, 0)
      return out
    }, {})
  }

  renderRatioChart() {
    const { rows, t } = this.props
    const creditRating = this.calculateRatio(rows, 'CREDITRATING')
    const creditRatingZt = this.calculateRatio(rows, 'CREDITRATING_ZT')
    const names = uniq([...Object.keys(creditRating), ...Object.keys(creditRatingZt)]).sort(
      (prev, next) => BOND_RATES_LIST.indexOf(prev) - BOND_RATES_LIST.indexOf(next),
    )
    const categories = names.map(category => {
      if (category === 'null') return `${t('quota.notRated')}`
      return category
    })
    const chartConfig = {
      chart: {
        type: 'column',
      },
      title: {
        text: t('quota.ratingMarketShare'),
      },
      yAxis: {
        title: {
          text: t('quota.proportion'),
        },
      },
      xAxis: {
        categories,
        crosshair: true,
      },
      tooltip: {
        headerFormat: '<b>{point.key}</b><br/>',
        pointFormat: '<span>{series.name}</span>: <b>{point.y:.2f}%</b><br/>',
        shared: true,
      },
      series: [
        {
          name: t('quota.bondRating'),
          data: names.map(name => (creditRating[name] || 0) * 100),
        },
        {
          name: t('quota.subjectRating'),
          data: names.map(name => (creditRatingZt[name] || 0) * 100),
        },
      ],
    }
    return <Chart options={chartConfig} />
  }

  renderChart() {
    const { rows, t } = this.props
    const creditRating = countBy(rows, 'CREDITRATING')
    const creditRatingZt = countBy(rows, 'CREDITRATING_ZT')
    const names = uniq([...Object.keys(creditRating), ...Object.keys(creditRatingZt)]).sort(
      (prev, next) => BOND_RATES_LIST.indexOf(prev) - BOND_RATES_LIST.indexOf(next),
    )
    const categories = names.map(category => {
      if (category === 'null') return `${t('quota.notRated')}`
      return category
    })
    const chartConfig = {
      chart: {
        type: 'column',
      },
      title: {
        text: t('quota.ratingTimes'),
      },
      yAxis: {
        title: {
          text: t('quota.times'),
        },
      },
      xAxis: {
        categories,
        crosshair: true,
      },
      tooltip: {
        headerFormat: '<b>{point.key}</b><br/>',
        pointFormat: '<span>{series.name}</span>: <b>{point.y}</b><br/>',
        shared: true,
      },
      series: [
        {
          name: t('quota.bondRating'),
          data: names.map(name => creditRating[name] || 0),
        },
        {
          name: t('quota.subjectRating'),
          data: names.map(name => creditRatingZt[name] || 0),
        },
      ],
    }
    return <Chart options={chartConfig} />
  }

  render() {
    const { rows, quotas } = this.props
    return (
      <Card title="债券评级">
        <DataTable quotas={quotas} data={rows} />
        <Row gutter={10}>
          <Col md={12} sm={24}>
            {this.renderChart()}
          </Col>
          <Col md={12} sm={24}>
            {this.renderRatioChart()}
          </Col>
        </Row>
      </Card>
    )
  }
}
