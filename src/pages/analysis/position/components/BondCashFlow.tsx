import React, { Component } from 'react'
import moment from 'moment'
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, <PERSON>u, <PERSON><PERSON>, Select, Card } from 'antd';
import groupBy from 'lodash/groupBy'
import uniqBy from 'lodash/uniqBy'
import Chart from '@/components/Chart/Chart'

interface ComponentProps {
  rows: Array<any>;
  t: any;
}
interface ComponentState {
  type: string;
  filterClasses: Array<any>;
}
export default class BondCashFlow extends Component<ComponentProps, ComponentState> {
  state = {
    type: 'all',
    filterClasses: [],
  };

  componentDidMount() {
    setTimeout(this.renderChart, 500)
  }

  onTypeSelect = event => {
    this.setState({ type: event.key, filterClasses: [] }, this.renderChart)
  };

  getFilterOptions = () => {
    const { type } = this.state
    const { rows } = this.props
    if (type === 'all') {
      return uniqBy(rows, 'REF_CODE').map(row => ({
        name: row.STOCK_NAME,
        value: row.REF_CODE,
      }))
    }
    return [...new Set(rows.map(row => row[type]))].filter(Boolean).map(item => ({
      name: item,
      value: item,
    }))
  };

  getFilterPlaceholder = () => {
    const { type } = this.state
    const { t } = this.props
    let name = 'bondSelectTip'
    if (type === 'CLASS1') {
      name = 'primaryClassSelectTip'
    }
    if (type === 'CLASS2') {
      name = 'secondClassSelectTip'
    }
    return t(`quota.${name}`)
  };

  handleClassChange = values => {
    this.setState({ filterClasses: values }, this.renderChart)
  };

  buildCashFlows = rows => {
    const { type, filterClasses } = this.state
    let finalRows = rows
    if (filterClasses.length) {
      if (type === 'all') {
        finalRows = rows.filter(row => filterClasses.includes(row.REF_CODE))
      } else {
        finalRows = rows.filter(row => filterClasses.includes(row[type]))
      }
    }
    const dateMap = groupBy(finalRows, 'BIZ_DATE')
    const cashflows = Object.keys(dateMap).reduce((out, item) => {
      const date = +moment(item).startOf('date')
      const sum = dateMap[item].reduce((res, it) => {
        res += it.PAYMENT || 0
        return res
      }, 0)
      out.push([date, sum])
      return out
    }, [])
    return cashflows
  };

  renderChart = () => {
    const { t, rows } = this.props
    const cashflows = this.buildCashFlows(rows)
    const options = {
      chart: {
        type: 'column',
      },
      xAxis: {
        categories: cashflows.map(item => moment(new Date(item[0])).format('YYYY-MM-DD')),
        crosshair: true,
      },
      yAxis: {
        labels: {
          format: '{value}',
        },
      },
      tooltip: {
        pointFormat:
          '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.2f}</b><br/>',
      },
      series: [
        {
          name: t('quota.bondFutureCashFlow'),
          data: cashflows.map(item => item[1]),
        },
      ],
    }
    return <Chart options={options} />
  };

  render() {
    const { t } = this.props
    const { type, filterClasses } = this.state
    const typeList = [
      {
        name: t('quota.allPosition'),
        value: 'all',
      },
      {
        name: t('quota.primaryClass'),
        value: 'CLASS1',
      },
      {
        name: t('quota.secondClass'),
        value: 'CLASS2',
      },
    ]
    const filterOptions = this.getFilterOptions()
    return (
      <Card
        title="债券未来现金流"
        extra={
          <>
            <Dropdown
              overlay={
                <Menu onClick={this.onTypeSelect}>
                  {typeList.map(item => (
                    <Menu.Item key={item.value}>{item.name}</Menu.Item>
                  ))}
                </Menu>
              }
            >
              <Button style={{ marginLeft: 8 }}>
                {typeList.find(item => item.value === type).name} <DownOutlined />
              </Button>
            </Dropdown>
            <Select
              mode="multiple"
              placeholder={this.getFilterPlaceholder()}
              onChange={this.handleClassChange}
              value={filterClasses}
              style={{ width: '250px' }}
            >
              {filterOptions.map(item => (
                <Select.Option key={item.value}>{item.name}</Select.Option>
              ))}
            </Select>
          </>
        }
      >
        {this.renderChart()}
      </Card>
    );
  }
}
