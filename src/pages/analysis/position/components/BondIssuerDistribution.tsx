import React, { Component } from 'react'
import groupBy from 'lodash/groupBy'
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import DataTable from '@/components/DataTable'

interface ComponentProps {
  rows: Array<any>;
  quotas: Array<any>;
  t: any;
}
interface ComponentState {
  category: {
    name: string;
    value: string;
  };
}
export default class BondIssuerDistribution extends Component<ComponentProps, ComponentState> {
  state = {
    category: {
      name: this.props.t('quota.secondaryIndustry'),
      value: 'ISSUER_CLASS2',
    },
  };

  onCategorySelect = event => {
    const eventKey = JSON.parse(event.key)
    this.setState({ category: eventKey })
  };

  getRows() {
    const { t } = this.props
    const { category } = this.state
    const result = groupBy(this.props.rows, category.value)
    const sum = items => items.reduce((out, item) => out + (item || 0), 0)
    const rows = Object.keys(result)
      .map(key => {
        const list = result[key]
        const ret = { category: key === 'null' ? t('quota.others') : key }
        ret.BALANCE = sum(list.map(item => item.BALANCE))
        ret.BALANCE_RATIO = sum(list.map(item => item.BALANCE_RATIO))
        ret.BOND_COUNT = list.length
        ret.BOND_LIST = list.map(item => item.STOCK_NAME).join(',')
        return ret
      })
      .sort((fst, snd) => snd.BALANCE - fst.BALANCE)
    return rows
  }

  render() {
    const { quotas, t } = this.props
    const categories = [
      {
        name: t('quota.primaryIndustry'),
        value: 'ISSUER_CLASS1',
      },
      {
        name: t('quota.secondaryIndustry'),
        value: 'ISSUER_CLASS2',
      },
      {
        name: t('quota.threeLevelIndustry'),
        value: 'ISSUER_CLASS3',
      },
      {
        name: t('quota.fourLevelIndustry'),
        value: 'ISSUER_CLASS4',
      },
    ]
    const { category } = this.state
    const rows = this.getRows()
    return (
      <Card
        title="债券主体行业分布"
        style={{
          minHeight: 426,
        }}
        extra={
          <Dropdown
            overlay={
              <Menu onClick={this.onCategorySelect}>
                {categories.map(item => (
                  <Menu.Item key={JSON.stringify(item)}>{item.name}</Menu.Item>
                ))}
              </Menu>
            }
          >
            <Button style={{ marginLeft: 8 }}>
              {category.name} <DownOutlined />
            </Button>
          </Dropdown>
        }
      >
        <DataTable quotas={quotas} data={rows} />
      </Card>
    );
  }
}
