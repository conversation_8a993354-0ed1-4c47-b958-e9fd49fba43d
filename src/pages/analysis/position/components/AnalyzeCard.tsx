import React from 'react'
import { Card, Row, Col } from 'antd'
import Chart from '@/components/Chart/Chart'
import DataTable from '@/components/DataTable'

interface AnalyzeCardProps {
  quotas: Array<any>;
  data: Array<any>;
  title: string;
  charts: Array<any>;
  extra?: any;
  reverseOrder?: boolean;
}

const AnalyzeCard: React.SFC<AnalyzeCardProps> = props => {
  const { title, charts, extra, reverseOrder, ...restProps } = props
  const chartOptions = charts[0].config
  const constructorType = charts[0].constructorType
  const span1 = reverseOrder ? 7 : 17
  const span2 = reverseOrder ? 17 : 7
  return (
    <Card title={title} extra={extra}>
      <Row>
        <Col md={span1} sm={24}>
          <DataTable {...restProps} />
        </Col>
        <Col md={span2} sm={24}>
          <Chart options={chartOptions} constructorType={constructorType} />
        </Col>
      </Row>
    </Card>
  )
}

export default AnalyzeCard
