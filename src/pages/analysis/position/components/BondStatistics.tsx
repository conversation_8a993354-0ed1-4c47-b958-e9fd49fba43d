import React, { Component } from 'react'
import groupBy from 'lodash/groupBy'
import DataTable from '@/components/DataTable'
import uniq from 'lodash/uniq'
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';

interface BondStatisticsProps {
  rows: Array<any>;
  quotas: Array<any>;
  t: any;
}
interface BondStatisticsState {
  category: {
    name: string;
    value: string;
  };
  filters: Array<any>;
  term?: {
    name: string;
    value: string;
  };
}
export default class BondStatistics extends Component<BondStatisticsProps, BondStatisticsState> {
  constructor(props: BondStatisticsProps) {
    super(props)
    const category = {
      name: 'WIND二级分类',
      value: 'CLASS2',
    }
    this.state = {
      category,
      filters: this.getFilters(category),
    }
  }

  onCategorySelect = event => {
    const category = JSON.parse(event.key)
    this.setState({
      category,
      filters: this.getFilters(category),
    })
  };

  getRows() {
    const { t } = this.props
    const sum = (items, key) => items.reduce((out, item) => out + (item[key] || 0), 0)
    const { category } = this.state
    const filteredRows = this.props.rows.map(item => ({ ...item }))
    const balanceSum = sum(filteredRows, 'BALANCE')
    filteredRows.forEach(item => (item.BALANCE_RATIO = item.BALANCE / balanceSum))
    const result = groupBy(filteredRows, category.value)
    const rows = Object.keys(result).map(key => {
      const ret = { category: key === 'null' ? t('quota.others') : key }
      const fields = [
        'COUPONRATE_CUR',
        'COUPONRATE',
        'YTM',
        'DURATION',
        'DURATION_IF_EXE',
        'PTM_YEAR',
        'TERM_IF_EXE',
        'WEIGHTED_YEAR',
        'CONVEXITY',
      ]
      fields.forEach(field => {
        const bonds = ~['DURATION_IF_EXE', 'TERM_IF_EXE'].indexOf(field)
          ? result[key].filter(item => item[field])
          : result[key]
        const sumBalance = sum(bonds, 'BALANCE')
        ret[field] =
          sumBalance === 0
            ? 0
            : sum(bonds.map(item => ({ [field]: item[field] * item.BALANCE })), field) / sumBalance
      })
      ret.BALANCE = sum(result[key], 'BALANCE')
      ret.BALANCE_RATIO = sum(result[key], 'BALANCE_RATIO')
      ret.ASSET_RATIO = sum(result[key], 'ASSET_RATIO')
      ret.NET_ASSET_RATIO = sum(result[key], 'NET_ASSET_RATIO')
      return ret
    })
    return rows
  }

  getFilters = quota => {
    const { rows } = this.props
    const names = rows.map(item => item[quota.value]).filter(Boolean)
    return uniq(names).map(item => ({ name: item, value: item, _id: item }))
  };

  render() {
    const { t } = this.props
    const categories = [
      {
        name: 'WIND二级分类',
        value: 'CLASS2',
      },
      {
        name: 'WIND一级分类',
        value: 'CLASS1',
      },
      {
        name: '信用类别',
        value: 'creditClass',
      },
    ]
    const { category } = this.state
    const { quotas } = this.props
    const rows = this.getRows()
    const finalRows = rows.concat(
      quotas.reduce(
        (out, quota) => {
          const sum = items => items.reduce((result, item) => result + (item || 0), 0)
          const balanceList = rows.map(item => item.BALANCE)
          const balance = sum(balanceList)
          if (quota.value === 'category') {
            out.category = t('quota.total')
            return out
          }
          if (
            quota.value === 'BALANCE' ||
            quota.value === 'BALANCE_RATIO' ||
            quota.value === 'ASSET_RATIO' ||
            quota.value === 'NET_ASSET_RATIO'
          ) {
            out[quota.value] = sum(rows.map(row => row[quota.value]))
            return out
          }
          const values = rows.map((item, index) => item[quota.value] * balanceList[index])
          out[quota.value] = sum(values) / balance
          return out
        },
        { isSum: true },
      ),
    )
    return (
      <Card
        title="债券指标统计"
        extra={
          <Dropdown
            overlay={
              <Menu onClick={this.onCategorySelect}>
                {categories.map(item => (
                  <Menu.Item key={JSON.stringify(item)}>{item.name}</Menu.Item>
                ))}
              </Menu>
            }
          >
            <Button style={{ marginLeft: 8 }}>
              {category.name} <DownOutlined />
            </Button>
          </Dropdown>
        }
      >
        <DataTable quotas={quotas} data={finalRows} />
      </Card>
    );
  }
}
