import React, { Component } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import groupBy from 'lodash/groupBy'
import map from 'lodash/map'
import sum from 'lodash/sum'
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, <PERSON>u, Button, Spin, Divider } from 'antd';
import { formatMessage } from 'umi-plugin-react/locale'
import AnalyzeCard from './AnalyzeCard'
import { FUND_EQ_INDUSTRY } from '../quotas'

const t = (id: string) => formatMessage({ id })

interface IndustryPreferenceProps {
  dispatch: Dispatch<any>;
  title: string;
  className?: string;
  isIndustryBoard?: boolean;
  benchmarkIndustryRatio?: any;
  dailyData?: any;
  date: string;
  loading?: boolean;
}

@connect(
  ({
    position,
    loading,
  }: {
    position: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    benchmarkIndustryRatio: position.benchmarkIndustryRatio,
    loading: loading.effects['position/fetchBenchmarkIndustyRatio'],
  }),
)
export default class IndustryPreference extends Component<IndustryPreferenceProps> {
  constructor(props: IndustryPreferenceProps) {
    super(props)
    const benchmark = props.dailyData.product.benchmark === '中证500' ? '中证500' : '沪深300'
    this.state = {
      benchmark,
      viewType: {
        name: '前十大行业',
        value: 'top10',
      },
      viewTypes: [
        {
          name: '前十大行业',
          value: 'top10',
        },
        {
          name: '超配前五/低配前五行业',
          value: 'headTail5',
        },
      ],
    }
  }

  onBenchmarkSelect = (event: { key: string }) => {
    const benchmark = event.key
    this.setState({ benchmark }, this.loadBenchmarkIndustryRatio)
  };

  onViewTypeSelect = (event: { key: string }) => {
    const viewType = this.state.viewTypes.find(item => item.value === event.key) || {}
    this.setState({ viewType })
  };

  loadBenchmarkIndustryRatio = () => {
    const { benchmark } = this.state
    const { dispatch, date } = this.props
    dispatch({
      type: 'position/fetchBenchmarkIndustyRatio',
      payload: {
        params: {
          date,
          benchmark,
        },
      },
    })
  };

  getData(industry, isIndustryBoard, benchmarkIndustry) {
    const ratioMap = benchmarkIndustry.reduce((out, item) => {
      out[item.INDUSTRY] = item.WEIGHT
      return out
    }, {})
    let rows = industry.map(item => {
      return {
        ...item,
        BASE_RATIO: ratioMap[item.INDUSTRY],
      }
    })
    if (isIndustryBoard) {
      const benchmarkIndustryMap = groupBy(benchmarkIndustry, this.getIndustryBoard)
      rows = map(groupBy(rows, this.getIndustryBoard), (values, name) => {
        return {
          INDUSTRY: name,
          BALANCE: sum(values.map(item => item.BALANCE)),
          BALANCE_RATIO: sum(values.map(item => item.BALANCE_RATIO)),
          BASE_RATIO: sum((benchmarkIndustryMap[name] || []).map(item => item.WEIGHT || 0)),
          isIndustryBoard: true,
        }
      })
    }
    return rows
      .sort((fst, snd) => snd.BALANCE_RATIO - fst.BALANCE_RATIO)
      .map((item, index) => {
        item.DIFF_RATIO = item.BALANCE_RATIO - item.BASE_RATIO
        item.RANK = item.RANK || index + 1
        return item
      })
  }

  getIndustryBoard(position) {
    const industry = position.INDUSTRY
    if (['采掘', '有色金属'].includes(industry)) {
      return '周期上游'
    } else if (['钢铁', '化工', '公用事业', '交通运输'].includes(industry)) {
      return '周期中游'
    } else if (['建筑材料', '建筑装饰', '汽车', '机械设备'].includes(industry)) {
      return '周期下游'
    } else if (['银行', '非银金融', '房地产'].includes(industry)) {
      return '大金融'
    } else if (
      [
        '轻工制造',
        '商业贸易',
        '休闲服务',
        '家用电器',
        '纺织服装',
        '医药生物',
        '食品饮料',
        '农林牧渔',
      ].includes(industry)
    ) {
      return '消费'
    } else if (['计算机', '传媒', '通信', '电气设备', '电子'].includes(industry)) {
      return 'TMT'
    } else if (industry && industry.includes('(HS)')) {
      return '港股'
    } else {
      return '其他'
    }
  }

  getChartOptions = data => {
    const { viewType } = this.state
    const total = data.length
    let rows
    if (viewType.value === 'top10') {
      rows = data.filter((item, index) => index < 10)
    } else {
      rows = data
        .sort((fst, snd) => snd.DIFF_RATIO - fst.DIFF_RATIO)
        .filter((item, index) => {
          return index < 5 || index >= total - 5
        })
    }
    let series
    if (viewType.value === 'top10') {
      series = [
        {
          name: t('quota.proportion'),
          data: rows.map(row => row.BALANCE_RATIO * 100),
        },
        {
          name: t('quota.benchmarkProportion'),
          data: rows.map(row => row.BASE_RATIO * 100),
          type: 'line',
          lineWidth: 0,
          states: {
            hover: {
              lineWidthPlus: 0,
            },
          },
          color: '#E49831',
          marker: {
            enabled: true,
            lineWidth: 4,
            symbol: 'circle',
            lineColor: '#E49831',
          },
        },
      ]
    } else {
      series = [
        {
          name: t('quota.DIFF_RATIO'),
          data: rows.map(row => row.DIFF_RATIO * 100),
        },
      ]
    }
    const options = {
      chart: {
        type: 'bar',
      },
      plotOptions: {
        column: {
          grouping: false,
          shadow: false,
          borderWidth: 0,
        },
      },
      xAxis: {
        categories: rows.map(item => item.INDUSTRY),
      },
      series,
    }
    return options
  };

  render() {
    const { benchmark, viewType, viewTypes } = this.state
    const {
      title,
      isIndustryBoard,
      dailyData: { industry },
      benchmarkIndustryRatio,
      date,
      loading,
    } = this.props
    const benchmarks = [
      {
        value: '沪深300',
      },
      {
        value: '中证500',
      },
      {
        value: '恒生指数',
      },
      {
        value: '50%沪深300+50%恒生指数',
      },
    ]
    const quotas = FUND_EQ_INDUSTRY
    const rows = this.getData(
      industry,
      isIndustryBoard,
      benchmarkIndustryRatio[`${benchmark}-${date}`] || [],
    )
    const finalRows = rows.concat(
      quotas.reduce(
        (out, quota) => {
          if (quota.value === 'RANK') {
            out.RANK = t('quota.total')
            return out
          }
          if (quota.value === 'INDUSTRY') {
            out[quota.value] = '-'
            return out
          }
          out[quota.value] = sum(rows.map(row => row[quota.value]))
          return out
        },
        { isSum: true },
      ),
    )
    const options = this.getChartOptions(rows)
    return (
      <Spin spinning={loading}>
        <AnalyzeCard
          title={title}
          quotas={FUND_EQ_INDUSTRY}
          data={finalRows}
          charts={[{ config: options }]}
          extra={
            <div>
              <span>投资基准：</span>
              <Dropdown
                overlay={
                  <Menu onClick={this.onBenchmarkSelect}>
                    {benchmarks.map(item => (
                      <Menu.Item key={item.value}>{item.value}</Menu.Item>
                    ))}
                  </Menu>
                }
              >
                <Button>
                  {benchmark} <DownOutlined />
                </Button>
              </Dropdown>
              {!isIndustryBoard && <Divider type="vertical" />}
              {!isIndustryBoard && (
                <Dropdown
                  overlay={
                    <Menu onClick={this.onViewTypeSelect}>
                      {viewTypes.map(item => (
                        <Menu.Item key={item.value}>{item.name}</Menu.Item>
                      ))}
                    </Menu>
                  }
                >
                  <Button>
                    {viewType.name} <DownOutlined />
                  </Button>
                </Dropdown>
              )}
            </div>
          }
        />
      </Spin>
    );
  }
}
