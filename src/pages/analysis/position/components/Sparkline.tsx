import React from 'react'
import HighchartsReact from 'highcharts-react-official'
import highcharts from 'highcharts'
import moment from 'moment'

interface ComponentProps {
  series: Array<any>;
}

const Sparkline: React.SFC<ComponentProps> = props => {
  const { series } = props
  const config = {
    chart: {
      backgroundColor: null,
      borderWidth: 0,
      type: 'area',
      margin: [2, 0, 2, 0],
      width: 250,
      height: 30,
      style: {
        overflow: 'visible',
      },
      // small optimalization, saves 1-2 ms each sparkline
      skipClone: true,
    },
    title: {
      text: '',
    },
    credits: {
      enabled: false,
    },
    xAxis: {
      labels: {
        enabled: false,
      },
      title: {
        text: null,
      },
      startOnTick: false,
      endOnTick: false,
      tickPositions: [],
      gridLineColor: '#3A404C',
      lineColor: '#3A404C',
      minorGridLineColor: '#505053',
      tickColor: '#3A404C',
    },
    yAxis: {
      endOnTick: false,
      startOnTick: false,
      labels: {
        enabled: false,
      },
      title: {
        text: null,
      },
      tickPositions: [0],
    },
    legend: {
      enabled: false,
    },
    exporting: {
      enabled: false,
    },
    tooltip: {
      backgroundColor: null,
      borderWidth: 0,
      shadow: false,
      useHTML: true,
      hideDelay: 0,
      shared: false,
      padding: 0,
      valueSuffix: '%',
      style: {
        color: '#fff',
      },
      positioner(width, height, point) {
        return { x: point.plotX + width * 0.2, y: point.plotY + height * 0 }
      },
      formatter: function() {
        // eslint-disable-line
        return `
        ${this.series.name}: <b>${Number(this.point.y.toFixed(2))}%</b><br/>
        ${moment(new Date(this.key)).format('YYYY-MM-DD')}
        `
      },
    },
    plotOptions: {
      series: {
        animation: false,
        lineWidth: 1,
        shadow: false,
        states: {
          hover: {
            lineWidth: 1,
          },
        },
        marker: {
          enabled: false,
          radius: 1,
          states: {
            hover: {
              radius: 2,
            },
          },
        },
        fillOpacity: 0.25,
      },
    },
    series,
  }
  return <HighchartsReact highcharts={highcharts} options={config} />
}

export default Sparkline
