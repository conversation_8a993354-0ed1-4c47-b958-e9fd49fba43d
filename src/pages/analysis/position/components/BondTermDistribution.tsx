import React, { Component } from 'react'
import groupBy from 'lodash/groupBy'
import uniq from 'lodash/uniq'
import DataTable from '@/components/DataTable'
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';

interface BondTermDistributionProps {
  rows: Array<any>;
  quotas: Array<any>;
  t: any;
  getTimeRangeFilters: any;
}
interface BondTermDistributionState {
  category: {
    name: string;
    value: string;
  };
  filters: Array<any>;
  term: {
    name: string;
    value: string;
  };
}
export default class BondTermDistribution extends Component<
  BondTermDistributionProps,
  BondTermDistributionState
> {

  constructor(props: BondTermDistributionProps) {
    super(props)
    const category = {
      name: 'WIND二级分类',
      value: 'CLASS2',
    }
    this.state = {
      category,
      filters: this.getFilters(category),
      term: {
        name: this.props.t('quota.DURATION'),
        value: 'DURATION',
      },
    }
  }

  onCategorySelect = (event: { key: string }) => {
    const category = JSON.parse(event.key)
    this.setState({
      category,
      filters: this.getFilters(category),
    })
  };

  onTermSelect = (event: { key: string }) => {
    const eventKey = JSON.parse(event.key)
    this.setState({ term: eventKey })
  };

  getFilters = (quota: any) => {
    const { rows } = this.props
    const names = rows.map(item => item[quota.value]).filter(Boolean)
    return uniq(names).map(item => ({
      name: item,
      value: item,
      _id: item,
    }))
  };

  getRows() {
    const { getTimeRangeFilters, t } = this.props
    const sum = (items, key) => items.reduce((out, item) => out + (item[key] || 0), 0)
    const { category, term } = this.state
    const termType = term.value
    const filteredRows = this.props.rows
    const balanceSum = sum(filteredRows, 'BALANCE')
    filteredRows.forEach(item => (item.BALANCE_RATIO = item.BALANCE / balanceSum))
    const result = groupBy(filteredRows, category.value)
    const timeRanges = getTimeRangeFilters(termType)
    const rows = Object.keys(result).map(key => {
      const ret = { category: key === 'null' ? t('quota.others') : key }
      timeRanges.forEach(range => {
        ret[range.name] = sum(result[key].filter(range.filter), 'BALANCE_RATIO')
      })
      ret.sum = timeRanges.reduce((out, range) => out + ret[range.name], 0)
      return ret
    })
    return rows
  }

  render() {
    const { t } = this.props
    const categories = [
      {
        name: 'WIND二级分类',
        value: 'CLASS2',
      },
      {
        name: 'WIND一级分类',
        value: 'CLASS1',
      },
      {
        name: '信用类别',
        value: 'creditClass',
      },
    ]
    const terms = [
      {
        name: t('quota.duration'),
        value: 'DURATION',
      },
      {
        name: t('quota.durationOfExercise'),
        value: 'DURATION_IF_EXE',
      },
      {
        name: t('quota.remainingPeriod'),
        value: 'PTM_YEAR',
      },
      {
        name: t('quota.remainPeriodOfExercise'),
        value: 'TERM_IF_EXE',
      },
    ]
    const { category, term } = this.state
    const { quotas } = this.props
    const rows = this.getRows()
    const finalRows = rows.concat(
      quotas.reduce(
        (out, quota) => {
          if (quota.value === 'category') {
            out.category = t('quota.total')
            return out
          }
          out[quota.value] = rows.reduce((sum, row) => sum + (row[quota.value] || 0), 0)
          return out
        },
        { isSum: true },
      ),
    )
    return (
      <Card
        title="债券期限分布"
        extra={
          <>
            <Dropdown
              overlay={
                <Menu onClick={this.onCategorySelect}>
                  {categories.map(item => (
                    <Menu.Item key={JSON.stringify(item)}>{item.name}</Menu.Item>
                  ))}
                </Menu>
              }
            >
              <Button style={{ marginLeft: 8 }}>
                {category.name} <DownOutlined />
              </Button>
            </Dropdown>
            <Dropdown
              overlay={
                <Menu onClick={this.onTermSelect}>
                  {terms.map(item => (
                    <Menu.Item key={JSON.stringify(item)}>{item.name}</Menu.Item>
                  ))}
                </Menu>
              }
            >
              <Button style={{ marginLeft: 8 }}>
                {term.name} <DownOutlined />
              </Button>
            </Dropdown>
          </>
        }
      >
        <DataTable quotas={quotas} data={finalRows} />
      </Card>
    );
  }
}
