import React, { Component } from 'react'
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu, Button } from 'antd';
import AnalyzeCard from './AnalyzeCard'
import { formatMessage } from 'umi-plugin-react/locale'

const t = (id: string) => formatMessage({ id })

interface EQPositionProps {
  rows: Array<any>;
  quotas: Array<any>;
}

export default class EQPosition extends Component<EQPositionProps> {
  state = {
    currentMenu: 'ratio',
    menuItems: {
      ratio: t('quota.proportion'),
      fundamental: t('quota.fundamental'),
      // index: '投资指数',
    },
    rowType: t('quota.topTen'),
  };

  onRowSelect = (event: { key: string }) => {
    const eventKey = event.key
    this.setState({
      rowType: eventKey,
    })
  };

  onMenuSelect = (event: { key: string }) => {
    const eventKey = event.key
    this.setState({
      currentMenu: eventKey,
    })
  };

  getChartOptions = () => {
    const { rows } = this.props
    const { currentMenu } = this.state
    const type = currentMenu === 'ratio' ? 'bar' : 'column'
    let quotas = [
      {
        name: t('quota.proportion'),
        value: 'BALANCE_RATIO',
      },
    ]
    if (currentMenu === 'fundamental') {
      quotas = [
        {
          name: 'PE（TTM）',
          value: 'PE_TTM',
        },
        {
          name: 'PB（MRQ）',
          value: 'PB_MRQ',
        },
        {
          name: 'ROE（%）',
          value: 'ROE',
        },
      ]
    }
    if (currentMenu === 'index') {
      quotas = [
        {
          name: t('quota.valInvestmentIndex'),
          value: 'VALUE_SCORE',
        },
        {
          name: t('quota.growthInvestmentIndex'),
          value: 'GROWTH_SCORE',
        },
      ]
    }
    const chartConfig = {
      chart: {
        type,
      },
      xAxis: {
        categories: rows.map(item => item.STOCK_NAME).slice(0, 10),
        crosshair: true,
      },
      tooltip: {
        pointFormat: `{series.name}: <b>{point.y:.2f}${
          currentMenu === 'ratio' ? '%' : ''
        }</b><br/>`,
        shared: true,
      },
      series: quotas.map(quota => ({
        name: quota.name,
        data: rows.map(row => row[quota.value] * (currentMenu === 'ratio' ? 100 : 1)).slice(0, 10),
      })),
    }
    return chartConfig
  };

  render() {
    const { currentMenu, rowType, menuItems } = this.state
    const { quotas, rows } = this.props
    const curRows = rowType === t('quota.topTen') ? rows.slice(0, 10) : rows
    const finalRows = curRows.concat(
      quotas.reduce(
        (out, quota) => {
          if (quota.value === 'RANK') {
            out.RANK = t('quota.total')
            return out
          }
          if (
            [
              'STOCK_CODE',
              'EXCHANGE_TYPE',
              'STOCK_NAME',
              'INDUSTRY',
              'ASSET_RATIO',
              'NET_ASSET_RATIO',
              'ROE',
              'PE_TTM',
              'PB_MRQ',
            ].includes(quota.value)
          ) {
            out[quota.value] = '-'
            return out
          }
          out[quota.value] = curRows.reduce((sum, row) => sum + (row[quota.value] || 0), 0)
          return out
        },
        { isSum: true },
      ),
    )
    const types = [t('quota.topTen'), t('quota.allPosition')]
    const options = this.getChartOptions()
    return (
      <AnalyzeCard
        title="股票持仓"
        quotas={quotas}
        data={finalRows}
        charts={[{ config: options }]}
        extra={
          <div>
            <Dropdown
              overlay={
                <Menu onClick={this.onRowSelect}>
                  {types.map(key => (
                    <Menu.Item key={key}>{key}</Menu.Item>
                  ))}
                </Menu>
              }
            >
              <Button style={{ marginLeft: 8 }}>
                {rowType} <DownOutlined />
              </Button>
            </Dropdown>
            <Dropdown
              overlay={
                <Menu onClick={this.onMenuSelect}>
                  {Object.keys(menuItems).map(key => (
                    <Menu.Item key={key}>{menuItems[key]}</Menu.Item>
                  ))}
                </Menu>
              }
            >
              <Button style={{ marginLeft: 8 }}>
                {menuItems[currentMenu]} <DownOutlined />
              </Button>
            </Dropdown>
          </div>
        }
      />
    );
  }
}
