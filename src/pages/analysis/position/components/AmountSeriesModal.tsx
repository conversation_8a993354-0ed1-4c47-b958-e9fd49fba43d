import React, { Component } from 'react'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import Highcharts from 'highcharts/highstock'
import moment from 'moment'
import { Modal, Button, Spin } from 'antd'
import { formatMessage } from 'umi-plugin-react/locale'
import Chart from '@/components/Chart/Chart'

const t = (id: string) => formatMessage({ id })

interface AmountSeriesModalProps {
  dispatch: Dispatch<any>;
  className?: string;
  bizDate: string;
  code: string;
  amountSeries?: any;
  analysisResult?: any;
  value?: any;
  stockName: string;
  exchangeType?: string;
  assetType?: string;
  productId?: string;
  dimensiton?: {
    name: string;
    value: string;
  };
}

@connect(
  ({
    position,
    loading,
  }: {
    position: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    amountSeries: position.amountSeries,
    loading: loading.effects['position/fetchAmountSeries'],
  }),
)
export default class AmountSeriesModal extends Component<AmountSeriesModalProps> {
  state = {
    show: false,
  };

  close = () => {
    this.setState({ show: false })
    this.props.dispatch({
      type: 'position/save',
      payload: {
        amountSeries: null,
      },
    })
  };

  open = () => {
    this.setState({ show: true }, () => {
      const {
        dispatch,
        productId,
        analysisResult,
        code,
        exchangeType,
        dimensiton,
        assetType,
      } = this.props
      dispatch({
        type: 'position/fetchAmountSeries',
        payload: {
          fundId: productId || analysisResult.valuation.productId,
          params: {
            code,
            exchangeType,
            dataField: dimensiton && dimensiton.value,
            assetType,
          },
        },
      })
    })
  };

  renderChart() {
    const { amountSeries } = this.props
    if (!this.state.show) {
      return false
    }
    if (!amountSeries) {
      return <Spin />
    }
    const amountRows = amountSeries.map((item: Array<any>) => [
      +moment(item[0]).startOf('day'),
      item[1],
    ])
    const dqRows = amountSeries.map((item: Array<any>) => [
      +moment(item[0]).startOf('day'),
      item[2],
    ])
    const { bizDate, value, dimensiton } = this.props
    let title = t('fund.numberOfPositions')
    if (dimensiton) {
      title = dimensiton.name
    }
    const options = {
      chart: {
        type: 'line',
      },
      yAxis: [
        {
          title: {
            text: title,
          },
          labels: {
            format: '{value}',
          },
        },
        {
          title: {
            text: t('fund.closingPrice'),
          },
          labels: {
            format: '{value}',
          },
          opposite: false,
        },
      ],
      series: [
        {
          name: title,
          id: 'amount-series',
          data: amountRows,
          yAxis: 0,
          tooltip: {
            pointFormat: '{series.name}: <b>{point.y:.2f}</b><br/>',
          },
        },
        {
          name: t('fund.closingPrice'),
          data: dqRows,
          yAxis: 1,
          tooltip: {
            pointFormat: '{series.name}: <b>{point.y:.2f}</b><br/>',
          },
        },
      ],
    }
    if (bizDate && value) {
      options.series.push({
        type: 'flags',
        data: [
          {
            x: +moment(bizDate).startOf('day'),
            title: t('userSurvey.sliderValueTip'),
          },
        ],
        yAxis: 0,
        tooltip: {
          pointFormat: `${t('userSurvey.sliderValueTip')}: ${value}`,
        },
        color: Highcharts.getOptions().colors[0], // same as onSeries
        fillColor: Highcharts.getOptions().colors[0],
        onSeries: 'amount-series',
        width: 40,
        showInLegend: false,
        style: {
          // text style
          color: 'white',
        },
        states: {
          hover: {
            fillColor: '#395C84', // darker
          },
        },
      })
    }
    return <Chart options={options} />
  }

  render() {
    const { children, className, stockName, dimensiton } = this.props
    return (
      <div style={{ display: 'inline-block' }} className={className}>
        <div onClick={this.open}>{children}</div>
        <Modal
          visible={this.state.show}
          onCancel={this.close}
          width={1024}
          title={
            dimensiton
              ? `${stockName} - ${t('fund.position')}${dimensiton.name}${t('fund.andClosingPrice')}`
              : `${stockName} - ${t('fund.posAmountAndClosePrice')}`
          }
          footer={[<Button onClick={this.close}>{t('preview.close')}</Button>]}
        >
          <div>{this.renderChart()}</div>
        </Modal>
      </div>
    )
  }
}
