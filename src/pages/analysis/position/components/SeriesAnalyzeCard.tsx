import React, { Component } from 'react'
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Card, Menu, Button } from 'antd';
import moment from 'moment'
import Chart from '@/components/Chart/Chart'

interface ComponentProps {
  t: any;
  title: string;
  rows: Array<any>;
  filters?: Array<any>;
  defaultFilter?: any;
  chartType?: string;
  buildChartData?: any;
  isMutual?: boolean;
  seriesOrder?: Array<any>;
  valueField?: string;
  serieField?: string;
  percentageValue?: boolean;
}

interface ComponentState {
  filter?: any;
  chartType: string;
  monthFilter?: any;
}

export default class SeriesAnalyzeCard extends Component<ComponentProps, ComponentState> {
  state = {
    filter: this.props.defaultFilter,
    chartType: this.props.chartType || 'area',
    monthFilter: {
      name: '全部',
      value: 'all',
    },
  };

  buildChartData(rows) {
    const { valueField, serieField, percentageValue, t } = this.props
    return rows
      .sort((fst, snd) => fst.BIZ_DATE - snd.BIZ_DATE)
      .reduce((out, row) => {
        const name = row[serieField] || t('quota.others')
        out[name] = out[name] || []
        const outNameDates = out[name].map(item => item[0])
        const rowDate = +moment(row.BIZ_DATE)
        const rowDateValue = percentageValue ? row[valueField] * 100 : row[valueField]
        if (!outNameDates.includes(rowDate)) {
          out[name].push([rowDate, rowDateValue])
        } else {
          const index = outNameDates.indexOf(rowDate)
          out[name][index][1] += rowDateValue
        }
        return out
      }, {})
  }

  handleFilterChange = (event: any) => {
    this.setState({ filter: JSON.parse(event.key) }, this.renderChart)
  };

  handleMonthFilterChange = (event: any) => {
    this.setState({ monthFilter: JSON.parse(event.key) }, this.renderChart)
  };

  renderChart = () => {
    const { isMutual, rows, seriesOrder, percentageValue } = this.props
    const { chartType, filter, monthFilter } = this.state
    const data = this.props.buildChartData
      ? this.props.buildChartData(rows, filter && filter.value)
      : this.buildChartData(rows)
    let names = Object.keys(data)
    if (seriesOrder) {
      names = names.sort((prev, next) => seriesOrder.indexOf(prev) - seriesOrder.indexOf(next))
    }
    const config = {
      chart: {
        type: chartType,
      },
      plotOptions: {
        area: {
          stacking: 'percent',
          lineWidth: 1,
          marker: {
            lineWidth: 2,
            lineColor: '#ffffff',
          },
        },
        column: {
          stacking: 'normal',
        },
      },
      series: names.map(name => {
        let configData = data[name]
        if (isMutual && monthFilter.value === 'sorfQuarter') {
          configData = data[name].filter(item => {
            return moment(new Date(item[0])).quarter() % 2 === 0
          })
        }
        return {
          name,
          data: configData,
        }
      }),
    }
    if (!percentageValue) {
      config.yAxis = {
        labels: {
          format: '{value}',
        },
      }
      config.tooltip = {
        pointFormat:
          '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.y:.2f}</b><br/>',
      }
    }
    return <Chart options={config} constructorType="stockChart" />
  };

  render() {
    const { filter, monthFilter } = this.state
    const { filters, isMutual, title } = this.props
    const monthFilters = [
      {
        name: '二四季度',
        value: 'sorfQuarter',
      },
      {
        name: '全部',
        value: 'all',
      },
    ]
    return (
      <Card
        title={title}
        extra={
          <>
            {isMutual && false && (
              <Dropdown
                overlay={
                  <Menu onClick={this.handleMonthFilterChange}>
                    {monthFilters.map(item => (
                      <Menu.Item key={JSON.stringify(item)}>{item.name}</Menu.Item>
                    ))}
                  </Menu>
                }
              >
                <Button style={{ marginLeft: 8 }}>
                  {monthFilter.name} <DownOutlined />
                </Button>
              </Dropdown>
            )}
            {filters && filters.length !== 0 && (
              <Dropdown
                overlay={
                  <Menu onClick={this.handleFilterChange}>
                    {filters.map(item => (
                      <Menu.Item key={JSON.stringify(item)}>{item.name}</Menu.Item>
                    ))}
                  </Menu>
                }
              >
                <Button style={{ marginLeft: 8 }}>
                  {filter.name} <DownOutlined />
                </Button>
              </Dropdown>
            )}
          </>
        }
      >
        {this.renderChart()}
      </Card>
    );
  }
}
