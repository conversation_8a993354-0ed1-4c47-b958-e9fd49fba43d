import React, { Component } from 'react'
import moment, { Moment } from 'moment'
import { DatePicker, Button } from 'antd'

interface ComponentProps {
  url: string;
  dates: Array<string>;
  startDate: Moment;
  endDate: Moment;
}
interface ComponentState {
  startDate: Moment | undefined;
  endDate: Moment | undefined;
  initialStartDate: Moment | undefined;
  initialEndDate: Moment | undefined;
}
export default class PositionDateRangeSelect extends Component<ComponentProps, ComponentState> {
  constructor(props: ComponentProps) {
    super(props)
    const { dates, startDate, endDate } = props
    this.state = {
      startDate,
      endDate,
      initialStartDate: moment(dates[0]),
      initialEndDate: moment(dates[dates.length - 1]),
    }
  }

  handleStartDateChange = (startDate: Moment | null) => {
    this.setState({ startDate })
  };

  handleEndDateChange = (endDate: Moment | null) => {
    this.setState({ endDate })
  };

  disabledStartDate = (startDate: Moment): Boolean => {
    const { endDate, initialStartDate } = this.state
    if (!startDate || !endDate) {
      return false
    }
    return (
      startDate.valueOf() > endDate.valueOf() || startDate.valueOf() < initialStartDate.valueOf()
    )
  };

  disabledEndDate = (endDate: Moment): Boolean => {
    const { startDate, initialEndDate } = this.state
    if (!endDate || !startDate) {
      return false
    }
    return endDate.valueOf() <= startDate.valueOf() || endDate.valueOf() > initialEndDate.valueOf()
  };

  render() {
    const { url } = this.props
    const { startDate, endDate } = this.state
    const href = `${url}?startDate=${startDate.format('YYYY-MM-DD')}&endDate=${endDate.format(
      'YYYY-MM-DD',
    )}`
    return (
      <>
        <span style={{ marginRight: '10px' }}>日期范围:</span>
        <DatePicker
          value={startDate}
          style={{ width: '140px' }}
          disabledDate={this.disabledStartDate}
          placeholder="起始日期"
          onChange={this.handleStartDateChange}
        />
        <span style={{ marginRight: '10px' }}></span>
        <DatePicker
          value={endDate}
          style={{ width: '140px' }}
          disabledDate={this.disabledEndDate}
          placeholder="结束日期"
          onChange={this.handleEndDateChange}
        />
        <Button type="primary" size="small" style={{ margin: '0 10px' }}>
          <a href={href}>确定</a>
        </Button>
      </>
    )
  }
}
