import React, { Component } from 'react'
import moment from 'moment'
import { Dispatch } from 'redux'
import { formatMessage } from 'umi-plugin-react/locale'
import { Card, Row, Col, Select, Table, Spin, Tabs, Affix } from 'antd'
import { connect } from 'dva'
import { scroller } from 'react-scroll'
import DataTable from '@/components/DataTable'
import AnalyzeCard from './components/AnalyzeCard'
import IndustryPreference from './components/IndustryPreference'
import EQPosition from './components/EQPosition'
import BondStatistics from './components/BondStatistics'
import BondTermDistribution from './components/BondTermDistribution'
import BondIssuerDistribution from './components/BondIssuerDistribution'
import BondCashFlow from './components/BondCashFlow'
import BondRating from './components/BondRating'
import Margin from './components/Margin'
import { StateType } from './model'
import PositionModel from './PositionModel'
import styles from './style.less'

const { TabPane } = Tabs
const { Option } = Select
const t = (id: string) => formatMessage({ id })

interface DailyProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  analyzeData: StateType;
  computedMatch: any;
  currentFund: any;
}

@connect(
  ({
    fund,
    position,
    loading,
    computedMatch,
  }: {
    fund: any;
    position: StateType;
    computedMatch: any;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    computedMatch,
    analyzeData: position,
    loading: loading.effects['position/fetch'],
  }),
)
class Daily extends Component<DailyProps> {
  constructor(props: DailyProps) {
    super(props)
    const { currentFund } = props
    const dates = [].concat(currentFund.dates).reverse()
    const currentDate = dates[0]
    this.state = {
      benchmark: '沪深300',
      dates,
      currentDate,
      activeKey: 'overview',
    }
  }

  componentDidMount() {
    const { currentDate } = this.state
    this.runAnalyze(currentDate)
  }

  isEmptyData = (keys: string[]) => {
    const {
      analyzeData: { dailyData },
    } = this.props
    return (
      !dailyData ||
      keys.every(key => {
        return !dailyData[key] || dailyData[key].length === 0
      })
    )
  };

  runAnalyze = currentDate => {
    const { dispatch, currentFund } = this.props
    const fundId = currentFund._id
    const benchmark = currentFund.benchmark === '中证500' ? '中证500' : '沪深300'
    const date = moment(new Date(currentDate)).format('YYYYMMDD')
    dispatch({
      type: 'position/fetch',
      payload: { fundId, date },
    })
    dispatch({
      type: 'position/fetchBenchmarkIndustyRatio',
      payload: {
        params: {
          date,
          benchmark,
        },
      },
    })
    dispatch({
      type: 'position/fetchBondCashflow',
      payload: { fundId, date },
    })
  };

  handleDateChange = (currentDate: string) => {
    scroller.scrollTo('overview', { offset: -100 })
    this.runAnalyze(currentDate)
  };

  handleTabChange = (activeKey: string) => {
    scroller.scrollTo(activeKey, { offset: -70 })
  };

  renderReturnValue = (value: number) => {
    const className = value > 0 ? 'colorUp' : 'colorDown'
    return <span className={className}>{(value * 100).toFixed(2) + '%'}</span>
  };

  renderTableNumericColumn = (val: number) => (
    <div style={{ textAlign: 'right' }}>{val.toFixed(2)}</div>
  );

  renderSummaryTable() {
    const {
      analyzeData: {
        dailyData: { summary },
      },
    } = this.props
    summary.LEVERAGE = summary.ASSET / summary.NET_ASSET
    const quotas = [
      {
        value: 'NAV',
        decimal: 4,
      },
      {
        value: 'ACC_NAV',
        decimal: 4,
      },
      {
        value: 'ASSET',
      },
      {
        value: 'DEBIT',
      },
      {
        value: 'NET_ASSET',
      },
      {
        value: 'LEVERAGE',
      },
    ]
    const columns = [
      {
        title: 'Name',
        dataIndex: 'name',
        render: (val: string, record: any) => {
          return <div style={{ paddingLeft: record.paddingLeft || 0 }}>{val}</div>
        },
      },
      {
        title: 'value',
        dataIndex: 'value',
        align: 'right',
      },
    ]
    const data = quotas.map(quota => {
      return {
        name: formatMessage({ id: `quota.${quota.value}` }),
        value: summary[quota.value] && summary[quota.value].toFixed(quota.decimal || 2),
      }
    })
    return (
      <Table
        showHeader={false}
        pagination={false}
        columns={columns}
        dataSource={data}
        className="stripe-table"
        size="small"
      />
    )
  }

  renderContent() {
    const {
      analyzeData: { dailyData, cashflows },
      dispatch,
      loading,
    } = this.props
    if (!dailyData) {
      return (
        <Spin>
          <Card style={{ height: '500px' }} />
        </Spin>
      )
    }

    const positionModel = new PositionModel({ analysisResult: dailyData, t })
    const { currentDate } = this.state
    const date = moment(new Date(currentDate)).format('YYYYMMDD')
    return (
      <div className={styles.wrapper}>
        <Spin spinning={loading}>
          <div id="overview" />
          <Row gutter={10}>
            <Col md={7} sm={24}>
              <Card
                title="资产概览"
                style={{
                  height: '376px',
                }}
              >
                {this.renderSummaryTable()}
              </Card>
            </Col>
            <Col md={17} sm={24}>
              <AnalyzeCard title="资产类型结构" {...positionModel.buildAssetStructureConfig()} />
            </Col>
          </Row>
          <Margin id="stock" />
          {!this.isEmptyData(['concept']) && (
            <>
              <AnalyzeCard title="股票投资概念" {...positionModel.buildConceptConfig()} />
              <Margin />
            </>
          )}
          {!this.isEmptyData(['industry']) && (
            <>
              <IndustryPreference
                title="股票投资行业偏好"
                dailyData={dailyData}
                date={date}
                dispatch={dispatch}
              />
              <Margin />
              <IndustryPreference
                isIndustryBoard
                title="股票投资板块偏好"
                dailyData={dailyData}
                date={date}
                dispatch={dispatch}
              />
              <Margin />
            </>
          )}
          {!this.isEmptyData(['position']) && (
            <EQPosition {...positionModel.buildPositionConfig()} />
          )}
          {!this.isEmptyData(['fundPosition']) && (
            <>
              <Margin id="fund" />
              <AnalyzeCard title="基金持仓" {...positionModel.buildFundPositionConfig()} />
            </>
          )}
          {!this.isEmptyData(['bondPosition']) && (
            <>
              <Margin id="bond" />
              <Row gutter={10}>
                <Col md={10} sm={24}>
                  <BondTermDistribution {...positionModel.buildBondTermDistributionConfig()} />
                </Col>
                <Col md={14} sm={24}>
                  <BondStatistics {...positionModel.buildBondStatisticsConfig()} />
                </Col>
              </Row>
            </>
          )}
          {!this.isEmptyData(['bondConcept']) && (
            <>
              <Margin />
              <AnalyzeCard title="债券投资概念" {...positionModel.buildBondConceptConfig()} />
            </>
          )}
          {!this.isEmptyData(['bondRegionalDistribution']) && (
            <>
              <Margin />
              <AnalyzeCard
                title="城投债地区分布"
                {...positionModel.buildBondRegionalDistribution()}
              />
            </>
          )}
          {!this.isEmptyData(['bondPosition']) && (
            <>
              <Margin />
              <Row gutter={10}>
                <Col md={10} sm={24}>
                  <BondIssuerDistribution {...positionModel.buildBondIssuerDistribution()} />
                </Col>
                <Col md={14} sm={24}>
                  <BondCashFlow rows={cashflows} t={t} />
                </Col>
              </Row>
              <Margin />
              <BondRating {...positionModel.buildBondCreditRatingConfig()} />
              <Margin />
              <Card title="债券持仓">
                <DataTable {...positionModel.buildBondPositionConfig()} />
              </Card>
            </>
          )}
        </Spin>
      </div>
    )
  }

  render() {
    const { currentDate, dates } = this.state
    console.log(this.props)
    return (
      <div>
        <Affix offsetTop={0}>
          <Tabs
            defaultActiveKey="overview"
            className={styles.tabs}
            onChange={this.handleTabChange}
            tabBarExtraContent={
              <div>
                <span>选择日期：</span>
                <Select
                  showSearch
                  defaultValue={currentDate}
                  style={{ width: 200 }}
                  placeholder="请选择日期"
                  optionFilterProp="children"
                  onChange={this.handleDateChange}
                  filterOption={(input, option) =>
                    option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {dates.map(date => (
                    <Option value={date}>{date}</Option>
                  ))}
                </Select>
              </div>
            }
          >
            <TabPane tab="资产概览" key="overview" />
            {!this.isEmptyData(['position', 'concept', 'industry']) && (
              <TabPane tab="股票持仓" key="stock" />
            )}
            {!this.isEmptyData(['fundPosition']) && <TabPane tab="基金持仓" key="fund" />}
            {!this.isEmptyData(['bondPosition', 'bondConcept', 'bondRegionalDistribution']) && (
              <TabPane tab="债券持仓" key="bond" />
            )}
          </Tabs>
        </Affix>
        {this.renderContent()}
      </div>
    )
  }
}

export default Daily
