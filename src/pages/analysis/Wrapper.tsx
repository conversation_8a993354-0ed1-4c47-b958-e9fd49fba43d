import React, { useEffect } from 'react'
import { connect } from 'dva'
import { Dispatch } from 'redux'
import Link from 'umi/link'
import { Empty, Breadcrumb, Button, Divider } from 'antd'
import PageLoading from '@/components/PageLoading'
import styles from './style.less'

interface WrapperProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  currentFund: any;
  match: any;
  location: any;
}

const Wrapper: React.FC<WrapperProps> = props => {
  const {
    dispatch,
    children,
    location: { pathname },
    match: {
      params: { id },
    },
    currentFund,
    loading,
  } = props
  useEffect(() => {
    dispatch({
      type: 'fund/fetchOne',
      payload: {
        id,
      },
    })
  }, [])
  if (!currentFund && loading) {
    return <PageLoading />
  }
  if (!currentFund) {
    return (
      <Empty
        style={{ padding: '100px' }}
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="你查看的基金不存在"
      />
    )
  }
  let activeTab = 'attribution'
  if (~pathname.indexOf('position')) {
    activeTab = 'position'
  }
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Link to="/fund/database">基金数据库</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{currentFund.name}</Breadcrumb.Item>
      </Breadcrumb>
      <Divider className={styles.divider} />
      <div className={styles.buttonWrapper}>
        <Button type={activeTab === 'attribution' ? 'primary' : 'default'}>
          <Link to={`/analysis/${currentFund._id}/attribution`}>归因分析</Link>
        </Button>
        <Button type={activeTab === 'position' ? 'primary' : 'default'}>
          <Link to={`/analysis/${currentFund._id}/position/daily`}>持仓分析</Link>
        </Button>
      </div>
      <div>{children}</div>
    </div>
  )
}

export default connect(
  ({
    fund,
    loading,
  }: {
    fund: any;
    loading: {
      models: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentFund: fund.currentFund,
    loading: loading.models.fund,
  }),
)(Wrapper)
