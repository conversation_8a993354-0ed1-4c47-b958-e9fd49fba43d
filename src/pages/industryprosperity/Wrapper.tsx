import React, { useState } from 'react'
import {
  Card,
  Affix,
  Tabs,
  Spin,
  Breadcrumb,
} from 'antd'
import router from 'umi/router'
import Link from 'umi/link'
import SelectDate from '@/components/SelectDate'
import { useRequest } from '@umijs/hooks'
import { queryIndProsperityData } from './service'
import Overview from './Overview'
import Detail from './Detail'
import Cycle from './Cycle'

const { TabPane } = Tabs

const Content = (props) => {
  const dates = props.dates
  const [date, setDate] = useState(dates[0])
  const tabs = [{
    name: '景气跟踪',
    tab: 'overview',
  }, {
    name: '行业景气指数',
    tab: 'detail',
  }, {
    name: '景气度与经济周期',
    tab: 'cycle',
  }, {
    name: '宏观框架',
    tab: 'framework'
  }]
  const [activeTab, setActiveTab] = useState(props.location.query.tab || tabs[0].tab)
  const handleTabChange = currentTab => {
    setActiveTab(currentTab)
  }
  const [ind, setInd] = useState(null)
  const handleIndustryClick = industry => {
    setInd(industry)
    setActiveTab('detail')
  }
  const getTabContent = () => {
    if (activeTab === 'overview') {
      return <Overview date={date} handleIndustryClick={handleIndustryClick}/>
    } else if (activeTab === 'detail') {
      return <Detail industry={ind} date={date}/>
    } else if (activeTab === 'cycle') {
      return <Cycle />
    } else if (activeTab === 'framework') {
      router.push(`/research/macrostrategy/framework/${props.location.query.id}`)
      return
    }
    return null
  }
  return (
    <div>
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>宏观策略研究</Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link to={'/research/macrostrategy/framework'}>宏观框架</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{props.location.query.name}</Breadcrumb.Item>
      </Breadcrumb>
      <Affix offsetTop={0}>
        <Card className="nav-tab-wrapper">
          <Tabs
            animated={false}
            activeKey={activeTab}
            onTabClick={handleTabChange}
            tabBarExtraContent={
              activeTab === 'overview' &&
              <SelectDate
                date={date}
                dates={dates}
                onChange={setDate}
              />
            }
          >
            {tabs.map(item => {
              return (
                <TabPane tab={item.name} key={item.tab} />
              )
            })}
          </Tabs>
        </Card>
      </Affix>
      <div style={{ marginTop: 15 }}>
        {getTabContent()}
      </div>
    </div>
  )
}

const Wrapper = (props) => {
  const { loading, data = [] } = useRequest(() => {
    return queryIndProsperityData({ dataKey: 'dateList' })
  })
  return (
    <Spin spinning={loading}>
      {!loading && <Content { ...props } dates={data} />}
    </Spin>
  )
}

export default Wrapper
