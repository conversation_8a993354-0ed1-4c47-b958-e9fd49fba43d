import React from 'react'
import {
  Spin,
  Divider,
} from 'antd'
import HeatMap from '@/components/HeatMap'
import { useRequest } from '@umijs/hooks'
import { queryIndProsperityData } from './service'

const Overview = ({
  date,
  handleIndustryClick,
}) => {
  const { loading, data = [] } = useRequest(() => {
    return queryIndProsperityData({ dataKey: 'overview', bizDate: date })
  }, {
    refreshDeps: [date],
  })
  const chartData = data.sort((fst, snd) => fst.cycle_value - snd.cycle_value).map(item => {
    return {
      id: item.sw_industry_code,
      name: item.sw_industry_name,
      value: [1, item.cycle_value, item.cycle_value * Math.abs(item.cycle_value) * 2],
    }
  })
  const onEvents = {
    click: (action) => {
      if (action.data) {
        handleIndustryClick({
          name: action.data.name,
          code: action.data.id,
        })
      }
    },
  }
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        <HeatMap height="420px" name="景气度" data={chartData} format="integer" visualDimension={2} onEvents={onEvents}/>
      </Spin>
      <Divider orientation="left">数据说明</Divider>
      <p>
        我们结合财务报表、一致预期等多个维度数据，构建了18个景气度指标来对各行业景气状态进行月度打分（该指标景气度向好打1分，恶化打-1分，无信号打0分）。综合得分越高的行业越景气，全市场综合景气度大于零的行业个数越多，意味着全市场景气度越高。
      </p>
    </div>
  )
}

export default Overview
