import React, { useState } from 'react'
import _ from 'lodash'
import {
  Spin,
  Divider,
  Radio,
  Card,
} from 'antd'
import { useRequest } from '@umijs/hooks'
import Echart from '@/components/Chart/Echarts'
import { queryIndProsperityData } from './service'

const getChartOptions = (data, chartSeries) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      // axisPointer: {
      //   type: 'shadow',
      // },
    },
    legend: {
      data: chartSeries.map(item => item.title),
    },
    xAxis: [
      {
        type: 'category',
        data: data.map(item => item.the_date),
      },
    ],
    yAxis: [{
      type: 'value',
    }, {
      type: 'value',
    }],
    series: chartSeries.map(serie => {
      return {
        name: serie.title,
        type: 'line',
        yAxisIndex: serie.yAxisIndex,
        data: data.map(item => _.round(item[serie.dataIndex] || 0, 2)),
        smooth: false,
        ...serie.serieConfig,
      }
    }),
  }
  return option
}

const Cycle = () => {
  const cycles = ['全部行业', '周期行业', '综合景气指数']
  const [cycle, setCycle] = useState(cycles[0])
  const handleCycleChange = event => {
    const type = event.target.value
    setCycle(type)
  }
  const { loading, data = [] } = useRequest(() => {
    const params = {
      dataKey: 'industryCycleCount', indLevel: '申万一级'
    }
    if (cycle === '周期行业') {
      params.indLevel = '申万一级(周期)'
    } else if (cycle === '综合景气指数') {
      params.dataKey = 'industryCycleValue'
      params.indLevel = '申万一级'
    }
    return queryIndProsperityData(params)
  }, {
    refreshDeps: [cycle],
  })
  const chartSeries = cycle !== '综合景气指数'
  ? [{
    title: '景气度大于0行业个数',
    dataIndex: 'up0_sum',
    serieConfig: {
      areaStyle: {},
    },
  }, {
    title: '沪深300',
    dataIndex: 'hs300_price',
    serieConfig: {
      yAxisIndex: 1,
    },
  }]
  : [{
    title: '行业整体景气度',
    dataIndex: 'cycle_value_mean',
    serieConfig: {
      areaStyle: {},
    },
  }, {
    title: '沪深300',
    dataIndex: 'hs300_price',
    serieConfig: {
      yAxisIndex: 1,
    },
  }]
  const options = getChartOptions(data, chartSeries)
  return (
    <div style={{ marginTop: 15 }}>
      <Spin spinning={loading}>
        <Card
          extra={
            <Radio.Group
              value={cycle}
              size="small"
              onChange={handleCycleChange}
            >
              {cycles.map(item => <Radio.Button value={item}>{item}</Radio.Button>)}
            </Radio.Group>
          }
        >
          <Echart style={{ height: '450px' }} options={options} key={cycle}/>
        </Card>
      </Spin>
      <Divider orientation="left">数据说明</Divider>
      <p>
        通过对景气度大于零的行业个数进行统计，可以看到全市场景气行业数呈周期性变化：当市场处于繁荣期，有半数以上的行业景气度大于零；当市场处于复苏期，景气行业个数明显较小，只有个位数行业景气度大于零。
      </p>
      <p>
        通过对比来看，市场景气行业个数从2015至今经历了两轮以上的周期性变化，每轮周期的平均持续时间在40个月左右，与实体经济基钦周期的长度相吻合。
      </p>
    </div>
  )
}

export default Cycle
