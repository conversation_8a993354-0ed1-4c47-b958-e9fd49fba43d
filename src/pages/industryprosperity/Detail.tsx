import React, { useState } from 'react'
import { useRequest } from '@umijs/hooks'
import {
  Spin,
  Card,
  Divider,
} from 'antd'
import { queryIndProsperityData } from './service'
import { industryListSW } from '@/utils/kymDefMapping'
import Echart from '@/components/Chart/Echarts'
import SearchSelect from '@/components/SearchSelect'

const getChartOptions = (data, chartSeries) => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: chartSeries.map(item => item.title),
    },
    xAxis: [
      {
        type: 'category',
        data: data.map(item => item.the_date),
      },
    ],
    yAxis: [{
      type: 'value',
    }, {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    }],
    series: chartSeries.map(serie => {
      return {
        name: serie.title,
        type: serie.chartType || 'bar',
        yAxisIndex: serie.yAxisIndex,
        data: data.map(item => _.round(item[serie.dataIndex] || 0, 2)),
        smooth: false,
      }
    }),
  }
  return option
}

const getQuotaChartOptions = (data) => {
  const option = {
    grid: {
      bottom: '30%',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    xAxis: [
      {
        type: 'category',
        data: data.map(item => item.factor_name.replace(/（.+）/, '')),
        axisLabel: {
          interval: 0,
          rotate: 40,
          // formatter: (value) => {
          //   return value.split('').join('\n')
          // }
        },
      },
    ],
    yAxis: [{
      type: 'value',
      interval: 1,
    }],
    series: [{
      name: '指标信号',
      type: 'bar',
      data: data.map(item => item.cycle_signal)
    }],
  }
  return option
}

const Detail = ({
  industry, date,
}) => {
  const [curIndustry, setCurIndustry] = useState(industry || industryListSW[0])
  const handleIndustryChange = (indCode) => {
    setCurIndustry(industryListSW.find(item => item.code === indCode))
  }
  const [bizDate, setBizDate] = useState(date)
  const [dates, setDates] = useState([])
  const onSeriesDataLoaded = (data) => {
    const dates = data.map(item => {
      return {
        title: item.the_date,
        dataIndex: item.the_date,
      }
    }).reverse()
    setDates(dates)
  }
  const { loading, data = [] } = useRequest(() => {
    return queryIndProsperityData({ dataKey: 'industrySeries', indCode: curIndustry.code })
  }, {
    onSuccess: onSeriesDataLoaded,
    refreshDeps: [curIndustry.code],
  })
  const { loading: loadingDetail, data: detailData = [] } = useRequest(() => {
    return queryIndProsperityData({ dataKey: 'factorDetail', indCode: curIndustry.code, bizDate })
  }, {
    refreshDeps: [bizDate, curIndustry.code],
  })
  const chartSeries = [{
    title: '景气度',
    dataIndex: 'cycle_value',
  }, {
    title: '营收增速(右轴)',
    dataIndex: 'opr_ttm_yoy',
    yAxisIndex: 1,
    chartType: 'line',
  }]
  const options = getChartOptions(data, chartSeries)
  const industryOptions = industryListSW.map(item => {
    return {
      title: item.name,
      dataIndex: item.code,
    }
  })
  const quotaOptions = getQuotaChartOptions(detailData)
  const onEvents = {
    click: (action) => {
      setBizDate(action.name)
    },
  }
  return (
    <div>
      <Card
        title={curIndustry.name}
        extra={
          <SearchSelect
            placeholder="请选择行业"
            value={curIndustry.code}
            options={industryOptions}
            onChange={handleIndustryChange}
            width="150px"
          />
        }
      >
        <Spin spinning={loading}>
          <Echart style={{ height: '400px' }} options={options} onEvents={onEvents}/>
        </Spin>
      </Card>
      <Card
        title={`${curIndustry.name}景气度指标信号`}
        extra={
          <SearchSelect
            placeholder="请选择日期"
            value={bizDate}
            options={dates}
            onChange={setBizDate}
            width="150px"
          />
        }
      >
        <Spin spinning={loadingDetail}>
          <Echart style={{ height: '400px' }} options={quotaOptions}/>
        </Spin>
      </Card>
      <Divider orientation="left">数据说明</Divider>
      <p>
        我们结合财务报表、一致预期等多个维度数据，构建了18个景气度指标来对各行业景气状态进行月度打分（该指标景气度向好打1分，恶化打-1分，无信号打0分）。综合得分越高的行业越景气，全市场综合景气度大于零的行业个数越多，意味着全市场景气度越高。
      </p>
    </div>
  )
}

export default Detail
