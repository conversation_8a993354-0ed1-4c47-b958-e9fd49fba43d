import { message } from 'antd';
import { Effect } from 'dva'
import { Reducer } from 'redux'
import { load, loadPreview, postTemporaryAnswer, postAnswer, extractMatrix, uploadAttachment } from './service'
import { notification } from 'antd'

export interface ModelState {
  survey?: any;
  surveyList?: any,
  extractResult?: any;
  attachmentResult?: any,
  saveSuccess?: boolean
}

export interface ModelType {
  namespace: 'survey';
  state: ModelState;
  effects: {
    fetchSurvey: Effect;
    fetchPreview: Effect;
    createTemporaryAnswer: Effect;
    createAnswer: Effect;
    fetchExtractMatrix: Effect;
    fetchUploadAttachment: Effect;
  };
  reducers: {
    save: Reducer<ModelState>;
    saveAttachment: Reducer<ModelState>;
    resetSurveyState: Reducer<ModelState>;
  };
}

const SurveysModel: ModelType = {
  namespace: 'survey',
  state: {
    survey: null,
    surveyList: null,
    extractResult: {},
    attachmentResult: {},
    saveSuccess: false
  },
  effects: {
    *fetchSurvey({ payload: { id, params } }, { call, put }) {
      const response = yield call(load, id, params)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
    },
    *fetchPreview({ payload: { id } }, { call, put }) {
      const response = yield call(loadPreview, id)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
    },
    *createTemporaryAnswer({ payload: { id, data, params } }, { call, put }) {
      yield call(postTemporaryAnswer, id, data, params)
      return message.success('临时保存成功')
    },
    *createAnswer({ payload: { id, data, params } }, { call, put }) {
      yield call(postAnswer, id, data, params)
      yield put({
        type: 'save',
        payload: {
          saveSuccess: true,
        },
      })
      return message.success('提交成功')
    },
    *fetchExtractMatrix({ payload: { id, data } }, { call, put }) {
      const response = yield call(extractMatrix, id, data)
      yield put({
        type: 'save',
        payload: {
          extractResult: {
            [response.id]: response.data
          },
        },
      })
      notification.success({ message: '上传成功' })
    },
    *fetchUploadAttachment({ payload: { id, data } }, { call, put }) {
      const response = yield call(uploadAttachment, id, data)
      yield put({
        type: 'saveAttachment',
        payload: {
          attachmentResult: response,
        },
      })
      notification.success({ message: '上传成功' })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    saveAttachment(state, action) {
      const list = {
        ...state.attachmentResult,
        [action.payload.attachmentResult.id]: action.payload.attachmentResult
      }
      return {
        ...state,
        attachmentResult: list,
      }
    },
    resetSurveyState(state, action) {
      return {
        ...state,
        ...action.payload.data,
      }
    }
  },
}

export default SurveysModel
