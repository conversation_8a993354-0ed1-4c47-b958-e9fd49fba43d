import request from '@/utils/request'

export async function load(id: string, params: any): Promise<any> {
  return request(`/api/survey/${id}`, {
    method: 'GET',
    params,
  })
}

export async function loadPreview(id: any) {
  return request(`/api/admin/survey/${id}`, {
    method: 'GET',
  })
}


export async function postAnswer(id: String, data: any, params: any) {
  return request(`/api/survey/${id}`, {
    method: 'POST',
    params,
    data
  })
}

export async function postTemporaryAnswer(id: String, data: any, params: any) {
  return request(`/api/survey/${id}/temporary`, {
    method: 'POST',
    params,
    data
  })
}

export async function extractMatrix(id: string, data: object): Promise<any> {
  const formData = new FormData()
  formData.append('attachment', data, data.name)
  return request('/api/survey/extractMatrix', {
    method: 'post',
    params: { id },
    body: formData,
  })
}

export async function uploadAttachment(id: string, data: object): Promise<any> {
  const formData = new FormData()
  formData.append('attachment', data, data.name)
  return request('/api/survey/attachment', {
    method: 'post',
    params: { id },
    body: formData,
  })
}
