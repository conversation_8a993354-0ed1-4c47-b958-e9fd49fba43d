import React, { Component } from 'react'
import classnames from 'classnames'
import { Link } from 'umi'
import Question from '@/components/Question'
import SurveyTab from '@/components/SurveyTab'
import GetPdfAndPng from '@/components/GetPdfAndPng'
import validateFundAnswer from '@/utils/validateFundAnswer'
import { CurrentUser, UserModelState } from '@/models/user'
import { getToken } from '@/utils/utils'
import { parse } from 'qs'
import { Tooltip, Progress, Button, Result, message, Spin, Tag, Breadcrumb } from 'antd'
import ClipboardJS from 'clipboard'
import { Dispatch } from 'redux'
import { connect } from 'dva'
import { ModelState } from './model'
import styles from './style.less'
interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  currentUser?: CurrentUser,
  survey?: any,
  attachmentResult?: any,
  extractResult?: any,
  saveSuccess?: boolean
}
interface ComponentState {
  inputError: Object,
  viewMode?: String,
  attachments?: Object,
  completes: Object,
  tabs: Array<object>,
  currentTab: String,
  answers?: any,
  remarks?: any
  otherAnswers?: any,
  sortResults?: any,
}

@connect(
  ({
    user,
    survey,
    loading,
  }: {
    user: UserModelState,
    survey: ModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }) => ({
    currentUser: user.currentUser,
    survey: survey.survey,
    extractResult: survey.extractResult,
    attachmentResult: survey.attachmentResult,
    saveSuccess: survey.saveSuccess,
    loading: loading.effects['survey/fetchSurvey'],
  }),
)

class Survey extends Component<ComponentProps, ComponentState>{

  state = this.buildState()

  componentDidMount() {
    const { survey, location } = this.props
    const queryObject = location.query || parse(location.search.substr(1))
    const authorId = queryObject && queryObject.authorId
    const pathname = location.pathname
    const id = pathname && pathname.split('/').slice(-1)[0]
    if (pathname && ~pathname.indexOf('/duediligence/preview')) {
      this.loadPreview(id)
    } else {
      this.load(id, { authorId })
    }
    if (survey && survey.hasTemporaryAnswer) {
      message.success('成功加载上次保存的答案')
    }
    window.onbeforeunload = () => 'Do you want to leave?'
  }

  componentWillReceiveProps(nextProps) {
    const { saveTemporarySuccess, uploadSuccess, survey, attachmentResult } = nextProps
    if (nextProps.attachmentResult != this.props.attachmentResult) {
      const { completes, answers, attachments } = this.state
      const id = Object.keys(attachmentResult)[0]
      const attachment = attachmentResult[id]
      this.setState({
        completes: { ...completes, [id]: attachment && attachment.answer },
        answers: { ...answers, [id]: attachment },
        attachments: { ...attachments, [id]: attachment }
      })
    }
    if (saveTemporarySuccess) {
      message.success('保存成功！')
      this.resetSurveyState({ saveTemporarySuccess: false })
    }
    if (uploadSuccess) {
      message.success('上传成功')
      this.resetSurveyState({ uploadSuccess: false })
    }
  }
  shouldComponentUpdate(nextProps, nextState) {
    if (nextProps.attachmentResult != this.props.attachmentResult) {
      return true
    }
    if (nextProps.survey != this.props.survey) {
      return true
    }
    if (nextState) {
      return true
    }
    return false
  }

  componentDidUpdate(prevProps) {
    const { extractResult, attachmentResult, survey } = this.props
    if (!prevProps.survey && survey) {
      this.setState(this.buildState()) // eslint-disable-line
    }
    if (Object.keys(extractResult).length !== 0) {
      this.resetSurveyState({ extractResult: {} })
    }
    if (Object.keys(attachmentResult).length !== 0) {
      this.resetSurveyState({ attachmentResult: {} })
    }
  }

  componentWillUnmount() {
    if (this.props.saveSuccess) {
      this.resetSurveyState({ saveSuccess: false })
    }
    window.onbeforeunload = null
  }

  load = (id: any, params: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'survey/fetchSurvey',
      payload: { id, params },
    })
  }

  loadPreview = (id: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'survey/fetchPreview',
      payload: { id },
    })
  }

  resetSurveyState = (data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'survey/resetSurveyState',
      payload: { data },
    })
  }

  createTemporaryAnswer = (id: string, data: object, params: object) => {
    const { dispatch } = this.props
    dispatch({
      type: 'survey/createTemporaryAnswer',
      payload: { id, data, params },
    })
  }


  postAnswer = (id: string, data: object, params: object) => {
    const { dispatch } = this.props
    dispatch({
      type: 'survey/createAnswer',
      payload: { id, data, params },
    })
  }

  extractMatrix = (id: string, data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'survey/fetchExtractMatrix',
      payload: { id, data },
    })
  }

  uploadAttachment = (id: string, data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'survey/fetchUploadAttachment',
      payload: { id, data },
    })
  }


  isNotA4() {
    const pdfFormat =
      (this.props.location.search &&
        parse(this.props.location.search.substr(1)).pdfFormat) ||
      (this.props.location.query && this.props.location.query.pdfFormat)
    return pdfFormat !== 'A4'
  }

  buildState() {
    const {
      location: { pathname },
      survey
    } = this.props
    const state = {
      inputError: {},
      viewMode: this.isNotA4() ? 'tab' : 'list',
      attachments: {},
      completes: {},
      tabs: [{}],
      currentTab: ''
    }
    state.isPreview = !!~pathname.indexOf('/duediligence/preview')
    if (!survey) {
      return state
    }
    const {
      questions,
      remarks,
      answers,
      otherAnswers,
      tabs,
      attachmentAnswers
    } = survey
    state.answers = !answers ? {} : answers
    state.remarks = remarks || {}
    state.otherAnswers = otherAnswers || {}
    state.attachments = attachmentAnswers || {}
    state.completes = !answers
      ? {}
      : questions.reduce((out, question) => {
        if (question.type === 'sort') {
          return out
        }
        if (!answers[question._id]) {
          return out
        }
        if (question.type === 'fund') {
          answers[question._id].forEach(answer => {
            answer.fundName = answer.question
          })
        }
        out[question._id] = answers[question._id]
        return out
      }, {})
    state.sortResults = questions
      .filter(question => question.type === 'sort')
      .reduce((out, question) => {
        if (!answers || !answers[question._id]) {
          out[question._id] = question.options.map((option, index) => ({
            question: option.value,
            answer: index + 1
          }))
        } else {
          out[question._id] = answers[question._id]
        }
        return out
      }, {})
    state.tabs = tabs
    state.currentTab = tabs[0].id
    return state
  }

  mergeAnswer = (id, answer) => {
    this.setState({
      completes: { ...this.state.completes, [id]: answer }
    })
  }

  mergeAttachment = (id, attachment) => {
    this.setState({
      completes: { ...this.state.completes, [id]: attachment && attachment.answer },
      answers: { ...this.state.answers, [id]: attachment },
      attachments: { ...this.state.attachments, [id]: attachment }
    })
  }

  mergeAnswerRemark = (id, remark) => {
    this.setState({
      remarks: { ...this.state.remarks, [id]: remark }
    })
  }

  mergeOtherAnswer = (id, answer) => {
    this.setState({
      otherAnswers: { ...this.state.otherAnswers, [id]: answer }
    })
  }

  mergeSortResult = (id, result) => {
    this.setState({
      sortResults: { ...this.state.sortResults, [id]: result }
    })
  }

  handleInputError = (key, error) => {
    const { inputError } = this.state
    inputError[key] = error
    this.setState({ inputError })
    if (error) {
      message.warning(error)
    }
  }

  calculateProgress = () => {
    const { completes } = this.state
    const {
      survey: { questions }
    } = this.props
    const isRequiredMap = questions.reduce((out, question) => {
      out[question._id] = question.isRequired
      return out
    }, {})
    const questionTypeMap = questions.reduce((out, question) => {
      out[question._id] = question.type
      return out
    }, {})
    const total = questions
      .filter(question => question.isRequired)
      .reduce((out, question) => {
        let ret
        if (~question.type.indexOf('matrix')) {
          ret = out + question.questions.length
        } else {
          ret = out + 1
        }
        return ret
      }, 0)
    const done = Object.keys(completes).reduce((out, key) => {
      if (completes[key] === '' || !isRequiredMap[key]) {
        return out
      }
      let ret
      if (
        questionTypeMap[key] === 'fund' &&
        completes[key] &&
        completes[key].length
      ) {
        ret = out + 1
      } else if (
        Array.isArray(completes[key]) &&
        questionTypeMap[key] !== 'table'
      ) {
        ret = out + completes[key].length
      } else {
        ret = out + 1
      }
      return ret
    }, 0)
    return total === 0 ? 100 : (done / total) * 100
  }

  validateComplete() {
    const {
      survey: { questions },
    } = this.props
    const { completes } = this.state
    const ret = {}
    questions
      .filter(question => question.isRequired)
      .some(question => {
        if (~question.type.indexOf('matrix')) {
          if (
            !completes[question._id] ||
            completes[question._id].length < question.questions.length
          ) {
            ret.error = '请填写完成再提交'
            ret.question = question
            return true
          }
        } else if (question.type === 'fund') {
          if (!completes[question._id] || !completes[question._id].length) {
            ret.error = '请填写完成再提交'
            ret.question = question
            return true
          }
        } else if (question.type === 'table') {
          const tableAnswers = completes[question._id]
          const isInvalid =
            !tableAnswers ||
            !tableAnswers.some(item => {
              const tableAnswer = JSON.parse(item.answer)
              return question.options.every(option => tableAnswer[option.value])
            })
          if (isInvalid) {
            ret.error = '请填写完成再提交'
            ret.question = question
            return true
          }
        } else if (!completes[question._id]) {
          ret.error = '请填写完成再提交'
          ret.question = question
          return true
        }
        return false
      })
    return ret
  }

  validateFundQuestions() {
    const { completes } = this.state
    const {
      survey: { questions },
    } = this.props
    const ret = {}
    questions
      .filter(question => question.type === 'fund')
      .some(question => {
        const answers = completes[question._id] || []
        return answers.some(answer => {
          const result = validateFundAnswer(answer)
          if (result.error) {
            ret.error = result.error
            ret.question = question
            return true
          }
          return false
        })
      })
    return ret
  }

  validateInputError() {
    const {
      survey: { questions }
    } = this.props
    const { inputError } = this.state
    const questionMap = questions.reduce((out, question) => {
      out[question._id] = question
      return out
    }, {})
    const ret = {}
    Object.keys(inputError).some(key => {
      if (inputError[key]) {
        ret.error = inputError[key]
        ret.question = questionMap[key.slice(0, 24)]
        return true
      }
      return false
    })
    return ret
  }

  switchTab = tab => {
    this.setState({ currentTab: tab })
  }

  nextTab = () => {
    let nextTab
    const { currentTab, tabs } = this.state
    tabs.some((tab, index) => {
      if (tab.id === currentTab) {
        nextTab = tabs[index + 1].id
        return true
      }
      return false
    })
    this.setState({
      currentTab: nextTab
    })
    window.document.body.scrollTop = 0
  }

  switchViewMode = event => {
    this.setState({
      viewMode: event.target.value,
      currentTab: this.state.tabs[0].id
    })
  }

  alertError(error) {
    message.error(error.error)
    const { question } = error
    this.setState(
      {
        currentTab: question.tab
      },
      () => {
        const { jQuery } = window
        const scrollTo = jQuery(`#question-${question._id}`)
        jQuery('html, body').animate(
          {
            scrollTop: scrollTo.offset().top - 130
          },
          500
        )
        setTimeout(() => {
          scrollTo.addClass('shake animated')
          setTimeout(() => {
            scrollTo.removeClass('shake animated')
          }, 500)
        }, 500)
      }
    )
  }

  getAuthorId = () => {
    const { location: { search, query } } = this.props
    return (search && parse(search.substr(1)).authorId) || (query && query.authorId)
  }

  temporarySubmit = () => {
    const {
      survey: { questions }
    } = this.props
    const {
      completes,
      remarks,
      sortResults,
      otherAnswers,
      attachments
    } = this.state
    const { survey } = this.props
    const authorId = this.getAuthorId()
    const answers = questions.map(question => {
      const key = question._id
      let ret = { question_id: key }
      const answer = completes[key] || sortResults[key]
      if (Array.isArray(answer)) {
        ret.answers = answer
      } else {
        ret.answer = answer
      }
      if (remarks[key]) {
        ret.remark = remarks[key]
      }
      if (otherAnswers[key]) {
        ret.other = otherAnswers[key]
      }
      if (attachments[key]) {
        ret = { ...ret, ...attachments[key] }
      }
      return ret
    })
    this.createTemporaryAnswer(survey._id, answers, { authorId })
  }

  submit = () => {
    const {
      survey: { questions }
    } = this.props
    const {
      completes,
      remarks,
      sortResults,
      otherAnswers,
      attachments
    } = this.state
    const { survey } = this.props
    const authorId = this.getAuthorId()
    const completeError = this.validateComplete()
    const fundError = this.validateFundQuestions()
    const inputError = this.validateInputError()
    if (completeError.error) {
      this.alertError(completeError)
      return
    }
    if (fundError.error) {
      this.alertError(fundError)
      return
    }
    if (inputError.error) {
      this.alertError(inputError)
      return
    }
    const answers = questions.map(question => {
      const key = question._id
      let ret = { question_id: key }
      let answer = completes[key] || sortResults[key]
      if (question.type === 'fund' && answer) {
        answer = answer.map(item => {
          item.nets = JSON.parse(item.answer)
          item.scale = JSON.parse(item.scaleAnswers)
          return item
        })
      }
      if (Array.isArray(answer)) {
        ret.answers = answer
      } else {
        ret.answer = answer
      }
      if (remarks[key]) {
        ret.remark = remarks[key]
      }
      if (otherAnswers[key]) {
        ret.other = otherAnswers[key]
      }
      if (attachments[key]) {
        ret = { ...ret, ...attachments[key] }
      }
      return ret
    })
    this.postAnswer(survey._id, answers, { authorId })
  }

  copyData = () => {
    message.success('复制成功！')
  }

  render() {
    if (!this.props.survey) {
      return <Spin />
    }
    if (this.props.saveSuccess) {
      return (
        <div style={{ paddingTop: '30px' }}>
          <Result
            status="success"
            title="提交成功，感谢您的参与!"
          />
        </div>
      )
    }
    const {
      survey: { title, description, status, resubmit, _id },
      currentUser,
      extractResult,
      attachmentResult
    } = this.props
    const {
      remarks,
      otherAnswers,
      inputError,
      tabs,
      currentTab,
      completes,
      sortResults,
      viewMode,
      isPreview,
      answers
    } = this.state
    const questions = (this.props.survey.questions || []).filter(
      question => viewMode === 'list' || question.tab === currentTab
    )
    const indexMap = (questions || [])
      .filter(question => question.type !== 'paragraph')
      .reduce((out, question, index) => {
        out[question._id] = index + 1
        return out
      }, {})
    const now = this.calculateProgress()
    const backTo =
      (this.props.location.search &&
        parse(this.props.location.search.substr(1)).backTo) ||
      (this.props.location.query && this.props.location.query.backTo)
    const backToUrl = backTo || `/duediligence/survey/edit?id=${_id}`
    const token = getToken()
    const url = `${window.location && window.location.origin}/survey/${_id}`
    new ClipboardJS('.copy-btn', {
      text: function () {
        return url
      }
    })
    return (
      <div>
        <Breadcrumb>
          <Breadcrumb.Item><Link style={{ color: 'orange' }} to={isPreview ? '/duediligence/history' : '/duediligence/survey'}>问卷列表</Link></Breadcrumb.Item>
          <Breadcrumb.Item>
            {isPreview
              ? <Link style={{ color: 'orange' }} to={`/duediligence/surveyDetail/${this.props.survey._id}`}>{this.props.survey.title}</Link>
              : this.props.survey.title
            }
          </Breadcrumb.Item>
        </Breadcrumb>
        {!isPreview &&
        <Progress strokeColor='orange' percent={now} showInfo={false} size="small" className={styles.progress} />}
        {false && isPreview && this.isNotA4() && <div className={styles.previewNavbar}>
          <GetPdfAndPng
            className={styles.pdfButton}
            id="surveyPreview-pdfAndPng"
          />
          <span className="copy-btn" onClick={this.copyData}>
            <Button>
              复制链接
            </Button>
          </span>
          <Link to={backToUrl}>
            <Button>
              关闭
            </Button>
          </Link>
        </div>
        }
        <div className={classnames('container-fluid', styles.surveyPage)}>
          {this.isNotA4() && !isPreview && (
            <div className={classnames('hidden-xs', styles.progressWapper)}>
              <div className={styles.progressCircle}>
                <Progress
                  type="circle"
                  width={40}
                  percent={now}
                  strokeColor="orange"
                />
              </div>
            </div>
          )}
          {false && !isPreview && (
            <a
              className={classnames('btn btn-default', styles.helpButton)}
              href="https://qutke.cn/articles/593a6f15e4b0f7d6dbcb0a48"
              target="_blank"
            >
              <i className="fa fa-question-circle-o" />{' '}
              帮助文档
            </a>
          )}
          {false && !isPreview && (
            <Tooltip
              placement="bottom"
              title='打印'
            >
              <button
                className={classnames('btn btn-default', styles.printButton)}
                onClick={() => {
                  window.print()
                }}
              >
                <i className="fa fa-print" />
              </button>
            </Tooltip>
          )}
          {status === 'off' && !isPreview && (
            <Tag color='gold' className={styles.offStatusTip}
            >问卷已经停止回收</Tag>
          )}
          {status !== 'off' && !resubmit && this.props.survey.hasAnswer && !isPreview && (
            <Tag color='gold' className={styles.offStatusTip}
            >您已经提交过问卷</Tag>
          )}
          <div
            className={classnames(styles.surveyWapper, {
              [styles.isPreview]: isPreview
            })}
          >
            {viewMode === 'tab' && tabs.length > 1 && (
              <SurveyTab
                switchSurveyTab={this.switchTab}
                {...{ tabs, currentTab }}
              />
            )}
            <div className={classnames(styles.surveyMain)}>
              {currentTab === tabs[0].id && (
                <div className={styles.surveyTitle}>
                  <div className="inner">
                    <h3>{title}</h3>
                  </div>
                </div>
              )}
              {currentTab === tabs[0].id && (
                <div className={styles.surveyDescription}>
                  <div className="inner">
                    <div
                      className="fr-element fr-view"
                      dangerouslySetInnerHTML={{ __html: description }}
                    />
                  </div>
                </div>
              )}
              <div className={styles.surveyContainer}>
                {(questions || []).map((question, index) => {
                  if (question.type === 'sort' && sortResults[question._id]) {
                    const answerMap = sortResults[question._id].reduce(
                      (map, item) => {
                        map[item.question] = item.answer
                        return map
                      },
                      {}
                    )
                    question.options = question.options.sort(
                      (fst, snd) => answerMap[fst.value] - answerMap[snd.value]
                    )
                  }
                  return (
                    <div key={'show' + question._id + index}>
                      <Question
                        {...{
                          survey: this.props.survey,
                          question,
                          inputError,
                          extractMatrix: this.extractMatrix,
                          extractResult,
                          attachmentResult,
                          uploadAttachment: this.uploadAttachment,
                          resetSurveyState: this.resetSurveyState,
                          currentUser,
                          isPreview
                        }}
                        index={indexMap[question._id]}
                        answer={
                          completes[question._id] ||
                          sortResults[question._id] ||
                          answers[question._id] ||
                          ''
                        }
                        answerRemark={remarks[question._id] || ''}
                        otherAnswer={otherAnswers[question._id] || ''}
                        handleInputError={this.handleInputError}
                        handleRemarkChange={this.mergeAnswerRemark}
                        handleOtherChange={this.mergeOtherAnswer}
                        handleSort={this.mergeSortResult}
                        handleAttachment={this.mergeAttachment}
                        handleChange={this.mergeAnswer}
                      />
                      {(index + 1) % 3 === 0 && <div className="pagebreak" />}
                    </div>
                  )
                })}
              </div>
              <div className={classnames('inner', styles.buttonWapper)}>
                {this.props.survey.hasAnswer && (
                  <a
                    className={classnames('btn btn-default', styles.nextButton)}
                    target="_blank"
                    href={`/api/admin/answers/${this.props.survey.answerId
                      }/excel?token=${token.slice(7)}`}
                  >
                    导出Excel
                  </a>
                )}
                {!isPreview &&
                  status === 'on' &&
                  (!this.props.survey.hasAnswer || resubmit) && (
                    <button
                      className={classnames('btn btn-info', styles.nextButton)}
                      onClick={this.temporarySubmit}
                    >
                      保存
                    </button>
                  )}
                {viewMode === 'tab' && currentTab !== tabs[tabs.length - 1].id && (
                  <button
                    className={classnames('btn btn-primary', styles.nextButton)}
                    onClick={this.nextTab}
                  >
                    下一页
                  </button>
                )}
                {!isPreview &&
                  status === 'on' &&
                  (!this.props.survey.hasAnswer || resubmit) &&
                  currentTab === tabs[tabs.length - 1].id && (
                    <button
                      className={classnames(
                        'btn btn-primary',
                        styles.nextButton
                      )}
                      onClick={this.submit}
                    >
                      提交
                    </button>
                  )}
              </div>
            </div>
          </div>
        </div>
      </div >
    )
  }

}
export default Survey
