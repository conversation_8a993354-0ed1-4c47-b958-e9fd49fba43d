.previewNavbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1030;
  height: 54px;
  background-color: #000;
  color:#fff;
  padding: 0 15px;
  display:flex;
  justify-content:flex-end;
  align-items: center;
  & > * {
    margin-right: 10px;
  }
}

.progress {
  width: 100%;
  position: fixed;
  top: -8px;
  left: -8px;
  z-index: 1050;
}

.surveyPage {
  position: relative;
  // max-width: 1000px;
  padding-top: 20px;
  .offStatusTip {
    width: 100%;
    height: 37px;
   line-height: 3;
    margin-bottom: 15px;
  }
  .progressWapper {
    position: absolute;
    right: -80px;
    width: 80px;
  }
  .progressCircle {
    width: 40px;
    height: 40px;
    position: fixed;
    top: 60px;
    right: 30px;
    z-index: 1;
  }
  .progressText {
    position: absolute;
    right: 3px;
    font-size: 11px;
    top: 12px;
    font-weight: bold;
    color: orange;
  }
  .printButton {
    position: fixed;
    top: 65px;
    right: 85px;
    z-index: 1;
    color:white;
    background-color: #343434;
    border: none;
  }
  .helpButton {
    position: fixed;
    top: 65px;
    right: 145px;
    z-index: 1;
    color:white;
    background-color: #343434;
    border: none;
  }
}
.surveyWapper {
  display: inline-block;
  zoom: 1;
  vertical-align: middle;
  white-space: normal;
  position: relative;
  font-size: 14px;
  text-align: left;
  width: 100%;
  :global(.inner) {
    margin-left: auto;
    margin-right: auto;
    // max-width: 710px;
  }
}
.surveyWapper.isPreview {
  margin-top: 15px;
}
.surveyMain {
  padding: 15px 30px 30px 30px;
  border: 1px solid #383737;
  border-top: 0;
  background-color: #0d131b;
  color: #fff;
  .surveyTitle, .surveyDescription {
    margin-bottom: 10px;
    padding: 5px 0;
  }
  .surveyTitle {
    text-align: center;
  }
  .surveyDescription {
    font-size: 15px;
    line-height: 1.6;
    p {
      white-space: pre-wrap;
    }
  }
  .buttonWapper {
    text-align: center;
    margin-top: 20px;
  }
  .submitButton {
    border-radius: 4px;
    width: 50%;
    height: 50px;
    font-size: 16px;
    background-color: transparent;
    color: orange;
    border-color: orange;

    &:hover,
    &:focus,
    &:active {
      color: white;
      background-color: orange;
      border-color: orange;
    }
  }
  .nextButton {
    padding: 12px;
    line-height: 1.6;
    min-width: 140px;
    margin: 10px 20px;
    background-color: orange;
  }
  .nextButton:focus {
    outline: none;
  }
  .nextButton:active {
   background-color: rgb(226, 147, 0);
  }
}
.surveyContainer {
}

.spinnerWapper {
  position: fixed;
  margin-left: -54px;
  top: 30%;
  left: 50%;
  z-index: 10000;
}

@media screen and (max-width: 992px) {
  .surveyWapper {
    padding-top: 0;
    margin-top: 0;
    background-color: transparent;
    border: none;
  }
  .surveyMain {
    .submitButton {
      width: 100%;
    }
  }
}

@media screen and (max-width: 768px) {
  :global(.ant-empty) {
    height: 2px;
  }
}
