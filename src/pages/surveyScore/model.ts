import { Effect } from 'dva'
import { Reducer } from 'redux'
import { loadAnswerList, querySurvey, loadList, update } from './service'

export interface SurveyScoreModelState {
  statSurveys?: any,
  survey?: any,
  answers?: any
}

export interface ModelType {
  namespace: 'surveyScore';
  state: SurveyScoreModelState;
  effects: {
    fetchAnswers: Effect;
    fetchSurvey: Effect;
    fetchStat: Effect;
    updateSurvey: Effect;

  };
  reducers: {
    save: Reducer<SurveyScoreModelState>;
    update: Reducer<SurveyScoreModelState>;
    resetSurveyState: Reducer<ModelState>;
  };
}

const StatModel: ModelType = {
  namespace: 'surveyScore',
  state: {
    statSurveys: [],
    survey: null,
    answers: []
  },
  effects: {
    *fetchSurvey({ payload: { id } }, { call, put }) {
      const response = yield call(querySurvey, id)
      yield put({
        type: 'save',
        payload: {
          survey: response,
        },
      })
    },
    *fetchAnswers({ payload: { id, params } }, { call, put }) {
      const response = yield call(loadAnswerList, id, params)
      yield put({
        type: 'save',
        payload: {
          answers: response,
        },
      })
    },
    *fetchStat({ payload: { params } }, { call, put }) {
      const response = yield call(loadList, params)
      yield put({
        type: 'save',
        payload: {
          statSurveys: response,
        },
      })
    },
    *updateSurvey({ payload: { id, data } }, { call, put }) {
      const response = yield call(update, id, data)
      console.log('response', response)
      yield put({
        type: 'update',
        payload: {
          result: response,
        },
      })
    },
  },
  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      }
    },
    update(state, action) {
      let list = [...state.statSurveys].map(survey => {
        if (survey._id === action.payload.result._id) {
          return {
            ...action.payload.result,
            questions: [...action.payload.result.questions].map((question, key) => ({
              ...question,
              answers:
                (state.survey &&
                  state.survey.questions[key] &&
                  state.survey.questions[key].answers) ||
                []
            })),
            answerAuthorList: survey.answerAuthorList
          }
        }
        return survey
      })
      let survey = {
        ...action.payload.result,
        questions: [...action.payload.result.questions].map((question, key) => ({
          ...question,
          answers:
            (state.survey &&
              state.survey.questions[key] &&
              state.survey.questions[key].answers) ||
            []
        }))
      }
      return {
        ...state,
        statSurveys: list,
        survey
      }
    },
    resetSurveyState(state, action) {
      return {
        ...state,
        ...action.payload.data,
      }
    }
  },
}

export default StatModel
