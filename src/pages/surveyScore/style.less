.spinnerWapper {
  position: fixed;
  margin-left: -54px;
  top: 30%;
  left: 50%;
  z-index: 10000;
}

.tableTitle {
  float: left;
}

.tableButton {
  float: right;
  margin-bottom: 20px;
}

.table {
  clear: both;
}

.chartContainer {
  margin-top: 50px;
  :global(.highcharts-container) {
    max-height: 650px;
  }
  .rightButton {
    float: right;
    margin-left: 10px;
    background-color: #424242 !important;
    outline: none;
    color: white;
  }
  .chartSwitcher {
    margin-bottom: 10px;
    :global(.btn.btn-default){
      color:#fffefe !important;
      background-color:#424242 !important;
      outline: none !important;
    }
    :global(.btn.btn-default:focus){
      background-color:rgb(189, 126, 9) !important;
    }
    :global(.btn.btn-default:visited){
      background-color:rgb(189, 126, 9) !important;
    }
    :global(.btn.btn-default:target){
      background-color:rgb(189, 126, 9) !important;
    }
    :global(.btn.btn-default.active){
      background-color:rgb(189, 126, 9) !important;
    }
  }
}
