import request from '@/utils/request'

export async function querySurveyList(params: any): Promise<any> {
  return request(`/api/admin/survey`, {
    method: 'GET',
    params,
  })
}

export async function querySurvey(id: string): Promise<any> {
  return request(`/api/admin/survey/${id}`, {
    method: 'GET',
  })
}

export async function loadAnswerList(id: string, params: any): Promise<any> {
  return request(`/api/admin/survey/${id}/answers`, {
    method: 'GET',
    params,
  })
}

export async function loadList(params: any): Promise<any> {
  return request(`/api/admin/stat`, {
    method: 'GET',
    params,
  })
}
export async function update(id: string, data: any): Promise<any> {
  return request(`/api/admin/survey/${id}`, {
    method: 'PUT',
    data,
  })
}









