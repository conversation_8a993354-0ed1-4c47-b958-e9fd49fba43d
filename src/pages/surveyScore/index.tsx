import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'dva'
import AntTable from '@/components/AntTable'
import ExportTableData from '@/components/ExportTableData'
import ScoringRuleModal from '@/components/ScoringRuleModal'
import CustomScoreModal from '@/components/CustomScoreModal'
import deduplication from '../../utils/deduplication'
import Chart from '@/components/Chart/Chart'
import classnames from 'classnames'
import { parse } from 'qs'
import { Button, Empty, Spin } from 'antd';
import styles from './style.less'
import { SurveyScoreModelState } from './model'

interface ComponentProps {
  dispatch: Dispatch<any>;
  loading: boolean;
  location: any;
  match: any;
  currentUser?: CurrentUser,
  survey: any,
  answers: any,
  surveys?: any
}

interface ComponentState {
  showRuleModal: boolean,
  showCustomScoreModal: boolean,
  type: string,
  quotas: Object
}


@connect(
  ({
    surveyScore,
    loading,
  }: {
    surveyScore: SurveyScoreModelState;
    loading: {
      effects: {
        [key: string]: boolean;
      };
    };
  }
  ) => {
    return ({
      survey: surveyScore.survey,
      surveys: surveyScore.statSurveys,
      answers: surveyScore.answers,
      loading: loading.effects['surveyScore/fetchSurveyList'],
    })
  }
)

export default class SurveyScore extends Component<ComponentProps, ComponentState> {
  static propTypes = {
    location: PropTypes.object.isRequired,
    loadSurvey: PropTypes.func.isRequired,
    loadAnswerList: PropTypes.func.isRequired,
    survey: PropTypes.object,
    answers: PropTypes.object.isRequired,
    update: PropTypes.func,
    surveys: PropTypes.array.isRequired,
    resetSurveyState: PropTypes.func,
    loadList: PropTypes.func
  }

  constructor(props) {
    super(props)
    const quotas = this.generateQuotas(props.surveys)
    this.state = {
      showRuleModal: true,
      showCustomScoreModal: false,
      type: 'line',
      quotas
    }
  }

  async componentDidMount() {
    const query = !!this.props.location.search && parse(this.props.location.search.substr(1))
    const category = query && query.category
    if (category) {
      await this.loadList({ category })
    }
    const id = query && query.id
    this.loadSurvey(id, {})
    this.loadAnswerList(id, {})
  }

  resetSurveyState = (data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyScore/resetSurveyState',
      payload: { data },
    })
  }

  loadSurvey = (id: any, params: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyScore/fetchSurvey',
      payload: { id, params },
    })
  }

  loadAnswerList = (id: any, params: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyScore/fetchAnswers',
      payload: { id, params },
    })
  }

  loadList = (params: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyScore/fetchStat',
      payload: { params },
    })
  }

  update = (id: string, data: any) => {
    const { dispatch } = this.props
    dispatch({
      type: 'surveyScore/updateSurvey',
      payload: { id, data },
    })
  }


  componentWillReceiveProps(nextProps) {
    const { survey, answers, surveys } = nextProps
    if (survey) {
      const { isWeightedScoring, questions, customScoringRules } = survey
      const isAllWeightSumOne = isWeightedScoring
        ? this.allWeightSumOne(questions, customScoringRules)
        : true
      const isRuleSeted = survey.questions
        .filter(this.questionFilter)
        .some(question => question.isScoring)
      const data = this.generateStatData()
      if (
        isAllWeightSumOne &&
        (isRuleSeted ||
          (!!survey && survey.customScoringRules && !!survey.customScoringRules.length))
      ) {
        this.setState({ showRuleModal: false })
      } else {
        this.setState({ showRuleModal: true })
      }
    }
    if (!this.props.surveys.length && surveys.length) {
      const quotas = this.generateQuotas(surveys)
      this.setState({ quotas })
    }
  }

  componentWillUnmount() {
    this.resetSurveyState({
      statSurveys: [],
      survey: null,
      answers: { list: [] }
    })
  }

  getTableDatas() {
    const { survey, answers } = this.props
    const surveyQuestions = survey && survey.questions
    return answers.list
      .map(answer => {
        const authorAnswer = { author: answer.author }
        authorAnswer.sumScore = (surveyQuestions || [])
          .filter(this.questionFilter)
          .reduce((sum, question) => {
            if (question.isScoring) {
              if (
                question.options &&
                !!question.options.length &&
                question.answers
              ) {
                const choosedAnswer = question.answers.find(
                  ans => ans.author._id === answer.author._id
                )
                const choosedOption =
                  choosedAnswer &&
                  question.options.find(
                    option => option.value === choosedAnswer.answer
                  )
                authorAnswer[question.title] = choosedOption
                  ? choosedOption.score *
                  (survey.isWeightedScoring ? question.weight : 1)
                  : 0
                sum += authorAnswer[question.title] // eslint-disable-line
              }
            }
            return sum
          }, 0)
        if (!!survey && survey.customScoringRules && !!survey.customScoringRules.length) {
          const customScoringRules = survey.customScoringRules
          const customScoring = survey.customScoring
          const authorCustomScoring = customScoringRules.reduce(
            (authorScore, rule) => {
              let score = ''
              const findScore =
                customScoring &&
                customScoring.find(
                  cus =>
                    cus.name === rule.name && cus.userId === answer.author._id
                )
              if (findScore) {
                score = findScore.score
              }
              authorScore.push({
                name: rule.name,
                weight: rule.weight,
                score: score
              })
              return authorScore
            },
            []
          )
          authorAnswer.sumScore += authorCustomScoring.reduce(
            (cusScoSum, cur) => {
              authorAnswer[cur.name] =
                cur.score * (survey.isWeightedScoring ? cur.weight : 1)
              cusScoSum += authorAnswer[cur.name] // eslint-disable-line
              return cusScoSum
            },
            0
          )
        }
        return authorAnswer
      })
      .map(item => ({
        __name: item.author && item.author.nickname,
        __company: item.author && item.author.company,
        _id: item.author && item.author._id,
        ...item
      }))
  }

  generateStatData() {
    const { surveys } = this.props
    const answerUserMap = surveys.reduce((out, survey) => {
      out.push(survey.answerAuthorList.map(author => author._id))
      return out
    }, [])
    const answerList = answerUserMap.map((answerUser, key) =>
      answerUser.reduce((sumMap, userId) => {
        const survey = surveys[key]
        sumMap[userId] = survey.questions
          .filter(this.questionFilter)
          .reduce((sum, question) => {
            if (question.isScoring) {
              if (
                question.options &&
                !!question.options.length &&
                question.answers &&
                !!question.answers.length
              ) {
                const choosedAnswer = question.answers.find(
                  ans => ans.author._id === userId
                )
                const choosedOption =
                  choosedAnswer &&
                  question.options.find(
                    option => option.value === choosedAnswer.answer
                  )
                if (choosedOption) {
                  sum +=
                    choosedOption.score *
                    (survey.isWeightedScoring ? question.weight : 1) // eslint-disable-line
                }
              }
            }
            return sum
          }, 0)
        if (!!survey && survey.customScoringRules && !!survey.customScoringRules.length) {
          const customScoringRules = survey.customScoringRules
          const customScoring = survey.customScoring
          const authorCustomScoring = customScoringRules.reduce(
            (authorScore, rule) => {
              let score = ''
              const findScore =
                customScoring &&
                customScoring.find(
                  cus => cus.name === rule.name && cus.userId === userId
                )
              if (findScore) {
                score = findScore.score
              }
              authorScore.push({
                name: rule.name,
                weight: rule.weight,
                score: score
              })
              return authorScore
            },
            []
          )
          sumMap[userId] += authorCustomScoring.reduce((cusScoSum, cur) => {
            cusScoSum += cur.score * (survey.isWeightedScoring ? cur.weight : 1) // eslint-disable-line
            return cusScoSum
          }, 0)
        }
        return sumMap
      }, {})
    )
    const userMap = surveys
      .reduce((out, survey) => {
        return out.concat(survey.answerAuthorList)
      }, [])
      .reduce((out, author) => {
        out[author._id] = author.company
        return out
      }, {})
    const userIds = deduplication(
      answerList
        .map(item => Object.keys(item))
        .reduce((out, keys) => out.concat(keys), [])
    )
    const data = userIds.map(userId => {
      const ret = { name: userMap[userId] }
      answerList.forEach((item, index) => {
        ret[`survey-${index}`] = item[userId]
      })
      return ret
    })
    return data
  }

  generateQuotas(surveys) {
    const quotas = [
      {
        name: '公司',
        value: 'name'
      }
    ]

    surveys.forEach((item, index) => {
      quotas.push({
        name: item.title,
        value: `survey-${index}`
      })
    })
    return quotas
  }

  allWeightSumOne(questions, customScoringRules) {
    const questionsWeights = questions.reduce((sum, cur) => {
      if (cur.isScoring) {
        return sum + Number(cur.weight)
      }
      return sum
    }, 0)
    const customWeights = customScoringRules.reduce(
      (sum, cur) => sum + Number(cur.weight),
      0
    )
    return Math.abs(questionsWeights + customWeights - 1) < 0.001
  }

  questionFilter = question =>
    question.type === 'select' || question.type === 'radio'

  switchChartType = type => () => {
    this.setState({ type })
  }

  renderChart() {
    const { type } = this.state
    const { surveys } = this.props
    const categories = surveys.map(survey => survey.title)
    const data = this.generateStatData()
    const series = data.map(item => {
      return {
        name: item.name,
        data: categories
          .map((category, index) => item[`survey-${index}`])
          .map(value =>
            value === undefined || Number.isNaN(Number(value))
              ? null
              : Number(value)
          )
      }
    })
    const config = {
      chart: {
        type: type,
        pinchType: '',
        height: 400
      },
      exporting: {
        enabled: false
      },
      title: {
        text: '总分交叉分析'
      },
      credits: {
        enabled: false
      },
      xAxis: {
        categories
      },
      yAxis: {
        title: {
          text: '分数'
        }
      },
      colors: [
        '#7cb5ec',
        '#90ed7d',
        '#f7a35c',
        '#8085e9',
        '#f15c80',
        '#e4d354',
        '#2b908f',
        '#f45b5b',
        '#91e8e1'
      ],
      series: series,
      tooltip: {
        useHTML: true,
        pointFormatter() {
          let result = ''
          result += `<span style="color: ${this.color}">\u25CF</span> ${this.series.name
            }: `
          result += `<span>${this.y}</span><br/>`
          return result
        }
      }
    }
    return <Chart options={config} />
  }

  render() {
    const { type, quotas } = this.state
    const { survey, answers, } = this.props
    const data = this.generateStatData()
    const tableDatas =
      answers && answers.list && !!answers.list.length ? this.getTableDatas() : []
    const tableQuotas = [
      { name: '姓名', value: '__name', hasFilter: true, fixed: 'left' },
      {
        name: '公司',
        value: '__company',
        hasFilter: true,
        fixed: 'left'
      },
      { name: '总分', value: 'sumScore', fixed: 'left' }
    ]
      .concat(
        deduplication(
          tableDatas.reduce((out, datas) => {
            return out.concat(Object.keys(datas))
          }, [])
        )
          .filter(
            item =>
              !~['author', 'sumScore', '__name', '__company', '_id'].indexOf(
                item
              )
          )
          .map(item => ({ name: item, value: item }))
      )
      .map(quota => ({ ...quota, hasSorter: true }))
    const finalDatas = { quotas: tableQuotas, data: tableDatas }
    const { showRuleModal, showCustomScoreModal } = this.state
    if (!survey) {
      return (
        <div className={styles.spinnerWapper}>
          <Spin />
        </div>
      )
    }
    if (!survey) {
      return false
    }
    return (
      <div style={{ backgroundColor: '#0a0a0a', minHeight: 400, color: 'white' }}>
        <div>
          <div style={{ textAlign: 'center', margin: 10 }}>
            <h3 >{survey.title}</h3>
          </div>
          <div style={{ float: 'right', marginRight: 10 }}>
            <Button style={{ marginRight: 10 }} onClick={() => { this.setState({ showRuleModal: true }) }}>编辑规则</Button>
            {
              survey && survey.customScoringRules &&
              survey.customScoringRules.length &&
              answers && answers.list &&
              answers.list.length &&
              (
                <Button onClick={() => { this.setState({ showCustomScoreModal: true }) }}>自定义问题打分</Button>
              )}
          </div>

        </div>
        {answers && answers.list && !answers.list.length ? (
          <Empty description='还没有回收到答案' />
        ) : (
          <div className="container">
            <div>
              <h4 className={styles.tableTitle}>
                用户得分表              </h4>
              <div
                className={styles.tableButton}
              >
                <ExportTableData
                  name={`${survey.title}-评分表`}
                  id="survey-score"
                  data={tableDatas}
                  quotas={tableQuotas}
                />
              </div>
              <div className={styles.table}>
                <AntTable {...finalDatas} />
              </div>
            </div>
            {!!data.length && (
              <div className={styles.chartContainer}>
                <div
                  className={classnames(
                    'btn-group',
                    styles.rightButton,
                    styles.chartSwitcher
                  )}
                >
                  <button
                    className={classnames('btn btn-default', {
                      active: type === 'line'
                    })}
                    onClick={this.switchChartType('line')}
                  >
                    <i className="fa fa-line-chart" />
                  </button>
                  <button
                    className={classnames('btn btn-default', {
                      active: type === 'column'
                    })}
                    onClick={this.switchChartType('column')}
                  >
                    <i className="fa fa-bar-chart" />
                  </button>
                </div>
                <button
                  className={classnames('btn btn-default', styles.rightButton)}
                >
                  <ExportTableData
                    name={`${survey.title}-交叉分析`}
                    id="cross-stat"
                    data={data}
                    quotas={quotas}
                  />
                </button>
                {this.renderChart()}
              </div>
            )}
          </div>
        )
        }
        <ScoringRuleModal
          show={showRuleModal}
          close={() => {
            this.setState({ showRuleModal: false })
          }}
          survey={survey}
          update={this.update}
          answers={answers}
        />
        <CustomScoreModal
          show={showCustomScoreModal}
          close={() => {
            this.setState({ showCustomScoreModal: false })
          }}
          survey={survey}
          answers={answers}
          update={this.update}
        />
      </div >
    )
  }
}
