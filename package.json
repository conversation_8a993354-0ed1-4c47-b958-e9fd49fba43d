{"name": "qutke-fof", "version": "1.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "format-imports": "import-sort --write '**/*.{js,jsx,ts,tsx}'", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "lint": "npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "check-prettier lint", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write \"**/*\"", "start": "UMI_UI=none umi dev", "start:no-mock": "cross-env MOCK=none umi dev", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components"}, "husky": {"hooks": {"f-pre-commit": "npm run lint-staged"}}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write", "git add"], "**/*.{js,jsx}": "npm run lint-staged:js", "**/*.{js,ts,tsx}": "npm run lint-staged:js"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@alifd/next": "^1.26.12", "@ant-design/compatible": "^1.0.1", "@ant-design/icons": "^4.0.2", "@ant-design/pro-form": "^1.5.1", "@ant-design/pro-layout": "^5.0.6", "@ant-design/pro-table": "^2.2.0", "@antv/data-set": "^0.10.2", "@formily/antd": "^1.2.10", "@formily/antd-components": "^1.2.10", "@formily/core": "^2.0.1", "@formily/printer": "^1.2.10", "@formily/react": "^2.0.1", "@types/highcharts": "^5.0.44", "@umijs/hooks": "^1.9.1", "ahooks": "^3.4.0", "ali-react-table": "^2.6.1", "antd": "^4.24.12", "bootstrap": "^3.4.1", "classnames": "^2.2.6", "clipboard": "^2.0.8", "compute-kurtosis": "^1.0.0", "compute-skewness": "^1.0.1", "dva": "^2.4.1", "echarts": "^4.9.0", "echarts-for-react": "^2.0.15-beta.1", "echarts-wordcloud": "^1.1.3", "font-awesome": "^4.7.0", "highcharts": "^6.2.0", "highcharts-export-csv": "^1.4.8", "highcharts-react-official": "^2.2.2", "jquery": "^3.6.0", "lodash": "^4.17.11", "moment": "^2.27.0", "omit.js": "^1.0.2", "path-to-regexp": "^3.0.0", "qrcode.react": "^1.0.0", "qs": "^6.7.0", "rc-progress": "^3.1.4", "react": "^16.8.6", "react-beautiful-dnd": "^13.0.0", "react-bootstrap": "^1.6.3", "react-color": "^2.18.0", "react-copy-to-clipboard": "^5.0.1", "react-data-grid": "^2.0.44", "react-document-title": "^2.0.3", "react-dom": "^16.8.6", "react-froala-wysiwyg": "2.7.0-1", "react-helmet-async": "^1.0.4", "react-scroll": "^1.7.14", "redux": "^4.0.1", "rheostat": "^2.1.0", "simple-statistics": "^7.0.5", "string-width": "^4.1.0", "superagent": "^6.1.0", "typescript": "^4.9.5", "umi": "^2.13.4", "umi-plugin-antd-icon-config": "^1.0.3", "umi-plugin-react": "^1.9.5", "umi-request": "^1.0.8", "use-merge-value": "^1.0.1", "validator": "^13.6.0", "xlsx": "^0.16.8"}, "devDependencies": {"@ant-design/colors": "^3.1.0", "@ant-design/pro-cli": "^1.0.0", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^24.0.13", "@types/lodash": "^4.14.133", "@types/qs": "^6.5.3", "@types/react": "^16.8.19", "@types/react-document-title": "^2.0.3", "@types/react-dom": "^16.8.4", "@umijs/fabric": "^1.1.0", "chalk": "^2.4.2", "check-prettier": "^1.0.3", "cross-env": "^5.2.0", "cross-port-killer": "^1.1.1", "enzyme": "^3.9.0", "eslint": "^6.3.0", "eslint-config-prettier": "^6.7.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^9.2.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "express": "^4.17.1", "gh-pages": "^2.0.1", "husky": "^3.0.0", "import-sort-cli": "^6.0.0", "import-sort-parser-babylon": "^6.0.0", "import-sort-parser-typescript": "^6.0.0", "import-sort-style-module": "^6.0.0", "jest-puppeteer": "^4.2.0", "lint-staged": "^9.0.0", "mockjs": "^1.1.0", "node-fetch": "^2.6.0", "prettier": "^1.17.1", "pro-download": "1.0.1", "slash2": "^2.0.0", "stylelint": "^10.1.0", "umi-plugin-ga": "^1.1.3", "umi-plugin-pro": "^1.0.2", "umi-types": "^0.3.8", "webpack-theme-color-replacer": "^1.2.15"}, "optionalDependencies": {"puppeteer": "^1.17.0"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}